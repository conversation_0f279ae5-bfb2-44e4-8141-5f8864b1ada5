module.exports = {
  env: {
    browser: true,
    es6: true,
  },
  parser: 'babel-eslint',
  extends: ['airbnb', 'prettier', 'prettier/react'],
  globals: {
    Atomics: 'readonly',
    SharedArrayBuffer: 'readonly',
  },
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
      modules: true,
    },
    ecmaVersion: 2018,
    sourceType: 'module',
  },
  plugins: ['react', 'prettier'],
  rules: {
    'prettier/prettier': [
      'error',
      { singleQuote: true, parser: 'flow', endOfLine: 'auto' },
    ],
    'react/jsx-filename-extension': [1, { extensions: ['.js', '.jsx'] }],
    'import/no-unresolved': [2, { ignore: ['@chill/*'] }],
    'react/forbid-prop-types': [0, { forbid: ['any'] }],
    'react/prop-types': 0,
    'import/no-extraneous-dependencies': [0],
    'react-hooks/exhaustive-deps': [0],
    'prefer-promise-reject-errors': [0],
    'no-console': [0],
    'no-underscore-dangle': [0],
    'jsx-a11y/media-has-caption': [0],
    camelcase: [0],
  },
  env: {
    jest: true,
    browser: true,
    node: true,
  },
};

# ChillBaby User Panel Frontend

## Getting Started

### Installation

```sh
$ <NAME_EMAIL>:chillbaby/chillbaby-admin.git
$ cd chillbaby-admin
$ yarn
$ yarn start
```
## Demo
[User Panel](https://www.chillbaby.io/)

And make `.env` in the root folder of working directory and add required keys like below format:

```
REACT_APP_AUTH0_DOMAIN=xxxx
REACT_APP_AUTH0_CLIENT_ID=xxxx
```
#### Credentials for Admin
```
email: <EMAIL>
password: 123456
```

#### Credentials for Brand Admin
```
email: <EMAIL>
password: 123456
```

## Authors

- **Groovy web LLP** - [<EMAIL>](https://groovyweb.firm.in/)

---

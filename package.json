{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.0.1", "private": true, "dependencies": {"@ant-design/charts": "^1.1.0", "@ant-design/icons": "^4.8.0", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@mui/material": "^5.12.1", "@mui/styled-engine-sc": "^5.12.0", "@react-google-maps/api": "^2.18.1", "@react-pdf/renderer": "2.1.0", "@reduxjs/toolkit": "^1.9.5", "@stripe/react-stripe-js": "^1.16.1", "@stripe/stripe-js": "^1.46.0", "@tinymce/tinymce-react": "^4.2.0", "antd": "^4.10.0", "antd-img-crop": "^4.12.2", "apexcharts": "^3.53.0", "auth0-lock": "^12.0.2", "axios": "^1.2.1", "base64-blob": "^1.4.1", "browser-image-compression": "^2.0.2", "fabric": "^5.2.4", "fabric-history": "^1.7.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "konva": "^9.2.0", "less": "^4.1.3", "lodash": "^4.17.21", "moment": "^2.29.4", "node-sass": "^8.0.0", "prop-types": "^15.8.1", "puppeteer": "^19.4.0", "query-string": "^8.0.3", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-async-script-loader": "^0.3.0", "react-beautiful-dnd": "^13.1.1", "react-circular-progressbar": "^2.1.0", "react-color": "^2.19.3", "react-content-loader": "^6.2.1", "react-custom-scrollbars": "^4.2.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-gauge-chart": "^0.4.1", "react-google-charts": "^4.0.0", "react-icons": "^4.10.1", "react-image-crop": "^10.0.9", "react-images-uploading": "^3.1.7", "react-intl": "^2.9.0", "react-inverted-scrollview": "^1.0.7", "react-konva": "^18.2.9", "react-otp-input": "^2.4.0", "react-redux": "^8.0.5", "react-responsive-carousel": "^3.2.18", "react-router": "^5.1.2", "react-router-dom": "^5.1.2", "react-scripts": "^4.0.3", "react-simple-maps": "^3.0.0", "react-slick": "^0.29.0", "react-tiny-popover": "^8.1.4", "react-to-print": "^2.14.11", "react-tooltip": "^4.2.19", "recharts": "^2.12.7", "redux": "^4.2.1", "redux-devtools": "^3.7.0", "redux-saga": "^1.2.2", "redux-thunk": "^2.4.2", "sails.io.js": "^1.2.1", "slick-carousel": "^1.8.1", "socket.io-client": "2.4.0", "styled-components": "^5.3.9", "styled-theme": "^0.3.3", "use-react-screenshot": "^3.0.0", "victory": "^36.6.8", "xml2js": "^0.6.2"}, "devDependencies": {"babel-eslint": "^10.1.0", "babel-plugin-import": "^1.12.2", "better-docs": "^2.3.0", "customize-cra": "^0.8.0", "eslint": "^7.11.0", "eslint-config-airbnb": "^18.1.0", "eslint-config-prettier": "^6.10.1", "eslint-plugin-import": "^2.20.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-react": "^7.19.0", "jest-puppeteer": "^4.4.0", "jsdoc": "^3.6.5", "less-loader": "^5.0.0", "prettier": "^2.8.8", "react-app-rewire-webpack-bundle-analyzer": "^1.1.0", "react-app-rewired": "^2.1.4", "redux-devtools-extension": "^2.13.8"}, "scripts": {"start": "react-app-rewired --max_old_space_size=4096 start", "build": "react-app-rewired --max_old_space_size=4096 build", "serve": "serve -s build", "test": "jest", "eject": "react-scripts eject", "docs": "jsdoc -c jsdoc.conf.json"}, "eslintConfig": {"extends": "react-app"}, "resolutions": {"@react-pdf/font": "2.2.0"}, "browserslist": [">0.2%", "not dead", "not op_mini all"]}
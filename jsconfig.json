{"compilerOptions": {"baseUrl": ".", "paths": {"@chill/assets/*": ["./src/assets/*"], "@chill/components/*": ["./src/components/*"], "@chill/config/*": ["./src/config/*"], "@chill/containers/*": ["./src/containers/*"], "@chill/redux/*": ["./src/redux/*"], "@chill/lib/*": ["./src/shared/common/library/*"], "@chill/editor/*": ["./src/editor/*"]}}, "exclude": ["node_modules", "**/node_modules/*", "coverage", "build", "__tests__"]}
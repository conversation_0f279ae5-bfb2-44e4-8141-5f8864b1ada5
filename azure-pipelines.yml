# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

trigger:
 branches:
   include:
     - UAT
   exclude:
     - dev

variables:
  uiName: 'admin-dashboard'
  uiBuild: '$(uiSource)/build'

pool:
  name: cbt_UAT

stages:
- stage: Build
  jobs:
    - job: buildjob
      steps:      
        - task: DownloadSecureFile@1
          inputs:
            secureFile: '.env'
         
        - task: CopyFiles@2
          inputs:
            SourceFolder: '$(Agent.TempDirectory)'
            Contents: '.env'
            TargetFolder: '$(Build.SourcesDirectory)'

      
        - script: |
            yarn install 
            echo "yarn version:"
            yarn --version
          displayName: 'yarn packages install'

        - script: |
            yarn build
          displayName: 'yarn build'
          
        - task: ArchiveFiles@2
          inputs:
            rootFolderOrFile: '$(System.DefaultWorkingDirectory)/build'
            includeRootFolder: false
            archiveType: 'tar'
            archiveFile: '$(Build.ArtifactStagingDirectory)/$(uiName).tar.gz'
            replaceExistingArchive: true

        - task: PublishBuildArtifacts@1
          inputs:
            PathtoPublish: '$(Build.ArtifactStagingDirectory)'
            ArtifactName: 'drop'
            publishLocation: 'Container' 
          
- stage:  DevDeploy
  dependsOn: Build
  jobs:
    - deployment: Deploy
      environment: Dev Environment
      strategy:
       runOnce:
        deploy:
            steps:
              - task: CmdLine@2
                inputs:
                  script: |
                    cd $(pipeline.workspace)/drop/                    
                    sudo mv admin-dashboard.tar.gz /var/www/html/
                    cd /var/www/html/
                    sudo rm -rf dashboard.old/
                    sudo mv dashboard/ dashboard.old/
                    sudo mkdir dashboard
                    sudo tar -xvf admin-dashboard.tar.gz --directory dashboard/
                    sudo rm admin-dashboard.tar.gz

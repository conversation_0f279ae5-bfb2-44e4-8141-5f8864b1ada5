const { waitFor } = require('./utility');

async function login(page, credentials = {}, type = 'waitNav') {
  /** Waiting to load form inputs */
  await waitFor(page, '.auth0-lock-form input');

  /** Fill signin form required fields */
  await page.click('[name="email"]');
  await page.type(
    '[name="email"]',
    credentials.email || '<EMAIL>',
  );
  await page.click('[name="password"]');
  await page.type('[name="password"]', credentials.password || '123456!aA');

  /** Submit the form and waiting to redirection */
  await Promise.all([
    page.click('.auth0-lock-submit'), // Clicking the submit btn redirect to Dashboard
    type === 'waitNav' && page.waitForNavigation(), // The promise resolves after navigation has finished
  ]);
}

module.exports = login;

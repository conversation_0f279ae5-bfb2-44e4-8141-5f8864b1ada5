async function waitFor(page, selector, type) {
  await page.waitForSelector(selector, {
    timeout: 10000,
    visible: true,
  });
  if (type === 'click') await page.click(selector);
}

async function clearNotification(page) {
  await page.$$eval('.ant-notification-notice-close', (elHandles) =>
    elHandles.forEach((el) => el.click()),
  );
}

async function waitRes(page, msg = '') {
  await Promise.race([
    page.waitForNavigation({ waitUntil: 'networkidle0' }),
    page.waitForSelector('.ant-notification-notice-message'),
  ]);

  const resHtml = await page.$eval(
    '.ant-notification-notice-message',
    (element) => {
      return element.textContent;
    },
  );

  const con = resHtml.includes(msg);
  return con;
}
function checkImage(file, type = '') {
  let isJpgOrPng =
    file.type === 'image/jpeg' ||
    file.type === 'image/png' ||
    file.type === 'image/jpg';
  if (type === 'supportPdf') {
    isJpgOrPng =
      file.type === 'image/jpeg' ||
      file.type === 'image/png' ||
      file.type === 'image/jpg' ||
      file.type === 'application/pdf';
  }
  let message = '';
  let status = true;
  let isLt2M = file.size / 1024 / 1024 < 2;
  if (type === 'custom') {
    isLt2M = file.size / 1024 < 200;
  }

  if (!isJpgOrPng) {
    message = type === 'supportPdf' ? 'err.onlyJPGPNGPDF' : 'err.onlyJPGPNG';
    status = false;
  } else if (!isLt2M && type === 'custom') {
    message = 'err.max200KBFile';
    status = false;
  } else if (!isLt2M) {
    message = 'err.max2MBFile';
    status = false;
  }

  return { message, status };
}

module.exports = {
  waitFor,
  clearNotification,
  waitRes,
  checkImage,
};

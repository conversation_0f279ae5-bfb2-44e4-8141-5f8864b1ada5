/* eslint-disable no-param-reassign */

/**
 * @name Team Management Tests
 *
 * @desc Checks insert, update and delete operation in Team management
 *
 */

const puppeteer = require('puppeteer');
const { waitFor, waitRes } = require('../commonFunc/utility');
const login = require('../commonFunc/login');

const timeout = 30000;

let browser;
let page;

beforeAll(async () => {
  browser = await puppeteer.launch({
    headless: false,
  });
  page = await browser.newPage();
  await page.goto(URL, {
    waitUntil: 'networkidle0',
  });
});

async function fillProjectData(cred = {}) {
  const pName = cred.pName || 'Test Project';
  const pDate = cred.pDate || '20-01-2022';
  const pDetails = cred.email || 'this is new Project';

  await page.click('#project_name');
  await page.type('#project_name', pName);

  await page.click('#due_date');
  await page.type('#due_date', pDate);

  await waitFor(page, '#reminder', 'click');
  await page.waitFor(500);

  await waitFor(page, '#before_day', 'click');

  await waitFor(page, '.tox-edit-area iframe', 'click');
  await page.keyboard.type('abcd');

  await waitFor(page, '.userHeader .ant-btn-link', 'click');
  await page.waitFor(500);

  await waitFor(page, '.ant-select-selection-search', 'click');
  await page.waitFor(500);

  await waitFor(page, '.ant-select-item-option', 'click');
  await page.waitFor(500);

  await fillAddUserData({});

  await waitFor(page, '#add-docs', 'click');
  await page.waitFor(500);

  await waitFor(
    page,
    '.ant-tree-list-holder-inner .ant-tree-treenode-switcher-close',
    'click',
  );
  await page.waitFor(500);

  await waitFor(page, '#choose-doc', 'click');
  await page.waitFor(500);

  await waitFor(page, '.wtActions .ant-btn-primary', 'click');
  await page.waitFor(500);

  /** Submit the form */
  // await page.click('button[type="submit"]');
}

async function fillAddUserData(cred = {}) {
  const fName = cred.pName || 'John';
  const lName = cred.pDate || 'Parker';
  const email =
    cred.email || new Date().getTime().toString() + '@mailinator.com';

  await page.click('#first_name');
  await page.type('#first_name', fName);

  await page.click('#last_name');
  await page.type('#last_name', lName);

  await page.click('#email');
  await page.type('#email', email);

  await waitFor(page, '#is_selected', 'click');
  await page.waitFor(500);

  await waitFor(page, '#add-user', 'click');
  await page.waitFor(500);

  const res = await waitRes(page, 'Contact has been added!');

  await waitFor(page, '#add-users', 'click');
  await page.waitFor(500);
  // expect(res).toBe(true);

  /** Submit the form */
  // await page.click('button[type="submit"]');
}

describe('Test Contact Module', () => {
  test(
    'Create Project',
    async () => {
      await login(page, {
        email: '<EMAIL>',
        password: '123456@aA',
      });

      await page.goto(`${URL}/dashboard/my-projects`, {
        waitUntil: 'domcontentloaded',
      });

      await waitFor(page, '#create-project', 'click');
      await page.waitFor(2000);

      await fillProjectData({});

      const res = await waitRes(page, 'Workflow has been added!');
      expect(res).toBe(true);
    },
    timeout,
  );

  test(
    'Delete Project',
    async () => {
      await page.goto(`${URL}/dashboard/my-projects`, {
        waitUntil: 'domcontentloaded',
      });

      await waitFor(page, '#delete-confirm-card', 'click');
      await page.waitFor(500);

      await waitFor(page, '#delete-item', 'click');
      await page.waitFor(500);

      const res = await waitRes(
        page,
        'Workflow has been deleted successfully.',
      );
      expect(res).toBe(true);

      browser.close();
    },
    timeout,
  );
});

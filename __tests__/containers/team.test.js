/* eslint-disable no-param-reassign */

/**
 * @name Team Management Tests
 *
 * @desc Checks insert, update and delete operation in Team management
 *
 */

const puppeteer = require('puppeteer');
const { waitFor, waitRes } = require('../commonFunc/utility');
const login = require('../commonFunc/login');

const timeout = 30000;

let browser;
let page;

beforeAll(async () => {
  browser = await puppeteer.launch({
    headless: false,
  });
  page = await browser.newPage();
  await page.goto(URL, {
    waitUntil: 'networkidle0',
  });
});

async function fillTeamDetail(cred = {}) {
  const name = cred.name || 'Testing';
  const email = cred.email || '<EMAIL>';

  await page.click('#name');
  await page.$eval('#name', (el) => {
    el.value = '';
  });
  await page.type('#name', name);

  if (email !== '-') {
    await page.click('#email');
    await page.$eval('#email', (el) => {
      el.value = '';
    });
    await page.type('#email', email);
  }

  /** Submit the form */
  await page.click('button[type="submit"]');
}

describe('Test Team Module', () => {
  test(
    'Add team member',
    async () => {
      await login(page, {});

      await page.goto(`${URL}/dashboard/manage-team`, {
        waitUntil: 'domcontentloaded',
      });

      /** Fill required fields */
      await waitFor(page, '#create-member', 'click');
      await page.waitFor(500);

      await fillTeamDetail({});

      /** Waiting for response message */
      const res = await waitRes(page, 'User has been created successfully.');
      expect(res).toBe(true);
    },
    timeout,
  );
  test(
    'Edit team member',
    async () => {
      await page.goto(`${URL}/dashboard/manage-team`, {
        waitUntil: 'networkidle0',
      });

      await page.waitFor(500);

      /** Fill required fields */
      await waitFor(page, '#edit-member', 'click');
      await page.waitFor(500);

      await fillTeamDetail({
        name: 'Test',
        email: '-',
      });
      /** Waiting for response message */
      const res = await waitRes(page, 'User has been updated successfully.');
      expect(res).toBe(true);
    },
    timeout,
  );

  test(
    'Delete team member',
    async () => {
      await page.waitFor(500);

      await page.goto(`${URL}/dashboard/manage-team`, {
        waitUntil: 'networkidle0',
      });

      await waitFor(page, '#delete-confirm-member', 'click');

      await page.waitFor(500);

      await waitFor(page, '#delete-item', 'click');

      /** Waiting for response message */
      const res = await waitRes(page, 'User has been deleted successfully.');
      expect(res).toBe(true);

      browser.close();
    },
    timeout,
  );
});

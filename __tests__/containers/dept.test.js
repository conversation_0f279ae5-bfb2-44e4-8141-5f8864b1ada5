/* eslint-disable no-param-reassign */

/**
 * @name Department Management Tests
 *
 * @desc Checks insert, update and delete operation in Department management
 *
 */

const puppeteer = require('puppeteer');
const { waitFor, waitRes } = require('../commonFunc/utility');
const login = require('../commonFunc/login');

const timeout = 30000;

let browser;
let page;

beforeAll(async () => {
  browser = await puppeteer.launch({
    headless: false,
  });
  page = await browser.newPage();
  await page.goto(URL, {
    waitUntil: 'networkidle0',
  });
});

async function fillDeptDetail(type = '') {
  await page.click('#name');
  await page.$eval('#name', (el) => {
    el.value = '';
  });
  await page.type('#name', 'Test dept');

  /**  Select department's head from dropdown */
  await page.click('#email');
  await page.waitForSelector(
    '.email-select .ant-select-item.ant-select-item-option',
    {
      visible: true,
    },
  );
  await page.click('.email-select .ant-select-item.ant-select-item-option');

  if (type !== 'edit') {
    /** Select department's members from dropdown */
    await page.click('#department_member');
    await page.waitForSelector(
      '.member-select .ant-select-item.ant-select-item-option',
      {
        visible: true,
      },
    );
    await page.click('.member-select .ant-select-item.ant-select-item-option');
  }

  /** Submit the form */
  await page.click('button[type="submit"]');
}

describe('Test Depatment Module', () => {
  test(
    'Add Depatment',
    async () => {
      await login(page, {});

      await page.goto(`${URL}/dashboard/manage-departments`, {
        waitUntil: 'domcontentloaded',
      });

      await waitFor(page, '#create-dept', 'click');
      await page.waitFor(500);

      /** Fill required fields */
      await fillDeptDetail();

      /** Waiting for response message */
      const res = await waitRes(
        page,
        'Department and its member has been created.',
      );
      expect(res).toBe(true);
    },
    timeout,
  );
  test(
    'Edit Depatment',
    async () => {
      await page.waitFor(500);

      await page.goto(`${URL}/dashboard/manage-departments`, {
        waitUntil: 'networkidle0',
      });

      await page.waitFor(500);

      await waitFor(page, '#edit-dept', 'click');
      await page.waitFor(500);

      /** Fill required fields */
      await fillDeptDetail('edit');

      /** Waiting for response message */
      const res = await waitRes(
        page,
        'Department and its member has been updated.',
      );
      expect(res).toBe(true);
    },
    timeout,
  );

  test(
    'Delete Depatment',
    async () => {
      await page.waitFor(500);

      await page.goto(`${URL}/dashboard/manage-departments`, {
        waitUntil: 'networkidle0',
      });

      await waitFor(page, '#delete-confirm-dept', 'click');

      await page.waitFor(500);

      await waitFor(page, '#delete-item', 'click');

      /** Waiting for response message */
      const res = await waitRes(
        page,
        'Department and its member has been deleted successfully.',
      );
      expect(res).toBe(true);

      browser.close();
    },
    timeout,
  );
});

/**
 * @name Signin Tests
 *
 * @desc Checks manual signin process with valid and invalid data
 *
 */

const puppeteer = require('puppeteer');
const { waitFor } = require('../commonFunc/utility');
const login = require('../commonFunc/login');

const timeout = 30000;

let browser;
let page;

beforeAll(async () => {
  browser = await puppeteer.launch({
    headless: false,
  });
  page = await browser.newPage();
  await page.goto(URL, {
    waitUntil: 'networkidle0',
  });
});

describe('Test Signin Form', () => {
  test(
    'Submit form with valid data',
    async () => {
      await login(page);
    },
    timeout,
  );

  test(
    'Submit form with invalid data',
    async () => {
      await page.goto(URL, {
        waitUntil: 'load',
      });

      await page.evaluate(() => localStorage.clear());
      await page.reload();

      /** Navigate to login form from Last login view */
      await waitFor(page, '.auth0-lock-alternative-link', 'click');

      /** Login with fake credentials */
      await login(
        page,
        { email: '<EMAIL>', password: '123456' },
        'notWait',
      );

      /** Waiting for error message */
      await waitFor(page, '.auth0-global-message-error > span > span');
      const msg = await page.$eval(
        '.auth0-global-message-error > span > span',
        (errorEl) => errorEl.innerHTML,
      );

      const errorMsgs = [
        'Wrong email or password.',
        'Your account has been blocked after multiple consecutive login attempts.',
      ];
      const con = errorMsgs.indexOf(msg) > -1;
      expect(con).toBe(true);
      browser.close();
    },
    timeout,
  );
});

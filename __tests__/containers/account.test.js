/* eslint-disable no-empty */
/* eslint-disable no-undef */
/* eslint-disable no-param-reassign */

/**
 * @name Account Setting Tests
 *
 * @desc Checks Update profile, Add/Edit/Delete signature
 *
 */

const puppeteer = require('puppeteer');
const { waitFor, waitRes } = require('../commonFunc/utility');
const login = require('../commonFunc/login');

const timeout = 30000;

const uData = {
  first_name: '<PERSON><PERSON><PERSON><PERSON>',
  last_name: 'Motwani',
  phone_no: '**********',
  company_name: 'Groovy web LLP',
  address_line_1: '21, Tulsi nagar',
  address_line_2: 'Manjipura Road',
  zip_code: '387001',
  city: 'Nadiad',
};

let browser;
let page;

beforeAll(async () => {
  browser = await puppeteer.launch({
    headless: false,
  });
  page = await browser.newPage();
  await page.goto(URL, {
    waitUntil: 'networkidle0',
  });
});

async function clearNotification() {
  await page.$$eval('.ant-notification-notice-close', (elHandles) =>
    elHandles.forEach((el) => el.click()),
  );
}

async function fillProfileData() {
  /** Upload image */

  await page.waitForSelector('.ant-upload > input');
  const inputUploadHandle = await page.$('.ant-upload > input');
  const fileToUpload = 'public/logo.png';

  /** Upload image */
  await inputUploadHandle.uploadFile(fileToUpload);

  /** Crop image */
  await waitFor(page, '.ant-modal-footer .ant-btn-primary', 'click');

  await page.waitFor(1500);

  await waitFor(page, '#first_name');
  await page.click('#first_name', { clickCount: 3 });
  await page.type('#first_name', uData.first_name);

  await waitFor(page, '#last_name');
  await page.click('#last_name', { clickCount: 3 });
  await page.type('#last_name', uData.last_name);

  await waitFor(page, '#phone_no');
  await page.click('#phone_no', { clickCount: 3 });
  await page.type('#phone_no', uData.phone_no);

  await waitFor(page, '#company_name');
  await page.click('#company_name', { clickCount: 3 });
  await page.type('#company_name', uData.company_name);

  await waitFor(page, '#address_line_1');
  await page.click('#address_line_1', { clickCount: 3 });
  await page.type('#address_line_1', uData.address_line_1);

  await waitFor(page, '#address_line_2');
  await page.click('#address_line_2', { clickCount: 3 });
  await page.type('#address_line_2', uData.address_line_2);

  await waitFor(page, '#zip_code');
  await page.click('#zip_code', { clickCount: 3 });
  await page.type('#zip_code', uData.zip_code);

  await waitFor(page, '#city');
  await page.click('#city', { clickCount: 3 });
  await page.type('#city', uData.city);

  // Promise.all(
  //   Object.keys(uData).map(async (key) => {
  //     const id = `#${key}`;
  //     Promise.all(
  //       await waitFor(page, id),
  //       await page.click(id, { clickCount: 3 }),
  //       await page.type(id, uData[key]),
  //       await page.waitFor(1000),
  //     );
  //   }),
  // );

  await page.waitFor(500);

  /** Submit the form */
  await waitFor(page, 'button[type="submit"]');
  await page.click('button[type="submit"]');

  await page.waitFor(1000);

  /** Waiting for response message */
  const res = await waitRes(page, 'Profile has been updated successfully.');
  expect(res).toBe(true);
}

describe('Account Settings', () => {
  test(
    'Update the profile data',
    async () => {
      await login(page, {});

      await page.goto(`${URL}/dashboard/account-settings`, {
        waitUntil: 'domcontentloaded',
      });

      /** Fill required fields */
      await fillProfileData();
    },
    timeout,
  );
});

describe('Signature', () => {
  test(
    'Add Signature',
    async () => {
      await page.goto(`${URL}/dashboard/account-settings`, {
        waitUntil: 'domcontentloaded',
      });

      await page.waitForSelector('#rc-tabs-0-tab-signature', {
        visible: true,
      });
      await page.click('#rc-tabs-0-tab-signature');

      await waitFor(page, '#create-sign', 'click');

      await waitFor(page, '.ant-list-item', 'click');

      await waitFor(page, '.ant-modal-footer .ant-btn-primary');

      await page.waitFor(500);

      await page.click('.ant-modal-footer .ant-btn-primary');

      await page.waitFor(1000);

      await page
        .waitForSelector('#confirm-sign-ok', {
          timeout: 4000,
          visible: true,
        })
        .then(async () => {
          await page.click('#confirm-sign-ok');
          /** Waiting for response message */
          const res = await waitRes(
            page,
            'Signature has been added successfully.',
          );
          expect(res).toBe(true);
        })
        .catch(async () => {
          /** Waiting for response message */
          const res = await waitRes(
            page,
            'Signature has been added successfully.',
          );
          expect(res).toBe(true);
        });
    },
    timeout,
  );
  test(
    'Edit Signature',
    async () => {
      await page.waitFor(500);

      clearNotification();

      await page.waitForSelector('.ant-list-item-action button', {
        visible: true,
      });
      await page.click('.ant-list-item-action button');

      await page.waitForSelector('.ant-list-item', {
        visible: true,
      });
      await page.click('.ant-list-item');

      await page.waitForSelector('#sign-ok', {
        visible: true,
      });
      await page.click('#sign-ok');

      await page.waitForSelector('#confirm-sign-ok', {
        timeout: 4000,
        visible: true,
      });
      await page.click('#confirm-sign-ok');

      /** Waiting for response message */
      const res = await waitRes(
        page,
        'Signature has been updated successfully.',
      );
      expect(res).toBe(true);
    },
    timeout,
  );
  test(
    'Delete Signature',
    async () => {
      await page.waitFor(2000);

      clearNotification();

      await page.waitForSelector('.deleteBtn > #delete-confirm-sign', {
        visible: true,
        timeout: 5000,
      });
      await page.click('.deleteBtn > #delete-confirm-sign');

      await page.waitFor(1000);

      await page.waitForSelector('#delete-item', {
        visible: true,
      });
      await page.click('#delete-item');
      await page.waitFor(500);

      /** Waiting for response message */
      const res = await waitRes(
        page,
        'Signature has been deleted successfully.',
      );
      expect(res).toBe(true);
      browser.close();
    },
    timeout,
  );
});

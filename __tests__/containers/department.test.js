/* eslint-disable no-param-reassign */

/**
 * @name Team Management Tests
 *
 * @desc Checks insert, update and delete operation in Team management
 *
 */

const puppeteer = require('puppeteer');
const { waitFor, waitRes } = require('../commonFunc/utility');
const login = require('../commonFunc/login');

const timeout = 30000;

let browser;
let page;

beforeAll(async () => {
  browser = await puppeteer.launch({
    headless: false,
  });
  page = await browser.newPage();
  await page.goto(URL, {
    waitUntil: 'networkidle0',
  });
});

async function fillDepDetails(cred = {}) {
  const name = cred.name || 'Testing';
  const email = cred.email || '';

  await page.click('#name');
  await page.$eval('#name', (el) => {
    el.value = '';
  });
  await page.type('#name', name);

  if (email != '-') {
    await waitFor(page, '#email', 'click');
    await page.waitFor(500);

    await waitFor(page, '.email-select .ant-select-item-option', 'click');
    await page.waitFor(500);

    await waitFor(page, '#department_member', 'click');
    await page.waitFor(500);

    await waitFor(page, '.member-select .ant-select-item-option', 'click');
    await page.waitFor(500);
  }

  /** Submit the form */
  await page.click('button[type="submit"]');
}

describe('Test Department Module', () => {
  test(
    'Add Department',
    async () => {
      await login(page, {
        email: '<EMAIL>',
        password: '123456@aA',
      });

      await page.goto(`${URL}/dashboard/manage-departments`, {
        waitUntil: 'domcontentloaded',
      });

      await waitFor(page, '#create-dept', 'click');
      await page.waitFor(500);

      await fillDepDetails({});

      const res = await waitRes(
        page,
        'Department and its member has been created.',
      );
      expect(res).toBe(true);
    },
    timeout,
  );

  test(
    'Edit Department',
    async () => {
      await page.goto(`${URL}/dashboard/manage-departments`, {
        waitUntil: 'domcontentloaded',
      });

      await waitFor(page, '#edit-dept', 'click');
      await page.waitFor(500);

      await fillDepDetails({
        name: 'testing56',
        email: '-',
      });

      const res = await waitRes(
        page,
        'Department and its member has been updated.',
      );
      expect(res).toBe(true);
    },
    timeout,
  );

  test(
    'Delete Department',
    async () => {
      await page.goto(`${URL}/dashboard/manage-departments`, {
        waitUntil: 'domcontentloaded',
      });

      await waitFor(page, '#delete-confirm-dept', 'click');
      await page.waitFor(500);

      await waitFor(page, '#delete-item', 'click');
      await page.waitFor(500);

      const res = await waitRes(
        page,
        'Department and its member has been deleted successfully.',
      );
      expect(res).toBe(true);

      browser.close();
    },
    timeout,
  );
});

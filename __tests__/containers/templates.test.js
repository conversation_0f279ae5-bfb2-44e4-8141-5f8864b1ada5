/* eslint-disable no-param-reassign */

/**
 * @name Team Management Tests
 *
 * @desc Checks insert, update and delete operation in Team management
 *
 */

const puppeteer = require('puppeteer');
const { waitFor, waitRes } = require('../commonFunc/utility');
const login = require('../commonFunc/login');

const timeout = 30000;

let browser;
let page;

beforeAll(async () => {
  browser = await puppeteer.launch({
    headless: false,
  });
  page = await browser.newPage();
  await page.goto(URL, {
    waitUntil: 'networkidle0',
  });
});

describe('Test Templates Editor', () => {
  test(
    'Create & Save Templates',
    async () => {
      await login(page, {
        email: '<EMAIL>',
        password: '123456@aA',
      });

      await page.goto(`${URL}/dashboard/templates`, {
        waitUntil: 'domcontentloaded',
      });

      await waitFor(page, '#new-doc', 'click');
      await page.waitFor(500);

      await waitFor(page, '#create-doc', 'click');
      await page.waitFor(500);

      await page.click('#name');
      await page.$eval('#name', (el) => {
        el.value = '';
      });
      await page.type('#name', new Date().getTime().toString());

      await waitFor(page, '.ant-modal-footer .ant-btn-primary', 'click');
      await page.waitForNavigation();

      await waitFor(page, '.menu .ant-btn-primary', 'click');
      await page.waitFor(500);

      const res = await waitRes(page, 'Document has been saved');
      expect(res).toBe(true);

      browser.close();
    },
    timeout,
  );
});

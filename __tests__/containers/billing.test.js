/* eslint-disable no-param-reassign */

/**
 * @name Team Management Tests
 *
 * @desc Checks insert, update and delete operation in Team management
 *
 */

const puppeteer = require('puppeteer');
const { waitFor, waitRes } = require('../commonFunc/utility');
const login = require('../commonFunc/login');

const timeout = 30000;

let browser;
let page;

beforeAll(async () => {
  browser = await puppeteer.launch({
    headless: false,
  });
  page = await browser.newPage();
  await page.goto(URL, {
    waitUntil: 'networkidle0',
  });
});

describe('Billing module', () => {
  test(
    'Add Card',
    async () => {
      await login(page, {
        email: '<EMAIL>',
        password: '123456@aA',
      });

      await page.goto(`${URL}/dashboard/account-settings?active=billing`, {
        waitUntil: 'domcontentloaded',
      });

      await waitFor(page, '#add-card', 'click');
      await page.waitFor(500);

      await waitFor(page, '.StripeElement iframe', 'click');
      await page.keyboard.type('4242424242424242424242', { delay: 200 });

      await waitFor(page, '#name', 'click');
      await page.type('#name', 'JOhn walter');

      await waitFor(page, '#address_line1', 'click');
      await page.type('#address_line1', 'asdasd');

      await waitFor(page, '#address_line2', 'click');
      await page.type('#address_line2', 'asdasda dsaad');

      await waitFor(page, '#address_city', 'click');
      await page.type('#address_city', 'asdaswd');

      await waitFor(page, '#address_zip', 'click');
      await page.type('#address_zip', '123456');

      await waitFor(page, '#address_state', 'click');
      await page.type('#address_state', 'asdasda');

      await waitFor(page, '#address_country', 'click');
      await page.waitFor(1000);
      await waitFor(page, '[countrycode="AF"]', 'click');

      await waitFor(page, '.ant-modal-footer .ant-btn-primary', 'click');

      const res = await waitRes(page, 'Card has been added successfully.');
      expect(res).toBe(true);
    },
    timeout,
  );

  test(
    'Upgrad Plan',
    async () => {
      await page.goto(`${URL}/dashboard/account-settings?active=billing`, {
        waitUntil: 'domcontentloaded',
      });

      try {
        await waitFor(page, '#cancel-sub', 'click');
        await page.waitFor(500);

        await waitFor(page, '#confirm-delete', 'click');
        await page.waitFor(500);
        await waitRes(
          page,
          'Your subscription has been cancelled successfully .',
        );

        await waitFor(page, '.subscription .action .ant-btn-primary', 'click');
        await page.waitFor(1000);

        await waitFor(page, '#plan_1', 'click');
        await page.waitFor(1000);

        await waitFor(page, '.checkoutSummary .ant-btn-primary', 'click');
        await waitFor(
          page,
          '.ant-result.ant-result-success .ant-btn-primary',
          'click',
        );

        browser.close();
      } catch (err) {
        await waitFor(page, '.subscription .action .ant-btn-primary', 'click');
        await page.waitFor(1000);

        await waitFor(page, '#plan_1', 'click');
        await page.waitFor(1000);

        await waitFor(page, '.checkoutSummary .ant-btn-primary', 'click');
        await waitFor(
          page,
          '.ant-result.ant-result-success .ant-btn-primary',
          'click',
        );

        await page.goto(`${URL}/dashboard/account-settings?active=billing`, {
          waitUntil: 'domcontentloaded',
        });

        await waitFor(page, '#cancel-sub', 'click');
        await page.waitFor(500);

        await waitFor(page, '#confirm-delete', 'click');
        await page.waitFor(500);
        await waitRes(
          page,
          'Your subscription has been cancelled successfully .',
        );

        browser.close();
      }
    },
    timeout,
  );
});

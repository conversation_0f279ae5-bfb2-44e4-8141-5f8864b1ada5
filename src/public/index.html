<!DOCTYPE html>
<html lang="en" dir="ltr">
  <head>
    <title>ChillBaby</title>

    <!-- Meta tags -->
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1"
    />
    <meta name="title" content="ChillBaby" />
    <meta
      name="description"
      content="Providing fast responses to customers who need help ensures high customer satisfaction and brand loyalty."
    />
    <meta name="theme-color" content="#1172EC" />

    <!-- Favicons -->
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="%PUBLIC_URL%/images/icons/apple-touch-icon.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="%PUBLIC_URL%/images/icons/favicon-32x32.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="%PUBLIC_URL%/images/icons/favicon-16x16.png"
    />

    <!-- Menifest JSON -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />

    <!-- Fonts Preloading -->
    <link
      rel="preload"
      href="/fonts/roboto/rubik-v12-latin-300.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link
      rel="preload"
      href="/fonts/roboto/rubik-v12-latin-regular.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link
      rel="preload"
      href="/fonts/roboto/rubik-v12-latin-700.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link
      rel="preload"
      href="/fonts/roboto/rubik-v12-latin-500.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link
      rel="preload"
      href="/fonts/roboto/rubik-v12-latin-900.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Comic+Neue:ital,wght@0,300;0,400;0,700;1,300;1,400;1,700&family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap"
      rel="stylesheet"
    />

    <!-- Google Fonts End -->

    <style>
      /* rubik-300 - latin */
      @font-face {
        font-family: 'Rubik';
        font-style: normal;
        font-weight: 300;
        src: url('../fonts/rubik/rubik-v12-latin-300.eot'); /* IE9 Compat Modes */
        src: local(''),
          url('../fonts/rubik/rubik-v12-latin-300.eot?#iefix')
            format('embedded-opentype'),
          /* IE6-IE8 */ url('../fonts/rubik/rubik-v12-latin-300.woff2')
            format('woff2'),
          /* Super Modern Browsers */
            url('../fonts/rubik/rubik-v12-latin-300.woff') format('woff'),
          /* Modern Browsers */ url('../fonts/rubik/rubik-v12-latin-300.ttf')
            format('truetype'),
          /* Safari, Android, iOS */
            url('../fonts/rubik/rubik-v12-latin-300.svg#Rubik') format('svg'); /* Legacy iOS */
      }
      /* rubik-regular - latin */
      @font-face {
        font-family: 'Rubik';
        font-style: normal;
        font-weight: 400;
        src: url('../fonts/rubik/rubik-v12-latin-regular.eot'); /* IE9 Compat Modes */
        src: local(''),
          url('../fonts/rubik/rubik-v12-latin-regular.eot?#iefix')
            format('embedded-opentype'),
          /* IE6-IE8 */ url('../fonts/rubik/rubik-v12-latin-regular.woff2')
            format('woff2'),
          /* Super Modern Browsers */
            url('../fonts/rubik/rubik-v12-latin-regular.woff') format('woff'),
          /* Modern Browsers */
            url('../fonts/rubik/rubik-v12-latin-regular.ttf') format('truetype'),
          /* Safari, Android, iOS */
            url('../fonts/rubik/rubik-v12-latin-regular.svg#Rubik')
            format('svg'); /* Legacy iOS */
      }
      /* rubik-500 - latin */
      @font-face {
        font-family: 'Rubik';
        font-style: normal;
        font-weight: 500;
        src: url('../fonts/rubik/rubik-v12-latin-500.eot'); /* IE9 Compat Modes */
        src: local(''),
          url('../fonts/rubik/rubik-v12-latin-500.eot?#iefix')
            format('embedded-opentype'),
          /* IE6-IE8 */ url('../fonts/rubik/rubik-v12-latin-500.woff2')
            format('woff2'),
          /* Super Modern Browsers */
            url('../fonts/rubik/rubik-v12-latin-500.woff') format('woff'),
          /* Modern Browsers */ url('../fonts/rubik/rubik-v12-latin-500.ttf')
            format('truetype'),
          /* Safari, Android, iOS */
            url('../fonts/rubik/rubik-v12-latin-500.svg#Rubik') format('svg'); /* Legacy iOS */
      }
      /* rubik-700 - latin */
      @font-face {
        font-family: 'Rubik';
        font-style: normal;
        font-weight: 700;
        src: url('../fonts/rubik/rubik-v12-latin-700.eot'); /* IE9 Compat Modes */
        src: local(''),
          url('../fonts/rubik/rubik-v12-latin-700.eot?#iefix')
            format('embedded-opentype'),
          /* IE6-IE8 */ url('../fonts/rubik/rubik-v12-latin-700.woff2')
            format('woff2'),
          /* Super Modern Browsers */
            url('../fonts/rubik/rubik-v12-latin-700.woff') format('woff'),
          /* Modern Browsers */ url('../fonts/rubik/rubik-v12-latin-700.ttf')
            format('truetype'),
          /* Safari, Android, iOS */
            url('../fonts/rubik/rubik-v12-latin-700.svg#Rubik') format('svg'); /* Legacy iOS */
      }
      /* rubik-600 - latin */
      @font-face {
        font-family: 'Rubik';
        font-style: normal;
        font-weight: 600;
        src: url('../fonts/rubik/rubik-v12-latin-600.eot'); /* IE9 Compat Modes */
        src: local(''),
          url('../fonts/rubik/rubik-v12-latin-600.eot?#iefix')
            format('embedded-opentype'),
          /* IE6-IE8 */ url('../fonts/rubik/rubik-v12-latin-600.woff2')
            format('woff2'),
          /* Super Modern Browsers */
            url('../fonts/rubik/rubik-v12-latin-600.woff') format('woff'),
          /* Modern Browsers */ url('../fonts/rubik/rubik-v12-latin-600.ttf')
            format('truetype'),
          /* Safari, Android, iOS */
            url('../fonts/rubik/rubik-v12-latin-600.svg#Rubik') format('svg'); /* Legacy iOS */
      }
      /* rubik-800 - latin */
      @font-face {
        font-family: 'Rubik';
        font-style: normal;
        font-weight: 800;
        src: url('../fonts/rubik/rubik-v12-latin-800.eot'); /* IE9 Compat Modes */
        src: local(''),
          url('../fonts/rubik/rubik-v12-latin-800.eot?#iefix')
            format('embedded-opentype'),
          /* IE6-IE8 */ url('../fonts/rubik/rubik-v12-latin-800.woff2')
            format('woff2'),
          /* Super Modern Browsers */
            url('../fonts/rubik/rubik-v12-latin-800.woff') format('woff'),
          /* Modern Browsers */ url('../fonts/rubik/rubik-v12-latin-800.ttf')
            format('truetype'),
          /* Safari, Android, iOS */
            url('../fonts/rubik/rubik-v12-latin-800.svg#Rubik') format('svg'); /* Legacy iOS */
      }
      /* rubik-900 - latin */
      @font-face {
        font-family: 'Rubik';
        font-style: normal;
        font-weight: 900;
        src: url('../fonts/rubik/rubik-v12-latin-900.eot'); /* IE9 Compat Modes */
        src: local(''),
          url('../fonts/rubik/rubik-v12-latin-900.eot?#iefix')
            format('embedded-opentype'),
          /* IE6-IE8 */ url('../fonts/rubik/rubik-v12-latin-900.woff2')
            format('woff2'),
          /* Super Modern Browsers */
            url('../fonts/rubik/rubik-v12-latin-900.woff') format('woff'),
          /* Modern Browsers */ url('../fonts/rubik/rubik-v12-latin-900.ttf')
            format('truetype'),
          /* Safari, Android, iOS */
            url('../fonts/rubik/rubik-v12-latin-900.svg#Rubik') format('svg'); /* Legacy iOS */
      }

      /* Helvetica */
      @font-face {
        font-family: 'Helvetica';
        src: local('Helvetica'),
          url('./fonts/helvetica/Helvetica.ttf') format('truetype');
      }

      /* UniNeue black */
      @font-face {
        font-family: 'UniNeue';
        src: url('./fonts/uniNeue/UniNeue-Trial-Black.eot'); /* IE9 Compat Modes */
        src: local('UniNeue-Black'),
          url('./fonts/uniNeue/UniNeue-Trial-Black.eot?#iefix')
            format('embedded-opentype'),
          /* IE6-IE8 */ url('./fonts/uniNeue/UniNeue-Trial-Black.woff2')
            format('woff2'),
          /* Super Modern Browsers */
            url('./fonts/uniNeue/UniNeue-Trial-Black.woff') format('woff'),
          /* Modern Browsers */ url('./fonts/uniNeue/UniNeue-Trial-Black.ttf')
            format('truetype');
      }
      /* UniNeue bold */
      @font-face {
        font-family: 'UniNeue';
        src: url('./fonts/uniNeue/UniNeue-Trial-Bold.eot'); /* IE9 Compat Modes */
        src: local(''),
          url('./fonts/uniNeue/UniNeue-Trial-Bold.eot?#iefix')
            format('embedded-opentype'),
          /* IE6-IE8 */ url('./fonts/uniNeue/UniNeue-Trial-Bold.woff2')
            format('woff2'),
          /* Super Modern Browsers */
            url('./fonts/uniNeue/UniNeue-Trial-Bold.woff') format('woff'),
          /* Modern Browsers */ url('./fonts/uniNeue/UniNeue-Trial-Bold.ttf')
            format('truetype');
      }

      /* UniNeue book */
      @font-face {
        font-family: 'UniNeue';
        src: url('./fonts/uniNeue/UniNeue-Trial-Book.eot'); /* IE9 Compat Modes */
        src: local(''),
          url('./fonts/uniNeue/UniNeue-Trial-Book.eot?#iefix')
            format('embedded-opentype'),
          /* IE6-IE8 */ url('./fonts/uniNeue/UniNeue-Trial-Book.woff2')
            format('woff2'),
          /* Super Modern Browsers */
            url('./fonts/uniNeue/UniNeue-Trial-Book.woff') format('woff'),
          /* Modern Browsers */ url('./fonts/uniNeue/UniNeue-Trial-Book.ttf')
            format('truetype');
      }

      /* UniNeue heavy */
      @font-face {
        font-family: 'UniNeue';
        src: url('./fonts/uniNeue/UniNeue-Trial-Heavy.eot'); /* IE9 Compat Modes */
        src: local(''),
          url('./fonts/uniNeue/UniNeue-Trial-Heavy.eot?#iefix')
            format('embedded-opentype'),
          /* IE6-IE8 */ url('./fonts/uniNeue/UniNeue-Trial-Heavy.woff2')
            format('woff2'),
          /* Super Modern Browsers */
            url('./fonts/uniNeue/UniNeue-Trial-Heavy.woff') format('woff'),
          /* Modern Browsers */ url('./fonts/uniNeue/UniNeue-Trial-Heavy.ttf')
            format('truetype');
      }

      /* UniNeue light*/
      @font-face {
        font-family: 'UniNeue';
        src: url('./fonts/uniNeue/UniNeue-Trial-Light.eot'); /* IE9 Compat Modes */
        src: local(''),
          url('./fonts/uniNeue/UniNeue-Trial-Light.eot?#iefix')
            format('embedded-opentype'),
          /* IE6-IE8 */ url('./fonts/uniNeue/UniNeue-Trial-Light.woff2')
            format('woff2'),
          /* Super Modern Browsers */
            url('./fonts/uniNeue/UniNeue-Trial-Light.woff') format('woff'),
          /* Modern Browsers */ url('./fonts/uniNeue/UniNeue-Trial-Light.ttf')
            format('truetype');
      }

      /* UniNeue regular */
      @font-face {
        font-family: 'UniNeue';
        src: url('./fonts/uniNeue/UniNeue-Trial-Regular.eot'); /* IE9 Compat Modes */
        src: local(''),
          url('./fonts/uniNeue/UniNeue-Trial-Regular.eot?#iefix')
            format('embedded-opentype'),
          /* IE6-IE8 */ url('./fonts/uniNeue/UniNeue-Trial-Regular.woff2')
            format('woff2'),
          /* Super Modern Browsers */
            url('./fonts/uniNeue/UniNeue-Trial-Regular.woff') format('woff'),
          /* Modern Browsers */ url('./fonts/uniNeue/UniNeue-Trial-Regular.ttf')
            format('truetype');
      }

      /* UniNeue thin */
      @font-face {
        font-family: 'UniNeue';
        src: url('./fonts/uniNeue/UniNeue-Trial-Thin.eot'); /* IE9 Compat Modes */
        src: local(''),
          url('./fonts/uniNeue/UniNeue-Trial-Thin.eot?#iefix')
            format('embedded-opentype'),
          /* IE6-IE8 */ url('./fonts/uniNeue/UniNeue-Trial-Thin.woff2')
            format('woff2'),
          /* Super Modern Browsers */
            url('./fonts/uniNeue/UniNeue-Trial-Thin.woff') format('woff'),
          /* Modern Browsers */ url('./fonts/uniNeue/UniNeue-Trial-Thin.ttf')
            format('truetype');
      }
    </style>
  </head>

  <body>
    <noscript> You need to enable JavaScript to run this app. </noscript>
    <div id="root"></div>
  </body>
</html>

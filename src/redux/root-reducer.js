import { combineReducers } from 'redux';
import App from '@chill/redux/app/reducer';
import Auth from '@chill/redux/auth/reducer';
import ThemeSwitcher from '@chill/redux/themeSwitcher/reducer';
import Notification from '@chill/redux/notification/reducer';
import LanguageSwitcher from '@chill/redux/languageSwitcher/reducer';
import Language from '@chill/redux/language/reducer';
import Chat from '@chill/redux/chat/reducers';

export default combineReducers({
  Auth,
  App,
  ThemeSwitcher,
  LanguageSwitcher,
  Notification,
  Language,
  Chat,
});

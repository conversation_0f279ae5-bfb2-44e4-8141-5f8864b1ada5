/* eslint-disable require-yield */
import { all, takeEvery, put, fork } from 'redux-saga/effects';
// import { getCountry } from '@chill/lib/helpers/utility';
import actions from './actions';

export const getApp = (state) => state.App;

export function* setCountry() {
  yield takeEvery(actions.SET_CURRENT_COUNTRY, function* (payload) {
    const { country } = payload;
    try {
      yield localStorage.setItem(
        'country',
        country ? JSON.stringify(country) : '',
      );
      yield put({
        type: actions.SET_COUNTRY,
        country,
      });
    } catch (e) {
      console.log(e);
    }
  });
}

export function* updateSettings() {
  console.log('update settings');
  // yield takeEvery('UPDATE_SETTINGS_SAGA', function* ({ lType }) {
  //   const app = yield select(getApp);
  //   const settings = app && isObject(app.settings) ? app.settings : {};

  //   try {
  //     yield put({
  //       type: actions.SET_SETTINGS,
  //       data: { ...settings, loading: lType === 'init' },
  //     });
  //     const res = yield getApiData('settings/getAllSettings');
  //     if (res.success && isArray(res.data)) {
  //       const sObj = {};
  //       res.data.map((item) => {
  //         sObj[item.meta_key] = item.meta_value;
  //         return item;
  //       });
  //       yield put({
  //         type: actions.SET_SETTINGS,
  //         data: sObj,
  //       });
  //     } else {
  //       yield put({
  //         type: actions.SET_SETTINGS,
  //         data: { ...settings },
  //       });
  //     }
  //   } catch (err) {
  //     yield put({
  //       type: actions.SET_SETTINGS,
  //       data: { ...settings },
  //     });
  //     console.log(err);
  //   }
  // });
}

export default function* rootSaga() {
  yield all([fork(updateSettings), fork(setCountry)]);
}

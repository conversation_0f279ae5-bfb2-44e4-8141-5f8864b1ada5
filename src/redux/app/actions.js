export function getView(width) {
  let newView = 'MobileView';
  if (width > 1220) {
    newView = 'DesktopView';
  } else if (width > 767) {
    newView = 'TabView';
  }
  return newView;
}
const actions = {
  COLLPSE_CHANGE: 'COLLPSE_CHANGE',
  COLLPSE_OPEN_DRAWER: 'COLLPSE_OPEN_DRAWER',
  CHANGE_OPEN_KEYS: 'CHANGE_OPEN_KEYS',
  TOGGLE_ALL: 'TOGGLE_ALL',
  CHANGE_CURRENT: 'CHANGE_CURRENT',
  CLEAR_MENU: 'CLEAR_MENU',
  SET_CURRENT_COUNTRY: 'SET_CURRENT_COUNTRY',
  SET_COUNTRY: 'SET_COUNTRY',
  SET_POST_DATA: 'SET_POST_DATA',
  UPDATE_SETTINGS_SAGA: 'UPDATE_SETTINGS_SAGA',
  SET_SETTINGS: 'SET_SETTINGS',
  FROM_GENERATE_AI: 'FROM_GENERATE_AI',

  toggleCollapsed: () => ({
    type: actions.COLLPSE_CHANGE,
  }),
  toggleAll: (width, height) => {
    const view = getView(width);
    const collapsed = view !== 'DesktopView';
    return {
      type: actions.TOGGLE_ALL,
      collapsed,
      view,
      height,
    };
  },
  toggleOpenDrawer: () => ({
    type: actions.COLLPSE_OPEN_DRAWER,
  }),
  changeOpenKeys: (openKeys) => ({
    type: actions.CHANGE_OPEN_KEYS,
    openKeys,
  }),
  changeCurrent: (current) => ({
    type: actions.CHANGE_CURRENT,
    current,
  }),
  clearMenu: () => ({ type: actions.CLEAR_MENU }),
  updateTopNotification: (data, type, count) => ({
    type: actions.UPDATE_TOP_NOTIFICATION,
    data,
    uType: type,
    count,
  }),
  setCurrentCountry: (data) => ({
    type: actions.SET_CURRENT_COUNTRY,
    country: data,
  }),
  setPostData: (data) => ({
    type: actions.SET_POST_DATA,
    data,
  }),
  updateSettings: () => ({
    type: actions.UPDATE_SETTINGS_SAGA,
  }),
  setFromFenerateAI: (data) => ({
    type: actions.FROM_GENERATE_AI,
    fromGenerateAI: data,
  }),
};
export default actions;

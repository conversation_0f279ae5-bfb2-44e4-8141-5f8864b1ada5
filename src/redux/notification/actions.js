/* eslint-disable no-console */
/* eslint-disable eqeqeq */
import Notification from '@chill/components/Notification';
import authActions from '@chill/redux/auth/actions';
import siteConfig from '@chill/config/site.config';
import { isEmpty, isObject } from 'lodash';
import {
  hideNotiTypes,
  staticNotifications,
} from '@chill/containers/Notifications/config';

const defaultHeaders = {
  'Content-Type': 'application/json',
  Accept: 'application/json',
};

const socketIOClient = require('socket.io-client');
const sailsIOClient = require('sails.io.js');

const io = sailsIOClient(socketIOClient);

if (io) {
  io.sails.url = siteConfig.sailsUrl;
  io.sails.autoConnect = false;
}

const actions = {
  NOTIFICATION_INIT: 'NOTIFICATION_INIT',
  SET_IO: 'SET_IO',
  SET_NOTIFICATION: 'SET_NOTIFICATION',
  UPDATE_TOP_NOTIFICATION: 'UPDATE_TOP_NOTIFICATION',
  SET_UNREAD_NOTIFICATION: 'SET_UNREAD_NOTIFICATION',

  initialization: () => (dispatch, getState) => {
    const Iosocket = getState().Notification.IOSocket;
    const token = getState().Auth.idToken;

    if (!Iosocket) {
      const socket = io.sails.connect();
      console.log('socket=====<>');
      console.log(socket);
      console.log('socket.isConnecting()======>');
      console.log(socket.isConnecting());
      console.log('socket.isConnected()======>');
      console.log(socket.isConnected());
      if (socket) {
        const headers = { ...defaultHeaders };
        if (token) headers.Authorization = `Bearer ${token}`;
        const options = {
          method: 'POST',
          url: '/api/chat/connect',
          headers,
        };
        try {
          socket.request(options, (resData) => {
            console.log('Sails responded with: ', resData);
            // console.log('with headers: ', jwres.headers);
            // console.log('and with status code: ', jwres.statusCode);
            dispatch({
              type: actions.SET_IO,
              data: socket,
              socketData:
                isObject(resData) && isObject(resData.data)
                  ? resData.data
                  : null,
            });
          });
        } catch (err) {
          console.log(err);
        }

        // RECIEVE NOTIFICATION
        socket.on('notification', (noti) => {
          console.log('notification===>');
          console.log(noti);
          if (isObject(noti) && !isEmpty(noti)) {
            dispatch(actions.receiveNoti(noti));
          }
        });

        socket.on('disconnect', () => {
          console.log('SOCKET DISCONNECTED ====>');
          dispatch({
            type: actions.SET_IO,
            data: null,
            socketData: null,
          });
        });
      }
    }
  },
  receiveNoti: (noti) => (dispatch) => {
    dispatch({
      type: actions.SET_NOTIFICATION,
      data: noti,
    });
    const nType = noti?.type || '';
    if (
      hideNotiTypes.indexOf(nType) < 0 &&
      staticNotifications.indexOf(nType) < 0
    ) {
      dispatch({
        type: actions.UPDATE_TOP_NOTIFICATION,
        data: noti,
        uType: 'add',
      });
    }
  },
  sendRequest:
    (url, data = null, callBack = () => {}) =>
    (dispatch, getState) => {
      const Iosocket = getState().Notification.IOSocket;
      const { idToken } = getState().Auth;
      const headers = { ...defaultHeaders };
      console.log('REQUEST URL===>');
      console.log(url);
      console.log('REQUEST data===>');
      console.log(data);
      if (idToken && headers) headers.Authorization = `Bearer ${idToken}`;
      const optionsReq = {
        url,
        data,
        method: 'POST',
        headers,
      };
      if (Iosocket) {
        Iosocket.request(optionsReq, (resData) => {
          console.log('Sails responded with: ', resData);
          if (resData && resData.message === 'Unauthorized') {
            Notification('error', 'common.sessionExpired');
            setTimeout(() => {
              dispatch(authActions.logout());
            }, 500);
          } else {
            callBack(resData);
          }
        });
      }
    },
  disconnect: () => (_, getState) => {
    const IOSocketVar = getState().Notification.IOSocket;
    if (IOSocketVar) {
      IOSocketVar.disconnect();
    }
  },
  setNotification: (data) => ({
    type: actions.SET_NOTIFICATION,
    data,
  }),
  setUnreadNotification: (data) => ({
    type: actions.SET_UNREAD_NOTIFICATION,
    data,
  }),
  updateTopNotification: (data, type, count) => ({
    type: actions.UPDATE_TOP_NOTIFICATION,
    data,
    uType: type,
    count,
  }),
};
export default actions;

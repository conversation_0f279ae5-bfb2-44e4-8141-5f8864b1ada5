import { all, takeEvery, put, select } from 'redux-saga/effects';
import { isObject, isEmpty, isArray, toNumber, isUndefined } from 'lodash';
import actions from './actions';

export function* updateTopNotification({ data, count, uType }) {
  const unreadNoti = yield select((state) => state.Notification.unreadNoti);
  const unreadNotiVar = isObject(unreadNoti) ? { ...unreadNoti } : {};

  // Get old notification list and count
  const aNotifications =
    isObject(unreadNotiVar) && isArray(unreadNotiVar.list) && uType !== 'clear'
      ? [...unreadNotiVar.list]
      : [];
  let cnt =
    isObject(unreadNotiVar) && unreadNotiVar.count
      ? toNumber(unreadNotiVar.count)
      : 0;

  if (isObject(data) && !isEmpty(data) && uType === 'add') {
    // Add new notification in top
    aNotifications.unshift(data);
    cnt += 1;
  } else if (isObject(data) && !isEmpty(data) && uType === 'delete') {
    // Remove read notification from top
    const fIndex = aNotifications.findIndex((noti) => noti.id === data.id);
    if (fIndex > -1) aNotifications.splice(fIndex, 1);

    // Decrease count if it didn't get from API
    if (isUndefined(count)) {
      cnt -= 1;
    }
  }

  yield put({
    type: actions.SET_UNREAD_NOTIFICATION,
    data: { list: aNotifications, count: !isUndefined(count) ? count : cnt },
  });
}

export default function* rootSaga() {
  yield all([
    takeEvery(actions.UPDATE_TOP_NOTIFICATION, updateTopNotification),
  ]);
}

import moment from 'moment';
import { getLanguage, setDirection } from '@chill/lib/helpers/utility';
import actions from './actions';

const lang = getLanguage();

// Set default locale globally
setTimeout(() => {
  moment.locale(lang || 'en');
}, 100);

// Set default direction
if (lang === 'ar') {
  setDirection('rtl');
}

const initState = {
  isActivated: false,
  allWords: {},
  language: lang || 'en',
};

export default function (state = initState, action) {
  switch (action.type) {
    case actions.ACTIVATE_LANG_MODAL:
      return {
        ...state,
        isActivated: !state.isActivated,
      };
    case actions.CHANGE_LANGUAGE:
      return {
        ...state,
        language: action.language,
      };
    case actions.SET_ALL_WORDS:
      return {
        ...state,
        allWords: action.allWords,
      };
    default:
      return state;
  }
}

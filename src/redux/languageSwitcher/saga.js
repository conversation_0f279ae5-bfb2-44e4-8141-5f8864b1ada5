/* eslint-disable func-names */
import { all, takeEvery, put, fork } from 'redux-saga/effects';
import { setDirection } from '@chill/lib/helpers/utility';
import moment from 'moment';
import actions from './actions';

export function* changeLang() {
  yield takeEvery(actions.CHANGE_LANGUAGE_SAGA, function* (payload) {
    const lang = payload.language || '';
    const dir = lang === 'ar' ? 'rtl' : 'ltr';
    setDirection(dir);

    // Change locale in moment also
    moment.locale(lang);

    yield localStorage.setItem('language', lang);
    yield put({
      type: actions.CHANGE_LANGUAGE,
      language: lang,
    });
  });
}

export default function* rootSaga() {
  yield all([fork(changeLang)]);
}

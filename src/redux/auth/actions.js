const actions = {
  CHECK_AUTHORIZATION: 'CHECK_AUTHORIZATION',
  LOGIN_REQUEST: 'LOGIN_REQUEST',
  LOGOUT: 'LOGOUT',
  LOGOUT_SAGA: 'LOGOUT_SAGA',
  LOGI<PERSON>_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_SUCCESS_SAGA: 'LOGIN_SUCCESS_SAGA',
  LOGIN_ERROR: 'LOGIN_ERROR',
  SET_USER: 'SET_USER',
  SET_USER_OBJ: 'SET_USER_OBJ',
  SET_LOGGED_OUT: 'SET_LOGGED_OUT',
  LOGIN_INIT: 'LOGIN_INIT',
  UPDATE_ACCESS_SAGA: 'UPDATE_ACCESS_SAGA',
  UPDATE_USER_DATA_SAGA: 'UPDATE_USER_DATA_SAGA',
  UPDATE_ACCESS: 'UPDATE_ACCESS',
  SET_TOKEN: 'SET_TOKEN',
  SET_TOKEN_SAGA: 'SET_TOKEN_SAGA',
  SET_BRAND_LIST: 'SET_BRAND_LIST',
  SET_FROM_AI: 'SET_FROM_AI',

  checkAuthorization: () => ({ type: actions.CHECK_AUTHORIZATION }),
  login: (data) => ({
    type: actions.LOGIN_REQUEST,
    payload: data,
  }),
  setUserObj: (user, token) => ({
    type: actions.SET_USER,
    user,
    token,
  }),
  setToken: (token) => ({
    type: actions.SET_TOKEN_SAGA,
    token,
  }),
  setBrand: (list) => ({
    type: actions.SET_FROM_AI,
    list,
  }),
  setFromAI: (fromAI) => ({
    type: actions.SET_BRAND_LIST,
    fromAI,
  }),
  setLoggedOut: (loading) => ({
    type: actions.SET_LOGGED_OUT,
    loading,
  }),
  updateAccess: (type = '') => ({
    type: actions.UPDATE_ACCESS_SAGA,
    lType: type,
  }),
  updateUserData: (type = '') => ({
    type: actions.UPDATE_USER_DATA_SAGA,
    lType: type,
  }),
  logout: () => ({
    type: actions.LOGOUT_SAGA,
  }),
};
export default actions;

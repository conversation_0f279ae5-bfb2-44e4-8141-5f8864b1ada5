import { getParsedJson, getToken } from '@chill/lib/helpers/utility';
import actions from './actions';

const token = getToken();

const initState = {
  idToken: token,
  userData: { initLoading: !!token },
  loggedOut: false,
  loading: false,
  error: {},
  access: {},
  list: [],
  fromAI: false,
};

export default function authReducer(state = initState, action) {
  switch (action.type) {
    case actions.LOGIN_INIT:
      return {
        ...state,
        loading: true,
        error: {},
      };
    case actions.LOGIN_SUCCESS:
      return {
        ...state,
        idToken: action.token,
        userData: action.user ? getParsedJson(action.user) : {},
        loading: false,
      };
    case actions.LOGIN_ERROR:
      return {
        ...state,
        loading: false,
        error: action.error,
      };
    case actions.LOGOUT: {
      return {
        ...initState,
        idToken: null,
        userData: {},
      };
    }
    case actions.SET_LOGGED_OUT:
      return {
        ...state,
        loggedOut: action.loading,
      };
    case actions.SET_USER_OBJ:
      return {
        ...state,
        userData: action.user ? action.user : {},
      };
    case actions.UPDATE_ACCESS:
      return {
        ...state,
        access: action.data ? action.data : {},
      };
    case actions.SET_TOKEN:
      return {
        ...state,
        idToken: action.token || '',
      };
    case actions.SET_BRAND_LIST:
      return {
        ...state,
        list: action.list,
      };
    case actions.SET_FROM_AI:
      return {
        ...state,
        fromAI: action.fromAI,
      };
    default:
      return state;
  }
}

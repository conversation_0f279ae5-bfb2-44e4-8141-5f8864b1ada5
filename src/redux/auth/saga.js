/* eslint-disable func-names */
import { all, takeEvery, put, fork, select } from 'redux-saga/effects';
import { isObject, isString } from 'lodash';
import notiActions from '@chill/redux/notification/actions';
import { getToken, clearToken, getUserData } from '@chill/lib/helpers/utility';
import getApiData from '@chill/lib/helpers/apiHelper';
import actions from './actions';

export const getAuth = (state) => state.Auth;

export function* loginRequest() {
  yield takeEvery('LOGIN_REQUEST', function* ({ payload }) {
    try {
      yield put({ type: actions.LOGIN_INIT });
      const res = yield getApiData('user/login', payload, 'POST');
      if (res.success) {
        yield put({
          type: actions.LOGIN_SUCCESS_SAGA,
          token: res.data.token,
          user: res.data.user,
        });
      } else {
        yield put({ type: actions.LOGIN_ERROR, error: res });
      }
    } catch (err) {
      yield put({
        type: actions.LOGIN_ERROR,
        error: { error: true, timeout: 1000 },
      });
    }
  });
}

export function* loginSuccess() {
  yield takeEvery(actions.LOGIN_SUCCESS_SAGA, function* (payload) {
    const user = isObject(payload.user)
      ? JSON.stringify(payload.user)
      : payload.user;
    const token = isString(payload.token) ? payload.token : '';

    yield localStorage.setItem('id_token', token);
    yield put({
      type: actions.LOGIN_SUCCESS,
      token,
      user,
    });
  });
}

export function* setToken() {
  yield takeEvery(actions.SET_TOKEN_SAGA, function* ({ token = '' }) {
    yield localStorage.setItem('id_token', token);
    yield put({
      type: actions.SET_TOKEN,
      token,
    });
  });
}

export function* setUser() {
  yield takeEvery('SET_USER', function* (payload) {
    yield put({
      type: actions.SET_USER_OBJ,
      user: payload.user,
    });
  });
}

export function* logout() {
  yield takeEvery(actions.LOGOUT_SAGA, function* () {
    try {
      getApiData('user/logout', {}, 'POST');
    } catch (e) {
      console.log(e);
    }
    yield clearToken();
    yield put(notiActions.disconnect());
    yield put({
      type: actions.LOGOUT,
    });
    // Auth0.logout();
  });
}
export function* checkAuthorization() {
  yield takeEvery(actions.CHECK_AUTHORIZATION, function* () {
    const token = getToken();
    const user = getUserData();
    if (token) {
      yield put({
        type: actions.LOGIN_SUCCESS,
        token,
        user,
      });
    }
  });
}
export function* updateUserData() {
  yield takeEvery('UPDATE_USER_DATA_SAGA', function* ({ lType }) {
    const auth = yield select(getAuth);
    const udata = auth && isObject(auth.userData) ? { ...auth.userData } : {};

    try {
      if (lType) {
        yield put({
          type: actions.SET_USER_OBJ,
          user: { ...udata, [lType]: true },
        });
      }

      const res = yield getApiData('getUserDetails', {}, 'GET');
      if (res.success && res.data) {
        yield put({
          type: actions.SET_USER_OBJ,
          user: res.data,
        });
      } else {
        yield put({
          type: actions.SET_USER_OBJ,
          user: udata,
        });
      }
    } catch (err) {
      yield put({
        type: actions.SET_USER_OBJ,
        user: udata,
      });
      console.log(err);
    }
  });
}

export default function* rootSaga() {
  yield all([
    fork(checkAuthorization),
    fork(loginRequest),
    fork(loginSuccess),
    fork(logout),
    fork(setUser),
    fork(setToken),
    fork(updateUserData),
  ]);
}

{"_from": "antd-img-crop@^2.2.2", "_id": "antd-img-crop@2.4.0", "_inBundle": false, "_integrity": "sha512-Tz1jTAis5XKS8Dja6TkmXhslXL11DuHVTrtrc/OAOujEmEmd0yuljydciF+koyXA/XFdHkkDhANQu+cmkq1G7Q==", "_location": "/antd-img-crop", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "antd-img-crop@^2.2.2", "name": "antd-img-crop", "escapedName": "antd-img-crop", "rawSpec": "^2.2.2", "saveSpec": null, "fetchSpec": "^2.2.2"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/antd-img-crop/-/antd-img-crop-2.4.0.tgz", "_shasum": "4569abf54072a012342d5a3a3d8ad4e4a715850f", "_spec": "antd-img-crop@^2.2.2", "_where": "/home/<USER>/Desktop/projects/websites/handyman", "author": {"name": "na<PERSON><PERSON><PERSON><PERSON>", "email": "nanxia<PERSON><PERSON>@gmail.com", "url": "https://github.com/nanxiaobei"}, "bugs": {"url": "https://github.com/nanxiaobei/antd-img-crop/issues"}, "bundleDependencies": false, "dependencies": {"@babel/runtime": "^7.8.4", "react-image-crop": "^8.5.0"}, "deprecated": false, "description": "An image cropper for Ant Design Upload", "devDependencies": {"@babel/core": "^7.8.4", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/preset-env": "^7.8.4", "@babel/preset-react": "^7.8.3", "@rollup/plugin-replace": "^2.3.1", "babel-eslint": "^10.0.3", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2", "eslint-plugin-react": "^7.18.2", "prettier": "^1.18.2", "rollup": "^1.31.0", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-sass": "^1.2.2"}, "eslintIgnore": ["dist", "index.d.ts"], "files": ["dist", "src"], "homepage": "https://github.com/nanxiaobei/antd-img-crop", "keywords": ["react", "antd", "ant-design", "upload", "image", "crop"], "license": "MIT", "main": "dist/antd-img-crop.cjs.js", "module": "dist/antd-img-crop.esm.js", "name": "antd-img-crop", "peerDependencies": {"antd": "^3.26.7", "prop-types": "^15.6.2", "react": "^16.7.0", "react-dom": "^16.7.0"}, "repository": {"type": "git", "url": "git+https://github.com/nanxiaobei/antd-img-crop.git"}, "scripts": {"build": "rimraf dist && rollup -c"}, "version": "2.4.0"}
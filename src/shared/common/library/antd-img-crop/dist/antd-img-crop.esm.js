/* eslint-disable no-unused-expressions */
/* eslint-disable no-loop-func */
/* eslint-disable default-case */
import _regeneratorRuntime from '@babel/runtime/regenerator';
import _asyncToGenerator from '@babel/runtime/helpers/esm/asyncToGenerator';
import _extends from '@babel/runtime/helpers/esm/extends';
import _assertThisInitialized from '@babel/runtime/helpers/esm/assertThisInitialized';
import _inheritsLoose from '@babel/runtime/helpers/esm/inheritsLoose';
import _defineProperty from '@babel/runtime/helpers/esm/defineProperty';
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import LocaleReceiver from 'antd/es/locale-provider/LocaleReceiver';
import Modal from 'antd/es/modal';
import ReactCrop from 'react-image-crop';

import 'antd/es/modal/style/index.css';

function ___$insertStyle(css) {
	if (!css) {
		return;
	}
	if (typeof window === 'undefined') {
		return;
	}

	var style = document.createElement('style');

	style.setAttribute('type', 'text/css');
	style.innerHTML = css;
	document.head.appendChild(style);
	return css;
}
// import 'antd/es/button/style/index.css';

___$insertStyle(
	'.ReactCrop {\n  position: relative;\n  display: inline-block;\n  cursor: crosshair;\n  overflow: hidden;\n  max-width: 100%;\n}\n.ReactCrop:focus {\n  outline: none;\n}\n.ReactCrop--disabled, .ReactCrop--locked {\n  cursor: inherit;\n}\n.ReactCrop__image {\n  display: block;\n  max-width: 100%;\n  touch-action: manipulation;\n}\n.ReactCrop--crop-invisible .ReactCrop__image {\n  opacity: 0.5;\n}\n.ReactCrop__crop-selection {\n  position: absolute;\n  top: 0;\n  left: 0;\n  transform: translate3d(0, 0, 0);\n  box-sizing: border-box;\n  cursor: move;\n  box-shadow: 0 0 0 9999em rgba(0, 0, 0, 0.5);\n  touch-action: manipulation;\n  border: 1px solid;\n  border-image-source: url("data:image/gif;base64,R0lGODlhCgAKAJECAAAAAP///////wAAACH/C05FVFNDQVBFMi4wAwEAAAAh/wtYTVAgRGF0YVhNUDw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6OEI5RDc5MTFDNkE2MTFFM0JCMDZEODI2QTI4MzJBOTIiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6OEI5RDc5MTBDNkE2MTFFM0JCMDZEODI2QTI4MzJBOTIiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoTWFjaW50b3NoKSI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuZGlkOjAyODAxMTc0MDcyMDY4MTE4MDgzQzNDMjA5MzREQ0ZDIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjAyODAxMTc0MDcyMDY4MTE4MDgzQzNDMjA5MzREQ0ZDIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+Af/+/fz7+vn49/b19PPy8fDv7u3s6+rp6Ofm5eTj4uHg397d3Nva2djX1tXU09LR0M/OzczLysnIx8bFxMPCwcC/vr28u7q5uLe2tbSzsrGwr66trKuqqainpqWko6KhoJ+enZybmpmYl5aVlJOSkZCPjo2Mi4qJiIeGhYSDgoGAf359fHt6eXh3dnV0c3JxcG9ubWxramloZ2ZlZGNiYWBfXl1cW1pZWFdWVVRTUlFQT05NTEtKSUhHRkVEQ0JBQD8+PTw7Ojk4NzY1NDMyMTAvLi0sKyopKCcmJSQjIiEgHx4dHBsaGRgXFhUUExIREA8ODQwLCgkIBwYFBAMCAQAAIfkEBQoAAgAsAAAAAAoACgAAAhWEERkn7W3ei7KlagMWF/dKgYeyGAUAIfkEBQoAAgAsAAAAAAoACgAAAg+UYwLJ7RnQm7QmsCyVKhUAIfkEBQoAAgAsAAAAAAoACgAAAhCUYgLJHdiinNSAVfOEKoUCACH5BAUKAAIALAAAAAAKAAoAAAIRVISAdusPo3RAzYtjaMIaUQAAIfkEBQoAAgAsAAAAAAoACgAAAg+MDiem7Q8bSLFaG5il6xQAIfkEBQoAAgAsAAAAAAoACgAAAg+UYRLJ7QnQm7SmsCyVKhUAIfkEBQoAAgAsAAAAAAoACgAAAhCUYBLJDdiinNSEVfOEKoECACH5BAUKAAIALAAAAAAKAAoAAAIRFISBdusPo3RBzYsjaMIaUQAAOw==");\n  border-image-slice: 1;\n  border-image-repeat: repeat;\n}\n.ReactCrop--disabled .ReactCrop__crop-selection {\n  cursor: inherit;\n}\n.ReactCrop--circular-crop .ReactCrop__crop-selection {\n  border-radius: 50%;\n  box-shadow: 0px 0px 1px 1px white, 0 0 0 9999em rgba(0, 0, 0, 0.5);\n}\n.ReactCrop__rule-of-thirds-vt::before, .ReactCrop__rule-of-thirds-vt::after, .ReactCrop__rule-of-thirds-hz::before, .ReactCrop__rule-of-thirds-hz::after {\n  content: "";\n  display: block;\n  position: absolute;\n  background-color: rgba(255, 255, 255, 0.4);\n}\n.ReactCrop__rule-of-thirds-vt::before, .ReactCrop__rule-of-thirds-vt::after {\n  width: 1px;\n  top: 0;\n  bottom: 0;\n}\n.ReactCrop__rule-of-thirds-vt::before {\n  left: 33.3333%;\n  left: calc(100% / 3);\n}\n.ReactCrop__rule-of-thirds-vt::after {\n  left: 66.6666%;\n  left: calc(100% / 3 * 2);\n}\n.ReactCrop__rule-of-thirds-hz::before, .ReactCrop__rule-of-thirds-hz::after {\n  left: 0;\n  right: 0;\n  height: 1px;\n}\n.ReactCrop__rule-of-thirds-hz::before {\n  top: 33.3333%;\n  top: calc(100% / 3);\n}\n.ReactCrop__rule-of-thirds-hz::after {\n  top: 66.6666%;\n  top: calc(100% / 3 * 2);\n}\n.ReactCrop__drag-handle {\n  position: absolute;\n  width: 10px;\n  height: 10px;\n  background-color: rgba(0, 0, 0, 0.2);\n  border: 1px solid rgba(255, 255, 255, 0.7);\n  box-sizing: border-box;\n  outline: 1px solid transparent;\n}\n.ReactCrop .ord-nw {\n  top: 0;\n  left: 0;\n  margin-top: -5px;\n  margin-left: -5px;\n  cursor: nw-resize;\n}\n.ReactCrop .ord-n {\n  top: 0;\n  left: 50%;\n  margin-top: -5px;\n  margin-left: -5px;\n  cursor: n-resize;\n}\n.ReactCrop .ord-ne {\n  top: 0;\n  right: 0;\n  margin-top: -5px;\n  margin-right: -5px;\n  cursor: ne-resize;\n}\n.ReactCrop .ord-e {\n  top: 50%;\n  right: 0;\n  margin-top: -5px;\n  margin-right: -5px;\n  cursor: e-resize;\n}\n.ReactCrop .ord-se {\n  bottom: 0;\n  right: 0;\n  margin-bottom: -5px;\n  margin-right: -5px;\n  cursor: se-resize;\n}\n.ReactCrop .ord-s {\n  bottom: 0;\n  left: 50%;\n  margin-bottom: -5px;\n  margin-left: -5px;\n  cursor: s-resize;\n}\n.ReactCrop .ord-sw {\n  bottom: 0;\n  left: 0;\n  margin-bottom: -5px;\n  margin-left: -5px;\n  cursor: sw-resize;\n}\n.ReactCrop .ord-w {\n  top: 50%;\n  left: 0;\n  margin-top: -5px;\n  margin-left: -5px;\n  cursor: w-resize;\n}\n.ReactCrop__disabled .ReactCrop__drag-handle {\n  cursor: inherit;\n}\n.ReactCrop__drag-bar {\n  position: absolute;\n}\n.ReactCrop__drag-bar.ord-n {\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 6px;\n  margin-top: -3px;\n}\n.ReactCrop__drag-bar.ord-e {\n  right: 0;\n  top: 0;\n  width: 6px;\n  height: 100%;\n  margin-right: -3px;\n}\n.ReactCrop__drag-bar.ord-s {\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 6px;\n  margin-bottom: -3px;\n}\n.ReactCrop__drag-bar.ord-w {\n  top: 0;\n  left: 0;\n  width: 6px;\n  height: 100%;\n  margin-left: -3px;\n}\n.ReactCrop--new-crop .ReactCrop__drag-bar, .ReactCrop--new-crop .ReactCrop__drag-handle, .ReactCrop--fixed-aspect .ReactCrop__drag-bar {\n  display: none;\n}\n.ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-n, .ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-e, .ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-s, .ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-w {\n  display: none;\n}\n@media (pointer: coarse) {\n  .ReactCrop__drag-handle {\n    width: 34px;\n    height: 34px;\n  }\n  .ReactCrop__drag-bar {\n    display: none;\n  }\n  .ReactCrop .ord-nw,\n.ReactCrop .ord-n,\n.ReactCrop .ord-ne,\n.ReactCrop .ord-e,\n.ReactCrop .ord-s,\n.ReactCrop .ord-sw,\n.ReactCrop .ord-w {\n    display: none;\n  }\n  .ReactCrop .ord-se {\n    margin-bottom: -1px;\n    margin-right: -1px;\n  }\n}\n\n.antd-img-crop-modal .ant-modal .ant-modal-body {\n  display: flex;\n  justify-content: center;\n}\n.antd-img-crop-modal .ant-modal .ant-modal-body .ReactCrop__image {\n  max-height: none;\n}'
);

var MODAL_TITLE = 'Edit image';

var ImgCrop =
	/*#__PURE__*/
	(function(_Component) {
		_inheritsLoose(ImgCrop, _Component);

		function ImgCrop(props) {
			var _this;

			_this = _Component.call(this, props) || this;

			_defineProperty(_assertThisInitialized(_this), 'renderUpload', function() {
				var children = _this.props.children;
				var uploadComponent = Array.isArray(children) ? children[0] : children;

				if (_this.newUploadProps === undefined) {
					var _uploadComponent$prop = uploadComponent.props,
						_uploadComponent$prop2 = _uploadComponent$prop.accept,
						accept = _uploadComponent$prop2 === void 0 ? 'image/*' : _uploadComponent$prop2,
						_uploadComponent$prop3 = _uploadComponent$prop.beforeUpload,
						beforeUpload =
							_uploadComponent$prop3 === void 0
								? function() {
										return true;
									}
								: _uploadComponent$prop3;
					_this.realBeforeUpload = beforeUpload;
					_this.newUploadProps = {
						accept: accept,
						beforeUpload: _this.beforeUpload
					};
				}

				return _extends({}, uploadComponent, {
					props: _extends({}, uploadComponent.props, {}, _this.newUploadProps)
				});
			});

			_defineProperty(_assertThisInitialized(_this), 'beforeUpload', function(file, fileList) {
				return new Promise(function(resolve, reject) {
					_this.resolve = resolve;
					_this.reject = reject; // 裁剪前校验图片

					var beforeCrop = _this.props.beforeCrop;

					if (beforeCrop && !beforeCrop(file, fileList)) {
						_this.reject();

						return;
					}

					_this.oldFile = file; // 读取添加的图片

					var reader = new FileReader();
					reader.addEventListener('load', function() {
						_this.setState({
							modalVisible: true,
							src: reader.result
						});
					});
					reader.readAsDataURL(file); // then -> `onImageLoaded`
				});
			});

			_defineProperty(_assertThisInitialized(_this), 'onImageLoaded', function(image) {
				if (_this.image !== undefined) return;
				_this.image = image; // realXXX 实际大小，showXXX 显示大小

				var realImgWidth = image.naturalWidth,
					realImgHeight = image.naturalHeight;
				var _this$props = _this.props,
					realCropWidth = _this$props.width,
					realCropHeight = _this$props.height,
					modalWidth = _this$props.modalWidth;
				var cropRate = realCropWidth / realCropHeight;
				var modalBodyWidth = modalWidth - 24 * 2;
				var scale = 1;
				var showImgWidth;
				var showImgHeight;
				var showCropWidth;
				var showCropHeight;
				var showCropX;
				var showCropY;
				var contain;

				if (realCropWidth > realImgWidth || realCropHeight > realImgHeight) {
					contain = true;
				} else {
					contain = _this.props.contain;
				} // 设置数值大小

				var setNumberData = function setNumberData() {
					showCropWidth = realCropWidth / scale;
					showCropHeight = realCropHeight / scale;
					showCropX = (showImgWidth - showCropWidth) / 2;
					showCropY = (showImgHeight - showCropHeight) / 2;
				}; // 设置填充容器

				var setContainData = function setContainData() {
					var imgRate = realImgWidth / realImgHeight;

					if (cropRate > imgRate) {
						// 裁剪框宽度大于图片
						showCropWidth = showImgWidth;
						showCropHeight = showCropWidth / cropRate;
						showCropX = 0;
						showCropY = (showImgHeight - showCropHeight) / 2;
					} else {
						// 裁剪框宽度小于图片
						showCropHeight = showImgHeight;
						showCropWidth = showCropHeight * cropRate;
						showCropX = (showImgWidth - showCropWidth) / 2;
						showCropY = 0;
					}
				}; // 设置数值大小 or 填充容器

				var setCropData = function setCropData() {
					contain === true ? setContainData() : setNumberData();
				}; // 设置裁切相关值

				if (realImgWidth > modalBodyWidth) {
					// 图片宽度大于 Model
					showImgWidth = modalBodyWidth;
					scale = realImgWidth / showImgWidth;
					showImgHeight = realImgHeight / scale;
					_this.scale = scale;
					setCropData();
				} else {
					// 图片宽度小于 Model
					showImgWidth = realImgWidth;
					showImgHeight = realImgHeight;
					setCropData();
				}

				_this.setState({
					crop: {
						unit: 'px',
						aspect: cropRate,
						width: showCropWidth,
						height: showCropHeight,
						x: showCropX,
						y: showCropY
					}
				});

				return false;
			});

			_defineProperty(_assertThisInitialized(_this), 'onCropChange', function(crop) {
				_this.setState({
					crop: crop
				});
			});

			_defineProperty(
				_assertThisInitialized(_this),
				'onOk',
				/*#__PURE__*/
				_asyncToGenerator(
					/*#__PURE__*/
					_regeneratorRuntime.mark(function _callee2() {
						var crop,
							sWidth,
							sHeight,
							image,
							scale,
							sx,
							sy,
							_this$props2,
							dWidth,
							dHeight,
							canvas,
							ctx,
							_this$oldFile,
							name,
							type,
							uid;

						return _regeneratorRuntime.wrap(function _callee2$(_context2) {
							while (1) {
								switch ((_context2.prev = _context2.next)) {
									case 0:
										crop = _this.state.crop;
										(sWidth = crop.width), (sHeight = crop.height);

										if (!(!sWidth || !sHeight)) {
											_context2.next = 5;
											break;
										}

										_this.onClose();

										return _context2.abrupt('return');

									case 5:
										image = _this.image;
										scale = _this.scale;
										(sx = crop.x), (sy = crop.y);

										if (scale !== undefined) {
											sx = sx * scale;
											sy = sy * scale;
											sWidth = sWidth * scale;
											sHeight = sHeight * scale;
										}

										(_this$props2 = _this.props),
											(dWidth = _this$props2.width),
											(dHeight = _this$props2.height); // 获取裁切后的图片

										canvas = document.createElement('canvas');
										canvas.width = dWidth;
										canvas.height = dHeight;
										ctx = canvas.getContext('2d');
										ctx.drawImage(image, sx, sy, sWidth, sHeight, 0, 0, dWidth, dHeight);
										(_this$oldFile = _this.oldFile),
											(name = _this$oldFile.name),
											(type = _this$oldFile.type),
											(uid = _this$oldFile.uid);
										canvas.toBlob(
											/*#__PURE__*/
											(function() {
												var _ref2 = _asyncToGenerator(
													/*#__PURE__*/
													_regeneratorRuntime.mark(function _callee(blob) {
														var croppedFile,
															response,
															croppedProcessedFile,
															fileType,
															useProcessedFile;
														return _regeneratorRuntime.wrap(
															function _callee$(_context) {
																while (1) {
																	switch ((_context.prev = _context.next)) {
																		case 0:
																			// 生成新图片
																			croppedFile = new File([ blob ], name, {
																				type: type,
																				lastModified: Date.now()
																			});
																			croppedFile.uid = uid; // 关闭弹窗

																			_this.onClose(); // 调用 beforeUpload

																			response = _this.realBeforeUpload(
																				croppedFile,
																				[ croppedFile ]
																			);

																			if (!(response === false)) {
																				_context.next = 7;
																				break;
																			}

																			_this.reject();

																			return _context.abrupt('return');

																		case 7:
																			if (
																				!(typeof response.then !== 'function')
																			) {
																				_context.next = 10;
																				break;
																			}

																			_this.resolve(croppedFile);

																			return _context.abrupt('return');

																		case 10:
																			_context.prev = 10;
																			_context.next = 13;
																			return response;

																		case 13:
																			croppedProcessedFile = _context.sent;
																			fileType = Object.prototype.toString.call(
																				croppedProcessedFile
																			);
																			useProcessedFile =
																				fileType === '[object File]' ||
																				fileType === '[object Blob]';

																			_this.resolve(
																				useProcessedFile
																					? croppedProcessedFile
																					: croppedFile
																			);

																			_context.next = 22;
																			break;

																		case 19:
																			_context.prev = 19;
																			_context.t0 = _context['catch'](10);

																			_this.reject(_context.t0);

																		case 22:
																		case 'end':
																			return _context.stop();
																	}
																}
															},
															_callee,
															null,
															[ [ 10, 19 ] ]
														);
													})
												);

												return function(_x) {
													return _ref2.apply(this, arguments);
												};
											})(),
											type
										);

									case 17:
									case 'end':
										return _context2.stop();
								}
							}
						}, _callee2);
					})
				)
			);

			_defineProperty(_assertThisInitialized(_this), 'onClose', function() {
				_this.oldFile = undefined;
				_this.image = undefined;
				_this.scale = undefined;

				_this.setState({
					modalVisible: false,
					crop: {}
				});
			});

			_this.state = {
				modalVisible: false,
				src: null,
				crop: {}
			};
			return _this;
		}
		/**
   * Upload 组件
   */
		// 渲染 Upload 组件

		var _proto = ImgCrop.prototype;

		_proto.render = function render() {
			var _this2 = this;

			var _this$props3 = this.props,
				modalTitle = _this$props3.modalTitle,
				modalWidth = _this$props3.modalWidth,
				resize = _this$props3.resize,
				resizeAndDrag = _this$props3.resizeAndDrag;
			var _this$state = this.state,
				modalVisible = _this$state.modalVisible,
				src = _this$state.src,
				crop = _this$state.crop;
			return React.createElement(LocaleReceiver, null, function(locale, localeCode) {
				return React.createElement(
					React.Fragment,
					null,
					_this2.renderUpload(),
					React.createElement(
						Modal,
						{
							visible: modalVisible,
							width: modalWidth,
							onOk: _this2.onOk,
							onCancel: _this2.onClose,
							wrapClassName: 'antd-img-crop-modal',
							title: localeCode === 'zh-cn' && modalTitle === MODAL_TITLE ? '编辑图片' : modalTitle,
							maskClosable: false,
							destroyOnClose: true
						},
						src &&
							React.createElement(ReactCrop, {
								src: src,
								crop: crop,
								locked: resize === false,
								disabled: resizeAndDrag === false,
								onImageLoaded: _this2.onImageLoaded,
								onChange: _this2.onCropChange,
								keepSelection: true
							})
					)
				);
			});
		};

		return ImgCrop;
	})(Component);

ImgCrop.propTypes = {
	width: PropTypes.number,
	height: PropTypes.number,
	contain: PropTypes.bool,
	resize: PropTypes.bool,
	resizeAndDrag: PropTypes.bool,
	modalTitle: PropTypes.string,
	modalWidth: PropTypes.number,
	beforeCrop: PropTypes.func,
	children: PropTypes.node
};
ImgCrop.defaultProps = {
	width: 100,
	height: 100,
	contain: false,
	resize: true,
	resizeAndDrag: true,
	modalTitle: MODAL_TITLE,
	modalWidth: 520
};

export default ImgCrop;

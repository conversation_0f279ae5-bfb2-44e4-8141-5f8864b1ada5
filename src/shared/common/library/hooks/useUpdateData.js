import { isArray, isEmpty, isFunction, isObject } from 'lodash';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import notiActions from '@chill/redux/notification/actions';

const { setNotification } = notiActions;

function useUpdateData({ action = () => {}, condition, type = [] }) {
  const noti = useSelector((state) => state.Notification.notification);
  const dispatch = useDispatch();
  // const containType = isArray(type) && type.indexOf(noti?.type) > -1;

  useEffect(() => {
    const notiVar = isObject(noti) ? noti : {};
    const con = isFunction(condition) ? condition(notiVar) : true;

    if (
      !isEmpty(notiVar) &&
      isArray(type) &&
      type.indexOf(notiVar.type) > -1 &&
      con
    ) {
      action(notiVar);

      // Hack: Clear notification data after it is used
      dispatch(setNotification({}));
    }
  }, [noti]);
}

export default useUpdateData;

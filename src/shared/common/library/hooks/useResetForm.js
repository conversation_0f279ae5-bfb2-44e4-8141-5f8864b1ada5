import { useEffect, useRef } from 'react';
import { isObject, isEmpty } from 'lodash';

// reset form fields when modal or drawer closed
function useResetFormOnClose({ form, visible, initVal }) {
  const prevVisibleRef = useRef();
  useEffect(() => {
    prevVisibleRef.current = visible;

    // Set initial form values when Modal appear
    if (visible && isObject(initVal) && !isEmpty(initVal)) {
      setTimeout(() => {
        form.setFieldsValue(initVal);
      }, 100);
    }
  }, [visible]);
  const prevVisible = prevVisibleRef.current;
  useEffect(() => {
    if (!visible && prevVisible) {
      form.resetFields();
    }
  }, [visible]);
}

export default useResetFormOnClose;

import { useEffect } from 'react';
import queryString from 'query-string';
import { isArray, isObject, omitBy, isEmpty, isNumber } from 'lodash';
import { useHistory } from 'react-router';
import getDefaultPath from '@chill/lib/helpers/url_sync';

const useFilters = ({ filters, restrictFlt = [] }) => {
  const history = useHistory();
  const preKeys = getDefaultPath();
  const currentPage = isArray(preKeys) && !isEmpty(preKeys) ? preKeys[0] : '';

  const rFlt = isArray(restrictFlt) ? restrictFlt : [];

  useEffect(() => {
    const flt = isObject(filters)
      ? omitBy(
          filters,
          (item, k) =>
            !item || (!isNumber(item) && isEmpty(item)) || rFlt.includes(k),
        )
      : {};

    const qs = queryString.stringify(flt, {
      arrayFormat: 'comma',
      encode: false,
    });
    const emptyFlt = isEmpty(flt);

    if (qs && !emptyFlt) {
      history.replace(`/dashboard/${currentPage}?${qs}`);
    } else {
      history.replace(`/dashboard/${currentPage}`);
    }
  }, [filters]);
};

export default useFilters;

/* eslint-disable array-callback-return */
import siteConfig from '@chill/config/site.config';
import { omitBy, isNull, isUndefined } from 'lodash';
import { getToken, getLanguage, getCountry } from '@chill/lib/helpers/utility';
import Notification from '@chill/components/Notification';
import authAction from '@chill/redux/auth/actions';
import { store } from '@chill/redux/store';

const allowedAccessUrls = ['user/update-profile'];

// Authrization will not work for below API urls
const restrictAuth = [
  'files/check-classified',
  'files/get-document-data',
  'user/get-signature',
  'contact/get-contact',
];

const defaultHeader = {
  'Content-Type': 'application/json',
  Accept: 'application/json',
};

function getAPIToken() {
  const token = getToken();
  return token ? `Bearer ${token}` : '';
}

function getAuthMsg(res = {}) {
  if (res?.message === 'Unauthorized') return 'common.sessionExpired';
  if (res?.data?.message === 'The user does not exist.')
    return 'err.accDeleted';
  if (res?.data?.blocked) return 'err.accBlocked';
  return '';
}

function getApiData(
  url,
  data = {},
  methodParm = 'GET',
  heads = defaultHeader,
  formData = false,
) {
  const headers = { ...heads };
  const method = methodParm.toUpperCase();
  const lang = getLanguage();
  const token = getAPIToken();
  const country = getCountry();
  const cId = country && country.id ? country.id : '';

  if (token && !headers.Authorization) headers.Authorization = token;

  headers.language = lang || 'en';

  let options = {
    method,
    headers,
  };
  let query = '';
  let qs = '';

  const apiData = !formData
    ? omitBy(data, (v) => isUndefined(v) || isNull(v))
    : data;

  if (cId && cId !== 'all') {
    headers.country = cId || '';
  }

  const dataLength = apiData ? Object.keys(apiData).length : 0;
  const body = formData ? apiData : JSON.stringify(apiData);

  if (method === 'POST' || method === 'PUT') options = { ...options, body };
  if (method === 'GET' && dataLength > 0) {
    Object.keys(apiData).map((key, i) => {
      const sep = i === dataLength - 1 ? '' : '&';
      query += `${encodeURIComponent(key)}=${encodeURIComponent(
        apiData[key],
      )}${sep}`;
    });
    qs = `?${query}`;
  }

  /**
   * Function to check session and if session expired then perform logout action
   * @function
   */
  function logoutExpired(msg) {
    const auth = store ? store.getState().Auth : {};
    store.dispatch(authAction.setLoggedOut(true));
    if (!auth.loggedOut) {
      Notification('error', msg);
      setTimeout(() => {
        store.dispatch(authAction.logout());
      }, 500);
    }
  }

  /**
   * Function to update user's features access;
   * @function
   */
  function updateAccess(res = {}, aUrl = '') {
    if (res.access || allowedAccessUrls.indexOf(aUrl) > -1) {
      store.dispatch(authAction.updateAccess());
    }
  }

  return new Promise((resolve, reject) => {
    fetch(`${siteConfig.apiUrl}${url}${qs}`, options)
      .then((response) => response.json())
      .then((resposeJson) => {
        const unAuthMsg = getAuthMsg(resposeJson || {});
        if (unAuthMsg && store && restrictAuth.indexOf(url) < 0) {
          logoutExpired(unAuthMsg);
        } else {
          updateAccess(resposeJson, url);
          resolve(resposeJson);
        }
      })
      .catch((err) => {
        console.log(err);
        reject(err);
      });
  });
}

function downloadFile({ url = '', filename = '', data, options = {} }) {
  // User's token
  const token = getAPIToken();

  // Options
  const headers = options.headers || {};
  const method = options.method ? options.method.toUpperCase() : 'GET';

  // Authorization Header
  if (token && !headers.Authorization) headers.Authorization = token;
  let query = '';
  let qs = '';

  // API data
  const apiData = data ? omitBy(data, (v) => isUndefined(v) || isNull(v)) : {};

  const dataLength = apiData ? Object.keys(apiData).length : 0;

  let body;

  if (method === 'POST' || method === 'PUT') body = JSON.stringify(apiData);
  if (method === 'GET' && dataLength > 0) {
    Object.keys(apiData).map((key, i) => {
      const sep = i === dataLength - 1 ? '' : '&';
      query += `${encodeURIComponent(key)}=${encodeURIComponent(
        apiData[key],
      )}${sep}`;
    });
    qs = `?${query}`;
  }

  return new Promise((resolve, reject) => {
    fetch(`${siteConfig.apiUrl}${url}${qs}`, {
      method,
      headers,
      body,
    })
      .then((res) => {
        if (res.status === 200) return res.blob();
        return false;
      })
      // 1. Convert the data into 'blob'
      .then((blob) => {
        if (blob) {
          // 2. Create blob link to download
          const fUrl = window.URL.createObjectURL(new Blob([blob]));
          const link = document.createElement('a');
          link.href = fUrl;
          if (filename) link.setAttribute('download', filename);
          // 3. Append to html page
          document.body.appendChild(link);
          // 4. Force download
          link.click();
          // 5. Clean up and remove the link
          link.parentNode.removeChild(link);
          resolve(true);
        } else {
          reject();
        }
      })
      .catch((err) => reject(err));
  });
}

function downloadStaticFile({ url = '', fileName = '' }) {
  return new Promise((resolve, reject) => {
    fetch(url, {
      method: 'GET',
    })
      .then((res) => {
        if (res.status === 200) return res.blob();
        return false;
      })
      // 1. Convert the data into 'blob'
      .then((blob) => {
        if (blob) {
          // 2. Create blob link to download
          const fUrl = window.URL.createObjectURL(new Blob([blob]));
          const link = document.createElement('a');
          link.href = fUrl;
          link.setAttribute('download', fileName || '');
          // 3. Append to html page
          document.body.appendChild(link);
          // 4. Force download
          link.click();
          // 5. Clean up and remove the link
          link.parentNode.removeChild(link);
          resolve(true);
        } else {
          reject();
        }
      })
      .catch((err) => reject(err));
  });
}

export { downloadFile, downloadStaticFile };

export default getApiData;

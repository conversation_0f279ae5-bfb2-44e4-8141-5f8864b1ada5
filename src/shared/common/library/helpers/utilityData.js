const docTypes = {
  'application/msword': 'doc',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
    'docx',
  'application/pdf': 'pdf',
  'image/png': 'png',
  'image/jpeg': 'jpeg',
  'image/jpg': 'jpg',
  'text/csv': 'csv',
  'text/html': 'html',
  'text/plain': 'txt',
  'application/vnd.ms-powerpoint': 'ppt',
  'application/octet-stream': 'ppt',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation':
    'pptx',
  'application/vnd.ms-excel': 'xls',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
};

const greenBackgroundTag = {
  height: '32px',
  padding: '5px 16px',
  gap: '4px',
  borderRadius: '8px',
  background: '#E7F7EF',
  maxWidth: '100px',
  innerText: {
    fontFamily: 'Inter',
    fontSize: 14,
    fontWeight: 500,
    lineHeight: '22.4px',
    textAlign: 'right',
    textUnderlinePosition: 'from-font',
    textDecorationSkipInk: 'none',
    color: '#0CAF60 !important',
  },
};
const orangeBackgroundTag = {
  height: '32px',
  padding: '5px 16px',
  gap: '4px',
  borderRadius: '8px',
  background: '#FFF0E6',
  maxWidth: '100px',
  color: '#FE964A !important',

  innerText: {
    fontFamily: 'Inter',
    fontSize: 14,
    fontWeight: 500,
    lineHeight: '22.4px',
    textAlign: 'right',
    textUnderlinePosition: 'from-font',
    textDecorationSkipInk: 'none',
    color: '#FE964A !important',
  },
};

const allStatus = [
  { id: '1', name: 'Draft' },
  { id: '2', name: 'Pending', color: 'orange' },
  { id: '3', name: 'In Process', color: 'orange', disabled: true },
  { id: '4', name: 'Preparing', color: 'orange', disabled: true },
  { id: '5', name: 'Pending Owner', color: 'orange', disabled: true },
  { id: '6', name: 'Completed', color: 'green' },
  { id: '7', name: 'Declined', color: 'red', disabled: true },
  { id: '8', name: 'Signed', color: 'green', disabled: true },
];

const wFlowStatus = [
  { id: '1', key: 'pending', name: 'Pending', color: 'orange' },
  { id: '2', key: 'inProcess', name: 'In Process', color: 'orange' },
  { id: '3', key: 'approve', name: 'Approve', color: 'green', disabled: true },
  { id: '4', key: 'declined', name: 'Declined', color: 'red', disabled: true },
  { id: '5', key: 'done', name: 'Done', color: 'green', disabled: true },
  { id: '6', key: 'completed', name: 'Completed', color: 'green' },
];

const reqStatus = [
  { id: '1', name: 'Pending', color: 'orange' },
  { id: '2', name: 'Signed', color: 'green' },
  { id: '3', name: 'Declined', color: 'red' },
];

const paymentReqStatus = [
  { id: 1, name: 'Pending', color: 'orange', disabled: true },
  { id: 2, name: 'In Process', color: 'orange' },
  { id: 3, name: 'Completed', color: 'green' },
  { id: 4, name: 'Cancelled', color: 'red' },
];

const signTabs = [
  {
    id: '1',
    title: 'signature.saved',
    type: 'saved',
  },
  {
    id: '2',
    title: 'signature.choose',
    type: 'choose',
  },
  {
    id: '3',
    title: 'signature.draw',
    type: 'draw',
  },
  {
    id: '4',
    title: 'signature.upload',
    type: 'upload',
  },
];

const trailerTypes = [
  {
    value: 'trailer',
    text: 'Trailer',
  },
  {
    value: 'camera',
    text: 'Camera',
  },
  {
    value: 'tracker',
    text: 'Tracker',
  },
];

const ruleTypes = [
  {
    value: 'exact',
    text: 'Exact',
  },
  {
    value: 'more_than',
    text: 'More than',
  },
  {
    value: 'mt_time_period',
    text: 'More than within a period',
  },
];

const metricTypes = [
  {
    value: 'mins',
    text: 'Mins',
  },
  {
    value: 'hours',
    text: 'Hours',
  },
  {
    value: 'days',
    text: 'Days',
  },
  {
    value: 'miles',
    text: 'Miles',
  },
  {
    value: 'km',
    text: 'KM',
  },
  {
    value: 'fixed_duration',
    text: 'Fixed Duration',
  },
];

const heighlightsMetricTypes = [
  {
    value: 'mins',
    text: 'Mins',
  },
  {
    value: 'hours',
    text: 'Hours',
  },
  {
    value: 'days',
    text: 'Days',
  },
  {
    value: 'miles',
    text: 'Miles',
  },
  {
    value: 'km',
    text: 'KM',
  },
  {
    value: 'percentage',
    text: 'Percentage',
  },
];

const challengeStatus = [
  {
    value: 'until_cancelled',
    text: 'Until cancelled',
  },
  {
    value: 'between_2_date_periods',
    text: 'Between 2 date periods',
  },
  // {
  //   value: 'numerical',
  //   text: 'Numerical',
  // },
  {
    value: 'distance',
    text: 'Distance',
  },
  {
    value: 'numerical_of_trips',
    text: 'Numerical of trips',
  },
];

const awardType = [
  {
    value: 'sticker',
    text: 'Sticker',
  },
  {
    value: 'coupon',
    text: 'Coupon',
  },
];

export {
  allStatus,
  docTypes,
  wFlowStatus,
  reqStatus,
  signTabs,
  paymentReqStatus,
  trailerTypes,
  ruleTypes,
  metricTypes,
  heighlightsMetricTypes,
  challengeStatus,
  awardType,
  greenBackgroundTag,
  orangeBackgroundTag,
};

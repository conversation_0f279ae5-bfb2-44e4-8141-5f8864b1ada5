class IdleTimer {
  constructor({ timeout, onTimeout, onExpired, warningDuration, onWarning }) {
    this.timeout = timeout;
    this.warningDuration = warningDuration;
    this.onWarning = onWarning;
    this.onTimeout = onTimeout;
    this.timerLocked = false;

    const expiredTime = parseInt(localStorage.getItem('_expiredTime'), 10);
    if (expiredTime > 0 && expiredTime < Date.now()) {
      onExpired();
      return;
    }

    this.eventHandler = this.updateExpiredTime.bind(this);
    this.tracker();
    this.startInterval();
  }

  startInterval() {
    this.updateExpiredTime();

    this.interval = setInterval(() => {
      const expiredTime = parseInt(localStorage.getItem('_expiredTime'), 10);
      if (expiredTime < Date.now()) {
        if (this.onTimeout) {
          this.onTimeout();
          this.cleanUp();
        }
      } else if (
        expiredTime - this.warningDuration * 1000 < Date.now() &&
        !this.timerLocked
      ) {
        this.timerLocked = true;
        this.onWarning();
      }
    }, 2000);
  }

  updateExpiredTime(type = '') {
    if (type === 'checkedIn') {
      this.timerLocked = false;
      localStorage.setItem('_expiredTime', Date.now() + this.timeout * 1000);
      return;
    }
    if (this.timerLocked) return;
    if (this.timeoutTracker) {
      clearTimeout(this.timeoutTracker);
    }
    this.timeoutTracker = setTimeout(() => {
      localStorage.setItem('_expiredTime', Date.now() + this.timeout * 1000);
    }, 500);
  }

  tracker() {
    window.addEventListener('mousemove', this.eventHandler);
    window.addEventListener('scroll', this.eventHandler);
    window.addEventListener('keydown', this.eventHandler);
  }

  cleanUp() {
    localStorage.removeItem('_expiredTime');
    clearInterval(this.interval);
    clearTimeout(this.timeoutTracker);
    window.removeEventListener('mousemove', this.eventHandler);
    window.removeEventListener('scroll', this.eventHandler);
    window.removeEventListener('keydown', this.eventHandler);
  }
}
export default IdleTimer;

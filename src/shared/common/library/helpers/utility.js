/* eslint-disable no-else-return */
/* eslint-disable no-param-reassign */
/* eslint-disable prefer-template */
// import { Map } from 'immutable';
import moment from 'moment';
import {
  isEmpty,
  isString,
  isObject,
  isArray,
  toNumber,
  omitBy,
  map,
} from 'lodash';
import { store } from '@chill/redux/store';
import getApiData from '@chill/lib/helpers/apiHelper';
import languageAction from '@chill/redux/language/actions';

import { docTypes } from './utilityData';

export function clearToken() {
  localStorage.removeItem('id_token');
  localStorage.removeItem('user_data');
}

export function getToken() {
  try {
    const idToken = localStorage.getItem('id_token');
    return idToken;
  } catch (err) {
    clearToken();
    return '';
  }
}

export function getLanguage() {
  try {
    const language = localStorage.getItem('language');
    return language;
  } catch (err) {
    return '';
  }
}

export function getCountry() {
  try {
    const country = localStorage.getItem('country');
    const parsedCountry = country ? JSON.parse(country) : {};
    return parsedCountry;
  } catch (err) {
    return {};
  }
}
export function getUserData() {
  try {
    const userData = localStorage.getItem('user_data');
    return userData;
  } catch (err) {
    clearToken();
    return {};
  }
}

export function getBase64(img, callback) {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result));
  reader.readAsDataURL(img);
}

export function getParsedJson(json) {
  if (isString(json) && !isEmpty(json)) return JSON.parse(json);
  return json;
}

export function getHours() {
  const timesArr = [];
  for (let i = 0; i <= 24; i += 1) {
    if (i < 10) {
      timesArr.push({
        value: `0${i}:00`,
        label: `0${i}:00`,
      });
    } else if (i === 24) {
      timesArr.push({
        value: `23:59`,
        label: `23:59`,
      });
    } else {
      timesArr.push({
        value: `${i}:00`,
        label: `${i}:00`,
      });
    }
  }
  return timesArr;
}

export function getFullName(fname, lname) {
  if (fname && lname) return `${fname} ${lname}`;
  if (fname) return fname;
  return '';
}

export function dropdownFilter(input, option, type = '') {
  if (type && option.props[type]) {
    return option.props[type].toLowerCase().indexOf(input.toLowerCase()) >= 0;
  }
  return option.props.children
    ? option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
    : false;
}
export function countryFilter(input, option) {
  return (
    option.props.countryname.toLowerCase().indexOf(input.toLowerCase()) >= 0
  );
}

export function formatFileSize(bytes, decimalPoint = 2) {
  // eslint-disable-next-line eqeqeq
  if (bytes == 0) return '0 Bytes';
  const k = 1000;
  const dm = decimalPoint || 2;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / k ** i).toFixed(dm))} ${sizes[i]}`;
}

export function checkImage(file, type = '') {
  console.log('file.type ==>', file.type);
  let isJpgOrPng =
    file.type === 'image/jpeg' ||
    file.type === 'image/png' ||
    file.type === 'image/jpg';
  if (type === 'supportPdf') {
    isJpgOrPng =
      file.type === 'image/jpeg' ||
      file.type === 'image/png' ||
      file.type === 'image/jpg' ||
      file.type === 'application/pdf';
  }
  if (type === 'video') {
    isJpgOrPng =
      file.type === 'video/mp4' ||
      file.type === 'video/webm' ||
      file.type === 'video/mkv' ||
      file.type === 'image/jpeg' ||
      file.type === 'image/png' ||
      file.type === 'image/jpg';
  }
  let message = '';
  let status = true;
  let isLt2M =
    type === 'video'
      ? file.size / 1024 / 1024 < 10
      : file.size / 1024 / 1024 < 2;
  if (type === 'custom') {
    isLt2M = file.size / 1024 < 200;
  }

  if (!isJpgOrPng) {
    message = type === 'supportPdf' ? 'err.onlyJPGPNGPDF' : 'err.onlyJPGPNG';
    status = false;
  } else if (!isLt2M && type === 'custom') {
    message = 'err.max200KBFile';
    status = false;
  } else if (!isLt2M) {
    message = type === 'video' ? 'err.max10MBFile' : 'err.max2MBFile';
    status = false;
  }

  return { message, status };
}

export function checkValidDoc(file) {
  const fType = file && file.type ? file.type : '';
  const isDoc = docTypes[fType];
  const isLt10M = file.size / 1024 / 1024 < 10;

  let message = '';
  let status = true;

  if (!isDoc) {
    message = 'err.validDoc';
    status = false;
  } else if (!isLt10M) {
    message = 'err.fileMust10MB';
    status = false;
  }

  return { message, status };
}

export function fixAutocomplete() {
  if (document) {
    document.querySelectorAll('input').forEach((e) => {
      // you can put any value but NOT "off" or "false" because they DO NOT works
      e.setAttribute('autocomplete', 'stopAutocomplete');
    });
  }
}

export function getCordZip(res) {
  if (isObject(res) && !isEmpty(res)) {
    const coordinates =
      res && isObject(res.location) && isArray(res.location.coordinates)
        ? res.location.coordinates
        : [];
    let location = {};
    if (!isEmpty(coordinates)) {
      location = {
        lat: coordinates[0],
        lng: coordinates[1],
      };
    }
    return location;
  }
  return {};
}

export function getSortOrder(order) {
  if (order === 'ascend') {
    return 'asc';
  }
  if (order === 'descend') {
    return 'desc';
  }
  return order;
}

export function randomAlphaNumString(length) {
  let result = '';
  const chars =
    '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  for (let i = length; i > 0; i -= 1) {
    result += chars[Math.round(Math.random() * (chars.length - 1))];
  }
  return result;
}

export function isUrlValid(userInput) {
  const res = userInput.match(
    /(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_+.~#?&//=]*)/g,
  );
  return !!res;
}

export function timeConvert(num) {
  const number = num ? toNumber(num) * 60 : 0;
  const hours = Math.floor(number / 60);
  const minutes = Math.round(number % 60);

  const hFormat = hours < 10 ? `0${hours}` : hours;
  const mFormat = minutes < 10 ? `0${minutes}` : minutes;
  return `${hFormat}:${mFormat} hour`;
}

export function getUConnection(user = {}) {
  if (isObject(user) && user.sub) {
    const sub = user.sub.split('|');
    const conn = isArray(sub) && sub[0] ? sub[0] : '';
    return conn;
  }
  return '';
}

export function setDirection(dir = 'ltr') {
  if (typeof window !== 'undefined') {
    document.getElementsByTagName('html')[0].setAttribute('dir', dir);
  }
}

export function getDummyArray(len = 10) {
  return Array.from(Array(len).keys());
}

export function checkAryObj(ary = [], type) {
  let found = false;
  if (isArray(ary)) {
    const aLen = ary.length || 0;
    for (let i = 0; i < aLen; i += 1) {
      if (ary[i][type]) {
        found = true;
        break;
      }
    }
  }

  return found;
}

export function checkNotEmptyFlt(ft = {}, restrict = [], displayFlt = false) {
  if (displayFlt) return true;
  const rKeys = ['_sort', ...restrict];
  const flt = isObject(ft)
    ? omitBy(
        ft,
        (f, k) => rKeys.some((r) => k.indexOf(r) > -1) || !f || isEmpty(f),
      )
    : {};
  return !isEmpty(flt);
}

export function getValidDocAry(type = '') {
  let aFiles = map(docTypes, (v, k) => k);
  if (type === 'val') {
    aFiles = map(docTypes, (v) => v);
  }
  if (type === 'ext') {
    aFiles = map(docTypes, (v) => `.${v}`);
  }
  return aFiles;
}

export function validateEmail(email = '') {
  const re =
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return re.test(String(email).toLowerCase());
}

export function getMediaPermission() {
  return new Promise((resolve) => {
    if (!navigator || (navigator && !navigator.mediaDevices)) resolve(false);
    navigator.mediaDevices
      .getUserMedia({ audio: true })
      .then(() => {
        resolve(true);
      })
      .catch(() => {
        resolve(false);
      });
  });
}

const urls = new WeakMap();

export const blobUrl = (blob) => {
  if (urls.has(blob)) {
    return urls.get(blob);
  }
  const url = URL.createObjectURL(blob);
  urls.set(blob, url);
  return url;
};

export function getCurrnetSecurity(uData) {
  if (isObject(uData)) {
    if (uData.finger_print === 1) return 'finger_print';
    if (uData.nfc === 1) return 'nfc';
    if (uData.six_digit === 1) return 'six_digit';
    if (uData.two_factor === 1) return 'two_factor';
  }
  return '';
}

export function getFileTypesAry() {
  const dcTypes = [];
  map(docTypes, (d) => {
    if (dcTypes.indexOf(d) > -1) return false;
    return dcTypes.push(d);
  });
  return dcTypes;
}

export function getStorageData(access) {
  const userAccess = isObject(access) ? access : {};
  const cSpace = isObject(userAccess.cloud_space) ? userAccess.cloud_space : {};
  const percentage = !isEmpty(cSpace)
    ? (100 * cSpace.getSizeOfDocumet) / cSpace.featuresSpace
    : 0;
  const per =
    percentage < 99 ? percentage.toPrecision(2) : percentage.toFixed(2);
  return per;
}

export function getObjVal(obj = {}, path = '') {
  const object = isObject(obj) ? obj : {};
  const pth = path || '';
  return pth.split('.').reduce((prev, curr) => {
    return prev ? prev[curr] : null;
  }, object || null);
}

export function disabledTillToday(current) {
  // Can not select days before today and today
  return current && current < moment().endOf('day');
}

// this function get list and update in redux
export async function getLanguageList() {
  try {
    const response = await getApiData('getLanguageList', {}, 'POST');
    if (response.success && isArray(response.data)) {
      store.dispatch(languageAction.setLanguage(response.data));
    }
  } catch (error) {
    console.log('error ===', error);
  }
}

export function timeDifference(givenTime) {
  givenTime = new Date(givenTime);
  const milliseconds = new Date().getTime() - givenTime.getTime();
  const numberEnding = (number) => {
    return number > 1 ? 'e' : '';
  };
  const number = (num) => (num > 9 ? '' + num : '0' + num);
  const getTime = () => {
    let temp = Math.floor(milliseconds / 1000);
    const years = Math.floor(temp / 31536000);
    if (years) {
      const month = number(givenTime.getUTCMonth() + 1);
      const day = number(givenTime.getUTCDate());
      const year = givenTime.getUTCFullYear() % 100;
      return `${day}-${month}-${year}`;
    }
    const days = Math.floor((temp %= 31536000) / 86400);
    if (days) {
      if (days < 28) {
        return 'vor ' + days + ' tag' + numberEnding(days);
      } else {
        const months = [
          'Jan',
          'Feb',
          'Mar',
          'Apr',
          'May',
          'Jun',
          'Jul',
          'Aug',
          'Sep',
          'Oct',
          'Nov',
          'Dec',
        ];
        const month = months[givenTime.getUTCMonth()];
        const day = number(givenTime.getUTCDate());
        return `${day} ${month}`;
      }
    }
    const hours = Math.floor((temp %= 86400) / 3600);
    if (hours) {
      return `vor ${hours} stund${numberEnding(hours)}`;
    }
    const minutes = Math.floor((temp %= 3600) / 60);
    if (minutes) {
      return `vor ${minutes} minute${numberEnding(minutes)}`;
    }
    return 'vor ein paar Sekunden';
  };
  return getTime();
}

export function colorArray() {
  return [
    { name: 'A', Color: '#1abc9c' },
    { name: 'B', Color: '#f56a00' },
    { name: 'C', Color: '#c0392b' },
    { name: 'D', Color: '#3498db' },
    { name: 'E', Color: '#9b59b6' },
    { name: 'F', Color: '#34495e' },
    { name: 'G', Color: '#16a085' },
    { name: 'H', Color: 'rgba(63, 81, 181,1.0)' },
    { name: 'I', Color: '#2980b9' },
    { name: 'J', Color: '#8e44ad' },
    { name: 'K', Color: '#2c3e50' },
    { name: 'L', Color: '#f1c40f' },
    { name: 'F', Color: '#e74c3c' },
    { name: 'M', Color: '#f39c12' },
    { name: 'N', Color: '#273c75' },
    { name: 'O', Color: '#c0392b' },
    { name: 'P', Color: '#f39c12' },
    { name: 'Q', Color: '#7265e6' },
    { name: 'R', Color: '#00a8ff' },
    { name: 'S', Color: '#e1b12c' },
    { name: 'T', Color: '#689F38' },
    { name: 'U', Color: '#353b48' },
    { name: 'V', Color: '#dcdde1' },
    { name: 'W', Color: '#c23616' },
    { name: 'X', Color: '#00a8ff' },
    { name: 'Y', Color: '#4cd137' },
    { name: 'Z', Color: '#9c88ff' },
  ];
}

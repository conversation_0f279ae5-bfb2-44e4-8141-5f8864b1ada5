import styled from 'styled-components';
import BoxComponent from '@chill/components/utility/box';
import { palette } from 'styled-theme';
import WithDirection from '@chill/lib/helpers/rtl';

const BottomViewWrapper = WithDirection(styled.div`
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e9e9e9;
  padding: 15px 20px;
  background: #fff;
  text-align: left;
  display: flex;
  justify-content: flex-start;

  & > .ant-form-item {
    &:first-child {
      button {
        margin-left: ${(props) =>
          props['data-rtl'] === 'rtl' ? '8px' : '0px'};
      }
    }
  }

  & .cancelBtnStyle {
    color: #1172ec;
    font-weight: 600;
    cursor: pointer;
  }
`);

const BoxWrapper = styled(BoxComponent)`
  .fileManagerBtn {
    margin-right: 10px;
    @media (max-width: 425px) {
      margin-right: 0px;
    }
  }

  & .settingsGrid {
    margin-bottom: 10px;
  }

  .isoInvoiceTableBtn {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
  }
  .isoInvoiceTableBtn.searchInputsWrapper {
    display: flex;
    justify-content: flex-end;
    // margin-bottom: 20px;
    @media (max-width: 768px) {
      justify-content: space-between !important;
      &.patientWrapper {
        width: 100%;
        justify-content: space-between;
      }
    }
  }
  .isoInvoiceTableBtn.searchInputsWrapper.newSearch {
    display: none;
    justify-content: flex-end;
    margin-bottom: 20px;
    @media (max-width: 768px) {
      display: flex;
      justify-content: space-between !important;
    }
  }
  .isoInvoiceTableBtn.searchInputsWrapper > .ant-btn-icon-only {
    display: none;
    @media (max-width: 768px) {
      display: flex;
    }
    @media (max-width: 320px) {
      padding: 0px 15px;
    }
  }
  .isoInvoiceTableBtn.hideResponsive {
    display: block;
    @media (max-width: 768px) {
      display: none;
    }
  }
  .appointmentFilterMain.invoiceFilter {
    margin-bottom: 10px;
    // justify-content: flex-start;

    & .filterWrapper {
      @media (min-width: 768px) {
        margin-left: 10px;
      }
    }
    & .isoInvoiceTableBtn.searchInputsWrapper {
      display: none;
    }
    @media (max-width: 1220px) {
      flex-direction: row;
    }
    @media (max-width: 768px) {
      & .filterDesktop {
        display: none;
      }
      & .isoInvoiceTableBtn.searchInputsWrapper {
        display: block;
      }
    }
  }
  .searchInputWrapper {
    display: none;
    @media (max-width: 768px) {
      display: inline-block;
      justify-content: space-between;
      width: 100%;
      & > div {
        padding-bottom: 15px;
      }
    }
  }
  .fileManagerTable {
    table {
      tbody {
        tr {
          cursor: pointer;
        }
      }
    }
  }
  .appointmentFilterMain {
    margin-bottom: 20px;
    align-items: center;
    & > div {
      display: flex;
      margin-bottom: 10px;
      & > div {
        margin-right: 10px;
        & .ant-select {
          min-width: 200px;
        }
      }
    }
    .filterBtnWrapper {
      & .radioWrapper {
        display: none;
      }
      @media (max-width: 768px) {
        display: flex;
        justify-content: space-between;
        width: 100%;
        align-items: center;
        & .radioWrapper {
          width: 33%;
          margin-top: 5px;
          justify-content: flex-end;
          display: flex;
          margin-bottom: 0px !important;
        }
      }
    }
    .filterBtnWrapper.fileFilter {
      & .radioWrapper {
        display: none;
      }
      & > div {
        @media (max-width: 768px) {
          display: flex;
          justify-content: space-between;
          width: 100%;
          align-items: center;
          & .radioWrapper {
            width: auto;
            justify-content: flex-end;
            display: flex;
            margin-bottom: 0px !important;
          }
        }

        @media (max-width: 425px) {
          display: block;
          & > button {
            margin-right: 15px;
            padding: 0px 13px;
          }
          & .radioWrapper {
            margin-top: 15px;
            display: flex;
            // justify-content: center;
          }

          & .fileManagerBtn {
            margin-right: 5px;
            padding: 0px 10px;
          }
          & .mateAddInvoiceBtn {
            padding: 0px 10px;
          }
        }
      }
    }
    & .searchInput {
      width: 60%;
    }
    & .isoInvoiceTableBtn {
      @media only screen and (max-width: 580px) {
        margin-bottom: 10px;
        margin-top: 0px;
      }
    }

    @media only screen and (max-width: 1250px) {
      flex-direction: column-reverse;
      align-items: flex-start;
      &.patient {
        flex-direction: row;
      }
    }
    @media only screen and (max-width: 1024px) {
      & > div {
        & > div {
          & .ant-select {
            min-width: 150px;
          }
        }
      }
    }
    @media only screen and (max-width: 768px) {
      &.patient {
        flex-direction: column-reverse;
      }
    }

    @media only screen and (max-width: 650px) {
      & > div {
        flex-wrap: wrap;
        & > div {
          width: 100%;
          margin: 0px 0px 5px 0px;
          & .ant-select {
            min-width: 100%;
          }
        }
      }
    }
  }
  @media (max-width: 768px) {
    .appointmentFilterMain.allWordsFilter {
      & .searchInput {
        margin-bottom: 15px;
        width: auto;
        & > div {
          margin-left: 0px !important;
          margin-bottom: 10px;
        }
        & > span {
          margin-bottom: 12px;
        }
      }
    }
  }
  @media (max-width: 768px) {
    .appointmentFilterMain.appoinmentSearchFilter {
      & .filterWrapper {
        display: block;
        width: 100%;
        & > div {
          padding-bottom: 10px;
        }
      }
    }
  }
  @media (max-width: 768px) {
    .appointmentFilterMain.feedbackFilter {
      & .feedbackWrapper {
        width: 100%;
      }
    }
    .appointmentFilterMain.fileFilterMain {
      & .fileFilterInput {
        width: 100%;
      }
      &.patient {
        margin-bottom: 10px;
      }
    }
  }
  @media (max-width: 580px) {
    .ant-row-flex.ant-row-flex-space-between.appointmentFilterMain.feedbackFilter
      > .feedbackWrapper {
      width: 100%;
    }
  }
`;

const CardWrapper = WithDirection(styled.div`
  width: auto;
  overflow: inherit;
  position: relative;

  .isoInvoiceTable {
    table {
      tbody {
        tr {
          td {
            .isoInvoiceBtnView {
              display: flex;
              flex-direction: row;
              // justify-content: center;
              > a {
                margin: ${(props) =>
                  props['data-rtl'] === 'rtl' ? '0 0 0 15px' : '0 15px 0 0'};
              }
              & > button {
                margin: ${(props) =>
                  props['data-rtl'] === 'rtl' ? '0 0 0 10px' : '0 10px 0 0'};
              }
              & > button.deleteCircleBtn {
                color: red;
              }
            }
            & a.MoreInfoValue.videoLink {
              margin-left: 10px;
              display: block;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
              width: 110px;
              margin-left: 10px;
            }
          }
        }
      }
    }
  }
  .invoiceListTable {
    .patientInfo {
      max-width: 70%;
      & .normalWhiteSpace {
        white-space: normal;
      }
      @media only screen and (max-width: 1600px) {
        max-width: 100%;
      }
      & .MoreInfoValue {
        margin-left: 10px;
        @media (max-width: 375px) {
          white-space: nowrap !important;
          width: 110px !important;
          overflow: hidden !important;
          text-overflow: ellipsis !important;
        }
      }
    }
    .protocolsInfo.patientInfo {
      & .MoreInfoValue {
        @media (max-width: 375px) {
          white-space: normal !important;
          width: auto !important;
          overflow: auto !important;
          text-overflow: initial !important;
        }
      }
    }
    .invoiceDltBtn {
      font-size: 18px;
      display: inline-block;

      & button,
      & button:hover {
        border: 0;
        color: ${palette('error', 0)};
      }
    }
  }
`);

const FormWrapper = styled.div`
  ._uploadTextContainer {
    padding: 10px 25px;
  }

  .ant-form-item-explain-error {
    padding: 0px 0px 0px 10px;
    font-family: Inter;
    font-size: 12px;
    font-weight: 400;
    line-height: 14.52px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #f57e77;
    span {
      font-family: Inter;
      font-size: 12px;
      font-weight: 400;
      line-height: 14.52px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #f57e77;
    }
  }

  .btn-form {
    margin-bottom: 10px;
    .ant-form-item-row {
      background-color: transparent !important;
    }
    .submitBtnStyle {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      border-radius: 12px !important;
      background-color: #007aff !important;
      height: 53px;
      span {
        font-family: Inter;
        font-size: 16px;
        font-weight: 400;
        line-height: 19.36px;
        text-align: center;
        color: #ffffff;
      }
    }
  }

  .ant-form-item-row {
    background-color: #eff1f999 !important;
    border-radius: 8px !important;
    padding: 10px 12px;
    .ant-input:focus,
    .ant-select-selector:focus {
      box-shadow: none !important;
    }

    .uploadImage-center {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .ant-input,
    .ant-select-selector,
    .ant-upload-select-picture-card,
    .ant-picker,
    .ant-input-number-in-form-item,
    .ant-input-number-input {
      background-color: #eff1f500 !important;
      border-radius: 8px !important;
      border: transparent !important;
      // padding: 4px 0px;
    }

    .ant-input,
    .ant-select-selector,
    .ant-upload-select-picture-card,
    .ant-picker,
    .ant-input-number-in-form-item,
    .ant-input-number-input {
      color: #2a2b2c;
      font-family: Inter;
      font-size: 14px;
      font-weight: 400;
      line-height: 16.94px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
    }

    .ant-input::placeholder,
    .ant-select-selector::placeholder,
    .ant-upload-select-picture-card::placeholder,
    .ant-picker::placeholder,
    .ant-input-number-in-form-item::placeholder,
    .ant-input-number-input::placeholder {
      color: #abafb1;
      font-family: Inter;
      font-size: 14px;
      font-weight: 400;
      line-height: 16.94px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
    }
  }

  .ant-form-item-label {
    label {
      span {
        color: #5e6366;
        font-family: Inter;
        font-size: 12px;
        text-transform: none;
        font-weight: 400;
        line-height: 14.52px;
      }
    }
  }

  ._uploadText {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  ._w-100 {
    width: 100%;
  }

  .avatar-uploader {
    margin: 0px 0px 20px;
    cursor: pointer;
  }
  .otpLabel {
    text-align: center;
    padding-bottom: 25px;

    & > h3 {
      color: rgb(68, 68, 68);
      font-weight: bold;
    }

    & > div {
      & .numberWrap {
        font-weight: bold;
      }
    }
  }
  .otpWrapper {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    padding-top: 20px;
    .timerWrapper {
      & .ant-statistic {
        display: flex;
        justify-content: center;

        & .ant-statistic-title {
          color: ${palette('primary', 0)};
          font-size: 14px;
          text-decoration: underline;
        }

        & .ant-statistic-content {
          padding: 0px 4px;
          font-size: 14px;
        }
      }
    }
  }

  .proofWrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;

    & > div {
      display: flex;
      align-items: center;
      padding: 10px 0;
    }
  }
  .phoneWrapper {
    padding: 16px;
    background: #f4f4f4;
    border-radius: 5px;

    & .phonoNumber {
      font-weight: 600;
    }
  }
  .uploadTitleWrap {
    font-weight: 550;
    font-size: 16px;
    color: ${palette('secondary', 0)};
  }
  .imageWrap {
    padding: 20px;
    border: 2px dashed #ddd;
    margin-top: 15px;
    background: #f4f4f4;
    text-align: center;
    border-radius: 5px;

    & > img {
      width: 130px;
    }
    & > div {
      text-align: center;
      color: #666;
      padding-top: 10px;
    }
  }

  .signatureList,
  .chooseList {
    width: 100%;
  }

  & .signatureList {
    margin-top: 15px;

    & ul.ant-list-items {
      & li.ant-list-item {
        padding: 15px 0;
        margin-bottom: 5px;
      }
    }

    & .sign-main {
      display: flex;
      align-items: center;
      justify-content: space-between;

      & .ant-tag {
        margin-right: 20px;
      }

      img {
        max-width: 190px;
        max-height: 100px;
        width: auto;
      }
    }
  }

  .settingList {
    & .settingTitleWrap {
      flex-direction: column;
      display: flex;

      & .settingTitle {
        font-size: 16px;
        font-weight: 550;
      }

      & .settingDesc {
        padding-top: 2px;
        color: #908c8c;
      }
    }
  }

  .chooseList {
    cursor: pointer;
    & .chooseFormItem {
      margin-bottom: 0px !important;
      border-bottom: 1px solid #ddd !important;
    }

    & ul.ant-list-items {
      & li.ant-list-item {
        & .ant-list-item-meta-title {
          margin-bottom: 0px;
        }

        padding: 15px 0 10px;
      }
    }
  }

  .createBtn {
    color: ${palette('primary', 0)};
    margin-top: 15px;
    border: none;
  }

  & .createSignBtn {
    color: ${palette('primary', 0)};
    margin: 10px 0 15px;
    border-color: ${palette('primary', 0)};
  }

  .createBtn.modifyBtn {
    margin-top: 0px;
  }

  .toggleSwitch {
    margin: 0 23px;
  }

  .absolute-remove {
    position: absolute;
    top: -11px;
    right: -9px;
    font-size: 19px;
  }

  .phoneCodePicker > div:first-child {
    border: none;
    box-shadow: none !important;
  }
  .perCheckbox {
    margin-left: 10px;
    margin-top: 15px;
  }
  .userAvtarOrdered {
    order: 2;
    @media only screen and (max-width: 768px) {
      order: 1;
      & + div {
        order: 2;
      }
    }
  }
  .userSettingsTitle {
    margin-bottom: 20px;
    font-size: 18px;
    color: ${palette('secondary', 0)};
  }

  .dynamicInputs {
    padding: 20px;
    margin: 5px 0 10px;
    border: 1px dashed #ddd;
    background: #f5f5f5;

    & > div {
      & > .ant-row.ant-form-item {
        margin-bottom: 0px;
      }
    }

    & .removeInputBtn {
      padding-left: 5px;
      font-size: 20px;
      color: ${palette('error', 0)};
      margin-top: 6px;
    }
  }

  .profilePic-acc {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;

    .profile-acc-upload {
      display: flex;
      flex-direction: column;
      gap: 11px;
      .profile-acc-upload-title {
        font-family: Inter;
        font-size: 10.5px;
        font-weight: 600;
        line-height: 12.71px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #555658;
      }

      .profile-acc-upload-warn {
        font-family: Inter;
        font-size: 9.2px;
        font-weight: 600;
        line-height: 11.13px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #939496;
      }
      .profile-acc-upload-btns {
        display: flex;
        align-items: center;
        gap: 10px;

        span {
          .ant-upload-select-text {
            .ant-upload {
              .uploadBtn {
                width: 116px;
                height: 34px;
                padding: 9px 5px;
                gap: 7px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 7px;
                border: 1px solid;
                border-color: #f5f5f5;
                span {
                  font-family: Inter;
                  font-size: 11.5px;
                  font-weight: 600;
                  line-height: 13.92px;
                  text-align: left;
                  text-underline-position: from-font;
                  text-decoration-skip-ink: none;
                  color: #68696b;
                }
              }
            }
          }
        }

        .removeBtn {
          width: 61px;
          height: 34px;
          padding: 9px 5px;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 5px;
          border-radius: 7px;
          border: 1px 0px 0px 0px;
          border-color: #f5f5f5;
          span {
            font-family: Inter;
            font-size: 11.5px;
            font-weight: 600;
            line-height: 13.92px;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #68696b;
          }
        }
      }
    }
  }

  & .invoiceInfo {
    & .ant-descriptions {
      & .ant-descriptions-title {
        margin-bottom: 10px;
        font-weight: 500;
        color: ${palette('secondary', 0)};
      }
    }
  }

  .ant-input-group-addon {
    background-color: #eff1f500 !important;
    border-radius: 8px !important;
    border: transparent !important;
  }

  .formLabelforCountry {
    .flag {
      margin-left: 5px;
    }
    .ant-input-group-addon {
      padding: 0px !important;
      width: 70px;
      & .countryFormItem-cform {
        margin-bottom: 0px;

        & .ant-form-item-row {
          padding: 5px 0px !important;
        }
        & .ant-form-item-control-input {
          min-height: auto;

          & .ant-select-selector {
            height: 31px;
          }
        }

        .ant-select-arrow {
          display: none;
        }

        .ant-select-selection-item {
          padding: 0px !important;
          font-family: Inter;
          font-size: 14px;
          font-weight: 400;
          // line-height: 16.94px;
          border-height: 1px;
          border-right: 1px solid #2a2b2c;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
        }
      }
    }

    input {
      margin-top: 3px;
      padding-left: 0px;
    }
  }

  & .basicDetailForm {
    & .countryFormItem {
      margin-bottom: 0px;

      & .ant-form-item-control-input {
        min-height: auto;

        & .ant-select-selector {
          height: 31px;
        }
      }
    }
  }

  & .attachmentView {
    border: 1px solid #a9abbc;
    border-radius: 15px;
    padding: 5px 15px !important;
    margin: 0px 7px 5px;
    // width: 50%;
  }

  & .deleteIconStyle {
    margin-left: 10px;
    color: #ff0000;
    cursor: pointer;
  }

  & .cancelBtnStyle {
    color: #1172ec;
    font-weight: 600;
    cursor: pointer;
  }

  @media only screen and (max-width: 768px) {
    & .signHint {
      font-size: 12px;
    }
    & .signatureList {
      & .ant-list-items {
        padding: 10px 0 7px;
      }
    }
  }
`;

const Sign = styled.div`
  border-left: 2px solid ${palette('primary', 0)};
  border-radius: 8px;
  display: block;
  // height: 55px;
  margin: 4px 0;
  overflow: visible;
  position: relative;

  &::before {
    border-top-left-radius: 8px;
    border-top-width: 2px;
    top: -2px;
  }

  &::after {
    border-bottom-left-radius: 8px;
    border-bottom-width: 2px;
    bottom: -2px;
  }

  &::before,
  &::after {
    border: 0px solid ${palette('primary', 0)};
    content: '';
    display: block;
    height: 38px;
    left: -1px;
    position: absolute;
    width: 20px;
  }

  & .title-top {
    font-weight: 700;
    top: -8px;
  }
  & .title-bottom {
    bottom: -7px;
  }
  & .title-bottom,
  & .title-top {
    font-size: 10px;
    left: 21px;
    position: absolute;
  }

  & .extraInfo {
    margin-left: 15px;

    & > span {
      display: block;
      font-size: 12px;
    }
  }

  & .sign-container {
    display: flex;
    align-items: center;
    height: 55px;
    white-space: nowrap;
    width: 100%;
    padding: 10px;
    justify-content: space-between;
    padding-left: 15px;

    & > img {
      max-width: 100%;
      max-height: 100%;
    }

    & .sign {
      font-size: 22px;
      line-height: 22px;
      margin-top: 8px;

      &.default {
        font-size: 20px;
        margin-left: 2px;
      }
    }

    @media (max-width: 768px) {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    @media (max-width: 580px) {
      padding-left: 5px;
      min-width: 100px;

      & .sign {
        font-size: 18px;

        &.default {
          font-size: 15px;
        }
      }
    }
  }
  @media (max-width: 580px) {
    & .title-top {
      left: 10px;
    }
  }
`;

const DrawerWrapper = styled.div`
  .ant-drawer-body {
    // padding-bottom: 140px;
  }
`;

const Box = WithDirection(BoxWrapper);
export {
  BottomViewWrapper,
  FormWrapper,
  CardWrapper,
  Box,
  DrawerWrapper,
  Sign,
};

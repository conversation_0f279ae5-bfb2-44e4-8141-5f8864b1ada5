import { createGlobalStyle } from 'styled-components';
import { palette, font } from 'styled-theme';

const GlobalStyles = createGlobalStyle`
  * {
    outline: 0 !important;
  }
  .break-word{
    word-break: break-all
  }
  .viewChatLink {
    fontSize: 14px;
    color: ${palette('primary', 0)} !important;
  }

  .isCustomDropDown {
    padding: 8px 0px;
    cursor: pointer;
    span {
      font-family: Inter;
      font-size: 14px;
      font-weight: 400;
      line-height: 16.94px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #96aac1;
    }
  }

  .campaignTable {
   .ant-pagination {   

   .ant-pagination-item-link {
     border: none !important;
     color: #A0AEC0 !important;
     background: transparent !important;
   }
     .ant-pagination-item-active {
        width: 40px;
        height: 40px;
        padding: 8px;
        gap: 10px;
        border-radius: 12px;
        background: #6C5DD321;
        a {
          font-family: Inter;
          font-size: 14px;
          font-weight: 800;
          line-height: 22.4px;
          letter-spacing: 0.20000000298023224px;
          text-align: center;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #6C5DD3 !important;
        }
     }
   }
  }
    
  .custom-submenu .ant-menu-submenu-title {
     margin-top: -20px;
     font-size: 14px;
     width: 100%;
     font-family: 'Inter';
     font-weight: 700;
     line-height: 24px;
     color: #252B42;
  }

  .ant-table-content {
    border: 1px solid #D4D4D4;
    border-radius: 8px 8px 0px 0px;
  }
  .custom-submenu .ant-menu-submenu-title:hover {
    color: #252B42 !important;
  }

  .primaryColor {
    color: ${palette('primary', 0)};
  }

  .linkSecondary {
    color: ${palette('text', 3)};
  }

  .ant-btn.ant-btn-link.linkBtn {
    color: ${palette('primary', 0)};
    padding: 0;
    border: none;
  }

  .ant-menu-sub.ant-menu-inline {
    background-color: transparent !important;
  }
  
  .link {
    color: ${palette('primary', 0)};
    cursor: pointer;
  }
  .loadingContainer {
    position: absolute;
    width: 100%;
    height: 100%;
    bottom: 0;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .smallIconImg { 
    width: 18px;
    height: 18px;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .logo {
    font-size: 32px;
    font-weight: 700;
    color: #fff;
  }

  .upperCase {
    text-transform: uppercase;
  }

  .has-success.has-feedback {
    .ant-select {
      .ant-select-selection {
        .ant-select-selection__rendered {
          .ant-select-selection__placeholder {
            display: none !important;
          }
        }
      }
    }
  }
  /*-----------------------------------------------*/ 
  // style for custom menu
  /*-----------------------------------------------*/
  .customMenu {
    & > li.ant-dropdown-menu-item {
      padding-top: 8px;
      padding-bottom: 8px;

      & .anticon {
        font-size: 14px;
      }
    }
  }


  /*-----------------------------------------------*/ 
  // style for Auth0 Customization
  /*-----------------------------------------------*/

  .auth0-lock-container {
    & .auth0-lock.auth0-lock {
      z-index: 1;
    }
  
    & .auth0-lock-badge-bottom {
      display: none;
    }
  }


  /*-----------------------------------------------*/ 
  // style for project category menu [ScrumBoard]
  /*-----------------------------------------------*/
  .project-category {
    .ant-select-dropdown-menu {
      .ant-select-dropdown-menu-item {
        padding: 8px 12px;
        color: #000000;
        font-family: 'Roboto';
        font-weight: 400;
      }
    }
  }

  /*-----------------------------------------------*/ 
  // style for project menu [ScrumBoard]
  /*-----------------------------------------------*/
  .ant-dropdown {
    &.project-menu {
      width: 280px;
      top: 133px !important;
      
      .ant-dropdown-menu {
        padding: 0;
        overflow: hidden;

        .ant-dropdown-menu-item {
          min-height: 54px;
          line-height: auto;
          display: flex;
          align-items: center;
          padding: 10px 20px;

          &:first-child {
            padding: 0;
            border-bottom: 1px solid #f4f6fd;

            &:hover,
            &:focus {
              background-color: #ffffff;
            }
          }

          &:hover,
          &:focus {
            background-color: #F3F5FD;
          }

          &:last-child {
            background-color: #E6EAF8;
          }
        }
      }
    }
  }

  /*-----------------------------------------------*/ 
  // style for popover [ScrumBoard]
  /*-----------------------------------------------*/
  .ant-popover {
    .ant-checkbox-group {
      display: flex;
      flex-direction: column;
      .ant-checkbox-group-item {
        margin: 5px 0;
        span {
          font-size: 14px;
          color: #788195;
          text-transform: capitalize;
        }
      }
    }
  }

  /*-----------------------------------------------*/ 
  // style for modal [ScrumBoard]
  /*-----------------------------------------------*/
  .ant-modal-wrap {
    .ant-modal {
      .ant-modal-content {
        .ant-modal-body {
          .render-form-wrapper {
            padding: 10px;
            h2 {
              margin: 0;
            }
            form {
              padding: 15px 0 3px;
              .field-container {
                margin-bottom: 26px;
              }
            }
          }
        }
      }
    }
  }

/*-----------------------------------------------*/ 
// style for React Multiple Date Selector
/*-----------------------------------------------*/

.DayPicker:not(.DayPicker--interactionDisabled) .DayPicker-Day:not(.DayPicker-Day--disabled):not(.DayPicker-Day--selected):not(.DayPicker-Day--outside):hover {
  background-color: unset;

  & div.day {
    background-color: #f0f0f0;
    opacity: 0.8;
  }
}

.DayPicker {
  line-height: 1.2;
  .DayPicker-WeekdaysRow {
    border-bottom: 1px solid #f0f0f0;
  }

  & .DayPicker-Day {
    border-radius: 2px;
    padding: 0.4em;
    position: relative;

    & .day {
      padding: 0.4em;
      border-radius: 50%;

      &.chosen {
        font-weight: bold;
  
        & span.dot {
          position: absolute;
          padding: 2.5px;
          border-radius: 50%;
          background: ${palette('primary', 0)};
          bottom: -2px;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }

  & .DayPicker-Day--today:not(.DayPicker-Day--disabled) {
    color: ${palette('primary', 0)};
  }

  & .DayPicker-Day--selected:not(.DayPicker-Day--disabled):not(.DayPicker-Day--outside):hover {
    background-color: unset;
    & div.day {
      background-color: ${palette('primary', 0)};
      opacity: 0.8;
    }
  }
  
  & .DayPicker-Day--selected:not(.DayPicker-Day--disabled):not(.DayPicker-Day--outside) {
    background-color: unset;

    & .day {
      color: #eee;
      background-color: ${palette('primary', 0)};
    }
  }
}



/*-----------------------------------------------*/ 
  // Checkbox Style
/*-----------------------------------------------*/

.inlineLabel {
  & > span {
    display: inline-block;
  }
}



/*-----------------------------------------------*/ 
  // style form previous GlobalStyles
  /*-----------------------------------------------*/

.ant-table {
  .ant-table-thead > tr.ant-table-row-hover:not(.ant-table-expanded-row) > td,
  .ant-table-tbody > tr.ant-table-row-hover:not(.ant-table-expanded-row) > td,
  .ant-table-thead > tr:hover:not(.ant-table-expanded-row) > td,
  .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    background: #f8f8f8;
    @media only screen and (max-width: 768px) {
      background: transparent;
    }
  }
}

font-family: ${font('primary', 0)};

h1,
h2,
h3,
h4,
h5,
h6,
a,
p,
li,
input,
textarea,
span,
div,
img,
strong,
footer,
svg {
  &::selection {
    background: ${palette('primary', 0)};
    color: #fff;
  }
}

.ant-row > div {
  padding: 0;
  & .ant-form-item-control > div > div > .usermanualstyle > div {
    width: 100% !important;
    height: 120px;
  }
}

.isoLeftRightComponent {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  width: 100%;
  &.remForgotPssd {
    margin-top: 10px !important;
    justify-content: flex-end;
  }
}

.isoRightComponent {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  width: 100%;
}

.flexCenter {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

/********** Add Your Global CSS Here **********/

body {
  -webkit-overflow-scrolling: touch;
}

html h1,
html h2,
html h3,
html h4,
html h5,
html h6,
html a,
html p,
html li,
input,
textarea,
span,
div,
html,
body,
html a {
  margin-bottom: 0;
  font-family: 'Rubik', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.004);
}

html ul {
  -webkit-padding-start: 0px;
  list-style: none;
  margin-bottom: 0;
}

.scrollbar-track-y,
.scrollbar-thumb-y {
  width: 5px !important;
}

.scrollbar-track-x,
.scrollbar-thumb-x {
  height: 5px !important;
}

.scrollbar-thumb {
  border-radius: 0 !important;
}

.scrollbar-track {
  background: rgba(222, 222, 222, 0.15) !important;
}

.scrollbar-thumb {
  border-radius: 0 !important;
  background: rgba(0, 0, 0, 0.5) !important;
}

.ant-popover-placement-bottom > .ant-popover-content > .ant-popover-arrow:after,
.ant-popover-placement-bottomLeft
  > .ant-popover-content
  > .ant-popover-arrow:after,
.ant-popover-placement-bottomRight
  > .ant-popover-content
  > .ant-popover-arrow:after,
.ant-popover-placement-top > .ant-popover-content > .ant-popover-arrow:after,
.ant-popover-placement-topLeft
  > .ant-popover-content
  > .ant-popover-arrow:after,
.ant-popover-placement-topRight
  > .ant-popover-content
  > .ant-popover-arrow:after {
  left: 0;
  margin-left: -4px;
}

/* Instagram Modal */

.ant-modal-wrap.instagram-modal .ant-modal {
  max-width: 935px;
  width: 100% !important;
}

@media only screen and (max-width: 991px) {
  .ant-modal-wrap.instagram-modal .ant-modal {
    padding: 0 60px;
  }
}

@media only screen and (max-width: 767px) {
  .ant-modal-wrap.instagram-modal .ant-modal {
    max-width: 580px;
  }
}

.ant-modal-wrap.instagram-modal .ant-modal-content {
  border-radius: 0;
}

.ant-modal-wrap.instagram-modal .ant-modal-content button.ant-modal-close {
  position: fixed;
  color: #fff;
}

.ant-modal-wrap.instagram-modal .ant-modal-content button.ant-modal-close i {
  font-size: 24px;
}

.ant-modal-wrap.instagram-modal .ant-modal-content .ant-modal-body {
  padding: 0;
}

/********** Add Your Global RTL CSS Here **********/

/* Popover */

html[dir='rtl'] .ant-popover {
  text-align: right;
}

/* Ecommerce Card */

html[dir='rtl'] .isoCardInfoForm .ant-input {
  text-align: right;
}

/* Modal */

html[dir='rtl'] .has-success.has-feedback:after,
html[dir='rtl'] .has-warning.has-feedback:after,
html[dir='rtl'] .has-error.has-feedback:after,
html[dir='rtl'] .is-validating.has-feedback:after {
  left: 0;
  right: auto;
}

html[dir='rtl'] .ant-modal-close {
  right: inherit;
  left: 0;
}

html[dir='rtl'] .ant-modal-footer {
  text-align: left;
}

html[dir='rtl'] .ant-modal-footer button + button {
  margin-left: 0;
  margin-right: 8px;
}

html[dir='rtl'] .ant-confirm-body .ant-confirm-content {
  margin-right: 42px;
}

html[dir='rtl'] .ant-btn > .anticon + span,
html[dir='rtl'] .ant-btn > span + .anticon {
  margin-right: 0.5em;
}

html[dir='rtl'] .ant-btn-loading span {
  margin-left: 0;
  margin-right: 0.5em;
}

html[dir='rtl']
  .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline) {
  padding-left: 25px;
  padding-right: 29px;
}

html[dir='rtl']
  .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline)
  .anticon {
  margin-right: -14px;
  margin-left: 0;
}

/* Confirm */

html[dir='rtl'] .ant-modal.ant-confirm .ant-confirm-body > .anticon {
  margin-left: 16px;
  margin-right: 0;
  float: right;
}

html[dir='rtl'] .ant-modal.ant-confirm .ant-confirm-btns {
  float: left;
}

html[dir='rtl'] .ant-modal.ant-confirm .ant-confirm-btns button + button {
  margin-right: 10px;
  margin-left: 0;
}

/* Message */

html[dir='rtl'] .ant-message .anticon {
  margin-left: 8px;
  margin-right: 0;
}

/* Pop Confirm */

html[dir='rtl'] .ant-popover-message-title {
  padding-right: 20px;
  padding-left: 0;
}

html[dir='rtl'] .ant-popover-buttons {
  text-align: left;
}

/* Notification */

html[dir='rtl']
  .ant-notification-notice-closable
  .ant-notification-notice-message {
  padding-left: 24px;
  padding-right: 0;
}

html[dir='rtl']
  .ant-notification-notice-with-icon
  .ant-notification-notice-message,
html[dir='rtl']
  .ant-notification-notice-with-icon
  .ant-notification-notice-description {
  margin-right: 48px;
}

html[dir='rtl'] .ant-notification-notice-close {
  right: auto;
  left: 16px;
}

html[dir='rtl'] .ant-notification-notice-with-icon {
  left: 0;
}

/* Dropzone */

html[dir='rtl'] .dz-hidden-input {
  display: none;
}

/* New RTL styles  */

html[dir='rtl'] {
  // Set default text alignment for RTL
  & .ant-form-item-label {
    text-align: unset !important;
  }

  & .ant-drawer-close {
    right: auto;
    left: 0;
  }
 
}


/* Custom global styles G */

/* GENERAL */
.mt5 {
  margin-top: 5px;
}
.mb5 {
  margin-bottom: 5px;
}
.mt10 {
  margin-top: 10px;
}
.mt15 {
  margin-top: 15px;
}
.mt25 {
  margin-top: 25px;
}
.mb10 {
  margin-bottom: 10px;
}
.mb20 {
  margin-bottom: 20px;
}
.ml10 {
  margin-left: 10px;
}
.ml5 {
  margin-left: 5px;
}
.mr5 {
  margin-right: 5px;
}
.pl5 {
  padding-left: 5px;
}
.mh100 {
  min-height: 100%;
}
.justCenter {
  justify-content: center;
}
.hr {
  height: 1px;
  margin: 10px 0px;
  background-color: #e9e9e9;
}

.pointer {
  cursor: pointer !important;
}

.w100 {
  width: 100%;
}

.block {
  display: block;
}

.wSpaceWrap {
  white-space: break-spaces;
}

.flex1 {
  flex: 1;
}

.avgTickets {
  flex: 1;
  text-align: center;
  align-items: center;
  gap: 24px;
  padding: 0px;
}

.slick-slide {
  border-radius: 20px;
}

.slick-list {
  border-radius: 20px;
}

.slick-slider {
  border: 1px solid #ebebeb;
  border-radius: 20px;
}

.slick-arrow {
  display: none !important;
}

.border-none {
  border: none !important;
}
.borders{
  border: none !important;
  height: 100%;
  border-radius: 25px;
}
.bg-gradient {
  background: linear-gradient(138deg, rgb(20, 135, 255) 12%, rgb(41, 239, 196) 90%);
}

.borderGradiant {
  border: none !important;
  height: 100%;
  border-radius: 25px;
  background: linear-gradient(138deg, rgb(20, 135, 255) 12%, rgb(41, 239, 196) 90%);
  justify-content: center;
  align-items: center;
  display: flex;
  flex-direction: column
}
.upgradeLink {
  & button {
    padding: 0 10px !important;
    font-weight: bold !important;
    text-transform: uppercase;
  }
}

.loaderMain { 
  min-height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.rowSpaceBetween { 
  display: flex;
  justify-content: space-between;
}
.rowCenter { 
  display: flex;
  justify-content: center;
}
.rowAlignCenter { 
  display: flex;
  justify-content: center;
  align-items: center;
}
.textCenter { 
  text-align: center;
}
.fw550 {
  font-weight: 550;
}
.fs12 {
  font-size: 12px;
}
.avatar-contain {
  .ant-avatar > img {
    object-fit: contain;
  }
}
.otpInputContainer {
  padding: 30px 0px;
  background: #f0f0f0;
  margin: 10px 0;
}
.otpInputStyle {
  width: 50px !important;
  text-align: center !important;
  height: 50px;
  margin: 0px 10px;
  font-size: 25px;
  border-radius: 5px;
  border: 0px solid rgba(0, 0, 0, 0.3);
  background: rgb(255, 255, 255);
  box-shadow: rgba(226, 219, 219, 0.53) -3px 2px 6px 3px;
}
td.ant-table-cell.actionCell {
  padding-left: 10px;
}

.uploadContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  & .uploadBox {
    padding: 20px;
    border: 1px dashed #ccc;
    min-height: 90px;
    width: 90px;
    border-radius: 5px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    position: relative;
    align-items: center;
    cursor: pointer;
    

    & .anticon.anticon-file {
      font-size: 36px;
      color: ${palette('primary', 0)};
    }

    & .anticon.anticon-close-circle {
      font-size: 22px;
      color: ${palette('danger', 0)};
      position: absolute;
      top: -10px;
      cursor: pointer;
      right: -10px;
    }
  }

  & p.fileName {
    margin-top: 15px;
    padding: 0 5px;
    line-height: 1.2;
    color: ${palette('text', 0)};
    word-break: break-word;
  }

}

.filterMain {

  &:not(.reports) > div {
    margin-bottom: 10px;
  }

  & .resetFlt {
    margin-bottom: 0px !important;
  }

  .filterWrapper {
    display: flex;
    flex-wrap: wrap;
    flex: 1;
    align-items: center;

    & .ant-breadcrumb {
      padding-left: 10px;
      margin-bottom: 0px;

      & .ant-breadcrumb-link {
        cursor: pointer
        font-size: 15px;

        & .anticon {
          font-size: 18px;
        }
      }
    }

    & > div {
      margin-right: 10px;
      margin-bottom: 10px;

      & .servicesMain {
        display: flex;

        & > div {
          margin: 0px 5px;
        }
      }

      & .ant-select {
        min-width: 220px;
        width: 100%;
      }
    }
  }

  .btnsMain {
    display: flex;
    align-items: center;

    & button {
      margin-left: 10px;
    }

    & .fltBtn {
      padding: 0px;
      min-width: 50px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }
  }

  /* Responsive Styles */
  @media (max-width: 1220px) {
    flex-direction: column-reverse !important;

    .filterWrapper {
      margin-top: 15px;

      & > div {
        margin-bottom: 10px;
      }
    }

    .ant-btn-icon-only {
      padding: 0px !important;
      min-width: 45px;
    }
  
    .btnsMain { 
      width: 100%;
      flex-direction: row-reverse;

    }
  }

  @media (max-width: 768px) {
    .filterWrapper {
      flex-direction: column;
      flex: unset;
      & > div {
        margin-right: 0px;
      }
    }
  }
}

/* Overwrite TinyMCE Text Editor CSS */

& .tox-tinymce  {
  & .tox-statusbar__branding {
    display: none !important;
  }
}


/* Style for Editor preview */
& .editorView {

  & h1, & h2, & h3, & h4, & h5, & h6 {
    display: block;
    font-weight: bold;
  }

  & h1 {
    font-size: 2em;
  }

  & h2 {
    font-size: 1.5em;
  }
  & h3 {
    font-size: 1.17em;
  }
  & h4 {
    font-size: 1em;
  }
  & h5 {
    font-size: .83em;
  }
  & h6 {
    font-size: .67em;
  }
}

/* SCROLLBAR */
.track-horizontal {
  display: none;
}

/* MODAL */
.ant-modal {
  & .ant-modal-content {
    & .ant-modal-header {
      & .ant-modal-title {
        &  > span {
          padding-right: 5px;
        }
      }
    }
  }
}

/* INPUT */

// .ant-input:focus, .ant-input:hover, .ant-input-number:hover , .ant-input-number:focus, .ant-input-number-focused {
//   border-color: ${palette('primary', 0)} !important;
//   box-shadow: 0 0 0 2px ${palette('primary', 14)} !important;
// }

.ant-input-affix-wrapper:hover .ant-input:not(.ant-input-disabled) {
  border-color:  ${palette('primary', 0)} !important;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance:textfield;
}

/* FORM ITEM */
.ant-form {
  & .ant-row.ant-form-item {
    margin-bottom: 10px;
  }
  & .ant-form-item-label {
    text-align: unset !important;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 14px;
  }
  & .ant-row.form-item-country {
    margin-bottom: 0px;
    & .ant-form-item-control-input {
      min-height: unset;
    }
  }
}


/* MOBILE APP EDITOR's STYLES */

.mobileAppBody {

  &.mobileApp {
    user-select: none !important;
  }

  // Modal styles for Responsive
  @media only screen and (max-width: 767px) {
    & .ant-modal-root {
      & .ant-modal-wrap {
        overflow: hidden;
        z-index: 111111;
      }
      & .ant-modal:not(.ant-modal-confirm) {
        margin: 0;
        height: 100%;
        max-width: 100%;
        top: 0 !important;
        padding-bottom: 0px;
  
        & .ant-modal-content {
          height: 100%;
  
          & .ant-modal-body {
            padding-bottom: 55px;
            overflow: auto;
            height: 92%;
          }
  
          & .ant-modal-footer {
            position: fixed;
            bottom: 0;
            width: 100%;
            background-color: #fff;
            border-top: 1px solid #eee;
          }
        }
      }
    }
  }
}


/* DRAWER  */

.commentDrawer {
  & .ant-drawer-content {
    overflow: hidden;
   
  }
}

& .ant-drawer-content {
  border-radius: 20px !important;
  border: 1px solid #DDE2E5;
}

& .ant-drawer-mask {
  background-color: transparent !important;
}

& .ant-drawer-header-title {
  width: 100%;
  flex-direction: row-reverse;

  & .ant-drawer-title {
    span {      
      font-family: Inter;
      font-size: 16px;
      font-weight: 400;
      line-height: 19.36px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #2B2F32;  
    }
  }
  & .ant-drawer-close {

    border: 1.5px solid #007AFF !important;
    color: #007AFF !important;
    border-radius: 50%;
    height: 20px;
    width: 20px;
    margin-right: 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
  }
}

.ant-drawer-body {
  overflow-y: auto !important;
}
.ant-drawer-content-wrapper {
  max-width: 100%;
   border-radius: 20px !important;
  border: 1px solid #DDE2E5;


  & .ant-drawer-body {

    & .bottomBtnWrapper {
      z-index: 999;
      border-radius: 0px 0px 20px 20px !important;
      & > div {
        margin-bottom: 0px;
      }
    }
  }
}

.ant-menu.langMenu {
  border-right: none !important;
  border-left: none !important;
  & .ant-menu-item {
    text-align: center !important;
    min-width: 80px;
  }
}

/* POPOVER CSS */
.ant-popover-placement-bottomLeft {
  & .ant-popover-arrow {
    @media (max-width: 425px) {
      // display: none;
    }
  }
}

@media (max-width: 425px) {
  td.ant-descriptions-item-content{
    word-break: break-all
  }
}

.confirmDeleteModal {

width: 740px;
height: 228px;

.ant-modal-content {
    border-radius: 18px;
    box-shadow: 0px 7px 4px 0px #CBD2D4D4;

    & .ant-modal-body {
      padding: 24px;
      border-radius: 18px;
      border: 3px solid #04B8FF;
    }
}

}

`;

export default GlobalStyles;

import siteConfig from './site.config';

export default {
  domain: process.env.REACT_APP_AUTH0_DOMAIN,
  clientID: process.env.REACT_APP_AUTH0_CLIENT_ID,

  redirectUrl: `${siteConfig.domainUrl}/auth0loginCallback`,
  responseType: 'token id_token',
  options: {
    rememberLastLogin: true,
    language: 'en',
    closable: false,
    initialScreen:
      window &&
      window.location &&
      window.location.pathname &&
      window.location.pathname.indexOf('signup') > -1
        ? 'signUp'
        : 'signIn',
    auth: {
      autoParseHash: true,
      responseType: 'token id_token',
      redirect: false,
      redirectUrl: `${siteConfig.domainUrl}/auth0loginCallback`,
    },
    languageDictionary: {
      title: '',
      signUpTitle: '',
    },
    theme: {
      labeledSubmitButton: true,
      logo: `${siteConfig.domainUrl}/logo.png`,
      primaryColor: '#1172EC',
      authButtons: {
        connectionName: {
          primaryColor: '#1172EC',
          foregroundColor: '#000000',
        },
      },
    },
  },
};

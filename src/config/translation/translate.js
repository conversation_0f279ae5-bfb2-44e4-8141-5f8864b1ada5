import AppLocale from '@chill/config/translation';
import { store } from '@chill/redux/store';
import { isObject, isEmpty } from 'lodash';

/**
 * Function for replace the string with Object's matched key's value
 * @param {string} str
 * @param {object} mapObj
 */
function replaceAll(str, mapObj) {
  return str.replace(/\{([^)]+)?\}/g, (_, $2) => {
    return mapObj[$2];
  });
}

/**
 * Function for translate string
 * @param {string} str - string for translation
 * @param {object} options - options for replacement
 */
const trans = (str, options) => {
  const state = store ? store.getState() : {};
  const language = state ? state.LanguageSwitcher.language : '';

  // Current language code
  const langCode = language || 'en';

  const currentAppLocale = AppLocale[langCode] || {};
  const allMsgs = currentAppLocale.messages || {};

  // Translated string
  let translatedStr = allMsgs[str] || str;

  if (isObject(options) && !isEmpty(options)) {
    translatedStr = replaceAll(translatedStr, options);
  }

  return translatedStr;
};

export default trans;

import { addLocaleData } from 'react-intl';
import Enlang from './entries/en_US';
import deLang from './entries/de_DE';
import Frlang from './entries/fr_FR';
import Eslang from './entries/es_ES';
import Ptlang from './entries/pt_PT';
import RuLang from './entries/ru_RU';
import ZhLang from './entries/zh_ZH';

// import locale for moment
import 'moment/locale/es';
import 'moment/locale/fr';
import 'moment/locale/de';
import 'moment/locale/en-gb';
import 'moment/locale/pt';
import 'moment/locale/ru';
import 'moment/locale/zh-cn';

const AppLocale = {
  en: Enlang,
  de: deLang,
  fr: Frlang,
  es: Eslang,
  pt: Ptlang,
  ru: RuLang,
  zh: ZhLang,
};

addLocaleData(AppLocale.en.data);
addLocaleData(AppLocale.de.data);
addLocaleData(AppLocale.fr.data);
addLocaleData(AppLocale.es.data);
addLocaleData(AppLocale.pt.data);
addLocaleData(AppLocale.ru.data);
addLocaleData(AppLocale.zh.data);

export default AppLocale;

{"sidebar.service": "Customer Care", "sidebar.support": "Support", "sidebar.analytics": "Analytics", "sidebar.customers": "Customers", "sidebar.customer": "Customer", "sidebar.marketing": "Marketing", "sidebar.ecommerce": "E-Commerce", "sidebar.products": "Products", "sidebar.productManage": "Product Management", "sidebar.product.category": "Category Management", "sidebar.manageDept": "Manage Departments", "sidebar.notification": "Notifications", "sidebar.Languages": "Languages", "sidebar.brands": "Brands", "sidebar.integrations": "Integrations", "sidebar.accounting": "Accounting", "sidebar.brandManage": "Brand Management", "sidebar.device": "Device Management", "sidebar.goalcompletion": "Goal Completion", "sidebar.faq": "FAQ's", "sidebar.strollers": "Strollers", "sidebar.appManagement": "App Management", "sidebar.campaignMarketing": "Campaign Marketing", "sidebar.AIMarketing": "AI Marketing", "sidebar.marketplace": "3rd party market place", "sidebar.feedcampaigns": "Feed campaigns", "sidebar.groupManagement": "Group Management", "sidebar.trailerManagement": "Trailer Management", "sidebar.gpxManagement": "GPX Management", "sidebar.maintenance": "Maintenance", "sidebar.highlights": "Highlights", "page404.title": "404", "page404.subTitle": "Looks like you got lost", "page404.description": "The page youre looking for doesnt exist or has been moved.", "page404.backButton": "BACK HOME", "page.signUpTermsConditions": "I agree with", "page.termsCondition": " Terms and condtions", "page.signUpAlreadyAccount": "Already have an account? Sign in.", "page.signUpButton": "Sign Up", "page.signInCreateAccount": "Dont have account? Sign up", "page.backtoSignin": "Back to Signin", "page.forgotPssd": "Forgot Password?", "page.login": "Log In", "page.forgotMsg": "Enter the email address associated with your account", "page.loginAuth0": "Log in with Auth0", "notification.viewAll": "View All Notifications", "notification.noData": "No Unread Notifications", "notification.noti": "Notification", "notification.view.btn": "View", "notification.attachment": "Attachment", "account.newPass": "New Password", "account.confirmPssd": "Confirm Password", "account.delete": "Delete Account", "account.edit": "Edit Account", "verification.idcard": "ID Card/Passport", "verification.address": "Proof of Address", "verification.liveness": "Liveness Check", "verification.mobile": "Mobile Number", "verification.mobileHint": "You didn't fill your mobile number yet, Please fill your mobile number in {link} then verify your number here.", "verification.accSettings": "account settings", "verification.email": "Email verification", "verification.fingerPrint": "Fingerprint verification", "verification.completeYour": "Complete your verification", "verification.completeHint": "Your {per}% verification is completed. Please verify {remains} from below tabs to complete your 100% verification.", "verification.fingerprintHint": "To complete Fingerprint verification (25%), you have to turn on \"Fingerprint\" from your {link}.", "workflow.deleteDocConfirm": "This file is a part of the workflow and many other users also using it, Are you still want to move in Trash Bin?", "dashboard.totalDoc": "Total Documents", "dashboard.totalFolders": "Total Folders", "dashboard.totalTemp": "Total Templates", "dashboard.totalContacts": "Total Contacts", "dashboard.totalPendingSignDoc": "Pending Sign Documents", "dashboard.totalSignedDoc": "Signed Documents", "dashboard.totalDeclinedDoc": "Declined Documents", "dashboard.totalBcSignDoc": "Blockchain Signed Documents", "dashboard.sharedFiles": "Shared Files", "dashboard.fileStates": "Files Statistics", "dashboard.docStates": "Documents Statistics", "dashboard.activeUser": "Active Users", "dashboard.reports": "Reports", "dashboard.totalusers": "Users", "dashboard.weeklySession": "Weekly Session", "dashboard.totalRevenue": "Total Revenue", "dashboard.custOverview": "CUSTOMER OVERVIEW", "dashboard.lastWeek": "Last week", "dashboard.churnRate": "Churn Rate", "dashboard.sessionDuration": "Session Duration", "dashboard.tickets": "TICKETS", "dashboard.viewAllTickets": "View All Tickets", "dashboard.marketingCampaigns": "Marketing Campaigns", "dashboard.userByRegion": "USERS BY REGION", "dashboard.userByTime": "USERS BY TIME", "dashboard.traficByLocation": "Traffic by Location", "dashboard.sessionsByLocation": "Sessions by Location", "dashboard.productType": "SESSIONS/PRODUCT TYPE", "dashboard.User": "User", "dashboard.environment": "Environment", "ecom.totalSales": "Total Sales", "ecom.topSellingProducts": "Top Selling Products", "ecom.mostViewedProducts": "Most Viewed Products", "ecom.topMarketingCamp": "Top Marketing Campaigns", "ecom.totalProductSold": "Total Products Sold", "eco.noofSold": "No. of Products Sold", "ecom.noProducts": "No Products", "ecom.noCamp": "No Campaigns", "accounting.totalUnitSales": "Total unit sales", "accounting.totalSalesValue": "Total sales value", "accounting.averageConRate": "Average conversion rate", "accounting.allbrandCommission": "All brand commission", "accounting.brandCommission": "Brand commission", "accounting.commissionRate%": "Commission Rate%", "accounting.commission": "Commission", "accounting.editCommission": "Edit commission rate", "accounting.downloadReport": "Download report", "accounting.transactions": "Transactions", "accounting.transactionNumber": "Transaction number", "accounting.transactionDate": "Transaction Date", "accounting.unitQTY": "Unit QTY", "accounting.salaesValue": "Sales Value", "customer.newSegment": "New Segment", "customer.newCategory": "New Category", "customer.no": "No", "customer.name": "Customer Name", "customers": "Customers", "customer.shareRate": "Share Rate", "customer.clickRate": "Click Rate", "customer.emailId": "Email <PERSON>d", "customer.language": "Language", "customer.noProduct": "No. of Products", "customer.status": "Status", "customer.action": "Action", "customer.segmentCriteria": "Users in this segment must meet the following criiteria", "customer.addSegment": "Add Segment", "customer.addCategory": "Add Category", "customer.viewUserProfile": "View user profile", "customer.userprofile": "USER PROFILE :", "customer.noOfTrips": "Total number of trips", "customer.connectedTime": "Total connected time", "customer.filteringTime": "Filtering time", "customer.AveAQS": "Average Air Quality", "customer.customerName": "Customer Name", "customer.email": "Customer <PERSON><PERSON>", "customer.connectedOn": "Connected on", "customer.lastDataUpload": "Last data upload", "customer.ssid": "SSID", "customer.firmwareVersion": "Firmware version", "customer.productname": "Product name", "customer.deviceDetails": "Device details", "customer.avLengthOfTrips": "Ave length of trips", "customer.appOpenTime": "App open time", "customer.totalUseTime": "Total use time", "customer.totalFiltering": "Total filtering", "customer.peakAQS": "Peak AQS", "customer.peakFilteredAQS": "Peak filtered AQS", "customer.canopyPositionUpdates": "Canopy position updates", "customer.canopyPosition": "Canopy position", "customer.airQualityIncrease": "Air quality increase", "customer.open": "Open", "customer.halfOpen": "Half open", "customer.closed": "Closed", "customer.raincover": "Raincover", "customer.highestTemp": "Hightest Temp", "customer.lowestTemp": "Lowest Temp", "customer.avgTemp": "Average Temp", "customer.totalNotification": "Total notification", "customer.airQAlerts": "Air quality alerts", "customer.tempAlerts": "Temp alerts", "customer.humidityAlerts": "Humidity alerts", "customer.notificationResRate": "NOTIFICATION RESPONSE RATE", "customer.notiTriggrd": "NOTIFICATIONS TRIGGERED", "customer.home": "Home", "customer.dashborad": "Dash", "customer.timeline": "Timeline", "customer.settings": "Settings", "customer.back": "Background", "customer.active": "Active", "customer.activeBg": "ACTIVE / BACKGROUND", "customer.pgView": "PAGE VIEWS", "customer.strollerType": "STROLLER TYPE", "customer.fox": "Fox", "customer.dragonfly": "Dragonfly", "customer.donkey": "<PERSON><PERSON>", "customer.bee6": "Bee 6", "customer.userInsights": "User Insights", "customer.childHeight": "Child Height", "customer.avgChildAgeAtDropOff": "Ave Child age at drop off", "customer.avgChildAgeAtRegi": "Ave Child age at registration", "customer.avgLengthActiveUse": "Ave length of active use", "customer.avgChildAge": "Ave Child age", "customer.childAgeRange": "Child age range", "customer.noOfChildProfiles": "NO OF CHILD PROFILES", "customer.cleanAirAna": "CLEAN AIR ANALYTICS", "customer.tripTimeOfDay": "TRIP TIME OF DAY", "customer.usage": "Usage", "marketing.createNew": "Create New", "marketing.sendTo": "SEND TO", "marketing.categories": "CATEGORIES", "marketing.postTitle": "POST TITLE", "marketing.postSubTitle": "POST SUBTITLE", "marketing.message": "MESSAGE", "marketing.customerSegment": "Choose customer segment(s)", "marketing.chooseCategory": "Choose category", "integration.chooseService": "Choose service", "integration.chooseIntegration": "Choose integration type", "integration.chooseBrand": "Choose brand", "marketing.postPreview": "POST PREVIEW", "marketing.launchUrl": "LAUNCH URL", "marketing.appName": "APP NAME", "marketing.icon": "BRAND LOGO", "marketing.button": "BUTTON", "marketing.brandName": "Brand Name", "marketing.sendToTest": "Send to Test Device", "marketing.selectAttachmentsType": "Select Attachment Type", "marketing.AttachmentsType": "Attachment Type", "marketing": "Marketing", "marketing.videolink": "Video link", "marketing.Webpage": "Webpage", "marketing.addButton": "<PERSON>d <PERSON>", "marketing.videoLink": "Video link or HTTP Link", "marketing.attachments": "ATTACHMENTS", "marketing.attachmentsUrl": "ATTACHMENTS URL", "marketing.attachFiles": "Attach Files", "marketing.messageType": "Message Type", "marketing.color": "Text color", "marketing.backColor": "BACKGROUND COLOR", "marketing.buttonTextColor": "Button Text color", "marketing.buttonUrl": "Button URL", "marketing.text1": "TEXT 1", "marketing.textAlign": "Text Alignments", "marketing.chooseMessagerTrigger": "Text Alignments", "marketing.buttonTextAlign": "Button Text Alignments", "marketing.textSize": "Text size", "marketing.ButtonTextSize": "Button Text size", "marketing.triggers": "Triggers", "marketing.messageTriggers": "<PERSON>ose <PERSON> Triggers", "marketing.enterButtonText": "<PERSON><PERSON> But<PERSON> Text", "marketing.campaignName": "Campaign Name", "marketing.sales": "Sales", "marketing.conversionRate": "Conversion rate", "marketing.createdBy": "Created By", "marketing.createdOn": "Created On", "marketing.type": "Type", "marketing.status": "Status", "marketing.clicks": "<PERSON>licks", "marketing.likes": "<PERSON>s", "marketing.failed": "Failed", "marketing.deliveries": "Deliveries", "marketing.action": "Action", "marketing.SEGMENTGROUP": "SEGMENT GROUP", "marketing.totalValue": "Total Value", "marketing.NumbCampaigns": "No. of Campaigns", "marketing.messagesSent": "Messages Sent", "analytics.activeUser": "ACTIVE USERS", "analytics.performanceAnalysis": "PERFORMANCE ANALYSIS", "analytics.operatingSystem": "OPERATING SYSTEM", "analytics.topLinkClicks": "TOP LINK CLICKS", "analytics.goalCOmpletion": "GOAL COMPLETION", "analytics.trafficReport": "TRAFFIC REPORT", "analytics.activity": "ACTIVITY", "analytics.weekly": "Weekly", "analytics.monthly": "Monthly", "analytics.Daily": "Daily (avg)", "analytics.android": "Android", "analytics.ios": "ios", "analytics.goalRelative": "Relative change vs ", "analytics.channel": "Channel", "analytics.session": "Session", "analytics.previous": "Previous", "analytics.change": "Change", "analytics.thisMonth": "This Month", "analytics.thisWeek": "This Week", "analytics.thisYear": "This Year", "analytics.devices": "Devices", "analytics.deviceName": "Device Name", "analytics.userCount": "User Count", "analytics.noOfChildProfiles": "NO OF CHILD PROFILES", "analytics.cleanAirAnalytics": "CLEAN AIR ANALYTICS", "analytics.triptimeOfDay": "TRIP TIME OF DAY", "analytics.avgTime": "Average time user spent per day", "analytics.dataRange": "Data Range", "analytics.location": "Location", "analytics.productsOwned": "Products Owned", "dashboard.tempFileStates": "Templates Statistics", "marketing.sentMessages": "Sent Messages", "marketing.totalClicks": "Total Clicks", "marketing.totalShares": "Total Shares", "marketing.totalSales": "Total Sales", "dashboard.fTyes": "{file} Files", "dashboard.fileTypesDesc": "Files differ by their types", "dashboard.verifyDesc": "It shows verification status", "dashboard.diskUsage": "Disk Usage", "dashboard.ssDesc": "Badges shows number of documents uses that setting.", "dashboard.signReqDesc": "It shows documents signing requests status", "dashboard.totalFiles": "Total Files", "dashboard.totalTag": "Total Tags", "common.tag": "Tag", "common.entertag": "Please Enter Tag", "dashboard.txtComments": "Text", "dashboard.voiceComments": "Voice", "dashboard.enabledDoc": "Enabled in {num} document(s)", "dashboard.reveivedReq": "Received signing requests", "dashboard.sentReq": "Sent signing requests", "dashboard.secureClsDocs": "Security settings", "dashboard.workflowDetails": "Workflow Details", "dashboard.title": "Dashboard", "dashboard.you": "You", "dashboard.yourData": "Your dashboard data", "dashboard.notEnabled": "Not Enabled", "dashboard.noSign": "No Signature", "dashboard.noInt": "No Initial", "notification": "Notification", "topbar.account": "Account", "topbar.help": "Help", "topbar.logout": "Logout", "topbar.setting": "Settings", "antTable.title": "Title", "antTable.title.name": "NAME", "antTable.title.fullName": "Full Name", "antTable.title.firstName": "First Name", "antTable.title.lastName": "Last Name", "antTable.title.email": "Email", "antTable.title.phone": "Phone No", "antTable.title.country": "Country", "antTable.title.company": "Company Name", "antTable.title.address1": "Address line 1", "antTable.title.address2": "Address line 2", "antTable.title.zipCode": "Zip-code/Post Code", "antTable.title.city": "City", "antTable.title.modified": "Modified", "antTable.title.size": "Size", "antTable.title.type": "Type", "antTable.title.segmentName": "Segment Name", "antTable.title.categoryName": "Category Name", "antTable.title.language": "LANGUAGE", "antTable.title.firstSession": "FIRST SESSION", "antTable.title.lastSession": "LAST SESSION", "antTable.title.noProducts": "NO. OF PRODUCTS", "antTable.title.startDate": "Start date", "antTable.title.endDate": "End Date", "antTable.title.status": "Status", "antTable.title.Country": "COUNTRY", "antTable.title.userTags": "USER TAGS", "antTable.title.role": "Role", "antTable.title.shareRate": "SHARE RATE", "antTable.title.clickRate": "CLICK RATE", "card.form": "Form", "card.submissions": "Submissions", "card.carts": "Carts", "card.abandoned": "Abandoned", "clickRate+50": "+50", "clickRate<50": "Less then 50", "common.release": "Release", "common.action": "Action", "common.more": "more", "common.active": "Active", "common.inActive": "Inactive", "common.blocked": "Blocked", "common.unblocked": "Unblocked", "common.block.status": "Block Status", "common.verified": "Verified", "common.share": "Share", "common.Search": "Search", "common.search.status": "Select Status", "common.search.block": "Block Status", "common.role": "Role", "common.selectRole": "Select Role", "common.createdAt": "Created At", "common.updatedAt": "Updated At", "common.search.email": "Search by <PERSON><PERSON>", "common.search.name": "Search by Name", "common.deactive": "Deactive", "common.cancel": "Cancel", "common.submit": "Submit", "common.status": "Status", "common.change": "Change", "common.continue": "Continue", "common.verifying": "Verifying...", "common.no": "No", "common.sureDelete": "Sure to delete?", "common.sureDefault": "Sure to Make <PERSON><PERSON><PERSON>?", "common.makeDefault": "Make Default", "common.deleteCap": "DELETE", "common.loading": "Loading...", "common.selectCountry": "Select Country", "common.edit": "Edit", "common.open": "New", "common.create": "Create", "common.clear": "Clear", "common.signature": "Signature", "common.initial": "initial", "common.successNoti": "Success!", "common.rename": "<PERSON><PERSON>", "common.download": "Download", "common.copy": "Copy", "common.copyto": "Copy to...", "common.delete": "Delete", "common.areuSure": "Are you sure?", "common.wantDeleteItems": "Are you sure to delete selected items?", "common.wantDeleteItem": "Are you sure to delete selected item?", "common.wantDeleteFolder": "This folder contains files. Are you still want to delete this folder?", "common.actions": "Actions", "common.phoneNo": "Phone Number", "common.otp": "Enter OTP", "common.otpDesc": "You'll receive OTP on", "common.save": "Save", "common.export": "Export", "common.move": "Move", "common.moveto": "Move to...", "common.default": "<PERSON><PERSON><PERSON>", "common.receiveOtp": "Didn't receive code?", "common.tags": "Tags", "common.add": "Add", "common.added": "Added", "common.upgrade": "Upgrade", "common.filters": "Filters", "common.filter": "Filter", "common.reset": "Reset", "common.password": "Password", "common.yes": "Yes", "common.enterPssd": "Enter new password to activate your account", "common.createPssd": "Create Password", "common.total": "Total", "common.summary": "Summary", "common.year": "year", "common.month": "month", "common.viewBilling": "View Billing", "common.gotoHome": "Go to Home", "common.convert": "Convert", "common.noLang": "No Languages", "common.subscribeHere": "Subscribe here", "common.date": "Date", "common.title": "Title", "common.message": "Message", "common.clickHere": "Click here", "common.selectAll": "Select All", "common.unselectAll": "Unselect All", "common.deleteSelected": "Delete Selected", "common.viewAll": "View All", "common.months": "months", "common.hour": "hour", "common.hours": "hours", "common.min": "minute", "common.mins": "minutes", "common.days": "days", "common.daysCap": "Days", "common.day": "day", "common.andMore": "and more...", "common.remaining": "remaining", "common.storageUsed": "{used} of {total} Storage used", "common.usedPer": "{used}% used", "common.admin": "Admin", "common.helloUser": "Hello {name},", "common.home": "Home", "common.new": "New", "common.enterMail": "Enter email...", "common.enterMessage": "Enter Message", "common.back": "Back", "common.done": "Done", "common.goBack": "Go Back", "common.tryAgain": "Try Again", "common.ddmmyyy": "DD/MM/YYYY", "common.placeholder": "Placeholder", "common.required": "Required", "common.settings": "Settings", "common.number": "Number", "common.fullName": "Full Name", "common.cardDetails": "Card Details", "common.helperTxt": "Add some text to help", "common.gpName": "Group Name", "common.changesNotSave": "Changes that you made may not be saved", "common.leave": "Leave", "common.all": "All", "common.blank": "Blank", "common.pgRange": "Page Range", "common.applyto": "Apply To", "common.start": "Start", "common.end": "End", "common.fInfo": "File Info", "common.fPreview": "File Preview", "common.comments": "Comments", "common.addComments": "Add Comments", "common.clickTo": "Click to {val}", "common.public": "Public", "common.private": "Private", "common.privateCmt": "Private Comment", "common.label": "Label", "common.value": "Value", "common.sessionExpired": "Session expired, Please login again", "common.todayDate": "Today's Date", "common.history": "History", "common.copied": "<PERSON>pied", "common.search": "Search", "common.nextStep": "Next Step", "common.finish": "Finish", "common.addSign": "Add Signature", "common.addInitial": "Add Initial", "common.send": "Send", "common.original": "Original", "common.decline": "Decline", "common.signDc": "Sign document", "common.requested": "Requested", "common.filename": "File Name", "common.signers": "Signers", "common.downloadStarted": "Download Started...", "common.regNow": "Register Now!", "common.reason": "Reason: ", "common.apply": "Apply", "common.addNew": "+ Add New", "common.addPgAbove": "Add page above", "common.addPgBelow": "Add page below", "common.searchNew": "Search user or add new", "common.enableNow": "Enable Now", "common.enableSetting": "Please enable this setting from Security setting", "common.permission": "Permission", "common.info": "Information", "common.assigned": "Assigned", "common.assignedHint": "This field is assigned to other user", "common.details": "Details", "common.verification": "Verification", "common.used": "Used", "common.free": "Free", "common.enabled": "Enabled", "common.disabled": "Disabled", "common.enable": "Enable", "common.explore": "Explore", "common.sent": "<PERSON><PERSON>", "common.received": "Received", "common.docMultiHint": "You can select multiple documents with ctrl(Windows) / command(Mac).", "common.searchEl": "Search...", "common.previous": "Previous", "common.update": "Update", "common.reminder": "Reminder", "common.accept": "Accept", "common.editDetails": "Edit Details", "common.oops": "Oops!", "common.you": "(You)", "common.copyRightTxt": "Copyright © {year} Chillbaby Technologies. All rights reserved.", "common.edited": "Edited", "common.merge": "<PERSON><PERSON>", "common.empty": "Empty", "common.content": "Content", "common.explorer": "Explorer", "common.here": "here", "common.uploadFile": "Upload File", "common.years": "Years", "common.totalPay": "Total: {price}", "common.toPay": "to pay", "common.amount": "Amount", "common.totAmount": "Total Amount", "common.notes": "Notes", "common.view": "View", "common.source": "Source", "common.sDate": "Start Date", "common.eDate": "End Date", "common.clickViewMore": "Click to view more", "common.someone": "Someone", "common.delegation": "Delegation", "common.split": "Split", "common.pgNum": "Page Number", "common.dateTime": "Date & Time", "common.on": "On", "common.off": "Off", "common.time": "Time", "common.signReason": "Reason", "common.online": "Online", "common.offline": "Offline", "common.avgScore": "Avg Score", "common.score": "Score", "common.confident": "Confident", "common.scoring": "Scoring", "common.security": "Security", "common.product.uploadError": "You can only upload JPG, PNG and PDF file!", "common.upload": "Upload", "common.pdfUpload": "You can only upload PDF file!", "common.img.upload": "You can only upload JPG and PNG file!", "common.pdf.upload": "You can only upload PDF file!", "common.uploadImg": "Please upload image", "common.attachmentsUrl": "Please enter attachments url", "common.add.attachment": "Please add attachment", "common.changes": "Save Changes", "common.select.language": "Please select language", "common.report": "Report", "common.states": "States", "common.users": "Users", "common.typing": "Typing ...", "common.type.msg": "Type your message", "common.close": "Closed", "common.noconversion": "No Conversation", "common.count": "Count", "common.clicked": "Clicked", "common.failed": "Failed", "common.uploadBin": "Please upload .bin file", "common.bin.upload": "You can only upload .bin file!", "common.emailNotification": "Email Notification", "common.filterPrice": "Filter By Price", "common.surelogout": "Are you sure want to logout?", "err.segmentName": "Please enter name", "err.email": "Please enter valid email", "err.sendTo": "Please enter valid send to", "err.confirmPssd": "Please confirm your password", "err.bothPssd": "Both passwords do not match", "err.max.20char": "Please enter maximum 20 character", "err.max.30char": "Please enter maximum 30 character", "err.max.50char": "Please enter maximum 50 character", "err.max.255char": "Please enter maximum 255 character", "err.max.15char": "Please enter maximum 15 character", "err.min.10char": "Please enter maximum 10 character", "err.min.50char": "Please enter maximum 50 character", "err.password": "Password must include more than 8 characters, at least one number, one letter, one capital letter and one symbol", "err.phoneNumber": "Please enter phone number", "err.invalidPno": "Phone number is not valid", "err.invalidEmail": "Invalid email!", "err.blankSpace": "Blank space not allowed", "err.wenWrong": "Something went wrong. Try later", "err.fileMust10MB": "File must be smaller than 10MB!", "err.validDoc": "Please upload only valid files", "err.max.5file": "Max 5 files are allowed at a time", "err.expireTime": "Please enter valid expiration time", "err.download": "Unable to download, Please try later", "err.recField": "Please assign <PERSON><PERSON><PERSON><PERSON> to Input Fields", "err.pendingSign": "Please put your sign to finish document", "err.authFailed": "Authentication failed", "err.workFlowDetails": "Please fill Workflow details", "err.validConfirmCode": "Please enter valid code", "err.maxFileValidation": "Max 5 files at a time, upto 10 MB each file and .pdf, .doc, .docx are allowed", "err.downloadFailed": "Download failed! Try later", "err.max2MBImg": "Image must smaller than 2MB!", "err.max2MBFile": "File must smaller than 2MB!", "err.max10MBFile": "File must smaller than 10MB!", "err.max200KBFile": "File must smaller than 200KB!", "err.onlyJPGPNG": "You can only upload JPG/PNG file!", "err.onlyJPGPNGPDF": "You can only upload JPG/PNG/PDF file!", "err.accBlocked": "Your account has been blocked!", "err.accDeleted": "Account has been deleted!", "err.tagBlank": "Tag should not be blank", "err.deleteClassified": "Classified documents are not deletable in this mode, Please delete classified documents individually.", "err.mergeClassified": "You can't merge classified document(s)", "err.pdfPgError": "Page number should be grater than 0 and less than total pages of PDF", "err.validPageNum": "Please enter valid page number", "err.master3ErrMsg": "Please save at least 3 master drawings", "err.drawNotEnough": "Drawing is not enough", "err.masterNotCorrect": "Master drawing is not correct", "err.productName": "Please enter product name", "err.fullname": "Please enter name", "err.link": "Please enter link", "err.firstSession": "Please select First Session", "err.startDate": "Please select Start Date", "err.devicename": "Please enter device name", "err.bluetoothname": "Please enter bluetooth name", "err.firmwareVersion": "Please enter firmware version", "err.firmwareDescription": "Please enter firmware description", "err.size": "Selected file must smaller than 10MB!", "err.nouser": "No users found", "err.enterTitle": "Please enter title", "req.dateRange": "Please select dates also to view reports", "req.product.category": "Please add product category", "req.password": "Please enter password", "req.firstname": "Please enter First Name", "req.appName": "Please enter app name", "req.name": "Please enter name", "req.initial": "Please enter Initital", "req.email": "Please enter email", "req.role": "Please select Role", "req.lastname": "Please enter Last Name", "req.newPssd": "Please enter New Password", "req.cPssd": "Please enter Current Password", "req.address": "Please enter Address", "req.zipcode": "Please enter Zipcode", "req.city": "Please enter City", "req.state": "Please enter State", "req.country": "Please select country", "req.phone": "Please enter Phone number", "req.otp": "Please enter OTP", "req.folder": "Please select folder", "req.selectAttachmentsType": "Please select attachment type", "req.file": "Please upload file", "req.deptHeadName": "Please enter Head's Name", "req.deptHead": "Please enter Department Head", "req.deptMembers": "Please select members", "req.cards": "Please select card", "req.shareUsers": "Please select User / Department or add new to share document", "req.value": "Please enter value", "req.label": "Please enter label", "req.status": "Please select status", "req.expireTime": "Please enter expiration time", "req.reason": "Please enter Reason", "req.selectRecipient": "Please select any recipient to send document", "req.message": "Please enter message", "req.voiceMessage": "Please record voice", "req.add_1": "Please enter Address line 1", "req.add_2": "Please enter Address line 2", "req.atleastOne": "Please select atleast one option", "req.type": "Please select type", "req.projectName": "Please enter Project name", "req.dueDate": "Please select Due date", "req.details": "Please enter details", "req.days": "Please enter days", "req.docs": "Please select document(s)", "req.users": "Please select user(s)", "req.confirmCode": "Please enter confirmation code", "req.content": "Please enter content", "req.dtTime": "Please select date and time", "req.dtRange": "Please select Date Range", "req.digCurr": "Please select Digital Currency", "req.digCurrPay": "Please choose Digital Currency", "req.years": "Please enter year(s)", "req.proof": "Please upload proof", "req.pgNum": "Please enter Page number", "req.uname": "Please enter username", "req.iconUrl": "Enter Icon url", "req.buttonText": "<PERSON><PERSON> But<PERSON> Text", "req.postTitle": "Please enter post title", "req.description": "Please enter description", "req.campaignTitle": "Please enter campaign title", "req.campaignName": "Please enter campaign name", "req.campaignTags": "Please enter campaign tags", "req.postSubTitle": "Please enter post sub title", "req.buttonUrl": "Please enter Button URL", "req.attachmentsUrl": "Please enter attachments url", "req.price": "Please enter price", "req.stock": "Please enter stock", "req.sku": "Please enter SKU", "req.category": "Please select category", "req.tags": "Please add tags", "req.desc": "Please enter description", "req.faq": "Please enter FAQ", "req.startdate": "Please select first session date", "req.enddate": "Please select last session date", "req.packagename": "Please enter package name", "req.serviceType": "Please select service type", "req.integrationType": "Please select integration type", "req.shopUrl": "Please enter shop url", "req.shopToken": "Please enter shop token", "req.msg": "Please type something", "req.customer": "Please select customer", "input.username": "USERNAME", "input.password": "PASSWORD", "input.packagename": "PACKAGE NAME", "input.serviceType": "SERVICE", "input.integrationType": "INTEGRATION TYPE", "input.shopUrl": "SHOP URL", "input.shopToken": "SHOP TOKEN", "common.login": "<PERSON><PERSON>", "Forgot Password?": "Forgot Password?", "LOGIN": "LOGIN", "Enter password": "Enter password", "Enter email": "Enter email", "INNOVATIVE": "INNOVATIVE", "IOT": "IOT", "TECHNOLOGY SOFTWARE": "TECHNOLOGY SOFTWARE", "Visit Website": "Visit Website", "Back to login?": "Back to login?", "FORGOT PASSWORD": "FORGOT PASSWORD", "SEARCH ANYTHING": "SEARCH ANYTHING", "DETAILS": "DETAILS", "BRAND NAME": "BRAND NAME", "CHANGE PASSWORD": "CHANGE PASSWORD", "CAMPAIGNNAME": "CAMPAIGN NAME", "CAMPAIGNTAGS": "CAMPAIGN TAGS", "DESCRIPTION": "DESCRIPTION", "CAMPAIGNTITLE": "CAMPAIGN TITLE", "Account": "Account", "Settings": "Settings", "changePassword": "Change Password", "Add User": "Add User", "Update User": "Update User", "Admin": "Admin", "LAST LOGIN": "LAST LOGIN", "LOGIN IP": "LOGIN IP", "SECURITY SETTINGS": "SECURITY SETTINGS", "These settings": "These settings are helps you keep your account secure.", "Change Password": "Change Password", "unique password": "Set a unique password to protect your account.", "USERNAME": "USERNAME", "PASSWORD": "PASSWORD", "NEWPASSWORD": "NEW PASSWORD", "CONFIRMPASSWORD": "CONFIRM PASSWORD", "EMAIL": "EMAIL", "PHONE NUMBER": "PHONE NUMBER", "req.username": "Please enter user name", "req.phonenumber": "Please enter phone number", "req.newPassword": "Please enter new password", "req.confirmPassword": "Please enter confirm password", "err.max.10digit": "Please enter valid phone number", "attachmentLike": "Attachment Should Like", "attachmentDepend": "its depending on message type", "PRODUCTS": "PRODUCTS", "Add Product": "Add Product", "Add Productcategory": "Add Product Category", "All": "All", "New": "New", "Out of Stock": "Out of Stock", "No": "No", "Product Name": "Product Name", "Product category": "Category Management", "ProductCategory": "PRODUCT CATEGORY", "SKU": "SKU", "Price": "Price", "Users": "Users", "Category": "Category", "Action": "Action", "View Product": "View Product", "Edit Product": "Edit Product", "Edit Productcategory": "Edit Product Category", "Editcategory": "Edit Category", "DeleteCategory": "Delete Category", "Delete Product": "Delete Product", "Delete Marketing": "Delete Campaign", "total_click": "Total Clicks", "total_View": "Total Views", "total_share": "Total Shares", "most_click": "Most clicked", "most_View": "Most viewed", "most_share": "Most shared", "err.language": "Please enter language", "req.noProducts": "Please enter no. of products", "err.status": "Please select status", "req.userTags": "Please create user tag", "Name": "Name", "Phone Number": "Phone Number", "Email ID": "Email ID", "Role": "Role", "Added on": "Added on", "title.goal completions": "GOAL COMPLETIONS", "title.customeroverview": "CUSTOMER OVERVIEW", "title.outcomeStatistics": "OUTCOME STATISTICS", "title.usersbytime": "USERS BY TIME", "title.top link click": "TOP LINK CLICKS", "title.usergrowth": "USER GROWTH", "title.activity": "ACTIVITY", "title.productsUserAvg": "Products/User Avg", "title.campaignMarketing": "Campaign Marketing / Overview", "title.AIMarketing": "AI Marketing / Overview", "title.conversionFunnel": "Conversion Funnel", "title.salesPerformance": "Sales Performance", "title.traffic": "Traffic", "title.Source": "Sources", "text.lorem ipsum": "Lorem Ipsum is simply dummy text of the printing and typesetting industry.", "text.relative": "Relative Change vs last month", "th.brandname": "Brand Name", "th.ecommerce": "E-Commerce", "th.integrations": "Integrations", "th.integrationsType": "Integrations Type", "th.users": "Users", "th.previousperiod": "Previous Period", "th.change": "%Change", "input.product.title": "Product Name", "input.product.regularprice": "Regular Price", "input.product.saleprice": "Sale Price", "input.product.stock": "Stock", "input.product.sku": "SKU", "input.product.category": "Product Category", "input.product.tags": "Tags", "input.category.placeholder": "Select Category", "input.customer.fullname": "Full Name", "input.desc.label": "Description", "input.usermanual.label": "User Manual", "input.firmware.label": "Upload Firmware", "input.firmware.version": "Firmware version", "input.firmware.description": "Firmware description", "input.faq.label": "FAQs", "input.video.label": "Video Link", "input.product.link.label": "Product Link", "input.filter.placeholder": "Select Filter", "input.sorting.placeholder": "Select Sorting", "input.catalouge.placeholder": "Select Collection type", "label.collection": "Select collection type", "product.detail.title": "Product Details", "product.subTitle": "View or edit information about the product", "product.category": "Add Product Category", "th.productname": "Product Name", "th.totalusers": "Total Users", "th.userperday": "Total Users Per Day", "th.activeuser": "Active Users(Avg)", "th.platforms": "Platforms", "title.addbrand": "Add Brand", "title.addIntegration": "Add Integration", "input.contactname": "Contact Name", "req.contactname": "Please enter contact name", "input.contactnumber": "Contact Number", "req.contactnumber": "Please enter contact number", "err.contactnumber": "Please enter valid contact number", "customer.add": "Add Customer", "customer.view": "View Customer", "customer.edit": "Edit Customer", "customer.delete": "Delete Customer", "customer.detail.title": "Customer Details", "customer.subTitle": "View or edit information about the customer", "status.option1": "Active", "status.option2": "Inactive", "brand.edit": "Edit Brand", "brand.delete": "Delete Brand", "brand.editIntegration": "Edit Integration", "brand.deleteIntegration": "Delete Integration", "chat.ticketId": "Ticket ID", "chat.close.ticket": "Close Ticket", "chat.escalate.ticket": "Submit to tech team", "chat.return.ticket": "Return to brand", "chat.input.placeholder": "Write a message", "chat.return.message": "Message", "chat.return.message.req": "Please enter message to send", "title.messagesSent": "MESSAGES SENT", "ticket.total": "Total Tickets", "ticket.active": "Active Tickets", "ticket.open": "Open Tickets", "ticket.closed": "Closed Tickets", "ticket.avg.resolved": "Avg Tickets Resolved Today", "ticket.ticketStatus": "Ticket Status", "users.hours": "hours", "tab.all": "All", "tab.new": "New", "tab.outofstock": "Out of Stock", "tab.delivered": "Delivered", "tab.inprocess": "In Process", "tab.live": "Live", "tab.expired": "Expired", "tab.archive": "Archive", "tab.feedpost": "Feed Post", "tab.pushmessage": "Push Message", "tab.inappmessage": "In App Message", "tab.status": "STATUS", "tab.country": "COUNTRY", "tab.language": "LANGUAGE", "tab.usertags": "USER TAGS", "tab.englishspeaking": "English Speaking", "tab.topshares": "Top Shares", "tab.allCampaign": "All Campaigns", "tab.archived": "Archived", "chart.newdailyusers": "New Daily Users", "chart.postshares": "Post Shares", "chart.productsperussers(Avg)": "Products per Users(Avg)", "chart.vslastmonth": "vs last month", "chart.childAges": "Child Ages", "chart.dailyAvg": "Daily Average", "chart.userAgeinsights": "User Age Insights", "chart.childWeight": "Child Weight", "chart.appPerformance": "App Performance Score", "chart.usersbyLocation": "Users by Location", "chart.weatherAlert": "Weather Alerts", "chart.climateCondition": "Climate Conditions", "chart.totalorders": "Total orders", "action.viewcampaign": "View All Campaigns", "action.archive": "Archive", "action.unarchive": "Un-Archive", "action.duplicatecampaign": "Duplicate Campaign", "filter.reset": "RESET FILTER", "marketing.display.post": "Disable Post", "CAMPAIGNTYPE": "CAMPAIGN TYPE", "TopSharers": "Top Sharers", "LoadingChart": "Loading Chart", "brands": "BRANDS", "dashboard.thisweek": "This Week", "dashboard.lastweek": "Last Week", "dashboard.thisMonth": "This Month", "dashboard.lastMonth": "Last Month", "dashboard.lastSixMonth": "Last 6 Months", "dashboard.lastYear": "Last Year", "device.addDevice": "Add <PERSON>", "device.nametitle": "Device Name", "device.bluetoothname": "Bluetooth Name", "device.description": "Description", "device.edit": "<PERSON>", "device.delete": "Delete Device", "device.userCount": "User Count", "dashboard.vslastmonth": "vs last month", "dashboard.vslastyear": "vs last year", "dashboard.vslastweek": "vs last week", "marketing.buttonurl": "URL", "marketing.enterbuttonurl": "Enter URL", "goal.addGoal": "Add Goal", "goal.goaltitle": "Goal Title", "goal.increseby": "Increase By", "goal.timePeriod": "Time Period", "goal.status": "Status", "req.goaltitle": "Please select goal title", "req.goalincrease": "Please enter increase by", "req.increasetype": "Please enter only number", "req.increaselength": "Please enter only 3 digit", "req.goalperiod": "Please select time period", "createnewgoal": "Add Goal", "edit.goal": "Edit Goal", "newusergrowth": "New user growth", "activeusergrowth": "Active user growth", "productperusergrowth": "Product per user growth", "campaignsopen": "Campaigns Open", "campaignsclicks": "Campaigns Clicks", "week": "week", "month": "month", "year": "year", "inprocess": "In Process", "completed": "Completed", "notcompleted": "Not Completed", "selectUser": "Select User", "dropdown.product": "Select Brand", "dropdown.userSegment": "Select user segment", "overviewTitle": "Overview", "overview1": "The Direct marketing suite gives your marketing team the tools to design, create and send targeted messaging from the Platform direct to your customers app, whether sending a highly targeted message to a segmented group of 100 people or a time limited sales event to 1M Push Notifications", "overview2": "Push Notifications", "overview2Detail": "Target, Create & Send Push notifications.", "overview3": "In-App Messaging", "overview3Detail": "Create sales events, new product launches or exciting news as in-app messages.", "overview4": "My Feed", "overview4Detail": "Send media or messages directly to your customers feed.", "faq.addFaq": "Add FAQ", "faq.edit": "Edit FAQ", "faq.delete": "Delete FAQ", "delete.goal": "Delete Goal", "View Device": "View Device", "req.brand": "Please select brand", "autoAdd": "Auto update segment", "dashboard.thisyear": "This Year", "dashboard.messagesent": "MESSAGES SENT", "dashboard.totalclick": "TOTAL CLICKS", "dashboard.totalshares": "TOTAL SHARES", "sidebar.mainDevice": "Devices", "sidebar.deviceData": "Device Data", "deviceData.name": "Customer Name", "deviceData.email": "Customer <PERSON><PERSON>", "deviceData.bleSsid": "SSID", "deviceData.macAddress": "<PERSON>dress", "deviceData.mfgDate": "Manufacture Date", "deviceData.serialNumber": "Serial Number", "deviceData.productName": "Product Name", "deviceData.productModal": "Product Modal", "deviceData.firmwareVersion": "Firmware Version", "chat.selectDevice": "Select Device", "deviceData.latestFirmwareVrsn": "Latest Firmware Version", "deviceData.userFirmware": "User Firmware", "strollers.addStrollers": "Add Strollers", "strollers.editStrollers": "<PERSON>", "strollers.delete": "Delete Strollers", "edit.commision": "Edit Commission rate", "label.commision": "COMMISSION RATE%", "req.commision": "Commission rate is required", "sidebar.gpx": "GPX", "req.group": "Please enter group name", "group.add": "Add Group", "group.edit": "Edit Group", "group.delete": "Delete Group", "trailer.add": "Add Trailer", "trailer.edit": "Edit Trailer", "trailer.delete": "Delete Trailer", "common.addgpx": "Add GPX", "common.skuCode": "SKU Code", "common.buggyName": "Buggy Name", "common.buggyImage": "Buggy Image", "common.eanBarcode": "EAN Barcode", "common.maintenanceGroup": "Maintenance Group", "common.search.maintenanceGroup": "Select Maintenance Group", "common.userManuals": "User Manuals", "common.trailerType": "Trailer Type", "common.search.trailerType": "Select Trailer Type", "common.trailerDescription": "Trailer Description", "req.skuCode": "Please enter sku code", "req.buggyName": "Please enter buggy name", "req.buggyImage": "Upload buggy image", "req.eanBarcode": "Enter EAN barcode", "req.maintenanceGroup": "Please select maintenance group", "req.userManuals": "Upload user manuals", "req.trailerType": "Please select trailer type", "req.trailerDescription": "Please select trailer description", "common.img.pngUpload": "You can only upload PNG file!", "antTable.title.buggyName": "Buggy Name", "antTable.title.eanBarcode": "EAN Barcode", "antTable.title.skuCode": "SKU Code", "antTable.title.trailerType": "Trailer Type", "maintenance.add": "Add Maintenance", "maintenance.edit": "Edit Maintenance", "maintenance.delete": "Delete Maintenance", "common.buggyGroup": "Buggy Group", "common.ruleType": "Rule Type", "common.metricType": "Metric Type", "common.ruleValue": "Rule Value", "common.metricValue": "Metric Value", "req.buggyGroup": "Please select buggy group", "req.ruleType": "Please select rule type", "req.metricType": "Please select metric type", "req.ruleValue": "Please enter rule vlaue", "req.metricValue": "Please enter metric vlaue", "common.search.buggyGroup": "Select Buggy Group", "common.search.ruleType": "Select Rule Type", "common.search.metricType": "Select Metric Type", "common.pdf&video.upload": "You can only upload PDF or Video file!", "antTable.metric": "Metric", "antTable.rule": "Rule", "antTable.description": "Description", "highlights.add": "Add Highlights", "highlights.edit": "Edit Highlights", "highlights.delete": "Delete Highlights", "common.distance": "Distance", "common.co2_equivalent": "CO2 Equivalent", "common.activities": "Activities", "common.altitude": "Altitude", "common.average_speed": "Average Speed", "common.buggy_mode": "Buggy Mode", "common.time_active": "Time Active", "common.challenge_status": "Challenge Status", "common.increase": "Increase", "common.challenge_metric": "Challenge Metric", "common.award_type": "Award Type", "common.image_url": "Sticker or Coupon Url", "common.award_text": "Award Text", "common.coupon_code_pdf": "Coupon Code Pdf", "err.distance": "Please enter distance", "err.co2_equivalent": "Please enter co2 equivalent", "err.altitude": "Please enter altitude", "err.activities": "Please enter activities", "err.buggy_mode": "Please enter buggy mode", "err.increase": "Please enter increase", "err.value": "Please enter value", "err.challenge_metric": "Please enter challenge metric", "err.challenge_status": "Please select challenge status", "err.award_text": "Please enter award text", "err.award_type": "Please select award type", "err.time_active": "Please select time active", "err.average_speed": "Please enter average speed", "common.search.challenge_status": "Select Challenge Status", "common.search.award_type": "Select Award Type", "common.img.all.Upload": "You can only upload Jpeg, JPG, PNG and PDF file!", "common.category": "Please Select Category", "err.tag": "Please enter tag", "err.gpx.upload": "You can only upload GPX file!", "gpx.locationType": "Location Type", "common.locationType": "Select Location Type", "req.locationType": "Please select location type", "highlight.type": "Highlight Type", "common.highlightType": "Select Highlight Type", "req.highlightType": "Please select highlight type", "common.challengeStartDate": "Challenge Start Date", "err.challengeStartDate": "Please select challenge start date", "common.challengeEndDate": "Challenge End Date", "err.challengeEndDate": "Please select challenge end date", "err.award_text_max_length": "Max 100 characters are allowed."}
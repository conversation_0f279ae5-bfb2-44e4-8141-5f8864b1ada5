/* eslint-disable react/jsx-props-no-spreading */
import React, { lazy, Suspense, useEffect, useState } from 'react';
import {
  Route,
  Redirect,
  BrowserRouter as Router,
  Switch,
  useLocation,
} from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import qs from 'query-string';
import Loader from '@chill/components/utility/Loader';
import authActions from '@chill/redux/auth/actions';
import appActions from '@chill/redux/app/actions';
import chatActions from '@chill/redux/chat/actions';
// import { useAuth0 } from '@auth0/auth0-react';
import useWindowSize from '@chill/lib/hooks/useWindowSize';
import sendPostMsg from '@chill/lib/helpers/RNWebview';
import getApiData from '@chill/lib/helpers/apiHelper';
import ErrorBoundary from './ErrorBoundary';
import PUBLIC_ROUTE from './route.constants';

const { updateUserData, setBrand } = authActions;
const { toggleAll, updateSettings } = appActions;
const { initialization } = chatActions;

const Dashboard = lazy(() => import('./containers/Dashboard/Dashboard'));
const ShowNotification = lazy(() => import('./components/ShowNotification'));
const DesignStudio = lazy(() => import('./containers/Marketing/DesignStudio'));
const publicRoutes = [
  {
    path: PUBLIC_ROUTE.LANDING,
    exact: true,
    component: lazy(() => import('@chill/containers/Pages/SignIn/SignIn')),
  },
  {
    path: PUBLIC_ROUTE.PAGE_404,
    component: lazy(() => import('@chill/containers/Pages/404/404')),
  },
  {
    path: PUBLIC_ROUTE.SIGN_IN,
    component: lazy(() => import('@chill/containers/Pages/SignIn/SignIn')),
  },
  {
    path: PUBLIC_ROUTE.SIGN_UP,
    component: lazy(() => import('@chill/containers/Pages/SignIn/SignIn')),
  },
  {
    path: PUBLIC_ROUTE.APP_URL,
    component: lazy(() => import('@chill/containers/Pages/appUrl')),
  },
  {
    path: PUBLIC_ROUTE.CREATE_PASSWORD,
    component: lazy(
      import('@chill/containers/Pages/CreatePassword/CreatePassword'),
    ),
  },
  {
    path: PUBLIC_ROUTE.AUTH0_CALLBACK,
    component: lazy(
      import('@chill/containers/Authentication/Auth0/Auth0Callback'),
    ),
  },
];

function PrivateRoute({ children, ...rest }) {
  const location = useLocation();
  const isLoggedIn = useSelector((state) => state.Auth.idToken);

  if (isLoggedIn) {
    // eslint-disable-next-line react/jsx-props-no-spreading
    return <Route {...rest}>{children}</Route>;
  }

  return (
    <Redirect
      to={{
        pathname: '/signin',
        state: { from: location },
      }}
    />
  );
}

let loadingTimer = null;
let socketTimer = null;
let socketCnt = 1;

export default function Routes() {
  const { idToken: isLoggedIn, userData } = useSelector((state) => state.Auth);
  const socketIo = useSelector((state) => state.Notification.IOSocket);
  const dispatch = useDispatch();

  const [state, setState] = useState({ loading: false });
  const { width, height } = useWindowSize();

  // Socket connection logic
  useEffect(() => {
    if (!socketIo && isLoggedIn && socketCnt <= 5) {
      // Initialize socket first time
      if (socketCnt === 1) dispatch(initialization());

      // Check socket connection and connect if it not connected yet
      socketTimer = setInterval(() => {
        if (!socketIo && socketCnt <= 5) {
          dispatch(initialization());
          socketCnt += 1;
        } else {
          clearInterval(socketTimer);
          socketCnt = 1;
        }
      }, 2000);
    } else {
      socketCnt = 1;
      clearInterval(socketTimer);
    }

    return () => {
      socketCnt = 1;
      clearInterval(socketTimer);
    };
  }, [socketIo, isLoggedIn]);

  useEffect(() => {
    // On window resize perform required actions
    dispatch(toggleAll(width, height));

    // Add classname to Body for Mobile realated styles
    if (width < 768) {
      document.body.className = 'mobileAppBody';
    }
  }, [width, height]);

  // this function for get brand list
  async function getBrandList() {
    try {
      const response = await getApiData('getBrandListDropdown');

      if (response.success) {
        dispatch(setBrand(response.data));
      }
    } catch (error) {
      console.log('error ===', error);
    }
  }

  useEffect(() => {
    const pObj = qs.parse(window.location.search);

    if (isLoggedIn && !pObj?.is_app_loading) {
      dispatch(updateSettings());
      dispatch(updateUserData('initLoading'));
      getBrandList();
    }

    if (pObj?.is_app_loading && !state.loading) {
      console.log('INIT APP LOADING ====================<>?');
      setState((pre) => ({ ...pre, loading: true }));

      loadingTimer = setTimeout(() => {
        console.log('TIME OUT ====================<>?');
        setState((pre) => ({ ...pre, loading: false }));
        sendPostMsg({ type: 'back', message: 'alert.somethingWrong' });
      }, 15000);
    }
    return () => {
      clearTimeout(loadingTimer);
    };
  }, [isLoggedIn]);

  // function handlePostMessage(e) {
  //   // console.log('🚀 ~ file: router.js ~ line 121 ~ handlePostMessage ~ e', e);
  //   if (
  //     e &&
  //     e?.data?.token &&
  //     e?.data?.is_mobile &&
  //     e.origin === siteConfig.domainUrl
  //   ) {
  //     // Set token into REDUX and localstorage, Store postData into REDUX
  //     dispatch(setToken(e.data.token));
  //     dispatch(setPostData(e.data));

  //     // Add classname to Body for App realated styles
  //     if (width < 768) {
  //       document.body.className = `${document.body.className || ''} mobileApp`;
  //     }
  //     console.log('window.location.pathname================<>?');
  //     console.log(window.location.pathname);

  //     setTimeout(() => {
  //       window.history.pushState(
  //         { id: 'doc-detail' },
  //         'Document',
  //         window.location.pathname,
  //       );
  //       console.log('INIT COMPLETED LOADING COMPLETED ====================<>?');
  //       setState((pre) => ({ ...pre, loading: false }));

  //       // Clear loading timer timeout
  //       clearTimeout(loadingTimer);
  //     }, 500);
  //   }

  //   if (e && e?.data?.type) dispatch(setPostData(e.data));
  // }

  // Disable right click in App
  // const handleContextMenu = (e) => e.preventDefault();
  // useEffect(() => {
  //   document.addEventListener('message', handlePostMessage);
  //   window.addEventListener('message', handlePostMessage);
  //   document.addEventListener('contextmenu', handleContextMenu);
  //   return () => {
  //     document.removeEventListener('message', handlePostMessage);
  //     window.removeEventListener('message', handlePostMessage);
  //     document.removeEventListener('contextmenu', handleContextMenu);
  //   };
  // }, []);

  // Wait for user's data and then render
  if ((userData && userData.initLoading) || state.loading) return <Loader />;

  return (
    <ErrorBoundary>
      <Suspense fallback={<Loader />}>
        <Router>
          <Switch>
            {publicRoutes.map((route) => {
              if (route.loginRequired && !isLoggedIn) return null;
              return (
                <Route key={route.path} path={route.path} exact={route.exact}>
                  <route.component />
                </Route>
              );
            })}

            <PrivateRoute path="/dashboard">
              <Dashboard />
            </PrivateRoute>
            <PrivateRoute path="/design-studio">
              <DesignStudio />
            </PrivateRoute>
            <Redirect to="/404" />
          </Switch>
          <ShowNotification />
        </Router>
      </Suspense>
    </ErrorBoundary>
  );
}

/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
/* eslint-disable no-nested-ternary */
import React, { useEffect, useState } from 'react';
import { Col, Drawer, Input, Row, Upload, Form, Icon, Select } from 'antd';
import { isEmpty } from 'lodash';
// import { FilePdfOutlined, UploadOutlined } from '@ant-design/icons';
import { useSelector } from 'react-redux';
import IntlMessages from '@chill/components/utility/intlMessages';
import {
  BottomViewWrapper,
  FormWrapper,
} from '@chill/assets/styles/drawerFormStyles';
import CButton from '@chill/components/uielements/CButton';
import { getBase64 } from '@chill/lib/helpers/utility';
import Notification from '@chill/components/Notification';
import getApiData from '@chill/lib/helpers/apiHelper';
import useResetFormOnClose from '@chill/lib/hooks/useResetForm';
import { trailerTypes } from '@chill/lib/helpers/utilityData';

const { Option } = Select;

function TrailerForm(props) {
  const { initialValues, onClose, visible, view } = props;
  const edit = !isEmpty(initialValues);
  const initVal = initialValues || {};
  const [form] = Form.useForm();
  const [btnLoader, setBtnLoader] = useState(false);
  const [trailerImg, setTrailerImg] = useState({});
  const [selectedImg, setSelectedImg] = useState(null);
  const [selectedPdf, setSelectedPdf] = useState(null);
  console.log(
    `hello ~ file: trailerForm.js:32 ~ TrailerForm ~ selectedPdf:`,
    selectedPdf,
  );
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);
  const [groupList, setGroupList] = useState([]);
  useResetFormOnClose({ visible, initVal, form });

  const getGroupList = async () => {
    const response = await getApiData('manage-group/index?allData=1');
    if (response.success) {
      setGroupList(response.data);
    }
  };

  useEffect(() => {
    getGroupList();
    if (!edit) {
      form.resetFields();
    }
  }, [visible]);

  const beforeUpload = (file) => {
    const validType = file.type === 'image/png';

    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      Notification('error', messageArray['err.size']);
      return false;
    }

    if (!validType) {
      Notification('error', messageArray['common.img.pngUpload']);
      return false;
    }

    getBase64(file, (imageUrl) => {
      setTrailerImg({ imageUrl, loading: false });
    });
    setSelectedImg(file);
    return false;
  };

  // const beforePdfUpload = (file) => {
  //   const validType = file.type === 'application/pdf';
  //   if (!validType) {
  //     Notification('error', messageArray['common.pdf.upload']);
  //     return false;
  //   }
  //   setSelectedPdf(file);
  //   return false;
  // };

  // this function reset form data when close modal
  function handleForm(type = '', data = {}) {
    form.resetFields();
    setTrailerImg({});
    setSelectedImg({});
    setSelectedPdf({});
    onClose(type, data);
  }

  // this function for add new trailers
  async function addTrailer(values) {
    const formData = new FormData();
    const url = edit
      ? 'manage-trailer/update-trailer'
      : 'manage-trailer/create-trailer';
    Object.keys(values).map((k) => formData.append(k, values[k]));
    formData.delete('buggy_image');
    if (!isEmpty(selectedImg)) {
      formData.append('buggy_image', selectedImg);
    }
    // if (!isEmpty(selectedPdf)) {
    //   formData.append('user_manuals', selectedPdf);
    // }

    try {
      const response = await getApiData(url, formData, 'POST', {}, true);
      if (response.success) {
        Notification('success', response.message);
        handleForm('success', response.data);
        setSelectedPdf(null);
        setSelectedImg(null);
      } else {
        Notification('error', response.message);
      }
      setBtnLoader(false);
    } catch (error) {
      console.log('error', error);
      setSelectedPdf({});
      setBtnLoader(false);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  // this function for validate data
  function validate(values) {
    let valid = true;
    const obj = values;
    if (isEmpty(selectedImg)) {
      obj.buggy_image = initVal.buggy_image || '';
    }

    // if (isEmpty(selectedPdf)) {
    //   obj.user_manuals = initVal.user_manuals || '';
    // }

    if (!edit && isEmpty(selectedImg)) {
      Notification('error', messageArray['common.uploadImg']);
      valid = false;
    }

    if (edit) obj.trailer_id = initVal.id;

    if (valid) {
      setBtnLoader(true);
      addTrailer(obj);
    }
  }

  const uploadButton = (
    <div>
      <Icon type="plus" />
      <div className="ant-upload-text">
        <IntlMessages id="common.upload" />
      </div>
    </div>
  );
  // const uploadPdfButton = (
  //   <div>
  //     <Icon type="plus" />
  //     <div className="ant-upload-text">
  //       <UploadOutlined /> <IntlMessages id="common.uploadFile" />
  //     </div>
  //   </div>
  // );

  return (
    <Drawer
      title={<IntlMessages id={edit ? 'trailer.edit' : 'trailer.add'} />}
      width={500}
      style={{ height: window.innerHeight - 60, marginTop: 70 }}
      onClose={() => handleForm()}
      visible={visible}
      bodyStyle={{ paddingBottom: 80, overflowY: 'scroll' }}
      destroyOnClose
    >
      <FormWrapper>
        <Form
          form={form}
          onFinish={(e) => validate(e)}
          layout="vertical"
          initialValues={initVal}
        >
          <Row
            gutter={[12, 20]}
            style={{
              marginBottom: 20,
            }}
          >
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="common.skuCode" />}
                name="sku_code"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.skuCode" />,
                  },
                ]}
              >
                <Input placeholder="Enter sku code" />
              </Form.Item>
            </Col>
            <Col xs={14}>
              <Form.Item
                label={<IntlMessages id="common.buggyName" />}
                name="buggy_name"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.buggyImage" />,
                  },
                ]}
              >
                <Input placeholder="Enter Buggy name" />
              </Form.Item>
            </Col>
            <Col xs={10}>
              <Form.Item
                name="buggy_image"
                label={<IntlMessages id="common.buggyImage" />}
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.buggyImage" />,
                  },
                ]}
              >
                <Upload
                  showUploadList={false}
                  listType="picture-card"
                  beforeUpload={beforeUpload}
                  disabled={!!view}
                  className="uploadImage-center"
                >
                  {trailerImg.imageUrl || initVal.buggy_image ? (
                    <img
                      src={trailerImg.imageUrl || initVal.buggy_image}
                      alt="product"
                      style={{ width: '100%', height: '100%' }}
                    />
                  ) : (
                    uploadButton
                  )}
                </Upload>
              </Form.Item>
            </Col>
            <Col xs={9}>
              <Form.Item
                label={<IntlMessages id="common.eanBarcode" />}
                name="ean_barcode"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.eanBarcode" />,
                  },
                ]}
              >
                <Input type="number" placeholder="Enter EAN barcode" />
              </Form.Item>
            </Col>
            <Col xs={15}>
              <Form.Item
                label={<IntlMessages id="common.maintenanceGroup" />}
                name="maintenance_group_id"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.maintenanceGroup" />,
                  },
                ]}
              >
                <Select
                  placeholder={
                    <IntlMessages id="common.search.maintenanceGroup" />
                  }
                >
                  {groupList.map((status) => {
                    if (status.disabled) return null;
                    return (
                      <Option key={status.id} value={status.id}>
                        {status.group_name}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="user_manuals"
                label={<IntlMessages id="input.usermanual.label" />}
              >
                <Input />
                {/* <Upload
                  className="usermanualstyle"
                  showUploadList={false}
                  listType="picture-card"
                  beforeUpload={beforePdfUpload}
                  disabled={!!view}
                  style={{ width: '100%' }}
                >
                  {(selectedPdf && !isEmpty(selectedPdf)) ||
                  (initVal && !isEmpty(initVal?.user_manuals)) ? (
                    <Row>
                      <Col xs={24}>
                        <FilePdfOutlined
                          style={{ fontSize: 50, color: 'red' }}
                        />
                      </Col>
                      <Col xs={24}>
                        <div className="_uploadTextContainer">
                          <div className="_uploadText">
                            {selectedPdf && !isEmpty(selectedPdf)
                              ? selectedPdf?.name
                                ? selectedPdf?.name
                                : initVal &&
                                  !isEmpty(initVal) &&
                                  initVal?.file_meta &&
                                  !isEmpty(initVal?.file_meta)
                                ? initVal?.file_meta?.filename
                                : ''
                              : initVal?.user_manuals}
                          </div>
                        </div>
                      </Col>
                    </Row>
                  ) : (
                    uploadPdfButton
                  )}
                </Upload> */}
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="common.trailerType" />}
                name="trailer_type"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.trailerType" />,
                  },
                ]}
              >
                <Select
                  placeholder={<IntlMessages id="common.search.trailerType" />}
                >
                  {trailerTypes.map((status) => {
                    if (status.disabled) return null;
                    return (
                      <Option key={status.value} value={status.value}>
                        {status.text}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="common.trailerDescription" />}
                name="trailer_description"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.trailerDescription" />,
                  },
                ]}
              >
                <Input.TextArea
                  rows={3}
                  placeholder="Enter trailer description..."
                />
              </Form.Item>
            </Col>
          </Row>
          <BottomViewWrapper className="bottomBtnWrapper">
            <Row gutter={12} style={{ width: '100%' }}>
              {view ? null : (
                <Col xs={24}>
                  <Form.Item className="btn-form">
                    <CButton
                      className="submitBtnStyle"
                      htmlType="submit"
                      loading={btnLoader}
                      disabled={btnLoader}
                      style={{ marginRight: 20 }}
                    >
                      <IntlMessages
                        id={edit ? 'common.changes' : 'common.submit'}
                      />
                    </CButton>
                  </Form.Item>
                </Col>
              )}
              {/* <Form.Item className="btn-form">
                <div
                  className="cancelBtnStyle"
                  onClick={handleForm}
                  role="button"
                  onKeyPress=""
                  tabIndex="-1"
                >
                  <IntlMessages id={view ? 'common.done' : 'common.cancel'} />
                </div>
              </Form.Item> */}
            </Row>
          </BottomViewWrapper>
        </Form>
      </FormWrapper>
    </Drawer>
  );
}

export default TrailerForm;

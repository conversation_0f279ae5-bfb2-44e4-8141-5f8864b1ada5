/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
import React, { useEffect } from 'react';
import { Col, Row, Divider, Input } from 'antd';
import { isArray, isEmpty, isObject } from 'lodash';
import { useSelector } from 'react-redux';
import {
  FilterOutlined,
  PlusCircleOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import theme from '@chill/config/theme/default';
import TableWrapper from '@chill/assets/styles/AntTables.styles';
import IntlMessages from '@chill/components/utility/intlMessages';
import Popover from '@chill/components/uielements/popover';
import getApiData from '@chill/lib/helpers/apiHelper';
import Popconfirms from '@chill/components/Feedback/Popconfirm';
import Notification from '@chill/components/Notification';
import TrailerManagementForm from './trailerForm';
import TrailerStyles from './trailerManagemet.styles';

/**
 *
 * @module TrailerManagement
 */

const TrailerManagement = () => {
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const [actionMenu, setActionMenu] = React.useState(false);
  const [isIndex, setIsIndex] = React.useState(null);
  const [state, setState] = React.useState({
    initObj: {},
    visible: false,
  });
  const [trailerState, setTrailerState] = React.useState({
    trailers: [],
    trailerLoad: false,
  });
  const [filter, setFilter] = React.useState({ pagination: {}, filters: {} });
  const [filterMode, setFilterMode] = React.useState(false);
  const [sort, setSort] = React.useState(false);
  const [currentPage, setCurrentPage] = React.useState(0);

  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  // this function for get trailer's
  /** this function for get trailer's list
   * @function getTrailerList
   * @param {object} data sort
   */
  async function getTrailerList(data = {}) {
    setTrailerState((p) => ({
      ...p,
      trailerLoad: true,
    }));

    try {
      const response = await getApiData('manage-trailer/index', data);
      if (response.success && isArray(response.data)) {
        setTrailerState((preState) => ({
          ...preState,
          trailers: isArray(response.data) ? response.data : [],
          trailerLoad: false,
        }));
        const pagination =
          filter && filter.pagination ? { ...filter.pagination } : {};
        pagination.total = response.total_count || 0;
        pagination.pageSize = response.per_page || 10;
        pagination.current = response.page || 1;
        setFilter((f) => ({ ...f, pagination }));
      } else {
        setTrailerState((preState) => ({
          ...preState,
          trailerLoad: false,
        }));
      }
    } catch (err) {
      setTrailerState((preState) => ({
        ...preState,
        trailerLoad: false,
      }));
    }
  }

  // this function filters data and get updated list of trailer
  /** this function filters data and get updated list of trailer
   * @function fetchDataFilters
   * @param {object} data page, title
   */
  function fetchDataFilters() {
    const page =
      filter && filter.pagination && filter.pagination.current
        ? filter.pagination.current
        : 1;
    const filters = filter && filter.filters ? filter.filters : {};
    getTrailerList({ page, ...filters });
  }

  useEffect(() => {
    fetchDataFilters();
  }, [sort]);

  // this function for delete trailer
  /** this function for delete trailer
   * @function deleteTrailer
   * @param {object} data trailer_id
   */
  async function deleteTrailer() {
    const formData = new FormData();
    formData.append(
      'trailer_id',
      isObject(state) &&
        isObject(state.initObj) &&
        !isEmpty(state.initObj) &&
        state.initObj.id,
    );
    const trailerAry = isArray(trailerState.trailers)
      ? trailerState.trailers
      : [];
    setTrailerState({
      trailerLoad: true,
    });
    try {
      const response = await getApiData(
        'manage-trailer/delete-trailer',
        formData,
        'POST',
        {},
        true,
      );
      let msgTitle = 'error';
      if (response.success) {
        msgTitle = 'success';
        fetchDataFilters();
      } else {
        setTrailerState((pre) => ({
          ...pre,
          trailers: trailerAry,
          trailerLoad: false,
        }));
      }
      Notification(msgTitle, response.message);
    } catch (error) {
      console.log('error ===', error);
      setTrailerState((pre) => ({
        ...pre,
        trailers: trailerAry,
        trailerLoad: false,
      }));
    }
  }

  // fun. for visible PopOver
  function handleVisible() {
    setActionMenu((visiblePre) => !visiblePre);
  }

  // this function for handle pagination
  /** this function for handle pagination
   * @function onChange
   * @param {object} data page
   */
  const onChange = (pagination) => {
    const pager = filter && filter.pagination ? { ...filter.pagination } : {};
    setCurrentPage((pagination.current - 1) * pagination.pageSize);
    pager.current = pagination.current;
    setFilter((f) => ({ ...f, pagination: pager }));
    getTrailerList({
      page: pagination.current,
      ...filter.filters,
    });
    setSort(false);
  };

  // Popover content or list
  const content = (
    <TrailerStyles>
      <div className="popMainDiv">
        <div
          role="button"
          className="isoDropdownLink"
          onKeyPress=""
          tabIndex="-1"
          onClick={() => {
            setState({ ...state, visible: true });
            setIsIndex(null);
          }}
        >
          <EditOutlined style={{ paddingRight: 12, fontSize: 16 }} />
          <IntlMessages id="trailer.edit" />
        </div>
        <div className="isoDropdownLink">
          <Popconfirms
            title={<IntlMessages id="Sure to delete?" />}
            okText={<IntlMessages id="DELETE" />}
            cancelText={<IntlMessages id="No" />}
            onConfirm={() => {
              deleteTrailer();
              setIsIndex(null);
            }}
            onCancel={null}
          >
            <DeleteOutlined style={{ paddingRight: 12, fontSize: 16 }} />
            <IntlMessages id="trailer.delete" />
          </Popconfirms>
        </div>
      </div>
    </TrailerStyles>
  );

  // set item id in state
  function handleVisibleChange(item1) {
    setIsIndex(item1.id);
  }

  const columns = [
    {
      title: <IntlMessages id="No" />,
      dataIndex: 'id',
      rowKey: 'id',
      width: 10,
      className: 'fullname-cell',
      render: (text, data, index) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="No" />
            </span>
            <span className="mobile-lbl-val">
              {index + currentPage + 1 || '-'}
            </span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="antTable.title.buggyName" />,
      dataIndex: 'buggy_name',
      rowKey: 'buggy_name',
      width: 180,
      className: 'fullname-cell',
      render: (text, data) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Product Name" />
            </span>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <img
                src={data.buggy_image}
                style={{
                  width: 30,
                  height: 30,
                  backgroundColor: '#eee',
                  marginRight: 12,
                  borderRadius: 30,
                }}
                alt=""
              />
              <span className="mobile-lbl-val">{text || '-'}</span>
            </div>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="antTable.title.eanBarcode" />,
      dataIndex: 'ean_barcode',
      rowKey: 'ean_barcode',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="antTable.title.eanBarcode" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="antTable.title.skuCode" />,
      dataIndex: 'sku_code',
      rowKey: 'sku_code',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="antTable.title.skuCode" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="antTable.title.trailerType" />,
      dataIndex: 'trailer_type',
      rowKey: 'trailer_type',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="antTable.title.trailerType" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Action" />,
      dataIndex: 'action',
      rowKey: 'action',
      className: 'fullname-cell',
      width: 50,
      fixed: 'right',
      render: (text, item) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Action" />
            </span>
            <span className="mobile-lbl-val">
              <Popover
                content={content}
                trigger="click"
                visible={isIndex === item.id ? !actionMenu : false}
                onVisibleChange={() => handleVisible()}
                arrowPointAtCenter
                placement="bottomRight"
              >
                <MoreOutlined
                  onClick={() => {
                    handleVisibleChange(item);
                    setState({ ...state, initObj: item });
                  }}
                  style={{ color: '#000', fontSize: 24 }}
                />
              </Popover>
            </span>
          </div>
        );
      },
    },
  ];

  // Add Trailer View
  const addNew = () => {
    return (
      <TrailerStyles>
        <div className="iconStyle marketingMainDiv">
          <span>
            <p
              className="addNewUser"
              style={{
                color: theme.colors.primaryColor,
                alignItems: 'center',
              }}
            >
              <FilterOutlined
                className="filterIcon"
                style={{
                  color: filterMode ? theme.colors.primaryColor : '#000',
                }}
                onClick={() => {
                  setFilterMode((pre) => !pre);
                  setFilter({ filters: {} });
                }}
              />
              <div
                role="button"
                tabIndex="0"
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                }}
                onClick={() => setState({ visible: true })}
                onKeyDown={() => setState({ visible: true })}
              >
                <p className="iconPadding" style={{ paddingRight: 8 }}>
                  <PlusCircleOutlined />
                </p>
                <IntlMessages id="trailer.add" />
              </div>
            </p>
          </span>
        </div>
      </TrailerStyles>
    );
  };

  // this function renders search view
  function renderSearchBar() {
    return (
      <div style={{ padding: '15px' }}>
        <Row gutter={10}>
          <Col lg={8} sm={24} md={12} xs={24} style={{ marginBottom: 8 }}>
            <Input.Search
              style={{ minWidth: '100%' }}
              onChange={(e) => {
                const val = e && e.target ? e.target.value : '';
                const flt = filter ? { ...filter } : {};
                flt.filters.buggy_name = val;
                setFilter(flt);
                getTrailerList(flt.filters);
              }}
              value={filter.title}
              allowClear
              placeholder={messageArray['common.buggyName']}
            />
          </Col>
        </Row>
      </div>
    );
  }

  const { initObj, visible } = state;
  return (
    <TrailerStyles>
      <Row className="isoTabRow">
        <Col xl={8} lg={8} sm={8} md={4} xs={8}>
          <div className="marketingText">
            <IntlMessages id="sidebar.trailerManagement" />
          </div>
        </Col>
        <Col sm={16} xs={16} className="hiddenCol">
          {addNew()}
        </Col>
        <Col xl={16} lg={16} md={20} xs={16} className="isoAddNew">
          {addNew()}
        </Col>
      </Row>
      <Divider className="horizontalDevider" />
      {filterMode ? renderSearchBar() : null}
      <TableWrapper
        loading={trailerState.trailerLoad}
        rowKey={(record) => record.id}
        dataSource={trailerState.trailers}
        onChange={onChange}
        columns={columns}
        className="invoiceListTable"
        pagination={filter.pagination || {}}
        showSorterTooltip={false}
      />
      <TrailerManagementForm
        initialValues={initObj}
        visible={visible}
        onClose={async (type) => {
          if (type === 'success') {
            fetchDataFilters();
            setState({ initObj: {}, visible: false });
          } else {
            setState({ initObj: {}, visible: false });
          }
        }}
      />
    </TrailerStyles>
  );
};

export default TrailerManagement;

/* eslint-disable react/jsx-no-bind */
import React, { useState } from 'react';
import { injectIntl } from 'react-intl';
import { isObject } from 'lodash';
import { Row, Col, Form } from 'antd';
import { useParams, useHistory } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { InputPassword } from '@chill/components/uielements/input';

import Notification from '@chill/components/Notification';
import getApiData from '@chill/lib/helpers/apiHelper';
import Button from '@chill/components/uielements/button';
import IntlMessages from '@chill/components/utility/intlMessages';
import CreatepasswordStyleWrapper from '@chill/containers/Pages/ForgotPassword/ForgotPassword.styles';

const passReg = new RegExp(
  '^(((?=.*[a-z])(?=.*[A-Z]))((?=.*[a-z])(?=.*[0-9]))(?=.*[!@#$%^&*])((?=.*[A-Z])(?=.*[0-9])))(?=.{8,})',
);

function Createpassword(props) {
  const { intl } = props;
  const [submitting, setSubmitting] = useState(false);
  const { tmpToken } = useParams();
  const history = useHistory();
  const [form] = Form.useForm();
  const cmData = useSelector((state) => state.App.commonData);

  const logoImg = cmData && cmData.applogo ? cmData.applogo : '/logo.png';
  async function handleForgot(values) {
    setSubmitting(true);
    try {
      const res = await getApiData('user/set-password', values, 'POST');
      setSubmitting(false);
      if (res.success) {
        Notification('success', res.message);
        form.resetFields();
        let rUrl = 'signin';
        if (isObject(res.data) && res.data.user_type === 'Admin') {
          rUrl = 'administrator';
        }
        history.replace(`/${rUrl}`);
      } else {
        Notification('error', res.message);
      }
    } catch (err) {
      setSubmitting(false);
      Notification('error');
    }
  }

  function handleSubmit(vals) {
    const values = { ...vals };
    values.hash_key = tmpToken || '';
    handleForgot(values);
  }

  return (
    <CreatepasswordStyleWrapper className="isoForgotPassPage">
      <div className="isoFormContentWrapper">
        <div className="isoFormContent">
          <div className="isoLogoWrapper">
            <img src={logoImg} alt="Logo" className="logo" />
          </div>

          <div className="isoFormHeadText">
            <h3>
              <IntlMessages id="common.createPssd" />
            </h3>
            <p>
              <IntlMessages id="common.enterPssd" />
            </p>
          </div>
          <Form onFinish={handleSubmit}>
            <div className="isoForgotPassForm">
              <div className="isoInputWrapper">
                <Row>
                  <Col xs={24}>
                    <Form.Item
                      name="new_password"
                      rules={[
                        {
                          required: true,
                          message: <IntlMessages id="req.newPssd" />,
                        },
                        () => ({
                          validator(rule, value) {
                            const errorMsg = <IntlMessages id="err.password" />;
                            if (value) {
                              if (passReg.test(value)) {
                                return Promise.resolve();
                              }
                              return Promise.reject(errorMsg);
                            }
                            return Promise.resolve();
                          },
                        }),
                      ]}
                      hasFeedback
                    >
                      <InputPassword
                        placeholder={intl.formatMessage({
                          id: 'account.newPass',
                        })}
                        type="large"
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Row>
                  <Col xs={24}>
                    <Form.Item
                      dependencies={['password']}
                      name="confirm_new_password"
                      rules={[
                        {
                          required: true,
                          message: <IntlMessages id="err.confirmPssd" />,
                        },
                        ({ getFieldValue }) => ({
                          validator(rule, value) {
                            if (
                              !value ||
                              getFieldValue('new_password') === value
                            ) {
                              return Promise.resolve();
                            }
                            return Promise.reject(
                              <IntlMessages id="err.bothPssd" />,
                            );
                          },
                        }),
                      ]}
                    >
                      <InputPassword
                        placeholder={intl.formatMessage({
                          id: 'account.confirmPssd',
                        })}
                        type="large"
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </div>

              <div className="isoInputWrapper">
                <Button type="primary" loading={submitting} htmlType="submit">
                  <IntlMessages id="common.submit" />
                </Button>
              </div>
            </div>
          </Form>
        </div>
      </div>
    </CreatepasswordStyleWrapper>
  );
}

export default injectIntl(Createpassword);

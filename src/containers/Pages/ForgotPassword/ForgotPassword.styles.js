import styled from 'styled-components';
import { palette } from 'styled-theme';
import bgImage from '@chill/assets/images/background1.png';
import WithDirection from '@chill/lib/helpers/rtl';

const ForgotPasswordStyleWrapper = styled.div`
  width: 100%;
  min-height: 100vh;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  background: url(${bgImage}) no-repeat center center;
  background-size: cover;

  .isoFormContentWrapper {
    width: 380px;
    overflow-y: auto;
    z-index: 10;
    position: relative;

    & .subTxt {
      width: 100%;
      display: flex;
      justify-content: center;
      margin-top: 25px;
      & a {
        text-align: center;
      }
      & .link {
        color: ${palette('primary', 0)};
        cursor: pointer;
      }
    }

    @media only screen and (max-width: 580px) {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      bottom: 0;
      width: 100%;
    }
  }

  .isoFormContent {
    min-height: 100%;
    display: flex;
    flex-direction: column;
    padding: 30px;
    position: relative;
    background-color: #ffffff;
    border-radius: 5px;

    @media only screen and (max-width: 767px) {
      width: 100%;
      border-radius: 0px;
    }

    .isoLogoWrapper {
      width: 100%;
      display: flex;
      margin-bottom: 30px;
      justify-content: center;
      & .logo {
        height: 60px;
      }
      a {
        font-size: 24px;
        font-weight: 300;
        line-height: 1;
        text-transform: uppercase;
        color: ${palette('secondary', 2)};
      }
    }

    .isoFormHeadText {
      width: 100%;
      display: flex;
      flex-direction: column;
      margin-bottom: 15px;
      justify-content: center;

      h3 {
        font-size: 14px;
        font-weight: 500;
        line-height: 1.2;
        margin: 0 0 7px;
        color: ${palette('text', 0)};
      }

      p {
        font-size: 13px;
        font-weight: 400;
        line-height: 1.2;
        margin: 0;
        color: ${palette('text', 2)};
      }
    }

    .isoForgotPassForm {
      width: 100%;
      display: flex;
      flex-shrink: 0;
      flex-direction: column;

      .isoInputWrapper {
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }

        input {
          &::-webkit-input-placeholder {
            color: ${palette('text', 2)};
          }

          &:-moz-placeholder {
            color: ${palette('text', 2)};
          }

          &::-moz-placeholder {
            color: ${palette('text', 2)};
          }
          &:-ms-input-placeholder {
            color: ${palette('text', 2)};
          }
        }

        button {
          height: 42px;
          width: 100%;
          font-weight: 500;
          font-size: 13px;
        }
      }
    }
  }
`;

export default WithDirection(ForgotPasswordStyleWrapper);

/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable react/jsx-no-bind */
/* eslint-disable no-plusplus */
/* eslint-disable no-param-reassign */
/* eslint-disable no-shadow */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import React, { useEffect, useState } from 'react';

import { injectIntl } from 'react-intl';

/**
 *
 * @module appUrl
 */

function appUrl() {
  const { userAgent } = window.navigator;
  const getOSName = () => {
    if (userAgent.indexOf('Android') !== -1) {
      return 'Android';
    }
    if (
      userAgent.indexOf('iPhone') !== -1 ||
      userAgent.indexOf('iPad') !== -1
    ) {
      return 'iOS';
    }
    if (userAgent.indexOf('Windows') !== -1) {
      return 'Windows';
    }
    if (userAgent.indexOf('Mac OS X') !== -1) {
      return 'Mac OS X';
    }
    if (userAgent.indexOf('Linux') !== -1) {
      return 'Linux';
    }
    return 'Unknown OS';
  };

  const osName = getOSName();

  console.log(`Operating system name: ${osName}`);

  useEffect(() => {
    const timeout = setTimeout(() => {
      window.location.replace(
        osName === 'Android'
          ? 'https://play.google.com/store/apps/details?id=com.chillbaby&pli=1'
          : osName === 'iOS'
          ? 'https://apps.apple.com/th/app/babyauto/id1558988820'
          : 'https://apps.apple.com/th/app/babyauto/id1558988820',
      );
    }, 3000);

    return () => clearTimeout(timeout);
  }, []);

  return (
    // eslint-disable-next-line react/style-prop-object
    <div
      style={{
        marginLeft: 20,
        marginTop: 30,
        fontFamily: 'Helvetica-Bold',
        fontSize: 18,
      }}
    >
      Will redirect in few seconds...
    </div>
  );
}

export default injectIntl(appUrl);

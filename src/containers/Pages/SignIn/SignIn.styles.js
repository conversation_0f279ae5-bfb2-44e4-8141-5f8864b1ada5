import styled from 'styled-components';
import { palette } from 'styled-theme';
import bgImage from '@chill/assets/images/background1.svg';
import WithDirection from '@chill/lib/helpers/rtl';

const SignInStyleWrapper = styled.div`
  width: 100%;
  min-height: 100vh;
  position: relative;
  background: ${({ mobile }) =>
    mobile ? 'none' : `url(${bgImage}) no-repeat center center`};
  background-size: cover;
  display: flex;
  flex-direction: column;

  & .headerDivStyle {
    padding: 15px 40px;
    z-index: 1;
  }

  & .rightAlignDiv {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  @media only screen and (max-width: 575px) {
    .rightAlignDiv {
      display: none;
      //   justify-content: center;
      //   margin-top: 10px;
      //   & > span {
      //     text-align: center;
      //   }
    }
  }

  & .isoLoginContentWrapper {
    width: 100%;
    flex: 1;
    display: flex;
    align-items: center;

    & .subTxt {
      width: 100%;
      display: flex;
      justify-content: center;
      margin-top: 25px;
      & a {
        text-align: center;
      }
    }

    .boxWrapper {
      background: rgba(255, 255, 255, 1);
      border-radius: 13px;
      padding: 30px 40px 50px;
      min-width: 400px;
      box-shadow: 10px 10px 10px 10px #cfd1d5;
      margin-top: 30px;

      & .shadowb {
        box-shadow: '1px 3px 1px #9E9E9E';
        min-width: 300px;
      }

      & .titleTextStyle {
        display: flex;
        width: 100%;
        align-items: center;
        justify-content: center;
        & > span {
          font-size: 20px;
          color: #3198f7;
          margin-bottom: 20px;
          font-weight: bold;
        }
      }

      & .forgotPasswordStyle {
        @media (min-width: 426px) {
          position: absolute;
          top: 4px;
          right: 0;
          margin: 0px;
        }
        cursor: pointer;
        margin-top: -10px;
        margin-bottom: 10px;
        & > span {
          font-size: 13px;
          color: #3198f7;
          font-weight: 500;
          @media only screen and (max-width: 335px) {
            font-size: 11px;
          }
        }
      }

      & .btnStyle {
        width: 100%;
        height: 50px;
        font-weight: 500;
        margin-top: 20px;
        background: linear-gradient(
          138deg,
          rgb(20, 135, 255) 12%,
          rgb(41, 239, 196) 90%
        );
      }
    }

    .backToLoginStyle {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 15px;
      cursor: pointer;
      & > span {
        color: #3198f7;
        font-weight: 500;
      }
    }

    .loginViewStyle {
      width: 100%;
      margin-left: 60px;
      flex-direction: column;
    }

    .bottomTextStyle {
      color: #000;
      margin-top: 20px;
      & > span {
        font-weight: 100;
        &:nth-child(2) {
          font-weight: 600;
        }
      }
    }

    .centerAlignDiv {
      display: flex;
      justify-content: center;
      min-width: 400px;
    }

    @media only screen and (max-width: 575px) {
      .boxWrapper {
        margin: 0px auto;
        margin-top: 20px;
      }
      .loginViewStyle {
        margin: 0px auto;
        margin-top: 100px;
      }
      .bottomTextStyle {
        text-align: center;
      }
      .responsiveCenterAlign {
        display: flex;
        justify-content: center;
      }
    }

    @media only screen and (max-width: 425px) {
      .boxWrapper {
        min-width: auto;
      }
      .centerAlignDiv {
        min-width: auto;
      }
    }

    // @media only screen and (max-width: 580px) {
    //   position: absolute;
    //   top: 0;
    //   right: 0;
    //   left: 0;
    //   bottom: 0;
    //   width: 100%;
    // }
  }

  .isoLoginContent {
    min-height: 100%;
    display: flex;
    flex-direction: column;
    padding: 30px;
    position: relative;
    background-color: #ffffff;
    border-radius: 5px;

    &.auth0Content {
      width: 300px;
      border-radius: 6px;
      min-height: 300px;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;
      background: #f9f9f9;
      margin: 0 auto;
    }

    @media only screen and (max-width: 767px) {
      width: 100%;
      border-radius: 0px;
    }

    .isoLogoWrapper {
      width: 100%;
      display: flex;
      margin-bottom: 30px;
      justify-content: center;
      flex-shrink: 0;

      & .logo {
        height: 60px;
      }
      a {
        font-size: 24px;
        font-weight: 300;
        line-height: 1;
        text-transform: uppercase;
        color: ${palette('secondary', 2)};
      }
    }

    .isoSignInForm {
      width: 100%;
      display: flex;
      flex-shrink: 0;
      flex-direction: column;

      button {
        font-weight: 500;
        width: 100%;
        height: 42px;
        border: 0;
      }

      .loginTypeMain {
        margin-bottom: 40px;
        display: flex;
        justify-content: center;
      }

      .isoInputWrapper {
        margin-bottom: 15px;

        &:last-of-type {
          margin-bottom: 0;
        }

        input {
          font-size: 14px;
          &::-webkit-input-placeholder {
            color: ${palette('text', 2)};
          }

          &:-moz-placeholder {
            color: ${palette('text', 2)};
          }

          &::-moz-placeholder {
            color: ${palette('text', 2)};
          }
          &:-ms-input-placeholder {
            color: ${palette('text', 2)};
          }
        }
      }

      .isoHelperText {
        font-size: 12px;
        font-weight: 400;
        line-height: 1.2;
        color: ${palette('grayscale', 1)};
        padding-left: ${(props) =>
          props['data-rtl'] === 'rtl' ? 'inherit' : '13px'};
        padding-right: ${(props) =>
          props['data-rtl'] === 'rtl' ? '13px' : 'inherit'};
        margin: 15px 0;
        position: relative;
        display: flex;
        align-items: center;

        &:before {
          content: '*';
          color: ${palette('error', 0)};
          padding-right: 3px;
          font-size: 14px;
          line-height: 1;
          position: absolute;
          top: 2px;
          left: ${(props) => (props['data-rtl'] === 'rtl' ? 'inherit' : '0')};
          right: ${(props) => (props['data-rtl'] === 'rtl' ? '0' : 'inherit')};
        }
      }

      .isoHelperWrapper {
        margin-top: 35px;
        flex-direction: column;
      }

      .isoOtherLogin {
        padding-top: 40px;
        margin-top: 35px;
        border-top: 1px dashed ${palette('grayscale', 2)};

        > a {
          display: flex;
          margin-bottom: 10px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        button {
          width: 100%;
          height: 42px;
          border: 0;
          font-weight: 500;

          &.btnFacebook {
            background-color: #3b5998;

            &:hover {
              background-color: darken(#3b5998, 5%);
            }
          }

          &.btnGooglePlus {
            background-color: #dd4b39;
            margin-top: 15px;

            &:hover {
              background-color: darken(#dd4b39, 5%);
            }
          }

          &.btnAuthZero {
            background-color: #e14615;

            &:hover {
              background-color: darken(#e14615, 5%);
            }
          }

          &.btnFirebase {
            background-color: ${palette('color', 5)};
            margin-top: 15px;

            &:hover {
              background-color: ${palette('color', 6)};
            }
          }

          &.btnAccountKit {
            ${'' /* background-color: rgb(150, 189, 235); */} margin-top: 15px;

            &:hover {
              ${'' /* background-color: ${palette('color', 6)}; */};
            }
          }
        }
      }

      .isoForgotPass {
        font-size: 12px;
        color: ${palette('text', 3)};
        margin-bottom: 10px;
        text-decoration: none;

        &:hover {
          color: ${palette('primary', 0)};
        }
      }

      button {
        font-weight: 500;
      }
    }
  }
  .mainLogo {
    text-align: center;
    min-width: 400px;
    // @media (max-width: 575px) {
    //   display: flex;
    //   justify-content: center;
    // }
  }
  .text {
    padding-right: 12px;
    @media (max-width: 575px) {
      padding-right: 0px;
    }
  }
  .lang {
    @media (max-width: 575px) {
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
      margin: 20px 0px;
    }
    @media (min-width: 575px) and (max-width: 767px) {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 20px 0px;
    }
  }
`;

export default WithDirection(SignInStyleWrapper);

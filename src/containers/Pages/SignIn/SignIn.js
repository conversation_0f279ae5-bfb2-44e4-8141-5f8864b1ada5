/* eslint-disable react/jsx-no-bind */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import React, { useEffect, useState } from 'react';
import { Col, Row, Form, Popover, Menu, Empty } from 'antd';
import { isEmpty, find, isObject } from 'lodash';
import { Redirect, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import authAction from '@chill/redux/auth/actions';
import appAction from '@chill/redux/app/actions';
import languageActions from '@chill/redux/languageSwitcher/actions';
import IntlMessages from '@chill/components/utility/intlMessages';
import logo from '@chill/assets/images/logo1.png';
import CButton from '@chill/components/uielements/CButton';
import Input, { InputPassword } from '@chill/components/uielements/input';
import Notification from '@chill/components/Notification';
import getApiData from '@chill/lib/helpers/apiHelper';
import { DownOutlined } from '@ant-design/icons';
import SignInStyleWrapper from './SignIn.styles';

// TODO: Add language in below array whenever new language's JSON file added
const allLangs = [
  { lang_code: 'en', name: 'English', apiKey: 'EN' },
  { lang_code: 'es', name: 'Spanish', apiKey: 'ES' },
  { lang_code: 'fr', name: 'French', apiKey: 'FR' },
  { lang_code: 'zh', name: 'Chinese', apiKey: 'ZH' },
  { lang_code: 'pt', name: 'Portuguese', apiKey: 'PT' },
  { lang_code: 'de', name: 'German', apiKey: 'DE' },
  { lang_code: 'ru', name: 'Russian', apiKey: 'RU' },
];

const { login } = authAction;
const { clearMenu } = appAction;

/**
 *
 * @module SignIn
 */
function SignIn() {
  const dispatch = useDispatch();
  const location = useLocation();
  const { idToken: isLoggedIn, error } = useSelector((state) => state.Auth);
  const { language: cLang } = useSelector((state) => state.LanguageSwitcher);
  const loading = useSelector((state) => state.Auth.loading);
  const [visible, setVisiblity] = useState(false);
  const [language, setLanguage] = useState('English');
  const [langCode, setLangCode] = useState('en');
  const [forgotType, setForgotType] = useState(false);
  const [btnLoader, setBtnLoader] = useState(false);
  const langs = allLangs;
  const { from } = location.state || { from: { pathname: '/dashboard' } };
  const [form] = Form.useForm();
  const signinError = isObject(error) && !isEmpty(error) ? error : null;
  const messageArray = require(`@chill/config/translation/locales/${cLang}.json`);
  const { view } = useSelector((state) => state.App);

  useEffect(() => {
    if (!isLoggedIn || signinError) {
      if (signinError) {
        Notification('error', signinError.message);
      }
    }
  }, [isLoggedIn, signinError]);

  useEffect(() => {
    const currentLanguage = find(allLangs, (ln) => ln.lang_code === cLang);
    setLanguage(currentLanguage.name);
    setLangCode(currentLanguage.lang_code);
  }, [cLang]);

  // this function sends request for change password
  /** this function sends request for change password
   * @function forgotPassword
   * @param {object} data username
   */
  async function forgotPassword(data) {
    setBtnLoader(true);
    try {
      const response = await getApiData(
        'user/forgot-password-admin',
        data,
        'POST',
      );

      if (response.success) {
        form.resetFields();
        Notification('success', response.message);
        setForgotType(false);
      } else {
        Notification('error', response.message);
      }
      setBtnLoader(false);
    } catch (err) {
      console.log('err ===', err);
      setBtnLoader(false);
    }
  }

  // this function for login
  /** this function sets language detail
   * @function handleLogin
   * @param {object} data username, password
   */
  async function handleLogin(values) {
    dispatch(clearMenu());
    if (forgotType) {
      forgotPassword(values);
    } else {
      const data = values;
      data.type = 'site';
      dispatch(login(data));
    }
  }

  const handleSubmit = (vals) => {
    const values = { ...vals };
    handleLogin(values);
  };

  // this function handles langauge pop up
  function handleVisibleChange() {
    setVisiblity((vis) => !vis);
  }

  // this function sets language detail in state and redux
  /** this function sets language detail
   * @function changeLang
   * @param {object} data lang_code
   */
  function changeLang(lngCode) {
    // dispatch(languageActions.changeLanguage(lngCode));
    const lName = find(allLangs, (ln) => ln.lang_code === lngCode);
    setLanguage(lName.name);
    setLangCode(lngCode);
    handleVisibleChange();
    if (isObject(lName) && !isEmpty(lName)) {
      localStorage.setItem('language', lName.lang_code);
      dispatch(languageActions.changeLanguage(lName.lang_code));
    }
  }

  // this function handles langauges
  function handleMenuClick({ key }) {
    if (key) {
      changeLang(key);
      if (langCode === 'ar') window.location.reload();
    }
  }

  if (isLoggedIn) {
    return <Redirect to={from} />;
  }

  // this function renders language list
  function renderLangs() {
    if (!isEmpty(langs)) {
      return langs.map((ct) => (
        <Menu.Item key={ct.lang_code} apikey={ct.apiKey}>
          {ct.name}
        </Menu.Item>
      ));
    }
    return <Empty description={<IntlMessages id="common.noLang" />} />;
  }

  // this function render content of langauge
  const content = (
    <Menu
      selectedKeys={[langCode]}
      className="langMenu"
      onSelect={handleMenuClick}
    >
      {renderLangs()}
    </Menu>
  );

  const langSelector = () => {
    return (
      <span className="lang">
        <Popover
          content={content}
          trigger="click"
          visible={visible}
          onVisibleChange={handleVisibleChange}
          arrowPointAtCenter
          placement="bottom"
        >
          <span style={{ marginLeft: 12, marginRight: 12, cursor: 'pointer' }}>
            {language}
            <DownOutlined style={{ fontSize: 12, paddingLeft: 5 }} />
          </span>
        </Popover>
        <span className="text">
          <IntlMessages id="Check out Chillbaby technologies product site here" />
        </span>
        <a
          href="https://www.chillbabytechnologies.com/"
          target="_blank"
          rel="noopener noreferrer"
        >
          <IntlMessages id="Visit Website" />
        </a>
      </span>
    );
  };

  return (
    <SignInStyleWrapper className="isoSignInPage">
      <Row className="headerDivStyle">
        <Col xs={24} sm={12} md={16} className="rightAlignDiv">
          {view === 'MobileView' ? null : langSelector()}
        </Col>
      </Row>
      <div className="isoLoginContentWrapper">
        <Row className="loginViewStyle">
          <Col xs={22} sm={14} md={10} lg={10} xl={5} className="mainLogo">
            <img src={logo} alt="logo" style={{ width: '250px' }} />
          </Col>
          <Col xs={22} sm={14} md={10} lg={10} xl={5} className="boxWrapper">
            <div className="titleTextStyle">
              {forgotType ? (
                <IntlMessages id="FORGOT PASSWORD" />
              ) : (
                <IntlMessages id="Log in" />
              )}
            </div>
            <Form form={form} onFinish={handleSubmit} layout="vertical">
              <Form.Item
                label={<IntlMessages id="antTable.title.email" />}
                name="username"
                rules={[
                  {
                    type: 'email',
                    required: true,
                    message: <IntlMessages id="req.email" />,
                  },
                ]}
                style={{ marginBottom: 20 }}
              >
                <Input name="email" placeholder={messageArray['Enter email']} />
              </Form.Item>
              {forgotType ? null : (
                <div style={{ position: 'relative' }}>
                  <Form.Item
                    label={<IntlMessages id="input.password" />}
                    name="password"
                    rules={[
                      {
                        required: true,
                        message: <IntlMessages id="req.password" />,
                      },
                      {
                        max: 50,
                        message: <IntlMessages id="err.max.50char" />,
                      },
                      {
                        whitespace: true,
                        message: <IntlMessages id="err.blankSpace" />,
                      },
                    ]}
                    style={{ marginBottom: 20 }}
                  >
                    <InputPassword
                      placeholder={messageArray['Enter password']}
                      style={{ height: 40 }}
                    />
                  </Form.Item>
                  <div
                    className="forgotPasswordStyle"
                    onClick={() => {
                      setForgotType(true);
                      form.resetFields();
                    }}
                  >
                    <IntlMessages id="Forgot Password?" />
                  </div>
                </div>
              )}
              <CButton
                type="primary"
                loading={loading || btnLoader}
                disabled={loading || btnLoader}
                className="btnStyle"
                htmlType="submit"
              >
                {forgotType ? (
                  <IntlMessages id="common.submit" />
                ) : (
                  <IntlMessages id="common.login" />
                )}
              </CButton>
              {forgotType ? (
                <div
                  className="backToLoginStyle"
                  onClick={() => {
                    setForgotType(false);
                    form.resetFields();
                  }}
                >
                  <IntlMessages id="Back to login?" />
                </div>
              ) : null}
            </Form>
          </Col>
          <Row className="responsiveCenterAlign">
            <Col
              xs={22}
              sm={14}
              md={10}
              lg={10}
              xl={5}
              className="centerAlignDiv"
            >
              <span className="bottomTextStyle">
                <IntlMessages id="INNOVATIVE" /> <IntlMessages id="IOT" />{' '}
                <IntlMessages id="TECHNOLOGY SOFTWARE" />
              </span>
            </Col>
          </Row>
        </Row>
      </div>
      <div className="langselector">
        {view === 'MobileView' ? langSelector() : null}
      </div>
    </SignInStyleWrapper>
  );
}

export default SignIn;

/* eslint-disable react/jsx-no-bind */
/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable react/jsx-props-no-spreading */
import React, { useEffect, useState } from 'react';
import { Drawer, Form, Input, Row } from 'antd';
import { useSelector } from 'react-redux';
import IntlMessages from '@chill/components/utility/intlMessages';
import { BottomViewWrapper } from '@chill/assets/styles/drawerFormStyles';
import getApiData from '@chill/lib/helpers/apiHelper';
import Button from '@chill/components/uielements/CButton';
import Notification from '@chill/components/Notification';
import useResetFormOnClose from '@chill/lib/hooks/useResetForm';
import CommisionStyle from './Commision.styles';

export default function CommissionForm(props) {
  const {
    visible,
    setState = () => {},
    updateData,
    initialValues,
    edit,
    setEdit = () => null,
  } = props;
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);
  const initVal = initialValues || {};
  const [btnLoader, setBtnLoader] = useState(false);
  const [form] = Form.useForm();

  const [commErr, setCommErr] = useState('');
  useEffect(() => {
    if (!edit) {
      form.resetFields();
    }
  }, [visible]);

  useResetFormOnClose({ visible, initVal, form });

  const onClose = (type = '', data = {}) => {
    form.resetFields();
    setState((p) => ({ ...p, visible: false, view: false }));
    setEdit(false);
    if (type === 'success') {
      updateData(data);
    } else {
      setState((p) => ({ ...p, initObj: {} }));
    }
  };

  async function addBrand(values) {
    const url = edit
      ? 'accounting-system/edit-commission-rate'
      : 'accounting-system/edit-commission-rate';
    try {
      const response = await getApiData(url, values, 'POST');
      if (response.success) {
        Notification('success', response.message);
        onClose('success', response.data);
      } else {
        Notification('error', response.message);
      }
      setBtnLoader(false);
    } catch (error) {
      console.log(error);
      setBtnLoader(false);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  function validate(values) {
    const obj = values;
    if (obj.commission_rate > 100) {
      setCommErr('Commiossion rate can not greater than 100');
      return;
    }
    // obj.type = "brand";
    if (edit) obj.user_id = initVal.id;
    obj.brand_id = initVal.brand_id;
    console.log('brand iddd---', obj);
    setBtnLoader(true);
    addBrand(obj);
  }

  return (
    <Drawer
      title={<IntlMessages id={edit ? 'edit.commision' : 'edit.commision'} />}
      width={450}
      style={{ height: window.innerHeight - 60, marginTop: 70 }}
      onClose={onClose}
      visible={visible}
      bodyStyle={{ paddingBottom: 80 }}
      destroyOnClose
      form={form}
    >
      <CommisionStyle>
        <Form
          layout="vertical"
          form={form}
          onFinish={validate}
          initialValues={initVal}
        >
          <Form.Item
            className="formLabel"
            label={<IntlMessages id="label.commision" />}
            name="commission_rate"
            rules={[
              {
                required: true,
                message: <IntlMessages id="req.commision" />,
              },
            ]}
          >
            <Input type="number" onChange={() => setCommErr('')} />
          </Form.Item>
          <span style={{ color: 'red' }}>{commErr !== '' ? commErr : ''}</span>
          <BottomViewWrapper className="bottomBtnWrapper">
            <Row gutter={12} style={{ width: '100%' }}>
              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={btnLoader}
                  disabled={btnLoader}
                  style={{ marginRight: 20 }}
                >
                  <IntlMessages
                    id={edit ? 'common.changes' : 'common.submit'}
                  />
                </Button>
              </Form.Item>
              <Form.Item>
                <div
                  className="cancelBtnStyle"
                  onClick={onClose}
                  role="button"
                  onKeyPress=""
                  tabIndex="-1"
                  style={{ cursor: 'pointer' }}
                >
                  <IntlMessages id="common.cancel" />
                </div>
              </Form.Item>
            </Row>
          </BottomViewWrapper>
        </Form>
      </CommisionStyle>
    </Drawer>
  );
}

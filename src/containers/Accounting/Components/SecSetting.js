import React from 'react';
import { Badge, List, Tag, Tooltip } from 'antd';
import { map } from 'lodash';
import { Link } from 'react-router-dom';
import IntlMessages from '@chill/components/utility/intlMessages';
import Button from '@chill/components/uielements/button';
import LinkBox from '@chill/components/LinkBox';
import theme from '@chill/config/theme/default';
import { securityData } from '../config';
import IsoWidgetsWrapper from '../WidgetsWrapper';
import ReportsWidget from '../Report/ReportWidget';

function SecSetting(props) {
  const { dashData = {}, isTeamUser = false } = props;

  function renderSecActions(item) {
    const actions = [];
    if (item.is_enabled) {
      actions.push(
        <Tag color="green" style={{ marginRight: 0 }}>
          <IntlMessages id="common.enabled" />
        </Tag>,
      );
    } else if (!isTeamUser) {
      actions.push(
        <Link to="/dashboard/account-settings?active=security">
          <Button size="small">
            <IntlMessages id="common.enable" />
          </Button>
        </Link>,
      );
    } else {
      actions.push(
        <Tag color="red" style={{ marginRight: 0 }}>
          <IntlMessages id="dashboard.notEnabled" />
        </Tag>,
      );
    }
    return actions;
  }

  /**
   * User's Security Settings lists
   */
  function renderSecurityList() {
    const sList = dashData?.security_setting || {};
    return map(sList, (item, key) => {
      const vData = securityData[key];
      return (
        <List.Item key={key} actions={renderSecActions(item)}>
          <List.Item.Meta
            title={
              <span>
                <IntlMessages id={vData.title} />{' '}
                {item.is_enabled && item.docs > 0 ? (
                  <LinkBox link={isTeamUser ? '' : '/dashboard/documents'}>
                    <Tooltip
                      title={
                        <IntlMessages
                          id="dashboard.enabledDoc"
                          values={{ num: item.docs }}
                        />
                      }
                    >
                      <Badge
                        style={{
                          backgroundColor: theme.colors.primaryColor,
                          fontWeight: 'bold',
                        }}
                        count={item.docs}
                      />
                    </Tooltip>
                  </LinkBox>
                ) : null}
              </span>
            }
          />
        </List.Item>
      );
    });
  }

  return (
    <IsoWidgetsWrapper style={{ height: '100%' }}>
      <ReportsWidget
        label={<IntlMessages id="dashboard.secureClsDocs" />}
        details={<IntlMessages id="dashboard.ssDesc" />}
        style={{ height: '100%' }}
        widgetClassName="flex1"
      >
        <List>{renderSecurityList()}</List>
      </ReportsWidget>
    </IsoWidgetsWrapper>
  );
}

export default SecSetting;

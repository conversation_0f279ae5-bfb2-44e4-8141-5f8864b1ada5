import React, { useEffect, useState } from 'react';
import { Col, Row, Select } from 'antd';
import basicStyle from '@chill/assets/styles/constants';
import IntlMessages from '@chill/components/utility/intlMessages';
import Notification from '@chill/components/Notification';
import { isEmpty, isObject, map } from 'lodash';
import getApiData from '@chill/lib/helpers/apiHelper';
import { useHistory } from 'react-router';
import { injectIntl } from 'react-intl';
import IsoWidgetsWrapper from '../WidgetsWrapper';
import { TeamSelectWrapper } from '../Widgets.styles';

const { Option } = Select;

function TeamSelect(props) {
  const { selUserId, intl } = props;
  const history = useHistory();

  const { rowStyle, colStyle } = basicStyle;

  const [uState, setuState] = useState({
    users: [],
    loading: false,
  });

  function filterFunc(input, option) {
    const opObj = option?.obj || {};

    // Filter with name and email both
    if (!isEmpty(opObj)) {
      const email = opObj.email ? opObj.email.toLowerCase() : '';
      const name = opObj.name ? opObj.name.toLowerCase() : '';
      return (
        email.indexOf(input.toLowerCase()) >= 0 ||
        name.indexOf(input.toLowerCase()) >= 0
      );
    }

    return false;
  }

  /** Function to fetch team members  */
  async function getTeamMembers() {
    setuState((pre) => ({ ...pre, loading: true }));
    try {
      const res = await getApiData('department/get-team-user');
      if (res.success && res.data) {
        setuState((pre) => ({
          ...pre,
          users: res.data || [],
          loading: false,
        }));
      } else {
        setuState((pre) => ({ ...pre, loading: false }));
        Notification('error', res.message);
      }
    } catch (err) {
      setuState((pre) => ({ ...pre, loading: false }));
      Notification('error');
    }
  }

  useEffect(() => {
    getTeamMembers();
  }, []);

  return (
    <Row style={rowStyle} gutter={0} justify="start">
      <Col xs={24} style={colStyle}>
        <IsoWidgetsWrapper>
          <TeamSelectWrapper>
            <Select
              loading={uState.loading}
              placeholder={<IntlMessages id="team.member" />}
              size="large"
              style={{ minWidth: 200 }}
              value={selUserId || 'all'}
              onChange={(_, o) => {
                const q =
                  isObject(o?.obj) && !isEmpty(o?.obj)
                    ? `?user_id=${o?.obj.user_id}&email=${o?.obj.email}`
                    : '';
                history.replace(`/dashboard${q}`);
              }}
              showSearch
              filterOption={filterFunc}
              optionLabelProp="name"
              defaultValue="all"
            >
              <Option
                name={intl.formatMessage({ id: 'dashboard.you' })}
                key="all"
                value="all"
              >
                <span className="block fw550">
                  <IntlMessages id="dashboard.you" />
                </span>
                <span>
                  <IntlMessages id="dashboard.yourData" />
                </span>
              </Option>
              {map(uState.users, (val) => {
                return (
                  <Option
                    name={val.name}
                    key={val.user_id}
                    value={val.user_id}
                    obj={val}
                  >
                    <span className="block fw550">{val.name}</span>
                    <span>{val.email}</span>
                  </Option>
                );
              })}
            </Select>
          </TeamSelectWrapper>
        </IsoWidgetsWrapper>
      </Col>
    </Row>
  );
}

export default injectIntl(TeamSelect);

import styled from 'styled-components';
import withDirection from '@chill/lib/helpers/rtl';
import { palette } from 'styled-theme';

const WidgetWrapper = styled.div`
  margin: 0 10px;
  width: ${(props) => props.width}px;
  margin-top: ${(props) => props.gutterTop}px;
  margin-right: ${(props) => props.gutterRight}px;
  margin-bottom: ${(props) => props.gutterBottom}px;
  margin-left: ${(props) => props.gutterLeft}px;
  padding: ${(props) => props.padding};
  background-color: ${(props) => props.bgColor}px;

  &.h100 {
    height: 100%;
    & > a {
      display: inline-block;
      height: 100%;
      width: 100%;
      & > .isoSaleWidget {
        height: 100%;
      }
    }
    & > .isoSaleWidget {
      height: 100%;
    }
  }

  @media only screen and (max-width: 1440px) {
    margin: 0 10px;
  }

  @media only screen and (max-width: 767px) {
    margin-right: 0 !important;
  }
  .mainDiv {
    @media (max-width: 720px) {
      padding: 0px 16px 0px 0px;
    }
  }
  .osChart {
    height: 100%;
    background-color: #fff;
    padding: 16px;
    border-radius: 12px;
  }
  .popMainDiv {
    display: flex;
    flex-direction: column;
  }
  .isoDropdownLink {
    color: #000;
    padding: 8px 0px;
    cursor: pointer;
  }
  .campaignsList > div:nth-child(1) {
    padding: 20px 16px 0px;
  }

  .chartLegend {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }
  .legendRow {
    display: flex;
    align-items: center;
    margin-right: 40px;
  }
  .legendColor {
    width: 14px;
    height: 14px;
    border-radius: 10px;
    margin-right: 8px;
  }
  .legend {
    display: flex;
    align-items: center;
  }
  .legendText {
    font-size: 14px;
    color: #233238;
  }
  .mapListDiv {
    padding: 10px 20px;
    @media (max-width: 400px) {
      height: 220px;
      overflow: scroll;
      padding: 10px 10px 20px 10px;
    }
  }
  .map > svg {
    // height: 100%;
    // padding: 50px 0px 0px;
    margin-top: 120px;
    overflow: initial;
    & > g:nth-child(1) > path:nth-child(7) {
      display: none;
    }
    @media (max-width: 575px) {
      margin-top: 60px;
    }
  }
`;

const WidgetBox = styled.div`
  width: 100%;
  height: ${(props) => (props.height ? `${props.height}px` : '100%')};
  padding: ${(props) => (props.padding ? props.padding : '30px')};
  background-color: #ffffff;
  border: 1px solid ${palette('border', 2)};

  canvas {
    width: 100% !important;
    height: 100% !important;
  }
  .topLinkWrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
  .topLinkCountWrapper {
    border-radius: 12px;
  }
  .topLinktitle {
    font-size: 15px;
    font-weight: 400;
  }
  .topLinkSubTitle {
    font-size: 13px;
    opacity: 0.6;
  }
  .statesTitle {
    font-size: 17px;
    font-weight: 400;
  }
`;
const getAlignContent = (align = 'flex-start') => {
  if (align === 'start') return 'flex-start';
  if (align === 'end') return 'flex-end';
  return align;
};
const WidgetColumn = styled.div`
  align-content: ${(props) => getAlignContent(props.align)};
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  margin-top: ${(props) => props.gutterTop}px;
  margin-right: ${(props) => props.gutterRight}px;
  margin-bottom: ${(props) => props.gutterBottom}px;
  margin-left: ${(props) => props.gutterLeft}px;
  padding: ${(props) => props.padding}px;
  width: ${(props) => props.width}px;
`;

const ListWrapper = withDirection(styled.div`
  & .ant-progress {
    & .ant-progress-text {
      font-size: 18px;
      color: #444 !important;
      font-weight: 550;
    }
  }
  & .ant-list {
    & .ant-list-item-meta-description {
      & .numMain {
        display: flex;
        margin-top: 15px;
        justify-content: space-around;

        & .numContent {
          display: flex;
          align-items: center;
          &:not(last-child) {
            margin-right: 20px;
          }
          & .anticon {
            font-size: 22px;
            margin-right: 5px;

            &.anticon-arrow-up {
              color: ${palette('primary', 0)};
            }
            &.anticon-arrow-down {
              color: ${palette('color', 3)};
            }
          }
        }
      }
    }
  }
`);

const DashboardWrapper = withDirection(styled.div`
  & .wflist {
    & .ant-list-item {
      text-align: center;
      & .ant-list-item-meta-title {
        margin-bottom: 10px;
      }
    }
  }
`);

const MarketingDashboardWrapper = withDirection(styled.div`
  & .wflist {
    & .ant-list-item {
      text-align: center;
      & .ant-list-item-meta-title {
        margin-bottom: 10px;
      }
    }
  }
  .previewText {
    background-color: #fff;
    border-radius: 16px;
    padding: 10px;
    overflow: hidden;
    box-shadow: 0px 0px 0px 1px #dcdcdc;
  }

  & .postCircle {
    width: 30px;
    height: 30px;
    border-radius: 30px;
    background-color: #ccc;
  }

  & .padding {
    padding-left: 5px;
  }

  & .titleStyle {
    font-weight: 500;
    font-size: 14px;
    text-transform: uppercase;
  }

  & .subTitle {
    color: #000;
    font-size: 13px;
    padding-left: 5px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  & .message {
    color: #808080;
    font-size: 12px;
    padding-left: 5px;
    // overflow: hidden;
  }

  .topLinkCountWrapper {
    background-color: #d0d9ef;
    color: #1172ec;
    border-radius: 7px;
    width: 20%;
    text-align: center;
    padding: 5px 2px 5px 2px;
    font-size: 12px;
    @media (max-width: 720px) {
      background-color: #d0d9ef;
      color: #1172ec;
      border-radius: 4px;
      width: 30%;
      text-align: center;
      font-size: 10px;
      padding: 5px 5px 5px 5px;
    }
    @supports (-webkit-line-clamp: 1) {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: initial;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      word-break: break-word;
      // width: auto;
    }
  }
  & .title {
    color: #555;
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 10px;
    text-transform: uppercase;
    @supports (-webkit-line-clamp: 2) {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: initial;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      word-break: break-word;
      // width: auto;
    }
  }
  & .marginWrapper {
    margin-bottom: 20px;
  }
  & .description {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    @supports (-webkit-line-clamp: 3) {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: initial;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      word-break: break-word;
      // width: auto;
    }
  }

  .listNameView {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }

  .listNameViewColum {
    display: flex;
    flex-direction: row;
  }
  .marginTop {
    margin-top: 20px;
  }

  .nameWithImg {
    width: 15px;
    height: 15px;
    background-color: #ec8e8e;
    margin-right: 12px;
    border-radius: 15px;
    margin-left: 20px;
  }

  .thisAc {
    flex-direction: row;
    display: flex;
    justify-content: flex-end;
    margin: 10px;
    margin-top: -30px;
    justify-content: space-between;
  }

  .changeBtn {
    border-radius: 8px;
    font-weight: 500;
    border: 0px;
    color: #fff
    background: linear-gradient(90deg, #039be5 38%, #03a9f4 90%);
    &:hover {
      background: linear-gradient(90deg, #039be5 38%, #03a9f4 90%);
    }
    margin-left: 20px;
    padding: 5px 40px 6px 40px;
    @media (min-width: 300px) and (max-width: 600px) {    
      padding: 7px 20px 6px 20px;
      margin-left: 10px;
    }
    @media (max-width: 280px) {    
      padding: 0px 12px;
      margin-left: 10px;
    }
  }
`);

const TeamSelectWrapper = withDirection(styled.div`
  width: 100%;
  display: flex;
  justify-content: flex-end;
`);

const MessageSingle = styled.div`
  display: flex;
  flex-wrap: wrap;
  padding: 15px 0px;
  margin: 0px 15px;
  align-items: flex-start;
  flex-shrink: 0;

  @media only screen and (max-width: 767px) {
    margin: 10px 0;
  }

  &.loggedUser {
    justify-content: flex-end;
  }
  .messageGravatar {
    width: 45px;
    height: 45px;
    flex-shrink: 0;
    overflow: hidden;
    margin: 0px 15px;

    img {
      border-radius: 50%;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .attchmentDivStyle {
    border: 1px solid #eee;
    border-radius: 15px !important;
    padding: 10px 15px;
    margin-top: 15px;

    .downloadIconStyle {
      font-size: 20px;
      color: #1172ec;
    }
  }
  .messageContent {
    width: 100%;
    display: flex;
    flex-direction: column;
    max-width: calc(100% - 110px);
    flex-shrink: 0;

    .messageContentText {
      position: relative;
      font-size: 12px;
      vertical-align: top;
      display: inline-block;
      ${'' /* overflow: hidden; */} word-break: break-word;

      p {
        margin: 0;
      }

      & .fileInfoContent {
        display: flex;
        // padding: 0px;
        align-items: center;
        & .fileInfo {
          margin: 0px 10px;
          white-space: nowrap;
          width: 150px;
          overflow: hidden;
          text-overflow: ellipsis;
          @media (max-width: 375px) {
            white-space: nowrap;
            width: 140px;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          & > b,
          & > p {
            white-space: nowrap;
            width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: 500;
            font-size: 13px;
            @media (max-width: 375px) {
              white-space: nowrap;
              width: 140px;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
    .msgUserDetail {
      display: flex;
      // align-items: center;
      justify-content: space-between;
      width: 100%;
    }
    .timeStampStyle {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      justify-content: flex-start;
    }
    .dateTextStyle {
      color: #000;
      font-size: 13px;
      font-weight: 500;
    }
    .messageTime {
      font-size: 12px;
      color: ${palette('text', 3)};
      margin-top: 5px;
    }
    &.isUser {
      align-items: flex-end;
      .messageContentText {
        background: ${palette('primary', 1)};
        // color: #ffffff;
        border-radius: 3px 0 3px 3px;

        &:after {
          content: '';
          position: absolute;
          border-style: solid;
          display: block;
          width: 0;
          top: 0;
          bottom: auto;
          left: auto;
          right: -9px;
          border-width: 0px 0 10px 10px;
          border-color: transparent ${palette('primary', 1)};
          margin-top: 0;
        }
      }
      .messageTime {
        margin-left: auto;
      }
    }
    &.notUser {
      align-items: flex-start;

      .messageContentText {
        // background: ${palette('grayscale', 4)};
        color: ${palette('text', 0)};
        border-radius: 0 3px 3px 3px;

        // &:after {
        //   content: '';
        //   position: absolute;
        //   border-style: solid;
        //   display: block;
        //   width: 0;
        //   top: 0;
        //   bottom: auto;
        //   left: -9px;
        //   border-width: 0px 10px 10px 0;
        //   border-color: transparent ${palette('grayscale', 4)};
        //   margin-top: 0;
        // }
      }
      .messageTime {
        margin-right: auto;
      }
    }
  }
`;
export {
  WidgetWrapper,
  WidgetBox,
  WidgetColumn,
  ListWrapper,
  DashboardWrapper,
  TeamSelectWrapper,
  MarketingDashboardWrapper,
  MessageSingle,
};

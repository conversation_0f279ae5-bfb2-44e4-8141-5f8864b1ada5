/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-no-bind */
/* eslint-disable no-prototype-builtins */
/* eslint-disable no-restricted-syntax */
/* eslint-disable no-param-reassign */
/* eslint-disable no-shadow */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import React, { useEffect, useState } from 'react';
import { Row, Col, Spin } from 'antd';
import {
  ClockCircleOutlined,
  DollarCircleOutlined,
  MoreOutlined,
  EditOutlined,
  DownOutlined,
  DownloadOutlined,
} from '@ant-design/icons';
import LayoutWrapper from '@chill/components/utility/layoutWrapper';
import basicStyle from '@chill/assets/styles/constants';
import IntlMessages from '@chill/components/utility/intlMessages';
import { findIndex, isArray, isEmpty } from 'lodash';
import getApiData from '@chill/lib/helpers/apiHelper';

import { injectIntl } from 'react-intl';
import { useSelector } from 'react-redux';
import moment from 'moment';
import LinkBox from '@chill/components/LinkBox';
import TableWrapper from '@chill/assets/styles/AntTables.styles';
import Popover from '@chill/components/uielements/popover';
import Popconfirms from '@chill/components/Feedback/Popconfirm';
import DatePicker from '@chill/components/uielements/datePicker';
import { PDFDownloadLink } from '@react-pdf/renderer';
import AccountingReport from '@chill/components/AccountingReport';
import IsoWidgetsWrapper from './WidgetsWrapper';
import ReportsWidget from './Report/ReportWidget';
import StickerWidget from './Sticker/StickerWidget';
import { DashboardWrapper, WidgetWrapper } from './Widgets.styles';
import { styles } from './config';
import CommissionForm from './CommisionForm';

// import { MessageSingle } from '../../Services/Messages.styles';
/**
 *
 * @module Accounting
 */

function AccountingWidget() {
  // const { intl } = props;

  const { rowStyle, colStyle } = basicStyle;

  const { RangePicker } = DatePicker;
  // const sPer = Number(getStorageData(access));
  // const dashData = dData?.data || {};
  const [dData, setdData] = useState({ data: {}, loading: true });

  const [durationVisible, setDurationVisibility] = useState(false);
  const [actionMenu, setActionMenu] = React.useState(false);
  const [isIndex, setIsIndex] = React.useState([]);
  const [edit, setEdit] = React.useState(false);

  const [duration, setDuration] = useState(
    <IntlMessages id="dashboard.thisweek" />,
  );

  const [cperiodType, setCPeriodType] = useState('this_week');
  const [state, setState] = React.useState({
    loading: false,
  });

  const { visible, initObj } = state;

  const dWidth = window.innerWidth;
  const [performanceChart, setPerformanceChart] = useState({});
  const [allDataArr, setAllDataArr] = useState([]);
  const { language } = useSelector((state) => state.LanguageSwitcher);

  const [fromDate, setFromDate] = useState();
  const [toDate, setToDate] = useState();
  const [stEndDate, setStEndDate] = useState({
    stDate: fromDate,
    endDate: toDate,
  });
  const [dateRange, changeDateRange] = useState(null);
  function handleVisible() {
    setActionMenu((visiblePre) => !visiblePre);
  }
  function handleVisibleChange(item1) {
    setIsIndex(item1.id);
  }
  const returnMomentDateRange = (start, finish) => {
    return [moment(start, 'YYYY-MM-DD'), moment(finish, 'YYYY-MM-DD')];
  };
  const TOP_BOXES = [
    {
      key: 'totalusers',
      text: 'accounting.totalUnitSales',
      icon: <ClockCircleOutlined style={{ color: 'pink' }} />,
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.data) ? dData.data.totalSales : 0,
    },
    {
      key: 'totalRevenue',
      text: 'accounting.totalSalesValue',
      // link: '/contacts',
      icon: <DollarCircleOutlined style={{ color: 'green' }} />,
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.data) ? dData.data.totalSold : 0,
    },
    {
      key: 'weeklySession',
      text: 'accounting.averageConRate',
      icon: <DollarCircleOutlined style={{ color: 'green' }} />,
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.data) ? dData.data.conversion_rate : 0,
    },
    {
      key: 'weeklySession',
      text: 'accounting.allbrandCommission',
      icon: <DollarCircleOutlined style={{ color: 'green' }} />,
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.data) ? dData.data.brand_commission : 0,
    },
  ];

  function disabledDate(current) {
    // Can not select days before today and today
    return current && current > moment().endOf('day');
  }
  function onDateChange(e) {
    if (e) {
      changeDateRange(returnMomentDateRange(e[0], e[1]));
    } else {
      changeDateRange([]);
    }
    if (e != null) {
      const date1 = moment(e[0]).format('YYYY-MM-DD');
      const date2 = moment(e[1]).format('YYYY-MM-DD');

      setStEndDate({ stDate: date1, endDate: date2 });
      console.log('date range---------------------', date1, date2);
      console.log('state ==>', state);
    }
  }

  function durationChange(type) {
    if (type !== 'user') {
      setDurationVisibility((visiblePre) => !visiblePre);
    }
  }

  const content = (index) => (
    <WidgetWrapper>
      <div className="popMainDiv">
        <div className="isoDropdownLink" style={{ cursor: 'pointer' }}>
          <div
            role="button"
            className="isoDropdownLink"
            onKeyPress=""
            tabIndex="-1"
            onClick={() => {
              setState({ ...state, visible: true });
              setIsIndex(null);
            }}
          >
            <EditOutlined style={{ paddingRight: 12, fontSize: 16 }} />
            <IntlMessages id="accounting.editCommission" />
          </div>
          <Popconfirms
            title={<IntlMessages id="Sure to delete?" />}
            okText={<IntlMessages id="DELETE" />}
            cancelText={<IntlMessages id="No" />}
            onConfirm={() => {
              // deleteAccount();
              setIsIndex(null);
            }}
            onCancel={null}
          >
            <PDFDownloadLink
              document={
                <AccountingReport
                  initialValues={allDataArr[index]}
                  campaignData={performanceChart}
                  language={language}
                />
              }
              fileName={`Analytics Report(${moment().format(
                'DD MMM YYYY',
              )}).pdf`}
            >
              {() => (
                <>
                  <DownloadOutlined
                    style={{
                      fontSize: dWidth > 281 ? 20 : 15,
                      marginRight: dWidth > 280 ? 10 : 0,
                    }}
                  />
                  <IntlMessages id="accounting.downloadReport" />
                </>
              )}
            </PDFDownloadLink>
            {/* <DownloadOutlined style={{ paddingRight: 12, fontSize: 16 }} />
            <IntlMessages id="accounting.downloadReport" /> */}
          </Popconfirms>
        </div>
      </div>
    </WidgetWrapper>
  );
  /**
   * Function to fetch dashboard's data
   */
  // async function fetchDashboardData(type = '') {
  //   setdData((pre) => ({ ...pre, loading: type === 'init' }));
  //   try {
  //     const res = await getApiData(
  //       'getDashboardData',
  //       {
  //         user_id: pObj.user_id,
  //         email: pObj.email,
  //       },
  //       'post',
  //     );
  //     if (res.success && res.data) {
  //       const uVerification = res?.data?.user_verification || [];
  //       const verification = map(uVerification, (key, value) => ({
  //         key,
  //         value,
  //       }));
  //       setdData((pre) => ({
  //         ...pre,
  //         data: res.data,
  //         uVerification: verification,
  //         loading: false,
  //       }));
  //     } else {
  //       setdData((pre) => ({ ...pre, loading: false }));
  //     }
  //   } catch (err) {
  //     setdData((pre) => ({ ...pre, loading: false }));
  //   }
  // }

  /** Function to fetch dashboard's data
   * @function getDashboardData
   * @param {object} data customer_overview, user_by_region
   */

  async function getDashboardData(data = {}) {
    data.period_type = cperiodType;
    console.log('sss', stEndDate);
    data.start_end_date = stEndDate;
    try {
      const res = await getApiData(
        'accounting-system/accounting-dashboard',
        // "getDashboardData",
        data,
        'post',
      );
      if (res.success && !isEmpty(res.data)) {
        setPerformanceChart(res?.data);
        setAllDataArr(res?.data?.allData);
        const data = {};
        data.totalSales = res.data.totalSales;
        data.totalSold = res.data.totalSold;
        data.brand_commission = res.data.brand_commission;
        data.conversion_rate = res.data.conversion_rate;
        data.allData = res.data.allData;
        setdData((pre) => ({ ...pre, data, loading: false }));
      } else {
        setdData((pre) => ({ ...pre, loading: false }));
      }
    } catch (err) {
      console.log('err ===', err);
      setdData((pre) => ({ ...pre, loading: false }));
    }
  }

  useEffect(() => {
    const obj = {
      totalSale: '372.83',
      shopify: { sales: 62.53, orderCount: 2 },
    };
    for (const propName in obj) {
      if (obj.hasOwnProperty(propName)) {
        const propValue = obj[propName];
        if (propValue.orderCount) {
          console.log('ddssss', propValue.orderCount);
        }
      }
    }
    getDashboardData();
  }, [cperiodType, stEndDate]);

  const columns = [
    {
      title: <IntlMessages id="No" />,
      dataIndex: 'id',
      rowKey: 'id',
      width: 50,
      className: 'fullname-cell',
      render: (text, data, index) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="No" />
            </span>
            <span className="mobile-lbl-val">{index + 1 || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="marketing.brandName" />,
      dataIndex: 'brand_name',
      rowKey: 'brand_name',
      width: 280,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="marketing.brandName" />
            </span>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <span className="mobile-lbl-val" style={{ marginLeft: 8 }}>
                {text || '-'}
              </span>
            </div>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="accounting.totalUnitSales" />,
      dataIndex: 'totalUnit',
      rowKey: 'totalUnit',
      // width: 150,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="accounting.totalUnitSales" />
            </span>
            <span className="mobile-lbl-val">{text || '0'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="accounting.totalSalesValue" />,
      dataIndex: 'totalSale',
      rowKey: 'totalSale',
      // width: 150,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="accounting.totalSalesValue" />
            </span>
            <span className="mobile-lbl-val">{text || '0'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="accounting.averageConRate" />,
      dataIndex: 'conversion_rate',
      rowKey: 'conversion_rate',
      // width: 150,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="accounting.averageConRate" />
            </span>
            <span className="mobile-lbl-val">{text || '0'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="accounting.commissionRate%" />,
      dataIndex: 'commission_rate',
      rowKey: 'commission_rate',
      // width: 150,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="accounting.averageConRate" />
            </span>
            <span className="mobile-lbl-val">{text || '0'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="accounting.commission" />,
      dataIndex: 'commission',
      rowKey: 'commission',
      // width: 250,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="accounting.commission" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Action" />,
      dataIndex: 'action',
      rowKey: 'action',
      className: 'fullname-cell',
      width: 50,
      render: (text, item, index) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Action" />
            </span>
            <span className="mobile-lbl-val">
              <Popover
                content={content(index)}
                trigger="click"
                open={isIndex === item.id ? !actionMenu : false}
                onOpenChange={handleVisible}
                arrowPointAtCenter
                placement="bottomRight"
              >
                <MoreOutlined
                  onClick={() => {
                    console.log('index----====', index);
                    handleVisibleChange(item);
                    setState({ ...state, initObj: item });
                  }}
                  style={{ color: '#000', fontSize: 24 }}
                />
              </Popover>
            </span>
          </div>
        );
      },
    },
  ];
  // this function for update new product data
  /** this function for update brand data
   * @function updateData
   * @param {object} data id
   */
  function updateData(data) {
    const allDataA = isArray(dData.data.allData) ? [...dData.data.allData] : [];
    const editMode = !isEmpty(state.initObj) && data.id;
    console.log('edit update--', editMode);
    console.log('update---', allDataA);
    const dataIndex = findIndex(allDataA, { id: data.id });
    console.log('update-dtindex--', dataIndex);
    // if (editMode && dataIndex > -1) {
    //   console.log("in if---", data);
    //   allDataA[dataIndex] = data;
    //   // setBrandState({
    //   //   brands: brandAry,
    //   // });
    //   console.log("in if-allDataA--", allDataA);
    //   setdData((pre) => ({ ...pre, dData.data.allData: allDataA }));
    // } else {
    getDashboardData();
    // }
  }
  const customerDurationContent = (
    <WidgetWrapper>
      <div className="popMainDiv">
        {durationVisible ? (
          <div
            className="isoDropdownLink"
            onClick={() => {
              changeDateRange('');
              setStEndDate({ stDate: '', endDate: '' });
              setDuration(<IntlMessages id="dashboard.thisweek" />);
              setDurationVisibility(!durationVisible);
              setCPeriodType('this_week');
            }}
            role="button"
            onKeyPress={() => {
              changeDateRange('');
              setStEndDate({ stDate: '', endDate: '' });
              setDuration(<IntlMessages id="dashboard.thisweek" />);
              setDurationVisibility(!durationVisible);
              setCPeriodType('this_week');
            }}
            tabIndex="-1"
          >
            <IntlMessages id="dashboard.thisweek" />
          </div>
        ) : null}
        <div
          className="isoDropdownLink"
          onClick={() => {
            changeDateRange('');
            setStEndDate({ stDate: '', endDate: '' });
            setDuration(<IntlMessages id="dashboard.lastweek" />);
            setDurationVisibility(!durationVisible);
            setCPeriodType('last_week');
          }}
          role="button"
          onKeyPress={() => {
            changeDateRange('');
            setStEndDate({ stDate: '', endDate: '' });
            setDuration(<IntlMessages id="dashboard.lastweek" />);
            setDurationVisibility(!durationVisible);
            setCPeriodType('last_week');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.lastweek" />{' '}
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            changeDateRange('');
            setStEndDate({ stDate: '', endDate: '' });
            setDuration(<IntlMessages id="dashboard.thisMonth" />);
            setDurationVisibility(!durationVisible);
            setCPeriodType('this_month');
          }}
          role="button"
          onKeyPress={() => {
            changeDateRange('');
            setStEndDate({ stDate: '', endDate: '' });
            setDuration(<IntlMessages id="dashboard.thisMonth" />);
            setDurationVisibility(!durationVisible);
            setCPeriodType('this_month');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.thisMonth" />
        </div>

        <div
          className="isoDropdownLink"
          onClick={() => {
            changeDateRange('');
            setStEndDate({ stDate: '', endDate: '' });
            setDuration(<IntlMessages id="dashboard.lastMonth" />);
            setDurationVisibility(!durationVisible);
            setCPeriodType('last_month');
          }}
          role="button"
          onKeyPress={() => {
            changeDateRange('');
            setStEndDate({ stDate: '', endDate: '' });
            setDuration(<IntlMessages id="dashboard.lastMonth" />);
            setDurationVisibility(!durationVisible);
            setCPeriodType('last_month');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.lastMonth" />
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            changeDateRange('');
            setStEndDate({ stDate: '', endDate: '' });
            setDuration(<IntlMessages id="dashboard.lastSixMonth" />);
            setDurationVisibility(!durationVisible);
            setCPeriodType('last_6_month');
          }}
          role="button"
          onKeyPress={() => {
            changeDateRange('');
            setStEndDate({ stDate: '', endDate: '' });
            setDuration(<IntlMessages id="dashboard.lastSixMonth" />);
            setDurationVisibility(!durationVisible);
            setCPeriodType('last_6_month');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.lastSixMonth" />
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            changeDateRange('');
            setStEndDate({ stDate: '', endDate: '' });
            setDuration(<IntlMessages id="dashboard.lastYear" />);
            setDurationVisibility(!durationVisible);
            setCPeriodType('last_year');
          }}
          role="button"
          onKeyPress={() => {
            changeDateRange('');
            setStEndDate({ stDate: '', endDate: '' });
            setDuration(<IntlMessages id="dashboard.lastYear" />);
            setDurationVisibility(!durationVisible);
            setCPeriodType('last_year');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.lastYear" />
        </div>
      </div>
    </WidgetWrapper>
  );
  /**
   * Function to render Top 4 boxes
   */
  function renderTopBoxes() {
    return (
      <Row style={{ flexFlow: 'nowrap' }} gutter={0} justify="start">
        {TOP_BOXES.map((widget) => {
          // const val = getObjVal(dashData, widget.key);
          // const lnk = widget.link ? `/dashboard${widget.link}` : '';
          return (
            <Col
              lg={8}
              md={8}
              sm={12}
              xs={24}
              style={colStyle}
              key={widget.text}
            >
              <IsoWidgetsWrapper>
                <LinkBox link="">
                  <StickerWidget
                    number={widget.count}
                    text={<IntlMessages id={widget.text} />}
                    icon={widget.icon}
                    fontColor={widget.fontColor}
                    bgColor={widget.bgColor}
                  />
                </LinkBox>
              </IsoWidgetsWrapper>
            </Col>
          );
        })}
      </Row>
    );
  }

  return (
    <LayoutWrapper>
      <DashboardWrapper>
        <Spin spinning={dData.loading}>
          <div style={styles.wisgetPageStyle}>
            <Row gutter={0} style={{ ...rowStyle }}>
              <Col sm={24} lg={16} md={24} xl={16}>
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    // justifyContent: "space-between",
                    marginBottom: 16,
                    marginLeft: 15,
                  }}
                >
                  <div>
                    <RangePicker
                      allowclear
                      style={{ minWidth: '100%' }}
                      format="DD-MM-YYYY"
                      disabledDate={disabledDate}
                      onChange={(val) => {
                        onDateChange(val);
                      }}
                      value={dateRange !== '' ? dateRange : ''}
                      // defaultValue={[fromDate, toDate]}
                    />
                  </div>
                  <Popover
                    content={customerDurationContent}
                    trigger="click"
                    open={durationVisible}
                    onPopupAlign={durationChange}
                    placement="bottomRight"
                  >
                    <p
                      style={{
                        cursor: 'pointer',
                        fontSize: window.innerWidth <= 400 ? '12px' : '15px',
                        marginLeft: 40,
                      }}
                    >
                      {duration}
                      <DownOutlined style={{ marginLeft: 5 }} />
                    </p>
                  </Popover>
                </div>
              </Col>
              <Col sm={24} lg={16} md={24} xl={16}>
                {renderTopBoxes()}
              </Col>
            </Row>

            <Row style={rowStyle} gutter={0} justify="start">
              <Col
                xl={24}
                lg={24}
                sm={24}
                md={24}
                xs={24}
                style={colStyle}
                className="isoTrafficList"
              >
                <IsoWidgetsWrapper style={{ height: '100%' }}>
                  <ReportsWidget
                    label={<IntlMessages id="sidebar.accounting" />}
                    labelClsName="labelStyle"
                    className="campaignsList"
                    style={{
                      height: '100%',
                      borderRadius: 25,
                      boxShadow: '5px 5px 5px 5px #0000',
                      padding: 0,
                    }}
                    widgetClassName="flex1"
                    // action={
                    //   <div
                    //     style={{
                    //       cursor: 'pointer',
                    //       color: theme.colors.primaryColor,
                    //       minWidth: '100px',
                    //       textAlign: 'right',
                    //     }}
                    //     onClick={() => {
                    //       history.push({ pathname: '/dashboard/brands' });
                    //     }}
                    //   >
                    //     <IntlMessages id="common.viewAll" />
                    //   </div>
                    // }
                  >
                    <TableWrapper
                      loading={false}
                      rowKey={(record) => record.id}
                      dataSource={dData.data.allData}
                      columns={columns}
                      pagination={{ showSizeChanger: false }}
                      // showSorterTooltip={false}
                    />
                  </ReportsWidget>
                </IsoWidgetsWrapper>
              </Col>
            </Row>
          </div>
        </Spin>
      </DashboardWrapper>
      <CommissionForm
        initialValues={initObj}
        visible={visible}
        state={state}
        edit={edit}
        setState={setState}
        setEdit={setEdit}
        updateData={updateData}
      />
    </LayoutWrapper>
  );
}

export default injectIntl(AccountingWidget);

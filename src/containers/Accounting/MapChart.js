import React from 'react';
import { isArray, isEmpty } from 'lodash';
import {
  ComposableMap,
  Geographies,
  Geography,
  Marker,
} from 'react-simple-maps';
import { colorArray } from '@chill/lib/helpers/utility';

const geoUrl =
  'https://raw.githubusercontent.com/zcreativelabs/react-simple-maps/master/topojson-maps/world-110m.json';

const MapChart = (prop) => {
  const { array, setToolTipContent } = prop;

  // this function for set color
  function getColor(name = '') {
    let color = '';
    const bgObj = colorArray().find(
      (item) => item.name === name.charAt(0).toUpperCase(),
    );
    color = bgObj && bgObj.Color ? bgObj.Color : '#3498db';

    return color;
  }

  return (
    <ComposableMap
      data-tip=""
      projection="geoMercator"
      projectionConfig={{
        scale: '100',
      }}
      height={320}
    >
      <Geographies geography={geoUrl}>
        {({ geographies }) =>
          geographies.map((geo) => (
            <Geography
              key={geo.rsmKey}
              geography={geo}
              fill="#EAEAEC"
              // stroke="#D6D6DA"
              xHeight={50}
            />
          ))
        }
      </Geographies>
      {!isEmpty(array) &&
        isArray(array) &&
        array.map(({ coordinates, key, country, totalCount }) => {
          const color = getColor(country);
          return (
            <Marker
              key={key}
              coordinates={coordinates}
              onMouseEnter={() => {
                setToolTipContent(`${country}: ${totalCount}`);
              }}
              onMouseLeave={() => {
                setToolTipContent('');
              }}
            >
              <circle r={5} fill={color} stroke={color} strokeWidth={2} />
            </Marker>
          );
        })}
    </ComposableMap>
  );
};

export default MapChart;

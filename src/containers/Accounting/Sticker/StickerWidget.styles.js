/* eslint-disable import/prefer-default-export */
import styled from 'styled-components';
import { borderRadius } from '@chill/lib/helpers/style_utils';

const StickerWidgetWrapper = styled.div`
  width: 100%;
  display: flex;
  align-items: stretch;
  overflow: hidden;
  box-shadow: 20px 10px 30px 10px #000000;
  // margin-top: 20px;
  ${borderRadius('5px')};
  box-shadow: 20px 10px 30px 10px #000000;
  .isoIconWrapper {
    // display: flex;
    padding-top: 20px;
    padding-left: 15px;
    align-items: flex-start;
    justify-content: center;
    width: 30px;
    // flex-shrink: 0;
    // background-color: rgba(0, 0, 0, 0.1);

    & span.anticon {
      font-size: 20px;
      color: #123456;
    }
  }

  .isoContentWrapper {
    width: 100%;
    padding: 20px 15px 20px 20px;
    display: flex;
    flex-direction: column;

    .isoStatNumber {
      font-size: 18px;
      font-weight: 400 !important;
      line-height: 1.1;
      margin: 5px 0 5px;
    }

    .isoLabel {
      font-size: 12px;
      font-weight: 400;
      margin: 0;
      line-height: 1.2;
      opacity: 0.6;
    }
  }
`;

export { StickerWidgetWrapper };

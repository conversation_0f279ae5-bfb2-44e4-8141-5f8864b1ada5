import React from 'react';
import { StickerWidgetWrapper } from './StickerWidget.styles';

export default function ({ fontColor, bgColor, icon, number, text, image }) {
  const textColor = {
    color: fontColor,
  };
  const widgetStyle = {
    backgroundColor: bgColor,
    // width: '105%',
  };

  return (
    <StickerWidgetWrapper
      className="isoStickerWidget"
      style={{
        ...widgetStyle,
        boxShadow: '0px 0px 10px -4px #ccc',
        borderRadius: 10,
      }}
    >
      {image ? (
        <div className="isoIconWrapper">
          <img
            src={image}
            alt="chat"
            style={{ width: 22, height: 22, marginTop: 2 }}
          />
        </div>
      ) : (
        <div className="isoIconWrapper">{icon}</div>
      )}

      <div className="isoContentWrapper">
        <span className="isoLabel" style={textColor}>
          {text}
        </span>
        <h4 className="isoStatNumber" style={textColor}>
          {number || 0}
        </h4>
      </div>
    </StickerWidgetWrapper>
  );
}

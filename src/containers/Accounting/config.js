import React from 'react';
import {
  ClockCircleOutlined,
  DollarCircleOutlined,
  FolderOutlined,
  MessageOutlined,
  TagsOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';

const styles = {
  wisgetPageStyle: {
    display: 'flex',
    flexFlow: 'row wrap',
    alignItems: 'flex-start',
    overflow: 'hidden',
  },
  panelStyle: {
    fontWeight: '600',
    opacity: 0.9,
  },
  rowStyle: {
    marginTop: '10px',
    marginBottom: '10px',
  },
  rowMargin: {
    marginRight: '-5px',
  },
};

const verifyData = {
  is_verify_email: {
    weightage: 2,
    type: 'email',
    title: 'verification.email',
  },
  is_verify_mobile: {
    weightage: 8,
    type: 'mobile',
    title: 'verification.mobile',
  },
  is_verify_id: {
    weightage: 25,
    type: 'idcard',
    title: 'verification.idcard',
  },
  is_verify_address: {
    weightage: 15,
    type: 'address',
    title: 'verification.address',
  },
  is_verify_fingerprint: {
    weightage: 25,
    type: 'fingerprint',
    title: 'settings.fingerprint',
  },
  is_verify_liveness: {
    weightage: 25,
    type: 'liveness',
    title: 'verification.liveness',
  },
};

const securityData = {
  finger_print: {
    title: 'settings.fingerprint',
  },
  nfc: {
    title: 'settings.nfc',
  },
  six_digit: {
    title: 'settings.6DigAuth',
  },
  two_factor: {
    title: 'settings.twoFactor',
  },
  password: {
    title: 'common.password',
  },
};

const SIGNLE_PROGRESS_WIDGET = [
  {
    key: 'totalPdf',
    type: 'PDF',
  },
  {
    key: 'totalMsWord',
    type: 'MS Word',
  },
  {
    key: 'totalHtml',
    type: 'HTML',
  },
  // Disable below types for now (04-02-2021), will be in use later
  // {
  //   key: 'totalExcel',
  //   type: 'Excel',
  // },
  // {
  //   key: 'totalCsv',
  //   type: 'CSV',
  // },
  // {
  //   key: 'totalPpt',
  //   type: 'PPT',
  // },
  // {
  //   key: 'totalText',
  //   type: 'TXT',
  // },
  // {
  //   key: 'totalImages',
  //   type: 'IMG',
  // },
];

const TOP_BOXES = [
  {
    key: 'totalusers',
    text: 'dashboard.totalusers',
    icon: <UserOutlined style={{ color: 'pink' }} />,
    fontColor: '#000000',
    bgColor: '#ffffffff',
  },
  {
    key: 'weeklySession',
    text: 'dashboard.weeklySession',
    icon: <ClockCircleOutlined style={{ color: 'pink' }} />,
    fontColor: '#000000',
    bgColor: '#ffffffff',
  },
  {
    key: 'totalRevenue',
    text: 'dashboard.totalRevenue',
    // link: '/contacts',
    icon: <DollarCircleOutlined style={{ color: 'green' }} />,
    fontColor: '#000000',
    bgColor: '#ffffffff',
  },
  // {
  //   key: 'totalTag',
  //   text: 'dashboard.totalTag',
  //   link: '/manage-tags',
  //   icon: <TagsOutlined />,
  //   fontColor: '#000000',
  //   bgColor: '#ffffffff',
  // },
];

const TOP_BOXES_MARKETING = [
  {
    key: 'totalusers',
    text: 'dashboard.totalusers',
    icon: <UserOutlined style={{ color: 'pink' }} />,
    fontColor: '#000000',
    bgColor: '#ffffffff',
  },
  {
    key: 'weeklySession',
    text: 'dashboard.weeklySession',
    icon: <ClockCircleOutlined style={{ color: 'pink' }} />,
    fontColor: '#000000',
    bgColor: '#ffffffff',
  },
  {
    key: 'totalRevenue',
    text: 'dashboard.totalRevenue',
    // link: '/contacts',
    icon: <DollarCircleOutlined style={{ color: 'green' }} />,
    fontColor: '#000000',
    bgColor: '#ffffffff',
  },
  {
    key: 'totalTag',
    text: 'dashboard.totalTag',
    link: '/manage-tags',
    icon: <TagsOutlined />,
    fontColor: '#000000',
    bgColor: '#ffffffff',
  },
];

export const TOP_Marketing_BOXES = [
  {
    key: 'sentMessages',
    text: 'marketing.sentMessages',
    icon: <MessageOutlined />,
    fontColor: '#000000',
    bgColor: '#ffffffff',
  },
  {
    key: 'totalClicks',
    text: 'marketing.totalClicks',
    icon: <FolderOutlined />,
    fontColor: '#000000',
    bgColor: '#ffffffff',
  },
  {
    key: 'totalShares',
    text: 'marketing.totalShares',
    link: '/contacts',
    icon: <TeamOutlined />,
    fontColor: '#000000',
    bgColor: '#ffffffff',
  },
  {
    key: 'totalSales',
    text: 'marketing.totalSales',
    link: '/manage-tags',
    icon: <DollarCircleOutlined />,
    fontColor: '#000000',
    bgColor: '#ffffffff',
  },
];

const SIGN_REQS = [
  {
    key: 'totalPendingSignDoc',
    label: 'dashboard.totalPendingSignDoc',
    link: '/signing-requests',
    fontColor: '#F75D81',
    receivedKey: 'receivedRequest.totalPendingDoc',
    sentKey: 'sentRequest.totalPendingDoc',
  },
  {
    key: 'totalSignedDoc',
    label: 'dashboard.totalSignedDoc',
    link: '/signing-requests',
    fontColor: '#F75D81',
    receivedKey: 'receivedRequest.totalSignedDoc',
    sentKey: 'sentRequest.totalSignedDoc',
  },
  {
    key: 'totalDeclinedDoc',
    label: 'dashboard.totalDeclinedDoc',
    link: '/signing-requests',
    fontColor: '#F75D81',
    receivedKey: 'receivedRequest.totalDeclinedDoc',
    sentKey: 'sentRequest.totalDeclinedDoc',
  },
];

const WF_DATA = [
  {
    key: 'totalCompleted',
    label: 'completed',
    link: '/my-projects',
    fontColor: '#F75D81',
  },
  {
    key: 'totalDeclined',
    label: 'declined',
    link: '/my-projects',
    fontColor: '#F75D81',
  },
  {
    key: 'totalInProcess',
    label: 'in process',
    link: '/my-projects',
    fontColor: '#F75D81',
  },
  {
    key: 'totalPending',
    label: 'pending',
    link: '/my-projects',
    fontColor: '#F75D81',
  },
];

const EXTRA_INFO = [
  {
    key: 'totalBcSignDoc',
    text: 'dashboard.totalBcSignDoc',
    fontColor: '#F75D81',
    link: '/blockchain-documents',
  },
  {
    key: 'ethAddress',
    link: '/blockchain-information',
    text: 'bc.ethAdd',
    fontColor: '#666',
    valClsName: 'eth',
  },
];

const disabledSettings = ['is_verify_liveness'];

export {
  EXTRA_INFO,
  SIGN_REQS,
  TOP_BOXES,
  SIGNLE_PROGRESS_WIDGET,
  WF_DATA,
  securityData,
  verifyData,
  styles,
  disabledSettings,
  TOP_BOXES_MARKETING,
};

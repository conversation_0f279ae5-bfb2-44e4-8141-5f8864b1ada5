/* eslint-disable no-console */
import { EventEmitter } from 'events';
import Auth0Lock from 'auth0-lock';
import Auth0Config from '@chill/config/auth0.config';
import authActions from '@chill/redux/auth/actions';
import { store } from '@chill/redux/store';

class Auth0Helper extends EventEmitter {
  constructor() {
    super();
    this.isValid = Auth0Config.clientID && Auth0Config.domain;
    this.initialized = false;
    this.lock = this.isValid
      ? new Auth0Lock(
          Auth0Config.clientID,
          Auth0Config.domain,
          Auth0Config.options,
        )
      : null;
    this.login = this.login.bind(this);
    this.logout = this.logout.bind(this);
    this.setSession = this.setSession.bind(this);
    this.handleAuthentication = this.handleAuthentication.bind(this);
  }

  login() {
    if (!this.lock) {
      return;
    }
    this.lock.show();
  }

  handleAuthentication() {
    if (!this.initialized && this.lock) {
      this.lock.on('authenticated', this.setSession);

      this.lock.on('authorization_error', (error) => {
        console.log(error);
      });
    }

    this.initialized = true;
  }

  hideLock() {
    if (this.lock) this.lock.hide();
  }

  setSession(authResult) {
    // Set the time that the access token will expire at
    const expiresAt = JSON.stringify(
      authResult.expiresIn * 1000 + new Date().getTime(),
    );

    const userData = authResult.idTokenPayload || {};

    store.dispatch(
      authActions.login({
        token: authResult.idToken,
        user: userData,
        expiresAt,
      }),
    );

    this.hideLock();
  }

  logout() {
    // logout from Auth0
    if (this.lock) this.lock.logout();
  }

  isAuthenticated = () => {
    // Check whether the current time is past the
    // access token's expiry time
    return (
      new Date().getTime() < JSON.parse(localStorage.getItem('expires_at'))
    );
  };
}

export default new Auth0Helper();

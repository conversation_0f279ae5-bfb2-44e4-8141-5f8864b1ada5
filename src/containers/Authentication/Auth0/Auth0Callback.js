import React, { useEffect } from 'react';
import Loader from '@chill/components/utility/Loader';
// import { useHistory } from 'react-router-dom';
// import { useSelector } from 'react-redux';
import Auth0 from './Auth0';

const Auth0Callback = () => {
  // const isLoggedIn = useSelector((state) => state.Auth.idToken);
  // const history = useHistory();

  useEffect(() => {
    Auth0.handleAuthentication();
  }, [Auth0.handleAuthentication]);

  // useEffect(() => {
  //   if (isLoggedIn) {
  //     history.replace('/dashboard');
  //   }
  // }, [isLoggedIn]);

  return <Loader />;
};

export default Auth0Callback;

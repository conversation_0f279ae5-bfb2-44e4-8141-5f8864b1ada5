/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-no-bind */
/* eslint-disable array-callback-return */
/* eslint-disable no-nested-ternary */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable no-param-reassign */
/* eslint-disable global-require */
/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable  no-use-before-define */
import React, { useRef, useState } from 'react';
import {
  Col,
  Row,
  Tabs,
  Divider,
  Input,
  Select,
  Drawer,
  Slider,
  Radio,
  Button,
} from 'antd';
import { findIndex, isArray, isEmpty, isObject } from 'lodash';
import { useSelector } from 'react-redux';
import {
  FilterOutlined,
  PlusCircleOutlined,
  SwapOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  SettingOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import theme from '@chill/config/theme/default';
import { TabPane } from '@chill/components/uielements/tabs';
import TableWrapper from '@chill/assets/styles/AntTables.styles';
import IntlMessages from '@chill/components/utility/intlMessages';
import Popover from '@chill/components/uielements/popover';
import getApiData from '@chill/lib/helpers/apiHelper';
import Popconfirms from '@chill/components/Feedback/Popconfirm';
import Notification from '@chill/components/Notification';
import editSVG from '@chill/assets/images/sidebarIcons/viewProduct.svg';
import ProductForm from './ProductForm';
import AddCategory from '../ProductCategory/ProductForm';
import ProductStyles from './Products.styles';
import './App.css';

/**
 *
 * @module Products
 */
const Products = () => {
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const [actionMenu, setActionMenu] = React.useState(false);
  const [isIndex, setIsIndex] = React.useState(null);
  const [state, setState] = React.useState({
    initObj: {},
    visible: false,
    visibleCategory: false,
    tag: '',
  });
  const [productState, setProductState] = React.useState({
    products: [],
    maxView: [],
    maxClick: [],
    maxShare: [],
    productLoad: false,
    productData: {},
  });
  const [shopifyProductState, setShopifyProductState] = React.useState({
    products: [],
    maxView: [],
    maxClick: [],
    maxShare: [],
    productLoad: false,
    productData: {},
  });
  const [filter, setFilter] = React.useState({ pagination: {}, filters: {} });
  const [shopifyFilter, setshopifyFilter] = React.useState({
    pagination: { simple: true },
    filters: {},
  });
  const [productListType, setProductListType] = React.useState('1');
  const [filterMode, setFilterMode] = React.useState(false);
  const [shopifyFilterMode, setShopifyFilterMode] = React.useState(false);
  const [categories, setCategories] = React.useState([]);
  const [productTypeArr, setProductTypeArr] = React.useState([]);
  const [collectionList, setCollectionList] = React.useState([
    { collection_id: 1, title: 'All Products' },
  ]);
  const [integrationType, setIntegrationType] = React.useState([]);
  const [ConfigProduct, setConfigProduct] = useState(false);
  const [sort, setSort] = React.useState(false);
  const [filterTab, setFilterTab] = useState('All');
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);
  const [relInfo, setRel] = useState('');
  const [pageInfo, setPageInfo] = useState('');
  const [previousPageInfo, setPreviousPageInfo] = useState('');
  const [selectedCollectionId, setselectedCollectionId] = useState(1);
  const selectInputRef = useRef();
  const [selected, setSelected] = useState();
  const [selectedSort, setSelectedSort] = useState();
  const sortingArr = [
    'Characteristic',
    'Alphabetically - A Z',
    'Alphabeticall - Z A',
    'Date - Old to recent',
    'Date - Recent to old',
  ];

  const [manufacturerRange, setManufacturerRange] = useState([4, 20]);
  const [recommendedRange, setRecommendedRange] = useState([4, 20]);
  const [accuracyweightage, setAccuracyweightage] = useState(10);
  const [manufacturerRangeHeight, setManufacturerRangeHeight] = useState([
    4, 20,
  ]);
  const [recommendedRangeHeight, setRecommendedRangeHeight] = useState([4, 20]);
  const [accuracyweightageHeight, setAccuracyweightageHeight] = useState(20);
  const [manufacturerRangeAqi, setManufacturerRangeAqi] = useState([4, 20]);
  const [recommendedRangeAqi, setRecommendedRangeAqi] = useState([4, 20]);
  const [accuracyweightageAqi, setAccuracyweightageAqi] = useState(20);
  const [weight, setWeight] = useState([3, 6]);
  const [weightUnit, setWeightUnit] = useState('kg');
  const [weight1, setWeight1] = useState([3, 6]);
  const [weightUnit1, setWeightUnit1] = useState('kg');
  const [accuracyweightageWeight, setAccuracyweightageWeight] = useState(60);
  const [temperatureUnit, setTemperatureUnit] = useState('F');
  const [temperature, setTemperature] = useState([32, 100]);
  const [temperatureUnit1, setTemperatureUnit1] = useState('F');
  const [temperature1, setTemperature1] = useState([32, 100]);
  const [accuracyweightageTemp, setAccuracyweightageTemp] = useState(20);
  const [availableForOwned, setAvailableForOwned] = useState([]);
  const [availableForNotOwned, setAvailableForNotOwned] = useState([]);
  const [productsOwnedIds, setProductsOwnedIds] = useState([]);
  const [productsNotOwnedIds, setProductsNotOwnedIds] = useState([]);
  const [productsOwned, setProductsOwned] = useState([]);
  const [productsNotOwned, setProductsNotOwned] = useState([]);
  // this function for get shopify products
  /** this function for get shopify products
   * @function getShopifyProductList
   * @param {object} data type, sort
   */
  async function getShopifyProductList(data = {}) {
    if (filterTab === 'shopify') {
      // call this api

      setShopifyProductState((p) => ({
        ...p,
        productLoad: true,
      }));

      if (data.product_tag || data.sort_obj) {
        data.collection_id = selectedCollectionId;
      }
      // data.limit = 10;
      // set limit == pagination.pageSize, for previous, set rel, when change per page cahnge limit
      // data = {
      //   limit: 25,
      // };
      // if (sort) {
      //   data.sort = 'ASC';
      // } else {
      //   data.sort = 'DESC';
      // }
      try {
        const response = await getApiData(
          'e-commerce/product-list',
          data,
          'POST',
        );
        if (response.success && response.data) {
          if (response.page_info_next) {
            setPreviousPageInfo(pageInfo);
            setPageInfo(response.page_info_next);
          }
          if (response.page_info_previous) {
            setPreviousPageInfo(response.page_info_previous);
          }
          if (response.rel) {
            setRel(response.rel);
          }
          if (isArray(response?.filter_for)) {
            if (!('product_tag' in data)) {
              setProductTypeArr(response?.filter_for);
            }
          }
          if (isArray(response?.data)) {
            setShopifyProductState((preState) => ({
              ...preState,
              products: isArray(response?.data) ? response?.data : [],
              maxView: isArray(response.product_analytics?.maxViewed)
                ? response?.product_analytics?.maxViewed
                : [],
              maxClick: isArray(response.product_analytics?.maxClicked)
                ? response?.product_analytics?.maxClicked
                : [],
              maxShare: isArray(response.product_analytics?.maxShared)
                ? response?.product_analytics?.maxShared
                : [],
              productLoad: false,
              productData: response,
            }));
            const pagination =
              shopifyFilter && shopifyFilter.pagination
                ? { ...shopifyFilter.pagination }
                : {};
            pagination.total = response.total_count || 0;
            pagination.pageSize = 10;
            pagination.current = response.page || 1;
            setshopifyFilter((f) => ({ ...f, pagination }));
          }
        } else {
          Notification('error', response.message);
          setShopifyProductState((preState) => ({
            ...preState,
            productLoad: false,
          }));
        }
      } catch (err) {
        setShopifyProductState((preState) => ({
          ...preState,
          productLoad: false,
        }));
      }
    }
  }

  // this function for get products
  /** this function for get products
   * @function getProductList
   * @param {object} data type, sort
   */
  async function getProductList(data = {}) {
    if (productListType === '1') {
      data.type = '';
    } else if (productListType === '2') {
      data.type = '';
    } else {
      data.type = '';
    }
    setProductState((p) => ({
      ...p,
      productLoad: true,
    }));
    if (sort) {
      data.sort = 'ASC';
    } else {
      data.sort = 'DESC';
    }
    try {
      const response = await getApiData('product/get-brand-products', 'GET');
      if (response.success && isArray(response.data)) {
        setProductState((preState) => ({
          ...preState,
          products: isArray(response.data) ? response.data : [],
          maxView: isArray(response.product_analytics?.maxViewed)
            ? response?.product_analytics?.maxViewed
            : [],
          maxClick: isArray(response.product_analytics?.maxClicked)
            ? response?.product_analytics?.maxClicked
            : [],
          maxShare: isArray(response.product_analytics?.maxShared)
            ? response?.product_analytics?.maxShared
            : [],
          productLoad: false,
          productData: response,
        }));
        setAvailableForOwned(isArray(response.data) ? response.data : []);
        setAvailableForNotOwned(isArray(response.data) ? response.data : []);

        const pagination =
          filter && filter.pagination ? { ...filter.pagination } : {};
        pagination.total = response.data.length || 0;
        // pagination.pageSize = response.per_page || 50;
        // pagination.current = response.page || 1;
        setFilter((f) => ({ ...f, pagination }));
      } else {
        setProductState((preState) => ({
          ...preState,
          productLoad: false,
        }));
      }
    } catch (err) {
      setProductState((preState) => ({
        ...preState,
        productLoad: false,
      }));
    }
  }
  // this function for get integration type list
  /** this function for get integration type list
   * @function getIntegrationTypeList
   * @param {object} data {}
   */
  async function getIntegrationTypeList() {
    try {
      const response = await getApiData(
        'e-commerce/integration-list',
        {},
        'POST',
      );
      if (response.success) {
        const data =
          isArray(response.data) && response.data.length > 0
            ? response.data
            : [];
        setIntegrationType(data);
      } else {
        Notification('error', response.message);
      }
    } catch (error) {
      console.error(error);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  // this function for get products category
  /** this function for get products category
   * @function getProductCategories
   * @param {object} data {}
   */
  async function getProductCategories() {
    try {
      const response = await getApiData('getProductCategories', {}, 'POST');
      if (response.success) {
        const data =
          isArray(response.data) && response.data.length > 0
            ? response.data
            : [];
        setCategories(data);
      } else {
        Notification('error', response.message);
      }
    } catch (error) {
      console.error(error);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  // this function for get collections
  /** this function for get collections
   * @function getCollections
   * @param {object} data {}
   */
  async function getCollections() {
    try {
      const response = await getApiData(
        'e-commerce/collections-list',
        {},
        'GET',
      );
      if (response.success) {
        const data =
          isArray(response.data) && response.data.length > 0
            ? response.data
            : [];
        setCollectionList(data);
      } else {
        Notification('error', response.message);
      }
    } catch (error) {
      console.error(error);
      Notification('error', messageArray['err.wenWrong']);
    }
  }
  React.useEffect(() => {
    getProductList();
    getProductCategories();
    getIntegrationTypeList();
    getCollections();
  }, []);

  React.useEffect(() => {
    if (filterTab === 'shopify') {
      getShopifyProductList({
        collection_id: collectionList[0]?.collection_id,
      });
    } else if (filterTab === 'All') {
      getProductList();
    }
  }, [filterTab]);

  React.useEffect(() => {
    if (filterTab === 'shopify') {
      // const page =
      //   shopifyFilter &&
      //   shopifyFilter.pagination &&
      //   shopifyFilter.pagination.current
      //     ? shopifyFilter.pagination.current
      //     : 1;
      // const flt = shopifyFilter ? { ...shopifyFilter } : {};
      // getShopifyProductList({ page, ...flt.filters });
    } else {
      const page =
        filter && filter.pagination && filter.pagination.current
          ? filter.pagination.current
          : 1;
      const flt = filter ? { ...filter } : {};
      getProductList({ page, ...flt.filters });
    }
  }, [sort]);

  React.useEffect(() => {
    if (!filterMode) {
      if (filterTab === 'shopify') {
        getShopifyProductList();
      } else {
        getProductList({});
      }
    }
  }, [filterMode, shopifyFilterMode]);

  // this function filters data and get updated list of product
  /** this function filters data and get updated list of product
   * @function fetchDataFilters
   * @param {object} data page, productName, Tags, Categories
   */
  function fetchDataFilters() {
    const page =
      filter && filter.pagination && filter.pagination.current
        ? filter.pagination.current
        : 1;
    const filters = filter && filter.filters ? filter.filters : {};
    getProductList({ page, ...filters });
  }

  // this function for delete products
  /** this function for delete products
   * @function deleteProduct
   * @param {object} data product_id
   */
  async function deleteProduct() {
    const data = {
      product_id:
        isObject(state) &&
        isObject(state.initObj) &&
        !isEmpty(state.initObj) &&
        state.initObj.id,
    };
    const productAry = isArray(productState.products)
      ? productState.products
      : [];
    setProductState({
      productLoad: true,
    });
    try {
      const response = await getApiData('product/delete', data, 'POST');
      let msgTitle = 'error';
      if (response.success) {
        msgTitle = 'success';
        fetchDataFilters();
      } else {
        setProductState((pre) => ({
          ...pre,
          products: productAry,
          productLoad: false,
        }));
      }
      Notification(msgTitle, response.message);
    } catch (error) {
      console.error('error ===', error);
      setProductState((pre) => ({
        ...pre,
        products: productAry,
        productLoad: false,
      }));
    }
  }

  // fun. for visible PopOver
  function handleVisible() {
    setActionMenu((visiblePre) => !visiblePre);
  }

  // this function for handle pagination
  /** this function for handle pagination
   * @function onChange
   * @param {object} data page
   */
  function onChange(pagination) {
    if (filterTab === 'shopify') {
      const pager =
        shopifyFilter && shopifyFilter.pagination
          ? { ...shopifyFilter.pagination }
          : {};
      pager.current = pagination.current;
      setshopifyFilter((f) => ({ ...f, pagination: pager }));
      if (pagination.total / pagination.pageSize < pagination.current) {
        if (pagination.current !== 1 && pageInfo !== '') {
          getShopifyProductList({
            // limit: 10,
            rel: pagination.current === 1 ? '' : relInfo,
            page_info: pagination.current === 1 ? '' : pageInfo,
            page: pagination.current,
            ...shopifyFilter.filters,
          });
        }
      } else {
        if (relInfo === 'privious') {
          getShopifyProductList({
            // limit: 10,
            rel: pagination.current === 1 ? '' : relInfo,
            page_info: pagination.current === 1 ? '' : previousPageInfo,
            page: pagination.current,
            ...shopifyFilter.filters,
          });
        }
        console.log(relInfo);
      }
      setSort(false);
    } else {
      const pager = filter && filter.pagination ? { ...filter.pagination } : {};
      pager.current = pagination.current;
      setFilter((f) => ({ ...f, pagination: pager }));
      getProductList({
        page: pagination.current,
        ...filter.filters,
      });
      setSort(false);
    }
  }

  // this function for update new product data
  /** this function for update new product data
   * @function updateData
   * @param {object} data id
   */
  const [open, setOpen] = useState(false);

  const showConfigDrawer = () => {
    setOpen(true);
    setIsIndex(null);
    setManufacturerRange([
      state.initObj.age?.min_manufacturer_range || 0,
      state.initObj.age?.max_manufacturer_range || 0,
    ]);
    setRecommendedRange([
      state.initObj.age?.min_recommended_range || 0,
      state.initObj.age?.max_recommended_range || 0,
    ]);
    setAccuracyweightage(state.initObj.age?.accuracy_weightage || 0);
    setManufacturerRangeHeight([
      state.initObj.height?.min_manufacturer_range || 0,
      state.initObj.height?.max_manufacturer_range || 0,
    ]);
    setRecommendedRangeHeight([
      state.initObj.height?.min_recommended_range || 0,
      state.initObj.height?.max_recommended_range || 0,
    ]);
    setAccuracyweightageHeight(state.initObj.height?.accuracy_weightage || 0);
    setManufacturerRangeAqi([
      state.initObj.aqi?.min_manufacturer_range || 0,
      state.initObj.aqi?.max_manufacturer_range || 0,
    ]);
    setRecommendedRangeAqi([
      state.initObj.aqi?.min_recommended_range || 0,
      state.initObj.aqi?.max_recommended_range || 0,
    ]);
    setAccuracyweightageAqi(state.initObj.aqi?.accuracy_weightage || 0);
    setWeight([
      state.initObj.weight?.min_manufacturer_range || 0,
      state.initObj.weight?.max_manufacturer_range || 0,
    ]);
    setWeight1([
      state.initObj.weight?.min_recommended_range || 0,
      state.initObj.weight?.max_recommended_range || 0,
    ]);
    setAccuracyweightageWeight(state.initObj.weight?.accuracy_weightage || 0);
    setTemperature([
      state.initObj.temperature?.min_manufacturer_range || 0,
      state.initObj.temperature?.max_manufacturer_range || 0,
    ]);
    setTemperature1([
      state.initObj.temperature?.min_recommended_range || 0,
      state.initObj.temperature?.max_recommended_range || 0,
    ]);
    setAccuracyweightageTemp(
      state.initObj.temperature?.accuracy_weightage || 0,
    );
    setWeightUnit(state.initObj.weight?.units);
    setTemperatureUnit(state.initObj.temperature?.units);
    setProductsOwned(
      matchProductNames(productState.products, state.initObj.products_owned),
    );
    setProductsNotOwned(
      matchProductNames(
        productState.products,
        state.initObj.products_not_owned,
      ),
    );
    setAvailableForOwned(
      dropDownOptions(productState.products, state.initObj.products_not_owned),
    );
    setAvailableForNotOwned(
      dropDownOptions(productState.products, state.initObj.products_owned),
    );
  };

  const matchProductNames = (allProductsList, arr) => {
    return allProductsList
      .filter((obj) => arr.includes(obj.id)) // Filter by matching `id`
      .map((obj) => obj.product_name);
  };

  const dropDownOptions = (allProductsList, arr) => {
    return allProductsList.filter((obj) => !arr.includes(obj.id));
  };

  const onClose = () => {
    setOpen(false);
  };

  function updateData(data) {
    const productAry = isArray(productState.products)
      ? [...productState.products]
      : [];
    const edit = !isEmpty(state.initObj) && data.id;
    const dataIndex = findIndex(productAry, { id: data.id });
    if (edit && dataIndex > -1) {
      productAry[dataIndex] = data;
    } else {
      getProductList();
    }
    setProductState({
      products: productAry,
    });
    setState({
      initObj: {},
      visible: false,
      view: false,
    });
  }

  const onManufacturerRangeHeightChange = (value) => {
    setManufacturerRangeHeight(value);
  };

  const onRecommendedRangeHeightChange = (value) => {
    setRecommendedRangeHeight(value);
  };

  const onAccuracyweightageHeight = (value) => {
    setAccuracyweightageHeight(value);
  };

  const onManufacturerRangeAqiChange = (value) => {
    setManufacturerRangeAqi(value);
  };

  const onRecommendedRangeAqiChange = (value) => {
    setRecommendedRangeAqi(value);
  };

  const onAccuracyweightageAqi = (value) => {
    setAccuracyweightageAqi(value);
  };

  const onManufacturerRangeChange = (value) => {
    setManufacturerRange(value);
  };

  const onRecommendedRangeChange = (value) => {
    setRecommendedRange(value);
  };

  const onAccuracyweightage = (value) => {
    setAccuracyweightage(value);
  };

  // weightage weight

  const onAccuracyweightagWeight = (value) => {
    setAccuracyweightageWeight(value);
  };

  const onAccuracyweightageTemp = (value) => {
    setAccuracyweightageTemp(value);
  };

  //  manufacturer weight

  const handleWeightChange = (value) => {
    setWeight(value);
  };

  const handleWeightUnitChange = (e) => {
    const checked = e.target.value === 'lbs';
    const newWeight = checked
      ? [weight[0] * 2.2, weight[1] * 2.2]
      : [weight[0] / 2.2, weight[1] / 2.2];
    setWeightUnit(checked ? 'lbs' : 'kg');
    setWeight(newWeight);
    handleWeightUnitChange1(e);
  };

  const handleWeightUnitChange1 = (e) => {
    const checked = e.target.value === 'lbs';
    const newWeight = checked
      ? [weight1[0] * 2.2, weight1[1] * 2.2]
      : [weight1[0] / 2.2, weight1[1] / 2.2];
    setWeightUnit1(checked ? 'lbs' : 'kg');
    setWeight1(newWeight);
  };

  const handleProductsOwned = (values, obj) => {
    const prodOwnedArr = obj.map((product) => Number(product.key));
    setProductsOwned(values);
    setProductsOwnedIds(prodOwnedArr);
    const updatedAvailableForNotOwned = productState.products.filter(
      (product) => !prodOwnedArr.includes(product.id),
    );
    setAvailableForNotOwned(updatedAvailableForNotOwned);
  };

  const handleProductsNotOwned = (values, obj) => {
    const prodNotOwnedArr = obj.map((product) => Number(product.key));
    setProductsNotOwned(values);
    setProductsNotOwnedIds(prodNotOwnedArr);
    const updatedAvailableForOwned = productState.products.filter(
      (product) => !prodNotOwnedArr.includes(product.id),
    );
    setAvailableForOwned(updatedAvailableForOwned);
  };

  const weightRadioGroupProps = {
    value: weightUnit,
    onChange: handleWeightUnitChange,
  };

  const weightRadioGroupProps1 = {
    value: weightUnit1,
    onChange: handleWeightUnitChange1,
  };

  const weightSliderProps = {
    range: true,
    min: 0,
    max: weightUnit === 'kg' ? 50 : 110,
    step: 0.1,
    onChange: handleWeightChange,
    value: weight,
  };

  const weightUnitLabel = weightUnit === 'kg' ? '(kgs)' : '(lbs)';

  // recomended weight
  const handleWeightChange1 = (value) => {
    setWeight1(value);
  };

  // const handleWeightUnitChange1 = (e) => {
  //   const checked = e.target.value === 'lbs';
  //   const newWeight = checked
  //     ? [weight1[0] * 2.2, weight1[1] * 2.2]
  //     : [weight1[0] / 2.2, weight1[1] / 2.2];
  //   setWeightUnit1(checked ? 'lbs' : 'kg');
  //   setWeight1(newWeight);
  // };

  // const weightRadioGroupProps1 = {
  //   value: weightUnit1,
  //   onChange: handleWeightUnitChange1,
  // };

  const weightSliderProps1 = {
    range: true,
    min: 0,
    max: weightUnit === 'kg' ? 50 : 110,
    step: 0.1,
    onChange: handleWeightChange1,
    value: weight1,
  };

  const weightUnitLabel1 = weightUnit1 === 'kg' ? '(kgs)' : '(lbs)';

  // temperature

  // Manufacturer Range temperature
  const handleTemperatureChange = (value) => {
    setTemperature(value);
  };

  const handleTemperatureUnitChange = (e) => {
    const checked = e.target.value === 'C';
    const newTemperature = checked
      ? [(temperature[0] - 32) / 1.8, (temperature[1] - 32) / 1.8]
      : [temperature[0] * 1.8 + 32, temperature[1] * 1.8 + 32];
    setTemperatureUnit(checked ? 'C' : 'F');
    setTemperature(newTemperature);
    handleTemperatureUnitChange1(e);
  };

  const handleTemperatureUnitChange1 = (e) => {
    const checked = e.target.value === 'C';
    const newTemperature = checked
      ? [(temperature1[0] - 32) / 1.8, (temperature1[1] - 32) / 1.8]
      : [temperature1[0] * 1.8 + 32, temperature1[1] * 1.8 + 32];
    setTemperatureUnit1(checked ? 'C' : 'F');
    setTemperature1(newTemperature);
  };

  const temperatureRadioGroupProps = {
    value: temperatureUnit,
    onChange: handleTemperatureUnitChange,
  };

  const temperatureSliderProps = {
    range: true,
    min: -40,
    max: temperatureUnit === 'F' ? 150 : 75,
    step: 0.1,
    onChange: handleTemperatureChange,
    value: temperature,
  };

  const temperatureUnitLabel = temperatureUnit === 'C' ? '(°C)' : '(F)';

  // recomended range temperature

  const handleTemperatureChange1 = (value) => {
    setTemperature1(value);
  };

  const temperatureRadioGroupProps1 = {
    value: temperatureUnit1,
    onChange: handleTemperatureUnitChange1,
  };

  const temperatureSliderProps1 = {
    range: true,
    min: -40,
    max: temperatureUnit === 'F' ? 150 : 75,
    step: 0.1,
    onChange: handleTemperatureChange1,
    value: temperature1,
  };

  const temperatureUnitLabel1 = temperatureUnit1 === 'C' ? '(°C)' : '(F)';

  // update api call
  const handleUpdate = async () => {
    const totalAccuracyWeightage =
      accuracyweightage +
      accuracyweightageHeight +
      accuracyweightageWeight +
      accuracyweightageTemp +
      accuracyweightageAqi;
    if (totalAccuracyWeightage !== 100) {
      return; // Exit the function without making the API call
    }

    const data1 = {
      product_id:
        isObject(state) &&
        isObject(state.initObj) &&
        !isEmpty(state.initObj) &&
        state.initObj.id,
      age: {
        max_manufacturer_range: manufacturerRange[1] || 0,
        min_manufacturer_range: manufacturerRange[0] || 0,
        max_recommended_range: recommendedRange[1] || 0,
        min_recommended_range: recommendedRange[0] || 0,
        accuracy_weightage: accuracyweightage || 0,
        units: 'months',
      },
      height: {
        max_manufacturer_range: manufacturerRangeHeight[1] || 0,
        min_manufacturer_range: manufacturerRangeHeight[0] || 0,
        max_recommended_range: recommendedRangeHeight[1] || 0,
        min_recommended_range: recommendedRangeHeight[0] || 0,
        accuracy_weightage: accuracyweightageHeight || 0,
        units: 'cms',
      },
      weight: {
        max_manufacturer_range: weight[1] || 0,
        min_manufacturer_range: weight[0] || 0,
        max_recommended_range: weight1[1] || 0,
        min_recommended_range: weight1[0] || 0,
        accuracy_weightage: accuracyweightageWeight || 0,
        units: weightUnit,
      },
      temperature: {
        max_manufacturer_range: temperature[1] || 0,
        min_manufacturer_range: temperature[0] || 0,
        max_recommended_range: temperature1[1] || 0,
        min_recommended_range: temperature1[0] || 0,
        accuracy_weightage: accuracyweightageTemp || 0,
        units: temperatureUnit,
      },
      aqi: {
        max_manufacturer_range: manufacturerRangeAqi[1] || 0,
        min_manufacturer_range: manufacturerRangeAqi[0] || 0,
        max_recommended_range: recommendedRangeAqi[1] || 0,
        min_recommended_range: recommendedRangeAqi[0] || 0,
        accuracy_weightage: accuracyweightageAqi || 0,
        units: 'AQI',
      },
      products_owned: productsOwnedIds,
      products_not_owned: productsNotOwnedIds,
    };
    try {
      const response = await getApiData(
        'product/update-product-config',
        data1,
        'POST',
      );
      if (response.success) {
        setOpen(false);
        fetchDataFilters();
        setProductState({
          productLoad: true,
        });
      } else {
        console.log(response);
      }
    } catch (err) {
      console.error(err);
    }
  };

  // Popover content or list
  const content = (
    <ProductStyles>
      <div className="popMainDiv">
        <div
          className="isoDropdownLink"
          onClick={() => {
            setState({ ...state, visible: true, view: true });
            setIsIndex(null);
          }}
        >
          <img
            className="iconImg"
            src={editSVG}
            style={{ width: 16, height: 16, marginRight: 12 }}
            alt="noIcon"
          />
          <IntlMessages id="View Product" />
        </div>
        <div
          role="button"
          className="isoDropdownLink"
          onKeyPress=""
          tabIndex="-1"
          onClick={() => {
            setState({ ...state, visible: true });
            setIsIndex(null);
          }}
        >
          <EditOutlined style={{ paddingRight: 12, fontSize: 16 }} />
          <IntlMessages id="Edit Product" />
        </div>
        <div
          className="isoDropdownLink"
          onClick={
            showConfigDrawer
            // setState({ ...state, visible: true });
            // setIsIndex(null);
          }
        >
          <SettingOutlined style={{ paddingRight: 12, fontSize: 16 }} />
          <IntlMessages id="Config Product" />
        </div>
        <Drawer
          title="Edit Config Product"
          placement="right"
          onClose={onClose}
          open={open}
          width={500}
          style={{ height: window.innerHeight - 60, marginTop: 70 }}
          bodyStyle={{ paddingBottom: 80 }}
        >
          <>
            <img
              src={state.initObj.product_image}
              alt="product"
              style={{ width: '30%', height: '30%' }}
            />
            <h3 style={{ marginTop: '10px' }}>{state.initObj.product_name}</h3>
            <div>
              <div>
                <h3
                  style={{
                    color: '#0a0606',
                    fontWeight: 'bold',
                    marginTop: 10,
                  }}
                >
                  Age
                </h3>
                <p
                  style={{
                    color: '#0a0606',
                    fontWeight: 'bold',
                    marginTop: 15,
                  }}
                >
                  Manufacturer Range
                  <span style={{ color: '#0a0606', fontWeight: 'normal' }}>
                    (months)
                  </span>
                </p>
                <div style={{ display: 'flex' }}>
                  <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
                  <Slider
                    range
                    min={0}
                    max={150}
                    value={manufacturerRange}
                    onChange={onManufacturerRangeChange}
                    trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
                    railStyle={{
                      boxShadow: '0px 0px 6px #00000029',
                      height: 5,
                    }}
                    style={{ width: '100%' }}
                  />
                  <span style={{ margin: '5px 0px 0px 5px' }}>150</span>
                </div>
                <span
                  style={{
                    color: '#040606',
                    opacity: 0.5,
                  }}
                >{`${manufacturerRange[0]} - ${manufacturerRange[1]} months`}</span>
              </div>
              <div>
                <p
                  style={{
                    color: '#0a0606',
                    fontWeight: 'bold',
                    marginTop: 10,
                  }}
                >
                  Recommended Range
                  <span style={{ color: '#0a0606', fontWeight: 'normal' }}>
                    (months)
                  </span>
                </p>
                <div style={{ display: 'flex' }}>
                  <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
                  <Slider
                    range
                    min={0}
                    max={150}
                    value={recommendedRange}
                    onChange={onRecommendedRangeChange}
                    trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
                    railStyle={{
                      boxShadow: '0px 0px 6px #00000029',
                      height: 5,
                    }}
                    style={{ width: '100%' }}
                  />
                  <span style={{ margin: '5px 0px 0px 5px' }}>150</span>
                </div>

                <span
                  style={{
                    color: '#040606',
                    opacity: 0.5,
                  }}
                >{`${recommendedRange[0]} - ${recommendedRange[1]} months`}</span>
              </div>
              <div>
                <p
                  style={{
                    color: '#0a0606',
                    fontWeight: 'bold',
                    marginTop: 10,
                  }}
                >
                  Accuracy Weightage
                  <span style={{ color: '#0a0606', fontWeight: 'normal' }}>
                    (%)
                  </span>
                </p>
                <div style={{ display: 'flex' }}>
                  <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
                  <Slider
                    min={0}
                    max={100}
                    value={accuracyweightage}
                    onChange={onAccuracyweightage}
                    trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
                    railStyle={{
                      boxShadow: '0px 0px 6px #00000029',
                      height: 5,
                    }}
                    style={{ width: '100%' }}
                  />
                  <span style={{ margin: '5px 0px 0px 5px' }}>100</span>
                </div>
                <span
                  style={{
                    color: '#040606',
                    opacity: 0.5,
                  }}
                >{`${accuracyweightage} %`}</span>
              </div>
            </div>
            <Divider style={{ color: '#000', fontSize: '12px' }} />
            <div>
              <div>
                <h3
                  style={{
                    color: '#0a0606',
                    fontWeight: 'bold',
                    marginTop: 20,
                  }}
                >
                  Height
                </h3>
                <p
                  style={{
                    color: '#0a0606',
                    fontWeight: 'bold',
                    marginTop: 15,
                  }}
                >
                  Manufacturer Range
                  <span style={{ color: '#0a0606', fontWeight: 'normal' }}>
                    (Cms)
                  </span>
                </p>
                <div style={{ display: 'flex' }}>
                  <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
                  <Slider
                    range
                    min={0}
                    max={150}
                    value={manufacturerRangeHeight}
                    onChange={onManufacturerRangeHeightChange}
                    trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
                    railStyle={{
                      boxShadow: '0px 0px 6px #00000029',
                      height: 5,
                    }}
                    style={{ width: '100%' }}
                  />
                  <span style={{ margin: '5px 0px 0px 5px' }}>150</span>
                </div>
                <span
                  style={{
                    color: '#040606',
                    opacity: 0.5,
                  }}
                >{`${manufacturerRangeHeight[0]} - ${manufacturerRangeHeight[1]} Cms`}</span>
              </div>
              <div>
                <p
                  style={{
                    color: '#0a0606',
                    fontWeight: 'bold',
                    marginTop: 10,
                  }}
                >
                  Recommended Range
                  <span style={{ color: '#0a0606', fontWeight: 'normal' }}>
                    (Cms)
                  </span>
                </p>
                <div style={{ display: 'flex' }}>
                  <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
                  <Slider
                    range
                    min={0}
                    max={150}
                    value={recommendedRangeHeight}
                    onChange={onRecommendedRangeHeightChange}
                    trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
                    railStyle={{
                      boxShadow: '0px 0px 6px #00000029',
                      height: 5,
                    }}
                    style={{ width: '100%' }}
                  />
                  <span style={{ margin: '5px 0px 0px 5px' }}>150</span>
                </div>

                <span
                  style={{
                    color: '#040606',
                    opacity: 0.5,
                  }}
                >{`${recommendedRangeHeight[0]} - ${recommendedRangeHeight[1]} Cms`}</span>
              </div>
              <div>
                <p
                  style={{
                    color: '#0a0606',
                    fontWeight: 'bold',
                    marginTop: 10,
                  }}
                >
                  Accuracy Weightage
                  <span style={{ color: '#0a0606', fontWeight: 'normal' }}>
                    (%)
                  </span>
                </p>
                <div style={{ display: 'flex' }}>
                  <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
                  <Slider
                    min={0}
                    max={100}
                    value={accuracyweightageHeight}
                    onChange={onAccuracyweightageHeight}
                    trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
                    railStyle={{
                      boxShadow: '0px 0px 6px #00000029',
                      height: 5,
                    }}
                    style={{ width: '100%' }}
                  />
                  <span style={{ margin: '5px 0px 0px 5px' }}>100</span>
                </div>
                <span
                  style={{
                    color: '#040606',
                    opacity: 0.5,
                  }}
                >{`${accuracyweightageHeight} %`}</span>
              </div>
            </div>
            <Divider style={{ color: '#000', fontSize: '12px' }} />
            <div>
              <div>
                <h3
                  style={{
                    color: '#0a0606',
                    fontWeight: 'bold',
                    marginTop: 20,
                  }}
                >
                  AQI
                </h3>
                <p
                  style={{
                    color: '#0a0606',
                    fontWeight: 'bold',
                    marginTop: 15,
                  }}
                >
                  Manufacturer Range
                  <span style={{ color: '#0a0606', fontWeight: 'normal' }}>
                    (AQI)
                  </span>
                </p>
                <div style={{ display: 'flex' }}>
                  <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
                  <Slider
                    range
                    min={0}
                    max={150}
                    value={manufacturerRangeAqi}
                    onChange={onManufacturerRangeAqiChange}
                    trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
                    railStyle={{
                      boxShadow: '0px 0px 6px #00000029',
                      height: 5,
                    }}
                    style={{ width: '100%' }}
                  />
                  <span style={{ margin: '5px 0px 0px 5px' }}>150</span>
                </div>
                <span
                  style={{
                    color: '#040606',
                    opacity: 0.5,
                  }}
                >{`${manufacturerRangeAqi[0]} - ${manufacturerRangeAqi[1]} AQI`}</span>
              </div>
              <div>
                <p
                  style={{
                    color: '#0a0606',
                    fontWeight: 'bold',
                    marginTop: 10,
                  }}
                >
                  Recommended Range
                  <span style={{ color: '#0a0606', fontWeight: 'normal' }}>
                    (AQI)
                  </span>
                </p>
                <div style={{ display: 'flex' }}>
                  <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
                  <Slider
                    range
                    min={0}
                    max={150}
                    value={recommendedRangeAqi}
                    onChange={onRecommendedRangeAqiChange}
                    trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
                    railStyle={{
                      boxShadow: '0px 0px 6px #00000029',
                      height: 5,
                    }}
                    style={{ width: '100%' }}
                  />
                  <span style={{ margin: '5px 0px 0px 5px' }}>150</span>
                </div>

                <span
                  style={{
                    color: '#040606',
                    opacity: 0.5,
                  }}
                >{`${recommendedRangeAqi[0]} - ${recommendedRangeAqi[1]} AQI`}</span>
              </div>
              <div>
                <p
                  style={{
                    color: '#0a0606',
                    fontWeight: 'bold',
                    marginTop: 10,
                  }}
                >
                  Accuracy Weightage
                  <span style={{ color: '#0a0606', fontWeight: 'normal' }}>
                    (%)
                  </span>
                </p>
                <div style={{ display: 'flex' }}>
                  <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
                  <Slider
                    min={0}
                    max={100}
                    // disabled
                    value={accuracyweightageAqi}
                    onChange={onAccuracyweightageAqi}
                    trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
                    railStyle={{
                      boxShadow: '0px 0px 6px #00000029',
                      height: 5,
                    }}
                    style={{ width: '100%' }}
                  />
                  <span style={{ margin: '5px 0px 0px 5px' }}>100</span>
                </div>
                <span
                  style={{
                    color: '#040606',
                    opacity: 0.5,
                  }}
                >{`${accuracyweightageAqi} %`}</span>
              </div>
            </div>
            <Divider style={{ color: '#000', fontSize: '12px' }} />
            <div>
              <h3
                style={{
                  color: '#0a0606',
                  fontWeight: 'bold',
                  marginTop: 20,
                }}
              >
                Weight
              </h3>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginTop: 10,
                }}
              >
                <div style={{ display: 'flex' }}>
                  <p
                    style={{
                      color: '#0a0606',
                      fontWeight: 'bold',
                      marginRight: 5,
                    }}
                  >
                    Manufacturer Range
                  </p>
                  <span>{weightUnitLabel}</span>
                </div>
                <div
                  style={{
                    marginRight: 13,
                  }}
                >
                  <Radio.Group {...weightRadioGroupProps} size="small">
                    <Radio.Button
                      value="kg"
                      style={{
                        backgroundColor: weightUnit === 'kg' ? '#2a77f4' : '',
                        color: weightUnit === 'kg' ? 'white' : '',
                        borderRadius: '10px 0px 0px 10px',
                      }}
                    >
                      kgs
                    </Radio.Button>
                    <Radio.Button
                      value="lbs"
                      style={{
                        backgroundColor: weightUnit === 'lbs' ? '#2a77f4' : '',
                        color: weightUnit === 'lbs' ? 'white' : '',
                        borderRadius: '0px 10px 10px 0px',
                      }}
                    >
                      lbs
                    </Radio.Button>
                  </Radio.Group>
                </div>
              </div>
              <div style={{ display: 'flex' }}>
                <span style={{ margin: '5px 3px 0px 0px' }}>0</span>
                <Slider
                  {...weightSliderProps}
                  trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
                  railStyle={{ boxShadow: '0px 0px 6px #00000029', height: 5 }}
                  style={{ width: '100%' }}
                  defaultValue={[0, 40]}
                  value={[weight[0], weight[1]]}
                />
                <span style={{ margin: '5px 0px 0px 3px' }}>
                  {weightUnit === 'kg' ? 50 : 110}
                </span>
              </div>
              <span style={{ color: '#040606', opacity: 0.5 }}>{`${
                Math.round(weight[0] * 10) / 10
              } - ${Math.round(weight[1] * 10) / 10} ${weightUnit}`}</span>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginTop: 10,
                }}
              >
                <div style={{ display: 'flex' }}>
                  <p
                    style={{
                      color: '#0a0606',
                      fontWeight: 'bold',
                      marginRight: 5,
                    }}
                  >
                    Recommended Range
                  </p>
                  <span>{weightUnitLabel}</span>
                </div>
                <div
                  style={{
                    marginRight: 13,
                  }}
                >
                  {/* <Radio.Group {...weightRadioGroupProps1} size="small">
                    <Radio.Button
                      value="kg"
                      style={{
                        backgroundColor: weightUnit1 === 'kg' ? '#2a77f4' : '',
                        color: weightUnit1 === 'kg' ? 'white' : '',
                        borderRadius: '10px 0px 0px 10px',
                      }}
                    >
                      kgs
                    </Radio.Button>
                    <Radio.Button
                      value="lbs"
                      style={{
                        backgroundColor: weightUnit1 === 'lbs' ? '#2a77f4' : '',
                        color: weightUnit1 === 'lbs' ? 'white' : '',
                        borderRadius: '0px 10px 10px 0px',
                      }}
                    >
                      lbs
                    </Radio.Button>
                  </Radio.Group> */}
                </div>
              </div>
              <div style={{ display: 'flex' }}>
                <span style={{ margin: '5px 3px 0px 0px' }}>0</span>
                <Slider
                  {...weightSliderProps1}
                  trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
                  railStyle={{ boxShadow: '0px 0px 6px #00000029', height: 5 }}
                  style={{ width: '100%' }}
                  defaultValue={[0, 40]}
                  value={[weight1[0], weight1[1]]}
                />
                <span style={{ margin: '5px 0px 0px 3px' }}>
                  {weightUnit === 'kg' ? 50 : 110}
                </span>
              </div>
              <span style={{ color: '#040606', opacity: 0.5 }}>{`${
                Math.round(weight1[0] * 10) / 10
              } - ${Math.round(weight1[1] * 10) / 10} ${weightUnit}`}</span>
              <div>
                <p
                  style={{
                    color: '#0a0606',
                    fontWeight: 'bold',
                    marginTop: 10,
                  }}
                >
                  Accuracy Weightage
                  <span style={{ color: '#0a0606', fontWeight: 'normal' }}>
                    (%)
                  </span>
                </p>
                <div style={{ display: 'flex' }}>
                  <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
                  <Slider
                    min={0}
                    max={100}
                    value={accuracyweightageWeight}
                    onChange={onAccuracyweightagWeight}
                    trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
                    railStyle={{
                      boxShadow: '0px 0px 6px #00000029',
                      height: 5,
                    }}
                    style={{ width: '100%' }}
                  />
                  <span style={{ margin: '5px 0px 0px 5px' }}>100</span>
                </div>
                <span
                  style={{
                    color: '#040606',
                    opacity: 0.5,
                  }}
                >{`${accuracyweightageWeight} %`}</span>
              </div>
            </div>
            <Divider style={{ color: '#000', fontSize: '12px' }} />
            <div>
              <h3
                style={{
                  color: '#0a0606',
                  fontWeight: 'bold',
                  marginTop: 20,
                }}
              >
                Temperature
              </h3>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginTop: 10,
                }}
              >
                <div style={{ display: 'flex' }}>
                  <p
                    style={{
                      color: '#0a0606',
                      fontWeight: 'bold',
                      marginRight: 5,
                    }}
                  >
                    Manufacturer Range
                  </p>
                  <span>{temperatureUnitLabel}</span>
                </div>
                <div
                  style={{
                    marginRight: 13,
                  }}
                >
                  <Radio.Group {...temperatureRadioGroupProps} size="small">
                    <Radio.Button
                      value="F"
                      style={{
                        backgroundColor:
                          temperatureUnit === 'F' ? '#2a77f4' : '',
                        color: temperatureUnit === 'F' ? 'white' : '',
                        width: '2.7vw',
                        textAlign: 'center',
                        borderRadius: '10px 0px 0px 10px',
                      }}
                    >
                      F
                    </Radio.Button>
                    <Radio.Button
                      value="C"
                      style={{
                        backgroundColor:
                          temperatureUnit === 'C' ? '#2a77f4' : '',
                        color: temperatureUnit === 'C' ? 'white' : '',
                        width: '2.7vw',
                        textAlign: 'center',
                        borderRadius: '0px 10px 10px 0px',
                      }}
                    >
                      °C
                    </Radio.Button>
                  </Radio.Group>
                </div>
              </div>
              <div style={{ display: 'flex' }}>
                <span style={{ margin: '5px 0px 0px 0px' }}>-40</span>
                <Slider
                  {...temperatureSliderProps}
                  trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
                  railStyle={{ boxShadow: '0px 0px 6px #00000029', height: 5 }}
                  style={{ width: '100%' }}
                />
                <span style={{ margin: '5px 0px 0px 0px' }}>
                  {temperatureUnit === 'F' ? 150 : 75}
                </span>
              </div>
              <span style={{ color: '#040606', opacity: 0.5 }}>{`${
                Math.round(temperature[0] * 10) / 10
              } - ${
                Math.round(temperature[1] * 10) / 10
              } ${temperatureUnit}`}</span>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginTop: 10,
                }}
              >
                <div style={{ display: 'flex' }}>
                  <p
                    style={{
                      color: '#0a0606',
                      fontWeight: 'bold',
                      marginRight: 5,
                    }}
                  >
                    Recommended Range
                  </p>
                  <span>{temperatureUnitLabel}</span>
                </div>
                <div
                  style={{
                    marginRight: 13,
                  }}
                >
                  {/* <Radio.Group {...temperatureRadioGroupProps1} size="small">
                    <Radio.Button
                      value="F"
                      style={{
                        backgroundColor:
                          temperatureUnit1 === 'F' ? '#2a77f4' : '',
                        color: temperatureUnit1 === 'F' ? 'white' : '',
                        width: '2.7vw',
                        textAlign: 'center',
                        borderRadius: '10px 0px 0px 10px',
                      }}
                    >
                      F
                    </Radio.Button>
                    <Radio.Button
                      value="C"
                      style={{
                        backgroundColor:
                          temperatureUnit1 === 'C' ? '#2a77f4' : '',
                        color: temperatureUnit1 === 'C' ? 'white' : '',
                        width: '2.7vw',
                        textAlign: 'center',
                        borderRadius: '0px 10px 10px 0px',
                      }}
                    >
                      °C
                    </Radio.Button>
                  </Radio.Group> */}
                </div>
              </div>
              <div style={{ display: 'flex' }}>
                <span style={{ margin: '5px 0px 0px 0px' }}>-40</span>
                <Slider
                  {...temperatureSliderProps1}
                  trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
                  railStyle={{ boxShadow: '0px 0px 6px #00000029', height: 5 }}
                  style={{ width: '100%' }}
                />
                <span style={{ margin: '5px 0px 0px 0px' }}>
                  {temperatureUnit === 'F' ? 150 : 75}
                </span>
              </div>
              <span style={{ color: '#040606', opacity: 0.5 }}>{`${
                Math.round(temperature1[0] * 10) / 10
              } - ${
                Math.round(temperature1[1] * 10) / 10
              } ${temperatureUnit}`}</span>
              <div>
                <p
                  style={{
                    color: '#0a0606',
                    fontWeight: 'bold',
                    marginTop: 10,
                  }}
                >
                  Accuracy Weightage
                  <span style={{ color: '#0a0606', fontWeight: 'normal' }}>
                    (%)
                  </span>
                </p>
                <div style={{ display: 'flex' }}>
                  <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
                  <Slider
                    min={0}
                    max={100}
                    // disabled
                    value={accuracyweightageTemp}
                    onChange={onAccuracyweightageTemp}
                    trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
                    railStyle={{
                      boxShadow: '0px 0px 6px #00000029',
                      height: 5,
                    }}
                    style={{ width: '100%' }}
                  />
                  <span style={{ margin: '5px 0px 0px 5px' }}>100</span>
                </div>
                <span
                  style={{
                    color: '#040606',
                    opacity: 0.5,
                  }}
                >{`${accuracyweightageTemp} %`}</span>
              </div>
            </div>
            <Divider style={{ color: '#000', fontSize: '12px' }} />
            <div>
              <h3
                style={{
                  color: '#0a0606',
                  fontWeight: 'bold',
                  marginTop: 20,
                }}
              >
                Products Owned
              </h3>
              <Select
                mode="multiple"
                style={{ width: '100%' }}
                placeholder="Select products owned"
                value={productsOwned}
                onChange={handleProductsOwned}
                optionLabelProp="label"
              >
                {availableForOwned.map((product) => (
                  <Select.Option
                    key={product.id}
                    value={product.product_name}
                    label={product.product_name}
                  >
                    <img
                      src={product.product_image}
                      alt={product.product_name}
                      className="option-image"
                    />
                    {product.product_name}
                  </Select.Option>
                ))}
              </Select>
            </div>
            <Divider style={{ color: '#000', fontSize: '12px' }} />
            <div>
              <h3
                style={{
                  color: '#0a0606',
                  fontWeight: 'bold',
                  marginTop: 20,
                }}
              >
                Products Not Owned
              </h3>
              <Select
                mode="multiple"
                style={{ width: '100%' }}
                placeholder="Select products not owned"
                value={productsNotOwned}
                onChange={handleProductsNotOwned}
                optionLabelProp="label"
              >
                {availableForNotOwned.map((product) => (
                  <Select.Option
                    key={product.id}
                    value={product.product_name}
                    label={product.product_name}
                  >
                    <img
                      src={product.product_image}
                      alt={product.product_name}
                      className="option-image"
                    />
                    {product.product_name}
                  </Select.Option>
                ))}
              </Select>
            </div>
          </>
          <div
            style={{
              position: 'fixed', // Set the container to a fixed position
              display: 'flex',
              justifyContent: 'flex-end',
              marginTop: '10px',
              right: 20, // Adjust the positioning as needed
              bottom: 10, // Adjust the positioning as needed
              zIndex: 4,
            }}
          >
            <Button
              type="primary"
              shape="round"
              icon={<UploadOutlined />}
              size="large"
              onClick={handleUpdate}
              style={
                {
                  // Remove the absolute positioning from the button
                }
              }
            >
              Update
            </Button>
          </div>
        </Drawer>
        <div className="isoDropdownLink">
          <Popconfirms
            title={<IntlMessages id="Sure to delete?" />}
            okText={<IntlMessages id="DELETE" />}
            cancelText={<IntlMessages id="No" />}
            onConfirm={() => {
              deleteProduct();
              setIsIndex(null);
            }}
            onCancel={null}
          >
            <DeleteOutlined style={{ paddingRight: 12, fontSize: 16 }} />
            <IntlMessages id="Delete Product" />
          </Popconfirms>
        </div>
      </div>
    </ProductStyles>
  );

  const renderTabBar = (props, DefaultTabBar) => (
    <DefaultTabBar {...props} className="site-custom-tab-bar" />
  );

  // set item id in state
  function handleVisibleChange(item1) {
    setIsIndex(item1.id);
  }

  const columns = [
    {
      title: <IntlMessages id="No" />,
      dataIndex: 'id',
      rowKey: 'id',
      width: 50,
      className: 'fullname-cell',
      render: (text, data, index) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="No" />
            </span>
            <span className="mobile-lbl-val">{index + 1 || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Product Name" />,
      dataIndex: filterTab === 'shopify' ? 'title' : 'product_name',
      rowKey: filterTab === 'shopify' ? 'title' : 'product_name',
      width: 280,
      className: 'fullname-cell',
      render: (text, data) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Product Name" />
            </span>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <img
                src={
                  filterTab === 'shopify'
                    ? data?.image?.src
                    : data.product_image
                }
                style={{
                  width: 30,
                  height: 30,
                  backgroundColor: '#eee',
                  marginRight: 12,
                  borderRadius: 30,
                }}
                alt=""
              />
              <span className="mobile-lbl-val">{text || '-'}</span>
            </div>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="SKU" />,
      dataIndex: filterTab !== 'shopify' ? 'sku_code' : 'variants',
      rowKey: filterTab !== 'shopify' ? 'sku_code' : 'variants',
      width: 175,
      className: 'fullname-cell',
      render: (text) => {
        if (isArray(text)) {
          const skus = [];
          text.map((obj) => {
            if (obj.sku !== '') {
              skus.push(obj.sku);
            }
          });
          if (skus.length > 0) {
            text = skus.join(', ');
          } else {
            text = '-';
          }
        }
        return (
          <div>
            <span className="label">
              <IntlMessages id="SKU" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Price" />,
      dataIndex: filterTab !== 'shopify' ? 'sell_price' : 'variants',
      rowKey: filterTab !== 'shopify' ? 'sell_price' : 'variants',
      width: 175,
      className: 'fullname-cell',
      render: (text) => {
        if (isArray(text)) {
          const prices = [];
          text.map((obj) => {
            if (obj.price !== '') {
              prices.push(obj.price);
            }
          });
          if (prices.length > 0) {
            text = prices.join(', ');
          } else {
            text = '-';
          }
        }
        return (
          <div>
            <span className="label">
              <IntlMessages id="Price" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Users" />,
      dataIndex: 'users',
      rowKey: 'users',
      width: 175,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Users" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Category" />,
      dataIndex: filterTab !== 'shopify' ? 'category_name' : 'product_tag',
      rowKey: filterTab !== 'shopify' ? 'category_name' : 'product_tag',
      width: 250,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Category" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    // {
    //   title: <IntlMessages id="Action" />,
    //   dataIndex: 'action',
    //   rowKey: 'action',
    //   className: 'fullname-cell',
    //   width: 50,
    //   fixed: 'right',
    //   render: (text, item) => {
    //     return (
    //       <div>
    //         <span className="label">
    //           <IntlMessages id="Action" />
    //         </span>
    //         <span className="mobile-lbl-val">
    //           <Popover
    //             content={content}
    //             trigger="click"
    //             visible={isIndex === item.id ? !actionMenu : false}
    //             onVisibleChange={handleVisible}
    //             arrowPointAtCenter
    //             placement="bottomRight"
    //           >
    //             <MoreOutlined
    //               onClick={() => {
    //                 handleVisibleChange(item);
    //                 setState({ ...state, initObj: item });
    //               }}
    //               style={{ color: '#000', fontSize: 24 }}
    //             />
    //           </Popover>
    //         </span>
    //       </div>
    //     );
    //   },
    // },
  ];

  const action = {
    key: 'sort_created',
    title: <IntlMessages id="Action" />,
    dataIndex: 'action',
    rowKey: 'action',
    className: 'fullname-cell',
    width: 50,
    fixed: 'right',
    render: (text, item) => {
      return (
        <div>
          <span className="label">
            <IntlMessages id="Action" />
          </span>
          <span className="mobile-lbl-val">
            <Popover
              content={content}
              trigger="click"
              open={isIndex === item.id ? !actionMenu : false}
              onOpenChange={handleVisible}
              arrowPointAtCenter
              placement="bottomRight"
            >
              <MoreOutlined
                onClick={() => {
                  handleVisibleChange(item);
                  setState({ ...state, initObj: item });
                }}
                style={{ color: '#000', fontSize: 24 }}
              />
            </Popover>
          </span>
        </div>
      );
    },
  };

  if (filterTab === 'All') {
    columns.splice(6, 0, action);
  }

  const viewColumns = [
    {
      title: <IntlMessages id="No" />,
      dataIndex: 'id',
      rowKey: 'id',
      width: 50,
      className: 'fullname-cell',
      render: (text, data, index) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="No" />
            </span>
            <span className="mobile-lbl-val">{index + 1 || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Product Name" />,
      dataIndex: filterTab === 'shopify' ? 'title' : 'product_name',
      rowKey: filterTab === 'shopify' ? 'title' : 'product_name',
      width: 230,
      className: 'fullname-cell',
      render: (text, data) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Product Name" />
            </span>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <img
                src={
                  filterTab === 'shopify'
                    ? data?.image?.src
                    : data.product_image
                }
                style={{
                  width: 30,
                  height: 30,
                  backgroundColor: '#eee',
                  marginRight: 12,
                  borderRadius: 30,
                }}
                alt=""
              />
              <span className="mobile-lbl-val">{text || '-'}</span>
            </div>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="total_View" />,
      dataIndex: 'maxViewed',
      rowKey: 'maxViewed',
      // width: 30,
      className: 'fullname-cell',
      fixed: 'right',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="SKU" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
  ];

  const clickColumns = [
    {
      title: <IntlMessages id="No" />,
      dataIndex: 'id',
      rowKey: 'id',
      width: 50,
      className: 'fullname-cell',
      // fixed: 'left',
      render: (text, data, index) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="No" />
            </span>
            <span className="mobile-lbl-val">{index + 1 || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Product Name" />,
      dataIndex: filterTab === 'shopify' ? 'title' : 'product_name',
      rowKey: filterTab === 'shopify' ? 'title' : 'product_name',
      width: 230,
      className: 'fullname-cell',
      // fixed: 'left',
      render: (text, data) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Product Name" />
            </span>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <img
                src={
                  filterTab === 'shopify'
                    ? data?.image?.src
                    : data.product_image
                }
                style={{
                  width: 30,
                  height: 30,
                  backgroundColor: '#eee',
                  marginRight: 12,
                  borderRadius: 30,
                }}
                alt=""
              />
              <span className="mobile-lbl-val">{text || '-'}</span>
            </div>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="total_click" />,
      dataIndex: 'maxClicked',
      rowKey: 'maxClicked',
      // width: 100,
      className: 'fullname-cell',
      fixed: 'right',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="SKU" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
  ];

  const shareColumns = [
    {
      title: <IntlMessages id="No" />,
      dataIndex: 'id',
      rowKey: 'id',
      width: 50,
      className: 'fullname-cell',
      // fixed: 'left',
      render: (text, data, index) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="No" />
            </span>
            <span className="mobile-lbl-val">{index + 1 || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Product Name" />,
      dataIndex: filterTab === 'shopify' ? 'title' : 'product_name',
      rowKey: filterTab === 'shopify' ? 'title' : 'product_name',
      width: 230,
      className: 'fullname-cell',
      // fixed: 'left',
      render: (text, data) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Product Name" />
            </span>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <img
                src={
                  filterTab === 'shopify'
                    ? data?.image?.src
                    : data.product_image
                }
                style={{
                  width: 30,
                  height: 30,
                  backgroundColor: '#eee',
                  marginRight: 12,
                  borderRadius: 30,
                }}
                alt=""
              />
              <span className="mobile-lbl-val">{text || '-'}</span>
            </div>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="total_share" />,
      dataIndex: 'maxShared',
      rowKey: 'maxShared',
      // width: 100,
      className: 'fullname-cell',
      fixed: 'right',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="SKU" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
  ];

  // Add Product View
  const addNew = () => {
    return (
      <ProductStyles>
        <div className="iconStyle marketingMainDiv">
          <span>
            <p
              className="addNewUser"
              style={{
                color: theme.colors.primaryColor,
                alignItems: 'center',
                // display:'flex'
              }}
            >
              {filterTab !== 'shopify' && (
                <SwapOutlined
                  className="filterIcon1"
                  style={{ color: sort ? theme.colors.primaryColor : '#000' }}
                  onClick={() => {
                    setSort(!sort);
                  }}
                />
              )}
              <FilterOutlined
                className="filterIcon"
                style={{
                  color:
                    filterTab === 'shopify'
                      ? theme.colors.primaryColor
                      : filterMode
                      ? theme.colors.primaryColor
                      : '#000',
                }}
                onClick={() => {
                  if (filterTab === 'shopify') {
                    setFilterMode((pre) => !pre);
                    setFilter({ filters: {} });
                  } else {
                    setFilterMode((pre) => !pre);
                    setFilter({ filters: {} });
                  }
                }}
              />
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                }}
                onClick={() => setState((p) => ({ ...p, visible: true }))}
              >
                <p className="iconPadding" style={{ paddingRight: 8 }}>
                  <PlusCircleOutlined />
                </p>
                <IntlMessages id="Add Product" />
              </div>
            </p>
          </span>
        </div>
      </ProductStyles>
    );
  };
  // handler
  // this function renders search view
  function renderSearchBar() {
    return filterTab !== 'shopify' ? (
      <div style={{ padding: '15px' }}>
        <Row gutter={10}>
          <Col lg={8} sm={24} md={12} xs={24} style={{ marginBottom: 8 }}>
            <Input.Search
              style={{ minWidth: '100%' }}
              onChange={(e) => {
                const val = e && e.target ? e.target.value : '';

                const flt = filter ? { ...filter } : {};
                flt.filters.product_name = val;

                setFilter(flt);
                getProductList(flt.filters);
              }}
              value={
                filterTab === 'shopify'
                  ? shopifyFilter.title
                  : filter.product_name
              }
              allowClear
              placeholder={messageArray['input.product.title']}
            />
          </Col>
          <Col lg={8} sm={24} md={12} xs={24} style={{ marginBottom: 8 }}>
            <Input.Search
              style={{ minWidth: '100%' }}
              onChange={(e) => {
                const val = e && e.target ? e.target.value : '';
                const flt = filter ? { ...filter } : {};
                flt.filters.tags = val;

                setFilter(flt);
                getProductList(flt.filters);
              }}
              value={filter.tags}
              allowClear
              placeholder={messageArray['input.product.tags']}
            />
          </Col>
          <Col lg={8} sm={24} md={12} xs={24}>
            <Select
              style={{ width: '100%' }}
              onChange={(val) => {
                const flt = filter ? { ...filter } : {};
                flt.filters.category_id = val || '';

                setFilter(flt);
                getProductList(flt.filters);
              }}
              placeholder={messageArray['input.category.placeholder']}
              allowClear
            >
              {categories.map((item) => (
                <Select.Option key={`categories${item.id}`} value={item.id}>
                  {item.category_name}
                </Select.Option>
              ))}
            </Select>
          </Col>
        </Row>
      </div>
    ) : (
      <div style={{ padding: '15px' }}>
        <h4>{messageArray['label.collection']}</h4>
        <Row gutter={10}>
          <Col lg={8} sm={24} md={12} xs={24}>
            <Select
              style={{ width: '100%' }}
              onChange={(val) => {
                const flt = shopifyFilter ? { ...shopifyFilter } : {};
                flt.filters.product_tag = '';
                flt.filters.sort_obj = '';
                setshopifyFilter(flt);
                setSelected(null);
                setSelectedSort(null);
                setselectedCollectionId(val);
                getShopifyProductList({ collection_id: val });
              }}
              placeholder={collectionList[0]?.title}
              allowClear
            >
              {collectionList.map((item) => (
                <Select.Option
                  key={`collectionList${item.collection_id}`}
                  value={item.collection_id}
                >
                  {item.title}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col lg={8} sm={24} md={12} xs={24}>
            <Select
              value={selected}
              ref={selectInputRef}
              style={{ width: '100%' }}
              onChange={(val) => {
                const flt = shopifyFilter ? { ...shopifyFilter } : {};
                flt.filters.product_tag = val || '';

                setSelected(val);
                setshopifyFilter(flt);
                getShopifyProductList(flt.filters);
              }}
              placeholder={messageArray['input.filter.placeholder']}
              allowClear
            >
              {productTypeArr.map((item) => (
                <Select.Option
                  ref={selectInputRef}
                  key={`productTagArr${item.product_tag}`}
                  value={item.product_tag}
                >
                  {item.product_tag}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col lg={8} sm={24} md={12} xs={24}>
            <Select
              value={selectedSort}
              id="ddlFruits"
              style={{ width: '100%' }}
              onChange={(val) => {
                const flt = shopifyFilter ? { ...shopifyFilter } : {};
                let obj = {};
                if (val === 'Characteristic') {
                  obj = {
                    sort_value: 'fulfillment_service',
                    sort_type: 'ascend',
                  };
                } else if (val === 'Alphabetically - A Z') {
                  obj = { sort_value: 'title', sort_type: 'ascend' };
                } else if (val === 'Alphabeticall - Z A') {
                  obj = { sort_value: 'title', sort_type: 'desc' };
                } else if (val === 'Date - Old to recent') {
                  obj = {
                    sort_value: 'created_at',
                    sort_type: 'ascend',
                  };
                } else if (val === 'Date - Recent to old') {
                  obj = { sort_value: 'created_at', sort_type: 'desc' };
                }
                flt.filters.sort_obj = obj || '';
                setshopifyFilter(flt);
                setSelectedSort(val);
                getShopifyProductList(flt.filters);
              }}
              placeholder={messageArray['input.sorting.placeholder']}
              allowClear
            >
              {sortingArr.map((item) => (
                <Select.Option key={`sortingArr${item}`} value={item}>
                  {item}
                </Select.Option>
              ))}
            </Select>
          </Col>
        </Row>
      </div>
    );
  }

  const { initObj, visible, view, visibleCategory } = state;

  return (
    <ProductStyles>
      <Row gutter={24}>
        <Col xl={18} lg={17} sm={24} md={24} xs={24}>
          <Row className="isoTabRow">
            <Col xl={8} lg={8} sm={8} md={4} xs={8}>
              <div className="marketingText">
                <IntlMessages id="PRODUCTS" />
              </div>
            </Col>
            <Col sm={16} xs={16} className="hiddenCol">
              {addNew()}
            </Col>
            <Col className="isoTab" xl={8} lg={8} sm={24} md={12} xs={24}>
              {/* <Tabs
                defaultActiveKey=""
                renderTabBar={renderTabBar}
                style={{ paddingLeft: 16 }}
                onChange={(key) => {
                  setFilterTab(key);
                }}
              >
                <TabPane tab={<IntlMessages id="tab.all" />} key="1" />
                <TabPane tab={<IntlMessages id="tab.new" />} key="2" />
                {/* <TabPane tab={<IntlMessages id="tab.outofstock" />} key="3" />mmmmmmm }
              </Tabs> */}
              <Tabs
                activeKey={filterTab}
                renderTabBar={renderTabBar}
                style={{ paddingLeft: 16 }}
                onChange={(key) => {
                  setFilterTab(key);
                }}
              >
                <TabPane tab={<IntlMessages id="tab.all" />} key="All" />
                {isArray(integrationType) && integrationType.length > 0
                  ? integrationType.map((sg) => (
                      <TabPane
                        tab={
                          <span
                            className="tabs"
                            style={{
                              display: 'flex',
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                              alignItems: 'center',
                            }}
                          >
                            {sg.integration_type}
                            {/* {filterTab == sg.id ? (
                              <Popconfirms
                                title={<IntlMessages id="Sure to delete?" />}
                                okText={<IntlMessages id="DELETE" />}
                                cancelText={<IntlMessages id="No" />}
                                onConfirm={() => {
                                  deleteSegment(sg.id);
                                }}
                                onCancel={null}
                              >
                                <DeleteOutlined
                                  style={{
                                    paddingLeft: 10,
                                    fontSize: 16,
                                    color: 'red',
                                  }}
                                />
                              </Popconfirms>
                            ) : null} */}
                          </span>
                        }
                        key={sg.integration_type}
                      />
                    ))
                  : null}
                {/* <TabPane
                tab={<IntlMessages id="tab.englishspeaking" />}
                key="2"
                />
              <TabPane tab={<IntlMessages id="tab.topshares" />} key="3" /> */}
              </Tabs>
            </Col>

            <Col xl={8} lg={8} md={8} className="isoAddNew">
              {addNew()}
            </Col>
          </Row>
          <Divider className="horizontalDevider" />
          {filterMode && filterTab !== 'shopify' ? renderSearchBar() : null}
          {filterTab === 'shopify' ? renderSearchBar() : null}
          <Row>
            <Col xl={24} lg={24} sm={24} md={24} xs={24}>
              {filterTab !== 'shopify' ? (
                <TableWrapper
                  loading={productState.productLoad}
                  rowKey={(record) => record.id}
                  dataSource={productState.products}
                  onChange={onChange}
                  columns={columns}
                  className="invoiceListTable"
                  pagination={filter.pagination || {}}
                  showSorterTooltip={false}
                />
              ) : (
                <TableWrapper
                  loading={shopifyProductState.productLoad}
                  rowKey={(record) => record.id}
                  dataSource={shopifyProductState.products}
                  onChange={onChange}
                  columns={columns}
                  className="invoiceListTable"
                  pagination={shopifyFilter.pagination}
                  showSorterTooltip={false}
                />
              )}
            </Col>
          </Row>
        </Col>

        <Col
          // style={{ padding: '40px 0px 0px 0px' }}
          xl={6}
          sm={24}
          xs={24}
          lg={7}
          md={24}
        >
          <Row>
            <Col
              className="secondCol"
              xl={24}
              lg={24}
              sm={24}
              md={24}
              xs={24}
              style={{ boxShadow: '-5px 6px 9px -3px #b2b2b2' }}
            >
              <Row className="isoTabRow">
                <Col>
                  <div className="marketingText">
                    <IntlMessages id="most_View" />:
                  </div>
                </Col>
              </Row>
              <TableWrapper
                // scroll={{ x: false }}
                loading={
                  filterTab === 'shopify'
                    ? shopifyProductState.productLoad
                    : productState.productLoad
                }
                rowKey={(record) => record.id}
                dataSource={
                  filterTab === 'shopify'
                    ? shopifyProductState?.maxView
                    : productState?.maxView
                }
                columns={viewColumns}
                className="invoiceListTable"
                pagination={false}
                showSorterTooltip={false}
              />
            </Col>
          </Row>
          <Row style={{ marginBottom: 16, marginTop: 16 }}>
            <Col
              className="secondCol"
              xl={24}
              lg={24}
              sm={24}
              md={24}
              xs={24}
              style={{ boxShadow: '-5px 6px 9px -3px #b2b2b2' }}
            >
              <Row className="isoTabRow">
                <Col>
                  <div className="marketingText">
                    <IntlMessages id="most_click" />:
                  </div>
                </Col>
              </Row>
              <TableWrapper
                // scroll={{ x: false }}
                loading={
                  filterTab === 'shopify'
                    ? shopifyProductState.productLoad
                    : productState.productLoad
                }
                rowKey={(record) => record.id}
                dataSource={
                  filterTab === 'shopify'
                    ? shopifyProductState?.maxClick
                    : productState?.maxClick
                }
                columns={clickColumns}
                className="invoiceListTable"
                pagination={false}
                showSorterTooltip={false}
              />
            </Col>
          </Row>
          <Row>
            <Col
              className="secondCol"
              xl={24}
              lg={24}
              sm={24}
              md={24}
              xs={24}
              style={{ boxShadow: '-5px 6px 9px -3px #b2b2b2' }}
            >
              <Row className="isoTabRow">
                <Col>
                  <div className="marketingText">
                    <IntlMessages id="most_share" />:
                  </div>
                </Col>
              </Row>
              <TableWrapper
                // scroll={{ x: false }}
                loading={
                  filterTab === 'shopify'
                    ? shopifyProductState.productLoad
                    : productState.productLoad
                }
                rowKey={(record) => record.id}
                dataSource={
                  filterTab === 'shopify'
                    ? shopifyProductState.maxShare
                    : productState.maxShare
                }
                columns={shareColumns}
                className="invoiceListTable"
                pagination={false}
                showSorterTooltip={false}
              />
            </Col>
          </Row>
        </Col>
      </Row>
      <ProductForm
        initialValues={initObj}
        visible={visible}
        view={view}
        ConfigProduct={ConfigProduct}
        addCategory={() => {
          setState((p) => ({ ...p, visibleCategory: !visibleCategory }));
        }}
        onClose={(type, data) => {
          if (type === 'success') {
            updateData(data);
          } else {
            setState((p) => ({
              ...p,
              initObj: {},
              visible: false,
              view: false,
            }));
          }
        }}
        categories={categories}
      />
      <AddCategory
        initialValues={initObj}
        visible={visibleCategory}
        view={view}
        ConfigProduct={ConfigProduct}
        addCategory={() => {
          setState((p) => ({ ...p, visibleCategory: !visibleCategory }));
        }}
        onClose={(type, data) => {
          if (type === 'success') {
            updateData(data);
          } else {
            setState((p) => ({
              ...p,
              initObj: {},
              visibleCategory: false,
            }));
          }
        }}
        categories={categories}
      />
    </ProductStyles>
  );
};

export default Products;

/* eslint-disable react/jsx-no-bind */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable no-undef */
/* eslint-disable no-unused-vars */
/* eslint-disable no-nested-ternary */
/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
import React, { useEffect, useState } from 'react';
import { Col, Drawer, Input, Row, Select, Upload, Form, Icon } from 'antd';
import { isEmpty, isNull, isObject } from 'lodash';
import { useSelector } from 'react-redux';
import IntlMessages from '@chill/components/utility/intlMessages';
import {
  BottomViewWrapper,
  FormWrapper,
} from '@chill/assets/styles/drawerFormStyles';
import {
  PaperClipOutlined,
  DeleteOutlined,
  FilterOutlined,
  SwapOutlined,
  PlusCircleOutlined,
} from '@ant-design/icons';
import CButton from '@chill/components/uielements/CButton';
import InputNumber from '@chill/components/uielements/InputNumber';
import { getBase64 } from '@chill/lib/helpers/utility';
import Notification from '@chill/components/Notification';
import getApiData from '@chill/lib/helpers/apiHelper';
import Editor from '@chill/components/uielements/editor';
import useResetFormOnClose from '@chill/lib/hooks/useResetForm';
import theme from '@chill/config/theme/default';
import pickerSVG from '@chill/assets/images/picker.svg';
import MarketingWrapper from '../Marketing/Marketing.Styles';

function ProductForm(props) {
  const { initialValues, onClose, visible, view, categories, addCategory } =
    props;
  const edit = !isEmpty(initialValues);
  const initVal = initialValues || {};
  const [form] = Form.useForm();
  const [attachments, setAttachments] = useState({});
  const [btnLoader, setBtnLoader] = useState(false);
  const [productImg, setProductImg] = useState({});
  const [isImage, setIsImage] = useState({});
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  useResetFormOnClose({ visible, initVal, form });

  useEffect(() => {
    if (!edit) {
      form.resetFields();
    }
    console.log('initVal ===', initVal);
    if (isObject(initVal) && !isEmpty(initVal)) {
      setAttachments(initVal.file_data);
    }
  }, [visible]);

  const beforeUpload = (file, type = '') => {
    let validType =
      file.type === 'image/jpg' ||
      file.type === 'image/jpeg' ||
      file.type === 'image/png';

    if (type === '') {
      validType = file.type === 'application/pdf';
    }

    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      Notification('error', messageArray['err.size']);
      return false;
    }

    if (!validType) {
      Notification(
        'error',
        type === ''
          ? messageArray['common.pdfUpload']
          : messageArray['common.img.upload'],
      );
      return false;
    }

    if (type === 'img') {
      getBase64(file, (imageUrl) => {
        setProductImg({ imageUrl, loading: false });
      });
      setIsImage(file);
    } else {
      setAttachments(file);
    }

    return false;
  };

  // this function reset form data when close modal
  function handleForm(type = '', data = {}) {
    form.resetFields();
    setProductImg({});
    setAttachments({});
    onClose(type, data);
  }

  // this function for add new products
  async function addProduct(values) {
    // return;
    const formData = new FormData();
    const url = edit ? 'product/update' : 'product/add-product';

    Object.keys(values).map((k) => formData.append(k, values[k]));

    if (edit && isEmpty(isImage)) {
      formData.append('isImage', 0);
    } else {
      formData.append('isImage', 1);
    }
    if (edit && attachments?.fullUrl) {
      formData.append('isPdf', 0);
    } else {
      formData.append('isPdf', 1);
    }
    if (!isEmpty(isImage)) {
      formData.append('product_image[]', isImage);
    }
    // else {
    //   formData.append('product_image', initVal.product_image);
    // }
    if (!isEmpty(attachments)) {
      formData.append('product_image[]', attachments);
    }

    try {
      const response = await getApiData(url, formData, 'POST', {}, true);
      if (response.success) {
        Notification('success', response.message);
        handleForm('success', response.data);
      } else {
        Notification('error', response.message);
      }
      setBtnLoader(false);
    } catch (error) {
      console.log(error);
      setBtnLoader(false);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  // this function for validate data
  function validate(values) {
    let valid = true;

    const obj = values;
    // obj.product_image = initVal.product_image || '';
    // if (isObject(isImage)) {
    //   obj.product_image = isImage;
    // }
    if (!edit && isEmpty(isImage)) {
      Notification('error', messageArray['common.uploadImg']);
      valid = false;
    }

    // if (isEmpty(attachments.fullUrl)) {
    //   obj.product_guide = initVal?.file_data?.fullUrl || '';
    // }
    if (
      (!edit && isEmpty(attachments)) ||
      (edit && initVal.product_guide && isEmpty(attachments)) ||
      (edit && isNull(initVal.product_guide) && isEmpty(attachments))
    ) {
      Notification('error', messageArray['common.add.attachment']);
      valid = false;
    }

    if (edit) obj.product_id = initVal.id;

    if (valid) {
      setBtnLoader(true);
      addProduct(obj);
    }
  }

  function renderAttachment(data = {}) {
    return (
      <div className="attachmentView">
        <p className="attachments">
          <img src={pickerSVG} alt="icon" style={{ marginRight: '5px' }} />
          {'  '}
          <IntlMessages id={data.name || data.filename} />
          {view ? null : (
            <DeleteOutlined
              className="deleteIconStyle"
              onClick={() => setAttachments({})}
            />
          )}
        </p>
      </div>
    );
  }

  const uploadButton = (
    <div>
      <Icon type="plus" />
      <div className="ant-upload-text">
        <IntlMessages id="common.upload" />
      </div>
    </div>
  );

  return (
    <Drawer
      title={
        <>
          <IntlMessages
            id={
              view
                ? 'product.detail.title'
                : edit
                ? 'Edit Product'
                : 'Add Product'
            }
          />
          {edit || view ? (
            <p
              style={{ fontSize: '14px', opacity: 0.8, fontWeight: 'normal' }}
              className="subTitleStyle"
            >
              <IntlMessages id="product.subTitle" />
            </p>
          ) : null}
        </>
      }
      width={500}
      style={{ height: window.innerHeight - 60, marginTop: 70 }}
      onClose={handleForm}
      visible={visible}
      bodyStyle={{ paddingBottom: 80 }}
      destroyOnClose
    >
      <FormWrapper>
        <Form
          form={form}
          onFinish={validate}
          layout="vertical"
          initialValues={initVal}
        >
          <Row
            gutter={[12, 20]}
            style={{
              marginBottom: 20,
            }}
          >
            <Col>
              <Form.Item>
                <Upload
                  showUploadList={false}
                  listType="picture-card"
                  beforeUpload={(file) => beforeUpload(file, 'img')}
                >
                  {productImg.imageUrl || initVal.product_image ? (
                    <img
                      src={productImg.imageUrl || initVal.product_image}
                      alt="product"
                      style={{ width: '100%', height: '100%' }}
                    />
                  ) : (
                    uploadButton
                  )}
                </Upload>
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="input.product.title" />}
                name="product_name"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.productName" />,
                  },
                  {
                    max: 50,
                    message: <IntlMessages id="err.max.50char" />,
                  },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="input.product.regularprice" />}
                name="regular_price"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.price" />,
                  },
                ]}
              >
                <InputNumber
                  min={0}
                  formatter={(value) =>
                    `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                />
              </Form.Item>
            </Col>
            <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="input.product.saleprice" />}
                name="sell_price"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.price" />,
                  },
                ]}
              >
                <InputNumber
                  min={0}
                  formatter={(value) =>
                    `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                />
              </Form.Item>
            </Col>
            {/* <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="input.product.stock" />}
                name="stock"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.stock" />,
                  },
                ]}
              >
                <Input />
              </Form.Item>
            </Col> */}
            <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="input.product.sku" />}
                name="sku_code"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.sku" />,
                  },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <div className="iconStyle marketingMainDiv">
                <span>
                  <p
                    className="addNewUser"
                    style={{
                      color: theme.colors.primaryColor,
                      alignItems: 'center',
                      // display:'flex'
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        cursor: 'pointer',
                      }}
                      role="button"
                      tabIndex={-1}
                      onKeyPress={addCategory}
                      onClick={addCategory}
                    >
                      <p className="iconPadding" style={{ paddingRight: 8 }}>
                        <PlusCircleOutlined />
                      </p>
                      <IntlMessages id="marketing.createNew" />
                      &nbsp;
                      <IntlMessages id="input.product.category" />
                    </div>
                  </p>
                </span>
              </div>

              <Form.Item
                label={<IntlMessages id="input.product.category" />}
                name="category_id"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.category" />,
                  },
                ]}
              >
                <Select>
                  {categories.map((item) => (
                    <Select.Option key={`categories${item.id}`} value={item.id}>
                      {item.category_name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="input.desc.label" />}
                name="description"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.desc" />,
                  },
                ]}
              >
                <Editor />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="input.product.tags" />}
                name="tags"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.tags" />,
                  },
                ]}
              >
                <Select mode="tags" />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="input.faq.label" />}
                name="faq"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.faq" />,
                  },
                ]}
              >
                <Editor />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="input.video.label" />}
                name="video_link"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.link" />,
                  },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="input.product.link.label" />}
                name="product_link"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.link" />,
                  },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item label={<IntlMessages id="marketing.attachments" />}>
                <div style={{ display: 'flex' }}>
                  {!isEmpty(attachments) ? renderAttachment(attachments) : null}
                </div>
                <Upload
                  beforeUpload={(file) => beforeUpload(file, '')}
                  showUploadList={false}
                >
                  <p className="attachments chooseList">
                    <PaperClipOutlined />
                    {'  '}
                    <IntlMessages id="marketing.attachFiles" />
                  </p>
                </Upload>
              </Form.Item>
            </Col>
          </Row>
          <BottomViewWrapper className="bottomBtnWrapper">
            <Row gutter={12} style={{ width: '100%' }}>
              {view ? null : (
                <Col xs={24}>
                  <Form.Item className="btn-form">
                    <CButton
                      htmlType="submit"
                      loading={btnLoader}
                      disabled={btnLoader}
                      className="submitBtnStyle"
                      style={{ marginRight: 20 }}
                    >
                      <IntlMessages
                        id={edit ? 'common.changes' : 'common.submit'}
                      />
                    </CButton>
                  </Form.Item>
                </Col>
              )}
              {/* <Col xs={24}>
                <Form.Item className="btn-form">
                  <div
                    className="cancelBtnStyle"
                    onClick={handleForm}
                    role="button"
                    onKeyPress=""
                    tabIndex="-1"
                  >
                    <IntlMessages id={view ? 'common.done' : 'common.cancel'} />
                  </div>
                </Form.Item>
              </Col> */}
            </Row>
          </BottomViewWrapper>
        </Form>
      </FormWrapper>
    </Drawer>
  );
}

export default ProductForm;

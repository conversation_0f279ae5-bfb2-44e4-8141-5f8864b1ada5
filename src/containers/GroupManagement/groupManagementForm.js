/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
import React, { useEffect, useState } from 'react';
import { Col, Drawer, Input, Row, Form } from 'antd';
import { isEmpty } from 'lodash';
import { useSelector } from 'react-redux';
import IntlMessages from '@chill/components/utility/intlMessages';
import {
  BottomViewWrapper,
  FormWrapper,
} from '@chill/assets/styles/drawerFormStyles';
import CButton from '@chill/components/uielements/CButton';
import Notification from '@chill/components/Notification';
import getApiData from '@chill/lib/helpers/apiHelper';
import useResetFormOnClose from '@chill/lib/hooks/useResetForm';

function GroupManagementForm(props) {
  const { initialValues, onClose, visible } = props;
  const edit = !isEmpty(initialValues);
  const initVal = initialValues || {};
  const [form] = Form.useForm();
  const [btnLoader, setBtnLoader] = useState(false);
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  useResetFormOnClose({ visible, initVal, form });

  useEffect(() => {
    if (!edit) {
      form.resetFields();
    }
  }, [visible]);

  // this function reset form data when close modal
  function handleForm(type = '', data = {}) {
    form.resetFields();
    onClose(type, data);
  }

  // this function for add new faqs
  async function addGroup(values) {
    const url = edit
      ? 'manage-group/update-group'
      : 'manage-group/create-group';

    try {
      const response = await getApiData(url, values, 'POST');
      if (response.success) {
        Notification('success', response.message);
        handleForm('success', response.data);
      } else {
        Notification('error', response.message);
      }
      setBtnLoader(false);
    } catch (error) {
      console.log(error);
      setBtnLoader(false);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  // this function for validate data
  function validate(values) {
    const obj = values;
    if (edit) obj.group_id = initVal.id;
    setBtnLoader(true);
    addGroup(obj);
  }

  return (
    <Drawer
      title={<IntlMessages id={edit ? 'group.edit' : 'group.add'} />}
      width={500}
      style={{ height: window.innerHeight - 60, marginTop: 70 }}
      onClose={() => handleForm()}
      visible={visible}
      bodyStyle={{ paddingBottom: 80 }}
      destroyOnClose
    >
      <FormWrapper>
        <Form
          form={form}
          onFinish={(e) => validate(e)}
          layout="vertical"
          initialValues={initVal}
        >
          <Row
            gutter={[12, 20]}
            style={{
              marginBottom: 20,
            }}
          >
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="common.gpName" />}
                name="group_name"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.group" />,
                  },
                ]}
              >
                <Input placeholder="Group Name" />
              </Form.Item>
            </Col>
          </Row>
          <BottomViewWrapper className="bottomBtnWrapper">
            <Row gutter={12} style={{ width: '100%' }}>
              <Col xs={24}>
                <Form.Item className="btn-form">
                  <CButton
                    className="submitBtnStyle"
                    htmlType="submit"
                    loading={btnLoader}
                    disabled={btnLoader}
                    style={{ marginRight: 20 }}
                  >
                    <IntlMessages
                      id={edit ? 'common.changes' : 'common.submit'}
                    />
                  </CButton>
                </Form.Item>
              </Col>
              {/* <Form.Item className="btn-form">
                <div
                  className="cancelBtnStyle"
                  onClick={handleForm}
                  role="button"
                  onKeyPress=""
                  tabIndex="-1"
                >
                  <IntlMessages id="common.cancel" />
                </div>
              </Form.Item> */}
            </Row>
          </BottomViewWrapper>
        </Form>
      </FormWrapper>
    </Drawer>
  );
}

export default GroupManagementForm;

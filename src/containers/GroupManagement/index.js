/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
import React, { useEffect } from 'react';
import { Col, Row, Divider, Input } from 'antd';
import { isArray, isEmpty, isObject } from 'lodash';
import { useSelector } from 'react-redux';
import {
  FilterOutlined,
  PlusCircleOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import theme from '@chill/config/theme/default';
import TableWrapper from '@chill/assets/styles/AntTables.styles';
import IntlMessages from '@chill/components/utility/intlMessages';
import Popover from '@chill/components/uielements/popover';
import getApiData from '@chill/lib/helpers/apiHelper';
import Popconfirms from '@chill/components/Feedback/Popconfirm';
import Notification from '@chill/components/Notification';
import GroupManagementForm from './groupManagementForm';
import GroupStyles from './groupManagemet.styles';

/**
 *
 * @module GroupManagement
 */

const GroupManagement = () => {
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const [actionMenu, setActionMenu] = React.useState(false);
  const [isIndex, setIsIndex] = React.useState(null);
  const [state, setState] = React.useState({
    initObj: {},
    visible: false,
  });
  const [groupState, setGroupState] = React.useState({
    groups: [],
    groupLoad: false,
  });
  const [filter, setFilter] = React.useState({ pagination: {}, filters: {} });
  const [filterMode, setFilterMode] = React.useState(false);
  const [sort, setSort] = React.useState(false);
  const [currentPage, setCurrentPage] = React.useState(0);

  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  // this function for get group's
  /** this function for get group's list
   * @function getGroupList
   * @param {object} data sort
   */
  async function getGroupList(data = {}) {
    setGroupState((p) => ({
      ...p,
      groupLoad: true,
    }));

    const url = `manage-group/index`;

    try {
      const response = await getApiData(url, data);
      if (response.success && isArray(response.data)) {
        setGroupState((preState) => ({
          ...preState,
          groups: isArray(response.data) ? response.data : [],
          groupLoad: false,
        }));
        const pagination =
          filter && filter.pagination ? { ...filter.pagination } : {};
        pagination.total = response.total_count || 0;
        pagination.pageSize = response.per_page || 10;
        pagination.current = response.page || 1;
        setFilter((f) => ({ ...f, pagination }));
      } else {
        setGroupState((preState) => ({
          ...preState,
          groupLoad: false,
        }));
      }
    } catch (err) {
      setGroupState((preState) => ({
        ...preState,
        groupLoad: false,
      }));
    }
  }

  // this function filters data and get updated list of group
  /** this function filters data and get updated list of group
   * @function fetchDataFilters
   * @param {object} data page, title
   */
  function fetchDataFilters() {
    const page =
      filter && filter.pagination && filter.pagination.current
        ? filter.pagination.current
        : 1;
    const filters = filter && filter.filters ? filter.filters : {};
    getGroupList({ page, ...filters });
  }

  useEffect(() => {
    fetchDataFilters();
  }, [sort]);

  // this function for delete group
  /** this function for delete group
   * @function deleteGroup
   * @param {object} data group_id
   */
  async function deleteGroup() {
    const data = {
      group_id:
        isObject(state) &&
        isObject(state.initObj) &&
        !isEmpty(state.initObj) &&
        state.initObj.id,
    };
    const groupAry = isArray(groupState.groups) ? groupState.groups : [];
    setGroupState({
      groupLoad: true,
    });
    try {
      const response = await getApiData(
        'manage-group/delete-group',
        data,
        'POST',
      );
      let msgTitle = 'error';
      if (response.success) {
        msgTitle = 'success';
        fetchDataFilters();
      } else {
        setGroupState((pre) => ({
          ...pre,
          groups: groupAry,
          groupLoad: false,
        }));
      }
      Notification(msgTitle, response.message);
    } catch (error) {
      console.log('error ===', error);
      setGroupState((pre) => ({
        ...pre,
        groups: groupAry,
        groupLoad: false,
      }));
    }
  }

  // fun. for visible PopOver
  function handleVisible() {
    setActionMenu((visiblePre) => !visiblePre);
  }

  // this function for handle pagination
  /** this function for handle pagination
   * @function onChange
   * @param {object} data page
   */
  const onChange = (pagination) => {
    const pager = filter && filter.pagination ? { ...filter.pagination } : {};
    setCurrentPage((pagination.current - 1) * pagination.pageSize);
    pager.current = pagination.current;
    setFilter((f) => ({ ...f, pagination: pager }));
    getGroupList({
      page: pagination.current,
      ...filter.filters,
    });
    setSort(false);
  };

  // Popover content or list
  const content = (
    <GroupStyles>
      <div className="popMainDiv">
        <div
          role="button"
          className="isoDropdownLink"
          onKeyPress=""
          tabIndex="-1"
          onClick={() => {
            setState({ ...state, visible: true });
            setIsIndex(null);
          }}
        >
          <EditOutlined style={{ paddingRight: 12, fontSize: 16 }} />
          <IntlMessages id="group.edit" />
        </div>
        <div className="isoDropdownLink">
          <Popconfirms
            title={<IntlMessages id="Sure to delete?" />}
            okText={<IntlMessages id="DELETE" />}
            cancelText={<IntlMessages id="No" />}
            onConfirm={() => {
              deleteGroup();
              setIsIndex(null);
            }}
            onCancel={null}
          >
            <DeleteOutlined style={{ paddingRight: 12, fontSize: 16 }} />
            <IntlMessages id="group.delete" />
          </Popconfirms>
        </div>
      </div>
    </GroupStyles>
  );

  // set item id in state
  function handleVisibleChange(item1) {
    setIsIndex(item1.id);
  }

  const columns = [
    {
      title: <IntlMessages id="ID" />,
      dataIndex: 'id',
      rowKey: 'id',
      width: 10,
      className: 'fullname-cell',
      render: (text, data, index) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="ID" />
            </span>
            <span className="mobile-lbl-val">
              {index + currentPage + 1 || '-'}
            </span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="antTable.title.name" />,
      dataIndex: 'group_name',
      rowKey: 'group_name',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="antTable.title.name" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Action" />,
      dataIndex: 'action',
      rowKey: 'action',
      className: 'fullname-cell',
      width: 50,
      fixed: 'right',
      render: (text, item) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Action" />
            </span>
            <span className="mobile-lbl-val">
              <Popover
                content={content}
                trigger="click"
                visible={isIndex === item.id ? !actionMenu : false}
                onVisibleChange={() => handleVisible()}
                arrowPointAtCenter
                placement="bottomRight"
              >
                <MoreOutlined
                  onClick={() => {
                    handleVisibleChange(item);
                    setState({ ...state, initObj: item });
                  }}
                  style={{ color: '#000', fontSize: 24 }}
                />
              </Popover>
            </span>
          </div>
        );
      },
    },
  ];

  // Add Group View
  const addNew = () => {
    return (
      <GroupStyles>
        <div className="iconStyle marketingMainDiv">
          <span>
            <p
              className="addNewUser"
              style={{
                color: theme.colors.primaryColor,
                alignItems: 'center',
              }}
            >
              <FilterOutlined
                className="filterIcon"
                style={{
                  color: filterMode ? theme.colors.primaryColor : '#000',
                }}
                onClick={() => {
                  setFilterMode((pre) => !pre);
                  setFilter({ filters: {} });
                }}
              />
              <div
                role="button"
                tabIndex="0"
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                }}
                onClick={() => setState({ visible: true })}
                onKeyDown={() => setState({ visible: true })}
              >
                <p className="iconPadding" style={{ paddingRight: 8 }}>
                  <PlusCircleOutlined />
                </p>
                <IntlMessages id="group.add" />
              </div>
            </p>
          </span>
        </div>
      </GroupStyles>
    );
  };

  // this function renders search view
  function renderSearchBar() {
    return (
      <div style={{ padding: '15px' }}>
        <Row gutter={10}>
          <Col lg={8} sm={24} md={12} xs={24} style={{ marginBottom: 8 }}>
            <Input.Search
              style={{ minWidth: '100%' }}
              onChange={(e) => {
                const val = e && e.target ? e.target.value : '';
                const flt = filter ? { ...filter } : {};
                flt.filters.group_name = val;
                setFilter(flt);
                getGroupList(flt.filters);
              }}
              value={filter.group_name}
              allowClear
              placeholder={messageArray['common.gpName']}
            />
          </Col>
        </Row>
      </div>
    );
  }

  const { initObj, visible } = state;
  return (
    <GroupStyles>
      <Row className="isoTabRow">
        <Col xl={8} lg={8} sm={8} md={4} xs={8}>
          <div className="marketingText">
            <IntlMessages id="sidebar.groupManagement" />
          </div>
        </Col>
        <Col sm={16} xs={16} className="hiddenCol">
          {addNew()}
        </Col>
        <Col xl={16} lg={16} md={20} xs={16} className="isoAddNew">
          {addNew()}
        </Col>
      </Row>
      <Divider className="horizontalDevider" />
      {filterMode ? renderSearchBar() : null}
      <TableWrapper
        loading={groupState.groupLoad}
        rowKey={(record) => record.id}
        dataSource={groupState.groups}
        onChange={onChange}
        columns={columns}
        className="invoiceListTable"
        pagination={filter.pagination || {}}
        showSorterTooltip={false}
      />
      <GroupManagementForm
        initialValues={initObj}
        visible={visible}
        onClose={(type) => {
          if (type === 'success') {
            fetchDataFilters();
            setState({ initObj: {}, visible: false });
          } else {
            setState({ initObj: {}, visible: false });
          }
        }}
      />
    </GroupStyles>
  );
};

export default GroupManagement;

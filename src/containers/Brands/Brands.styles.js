import styled from 'styled-components';
import WithDirection from '@chill/lib/helpers/rtl';

const BrandStyle = styled.div`
  .popMainDiv {
    display: flex;
    flex-direction: column;
  }
  .isoDropdownLink {
    color: #000;
    padding: 8px 0px;
    cursor: pointer;
  }

  .isoTabRow {
    padding: 16px;
  }

  .dataList > div > div > div > ul {
    margin: 16px;
  }
  .listNameView {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .nameWithImg {
    width: 30px;
    height: 30px;
    background-color: #eee;
    margin-right: 12px;
    border-radius: 30px;
  }
  .addNewUser {
    display: flex;
    justify-content: flex-end;
    cursor: pointer;
  }
  .iconPadding {
    @media (max-width: 281px) {
      display: none;
    }
  }
  .iconImg {
    width: 16px;
    height: 16px;
    margin-right: 12px;
  }
  .filterIcon {
    padding-left: 12px;
    padding-right: 12px;
    color: rgb(0, 0, 0);
    margin: 0px 12px;
    border-left: 1px solid #eee;
    border-right: 1px solid #eee;
  }
  .filterIcon1 {
    transform: rotate(90deg);
  }
  .changeBtn {
    border-radius: 8px;
    font-weight: 500;
    border: 0px;
    background: linear-gradient(90deg, #039be5 38%, #03a9f4 90%);
    &:hover {
      background: linear-gradient(90deg, #039be5 38%, #03a9f4 90%);
    }
  }
`;

export default WithDirection(BrandStyle);

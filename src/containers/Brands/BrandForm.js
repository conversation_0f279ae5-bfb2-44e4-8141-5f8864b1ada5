/* eslint-disable react/jsx-no-bind */
/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable react/jsx-props-no-spreading */
import React, { useEffect, useState } from 'react';
import { Drawer, Form, Input, Row } from 'antd';
import { useSelector } from 'react-redux';
import IntlMessages from '@chill/components/utility/intlMessages';
import { BottomViewWrapper } from '@chill/assets/styles/drawerFormStyles';
import getApiData from '@chill/lib/helpers/apiHelper';
import Button from '@chill/components/uielements/CButton';
import Notification from '@chill/components/Notification';
import CountryPicker from '@chill/components/uielements/countryPicker';
import useResetFormOnClose from '@chill/lib/hooks/useResetForm';
import BrandStyle from './Brands.styles';

export default function BrandForm(props) {
  const {
    visible,
    setState = () => {},
    updateData,
    initialValues,
    edit,
    setEdit = () => null,
  } = props;
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);
  const initVal = initialValues || {};
  const [btnLoader, setBtnLoader] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    if (!edit) {
      form.resetFields();
    }
  }, [visible]);

  useResetFormOnClose({ visible, initVal, form });

  const onClose = (type = '', data = {}) => {
    form.resetFields();
    setState((p) => ({ ...p, visible: false, view: false }));
    setEdit(false);
    if (type === 'success') {
      updateData(data);
    } else {
      setState((p) => ({ ...p, initObj: {} }));
    }
  };

  async function addBrand(values) {
    const url = edit ? 'user/update-user' : 'user/add-user';
    try {
      const response = await getApiData(url, values, 'POST');
      if (response.success) {
        Notification('success', response.message);
        onClose('success', response.data);
      } else {
        Notification('error', response.message);
      }
      setBtnLoader(false);
    } catch (error) {
      console.log(error);
      setBtnLoader(false);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  function validate(values) {
    const obj = values;
    obj.type = 'brand';
    if (edit) obj.user_id = initVal.id;
    setBtnLoader(true);
    addBrand(obj);
  }

  const prefixSelector = (
    <Form.Item
      name="phone_code"
      className="countryFormItem"
      style={{ marginBottom: -2 }}
    >
      <CountryPicker valType="dial_code" className="countryPicker" />
    </Form.Item>
  );

  return (
    <Drawer
      title={<IntlMessages id={edit ? 'brand.edit' : 'title.addbrand'} />}
      width={450}
      style={{ height: window.innerHeight - 60, marginTop: 70 }}
      onClose={onClose}
      visible={visible}
      bodyStyle={{ paddingBottom: 80 }}
      destroyOnClose
      form={form}
    >
      <BrandStyle>
        <Form
          layout="vertical"
          form={form}
          onFinish={validate}
          initialValues={initVal}
        >
          <Form.Item
            label={<IntlMessages id="antTable.title.name" />}
            name="full_name"
            rules={[
              {
                required: true,
                message: <IntlMessages id="req.name" />,
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            className="formLabel"
            label={<IntlMessages id="antTable.title.email" />}
            name="email"
            rules={[
              {
                type: 'email',
                required: true,
                message: <IntlMessages id="req.email" />,
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            className="formLabel"
            label={<IntlMessages id="input.contactname" />}
            name="contact_name"
            rules={[
              {
                required: true,
                message: <IntlMessages id="req.contactname" />,
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            className="formLabel"
            label={<IntlMessages id="input.contactnumber" />}
            name="phone"
            rules={[
              {
                required: true,
                message: <IntlMessages id="req.contactnumber" />,
              },
              {
                max: 12,
                message: <IntlMessages id="err.contactnumber" />,
              },
              {
                min: 4,
                message: <IntlMessages id="err.contactnumber" />,
              },
            ]}
          >
            <Input
              style={{ width: '100%' }}
              min={1}
              maxLength={15}
              type="number"
              addonBefore={prefixSelector}
            />
          </Form.Item>
          <Form.Item
            className="formLabel"
            label={<IntlMessages id="input.packagename" />}
            name="package_name"
            rules={[
              {
                required: true,
                message: <IntlMessages id="req.packagename" />,
              },
            ]}
          >
            <Input />
          </Form.Item>
          <BottomViewWrapper className="bottomBtnWrapper">
            <Row gutter={12} style={{ width: '100%' }}>
              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={btnLoader}
                  disabled={btnLoader}
                  style={{ marginRight: 20 }}
                >
                  <IntlMessages
                    id={edit ? 'common.changes' : 'common.submit'}
                  />
                </Button>
              </Form.Item>
              <Form.Item>
                <div
                  className="cancelBtnStyle"
                  onClick={onClose}
                  role="button"
                  onKeyPress=""
                  tabIndex="-1"
                  style={{ cursor: 'pointer' }}
                >
                  <IntlMessages id="common.cancel" />
                </div>
              </Form.Item>
            </Row>
          </BottomViewWrapper>
        </Form>
      </BrandStyle>
    </Drawer>
  );
}

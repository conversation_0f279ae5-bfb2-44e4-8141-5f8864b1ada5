/* eslint-disable react/jsx-no-bind */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable prefer-template */
/* eslint-disable global-require */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import { Row, Col, Input } from 'antd';
import React, { useState } from 'react';
import { isArray, isEmpty, isObject, findIndex } from 'lodash';
import { useSelector } from 'react-redux';
import TableWrapper from '@chill/assets/styles/AntTables.styles';
import {
  PlusCircleOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  FilterOutlined,
  SwapOutlined,
} from '@ant-design/icons';
import theme from '@chill/config/theme/default';
import IntlMessages from '@chill/components/utility/intlMessages';
import Popover from '@chill/components/uielements/popover';
import getApiData from '@chill/lib/helpers/apiHelper';
import Popconfirms from '@chill/components/Feedback/Popconfirm';
import Notification from '@chill/components/Notification';
import BrandStyle from './Brands.styles';
import BrandForm from './BrandForm';

/**
 *
 * @module Brands
 */

const Brands = () => {
  const uData = useSelector((state) => state.Auth.userData);
  const user = isObject(uData) ? uData : {};
  const userId = isObject(user) && user.id ? user.id : '';

  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  const [actionMenu, setActionMenu] = React.useState(false);
  const [sort, setSort] = useState(false);
  const [isIndex, setIsIndex] = React.useState(null);
  const [state, setState] = useState({
    initObj: {},
    visible: false,
  });
  const { visible, initObj } = state;
  const [brandState, setBrandState] = React.useState({
    brands: [],
    brandLoad: false,
  });
  const [filter, setFilter] = React.useState({ pagination: {}, filters: {} });
  const [filterMode, setFilterMode] = React.useState(false);
  const [edit, setEdit] = React.useState(false);

  function handleVisible() {
    setActionMenu((visiblePre) => !visiblePre);
  }
  function handleVisibleChange(item1) {
    setIsIndex(item1.id);
  }

  // this function for get brands
  /** this function for get brands list
   * @function getBrandList
   * @param {object} data sort || {}
   */
  async function getBrandList(data = {}, sorting) {
    setBrandState((p) => ({
      ...p,
      brandLoad: true,
    }));
    const dataSort = {};
    if (sorting === 'sort' && sort) {
      dataSort.sort = 'ASC';
    }
    if (sorting === 'sort' && !sort) {
      dataSort.sort = 'DESC';
    }
    // if (filter?.pagination?.current >= 1) {
    //   dataSort.page = filter.pagination.current;
    // }
    try {
      const response = await getApiData(
        'brands/get-brands',
        sorting === 'sort' ? dataSort : data,
        'POST',
      );
      if (response.success && isArray(response.data)) {
        setBrandState((preState) => ({
          ...preState,
          brands: isArray(response.data) ? response.data : [],
          brandLoad: false,
        }));
        const pagination =
          filter && filter.pagination ? { ...filter.pagination } : {};
        pagination.total = response.total_count || 0;
        pagination.pageSize = response.per_page || 10;
        pagination.current = response.page || 1;
        setFilter((f) => ({ ...f, pagination }));
      } else {
        setBrandState((preState) => ({
          ...preState,
          brandLoad: false,
        }));
      }
    } catch (err) {
      setBrandState((preState) => ({
        ...preState,
        brandLoad: false,
      }));
    }
  }
  React.useEffect(() => {
    getBrandList();
  }, []);

  React.useEffect(() => {
    if (!filterMode) {
      getBrandList();
    }
  }, [filterMode]);

  /** this function for filter
   * @function fetchDataFilters
   * @param {object} data Brand name
   */
  function fetchDataFilters() {
    const page =
      filter && filter.pagination && filter.pagination.current
        ? filter.pagination.current
        : 1;
    const filters = filter && filter.filters ? filter.filters : {};
    getBrandList({ page, ...filters });
  }

  // this function for update new product data
  /** this function for update brand data
   * @function updateData
   * @param {object} data id
   */
  function updateData(data) {
    const brandAry = isArray(brandState.brands) ? [...brandState.brands] : [];
    const editMode = !isEmpty(state.initObj) && data.id;
    const dataIndex = findIndex(brandAry, { id: data.id });
    if (editMode && dataIndex > -1) {
      brandAry[dataIndex] = data;
      setBrandState({
        brands: brandAry,
      });
    } else {
      getBrandList();
    }
    setState({ initObj: {}, visible: false, view: false });
  }

  /** this function for delete brand
   * @function deleteProduct
   * @param {object} data user_id, brand_id
   */
  async function deleteProduct() {
    const data = {
      user_id: userId,
      brand_id:
        isObject(state) &&
        isObject(state.initObj) &&
        !isEmpty(state.initObj) &&
        state.initObj.id,
    };
    const brandAry = isArray(brandState.brands) ? brandState.brands : [];
    setBrandState({
      brandLoad: true,
    });

    try {
      const response = await getApiData('brands/remove-brand', data, 'POST');
      let msgTitle = 'error';
      if (response.success) {
        msgTitle = 'success';
        fetchDataFilters();
      } else {
        setBrandState((pre) => ({
          ...pre,
          brands: brandAry,
          brandLoad: false,
        }));
      }
      Notification(msgTitle, response.message);
    } catch (error) {
      console.log('error ===', error);
      setBrandState((pre) => ({
        ...pre,
        brands: brandAry,
        brandLoad: false,
      }));
    }
  }

  /** this function for pagination
   * @function onChange
   * @param {object} data page
   */
  function onChange(pagination) {
    const pager = filter && filter.pagination ? { ...filter.pagination } : {};
    pager.current = pagination.current;
    setFilter((f) => ({ ...f, pagination: pager }));
    getBrandList({
      page: pagination.current,
      ...filter.filters,
    });
    setSort(false);
  }

  const content = (
    <BrandStyle>
      <div className="popMainDiv">
        <div
          className="isoDropdownLink"
          onClick={() => {
            setState({ ...state, visible: true });
            setEdit(true);
            setIsIndex(null);
          }}
        >
          <EditOutlined style={{ paddingRight: 12, fontSize: 16 }} />
          <IntlMessages id="brand.edit" />
        </div>
        <div className="isoDropdownLink">
          <Popconfirms
            title={<IntlMessages id="Sure to delete?" />}
            okText={<IntlMessages id="DELETE" />}
            cancelText={<IntlMessages id="No" />}
            onConfirm={() => {
              deleteProduct();
              setIsIndex(null);
            }}
            onCancel={null}
          >
            <DeleteOutlined style={{ paddingRight: 12, fontSize: 16 }} />
            <IntlMessages id="brand.delete" />
          </Popconfirms>
        </div>
      </div>
    </BrandStyle>
  );

  const columns = [
    {
      title: <IntlMessages id="No" />,
      dataIndex: 'id',
      rowKey: 'id',
      width: 50,
      className: 'fullname-cell',
      render: (text, data, index) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="No" />
            </span>
            <span className="mobile-lbl-val">{index + 1 || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="th.productname" />,
      dataIndex: 'product_name',
      rowKey: 'product_name',
      className: 'fullname-cell',
      render: (text, data) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="th.productname" />
            </span>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              {data.product_image && (
                <img
                  src={data.product_image}
                  style={{
                    width: 30,
                    height: 30,
                    backgroundColor: '#eee',
                    marginRight: 12,
                    borderRadius: 30,
                  }}
                  alt="product"
                />
              )}

              <span className="mobile-lbl-val" style={{ marginLeft: 8 }}>
                {text || '-'}
              </span>
            </div>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="dashboard.totalusers" />,
      dataIndex: 'total_users',
      rowKey: 'total_users',
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="dashboard.totalusers" />
            </span>
            <span className="mobile-lbl-val">{text || '0'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="th.userperday" />,
      dataIndex: 'total_user_per_day',
      rowKey: 'total_user_per_day',
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="th.userperday" />
            </span>
            <span className="mobile-lbl-val">{text || '0'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="th.activeuser" />,
      dataIndex: 'total_active_users',
      rowKey: 'total_active_users',
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="th.activeuser" />
            </span>
            <span className="mobile-lbl-val">{text || '0'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="th.platforms" />,
      dataIndex: 'platforms',
      rowKey: 'platforms',
      className: 'fullname-cell',
      render: (text, item) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="th.platforms" />
            </span>
            <span className="mobile-lbl-val">
              {!isEmpty(item.platforms) ? text.join(', ') : '-'}
            </span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="th.brandname" />,
      dataIndex: 'full_name',
      rowKey: 'full_name',
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="th.brandname" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      key: 'sort_created',
      title: <IntlMessages id="Action" />,
      dataIndex: 'action',
      rowKey: 'action',
      className: 'fullname-cell',
      width: 50,
      render: (text, item) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Action" />
            </span>
            <span className="mobile-lbl-val">
              <Popover
                content={content}
                trigger="click"
                open={isIndex === item.id ? !actionMenu : false}
                onOpenChange={handleVisible}
                arrowPointAtCenter
                placement="bottomRight"
              >
                <MoreOutlined
                  onClick={() => {
                    handleVisibleChange(item);
                    setState({ ...state, initObj: item });
                  }}
                  style={{ color: '#000', fontSize: 24 }}
                />
              </Popover>
            </span>
          </div>
        );
      },
    },
  ];

  const addNew = () => {
    return (
      <BrandStyle>
        <div className="iconStyle">
          <span>
            <div
              className="addNewUser"
              style={{
                color: theme.colors.primaryColor,
                alignItems: 'center',
              }}
            >
              <SwapOutlined
                className="filterIcon1"
                style={{ color: sort ? theme.colors.primaryColor : '#000' }}
                onClick={() => {
                  setSort(!sort);
                  getBrandList({}, 'sort');
                }}
              />
              <div
                className="filterIcon"
                style={{
                  color: filterMode ? theme.colors.primaryColor : '#000',
                }}
                onClick={() => setFilterMode((pre) => !pre)}
              >
                <FilterOutlined />
              </div>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                }}
                onClick={() => {
                  setState((p) => ({
                    ...p,
                    visible: true,
                    initObj: { phone_code: '+44' },
                  }));
                }}
              >
                <p className="iconPadding" style={{ paddingRight: 8 }}>
                  <PlusCircleOutlined />
                </p>
                <IntlMessages id="title.addbrand" />
              </div>
            </div>
          </span>
        </div>
      </BrandStyle>
    );
  };

  // this function renders search view
  function renderSearchBar() {
    return (
      <div style={{ padding: '15px' }}>
        <Row gutter={10}>
          <Col lg={8} sm={24} md={12} xs={24}>
            <Input.Search
              style={{ minWidth: '100%', marginBottom: 12 }}
              onChange={(e) => {
                const val = e && e.target ? e.target.value : '';
                const flt = filter ? { ...filter } : {};
                flt.filters.full_name = val;
                setFilter(flt);
                getBrandList(flt.filters);
              }}
              value={filter.full_name}
              allowClear
              placeholder={messageArray['th.brandname']}
            />
          </Col>
          {/* <Col lg={8} sm={24} md={12} xs={24}>
            <Input.Search
              style={{ minWidth: '100%', marginBottom: 12 }}
              onChange={(e) => {
                const val = e && e.target ? e.target.value : '';
                const flt = filter ? { ...filter } : {};
                flt.filters.package_name = val;
                setFilter(flt);
                getBrandList(flt.filters);
              }}
              value={filter.package_name}
              allowClear
              placeholder={messageArray['input.packagename']}
            />
          </Col> */}
        </Row>
      </div>
    );
  }

  return (
    <div style={{ backgroundColor: '#fff', height: '100%' }}>
      <BrandStyle>
        <Row className="isoTabRow" align="middle">
          <Col xl={16} lg={16} sm={14} md={16} xs={10}>
            <div>
              <IntlMessages id="BRAND NAME" />
            </div>
          </Col>

          <Col xl={8} lg={8} md={8} sm={10} xs={14} className="isoAddNew">
            {addNew()}
          </Col>
        </Row>
        {filterMode ? renderSearchBar() : null}
        <Row>
          <Col className="dataList" xl={24} lg={24} sm={24} md={24} xs={24}>
            <TableWrapper
              loading={brandState.brandLoad}
              rowKey={(record) => record.id}
              dataSource={brandState.brands}
              columns={columns}
              pagination={{ showSizeChanger: false }}
              showSorterTooltip={false}
              onChange={onChange}
            />
          </Col>
        </Row>
      </BrandStyle>
      <BrandForm
        initialValues={initObj}
        visible={visible}
        state={state}
        edit={edit}
        setState={setState}
        setEdit={setEdit}
        updateData={updateData}
      />
    </div>
  );
};

export default Brands;

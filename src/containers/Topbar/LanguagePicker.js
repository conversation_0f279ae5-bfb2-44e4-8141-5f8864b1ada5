import React from 'react';
import { Menu, Popover, Empty } from 'antd';
import { isEmpty } from 'lodash';
import IntlMessages from '@chill/components/utility/intlMessages';
import languageActions from '@chill/redux/languageSwitcher/actions';
import { useSelector, useDispatch } from 'react-redux';
import { TranslationOutlined } from '@ant-design/icons';

// TODO: Add language in below array whenever new language's JSON file added
const allLangs = [
  { lang_code: 'en', name: 'English', apiKey: 'EN' },
  { lang_code: 'de', name: 'Dutch', apiKey: 'NL' },
  { lang_code: 'fr', name: 'French', apiKey: 'FR' },
  { lang_code: 'ar', name: 'Arabic', apiKey: 'AR' },
  { lang_code: 'es', name: 'Spanish', apiKey: 'ES' },
  { lang_code: 'hi', name: 'Hindi', apiKey: 'HI' },
  { lang_code: 'bg', name: 'Bulgarian', apiKey: 'bg' },
];

export default function LanguagePicker() {
  const [visible, setVisiblity] = React.useState(false);
  const customizedTheme = useSelector(
    (state) => state.ThemeSwitcher.topbarTheme,
  );
  const language = useSelector((state) => state.LanguageSwitcher.language);

  const langCode = language || 'en';
  const dispatch = useDispatch();

  const langs = allLangs;

  function handleVisibleChange() {
    setVisiblity((vis) => !vis);
  }

  function changeLang(lngCode) {
    dispatch(languageActions.changeLanguage(lngCode));
    handleVisibleChange();
  }

  function handleMenuClick({ key }) {
    if (key) {
      changeLang(key);
      if (langCode === 'ar') window.location.reload();
    }
  }

  function renderLangs() {
    if (!isEmpty(langs)) {
      return langs.map((ct) => (
        <Menu.Item key={ct.lang_code} apikey={ct.apiKey}>
          {ct.name}
        </Menu.Item>
      ));
    }
    return <Empty description={<IntlMessages id="common.noLang" />} />;
  }

  const content = (
    <Menu
      selectedKeys={[langCode]}
      className="langMenu"
      onSelect={handleMenuClick}
    >
      {renderLangs()}
    </Menu>
  );

  return (
    <Popover
      content={content}
      trigger="click"
      visible={visible}
      onVisibleChange={handleVisibleChange}
      arrowPointAtCenter
      placement="bottom"
    >
      <div
        className="isoRightIcon"
        style={{ color: customizedTheme.textColor }}
      >
        <TranslationOutlined />
      </div>
    </Popover>
  );
}

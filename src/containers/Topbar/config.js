import React from 'react';
import {
  CalendarOutlined,
  FileTextOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons';
import IntlMessages from '@chill/components/utility/intlMessages';

const notiTypes = {
  new_job: {
    title: <IntlMessages id="notification.jobCreated" />,
    icon: <CalendarOutlined />,
  },
  job_cancel: {
    title: <IntlMessages id="notification.jobCancelled" />,
    icon: <CloseCircleOutlined style={{ color: 'red' }} />,
  },
  invoice_generated: {
    title: <IntlMessages id="notification.invoiceCreated" />,
    icon: <FileTextOutlined />,
  },
  payment_completed: {
    title: <IntlMessages id="notification.paymentCompleted" />,
    icon: <CalendarOutlined />,
  },
};

export default notiTypes;

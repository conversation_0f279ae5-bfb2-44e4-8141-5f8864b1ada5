/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { Layout, Alert, Divider } from 'antd';
import IntlMessages from '@chill/components/utility/intlMessages';
import appActions from '@chill/redux/app/actions';
import { isObject } from 'lodash';
import { Link } from 'react-router-dom';
import Input from '@chill/components/uielements/input';
// import LanguagePicker from './LanguagePicker';
import TopbarUser from './TopbarUser';
import TopbarWrapper from './Topbar.styles';
import TopbarNotification from './TopbarNotification';
// import TopbarHelp from './TopbarHelp';

const { Header } = Layout;
const { toggleCollapsed } = appActions;

export default function Topbar() {
  const customizedTheme = useSelector(
    (state) => state.ThemeSwitcher.topbarTheme,
  );
  const { collapsed, openDrawer } = useSelector((state) => state.App);

  const userData = useSelector((state) => state.Auth.userData);
  const uData = isObject(userData) ? { ...userData } : {};
  const planData = uData?.plan?.currentPlan || {};
  const isFree = !!planData.is_free;
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  const dispatch = useDispatch();
  const handleToggle = React.useCallback(() => dispatch(toggleCollapsed()), []);
  const isCollapsed = collapsed && !openDrawer;
  const styling = {
    background: customizedTheme.backgroundColor,
    position: 'fixed',
    width: '100%',
    height: 70,
    zIndex: 9,
  };
  const dWidth = window.innerWidth;
  /**
   * Renders Upgrade plan's alert message
   */
  function renderFreeAlert() {
    if (isFree)
      return (
        <Alert
          type="warning"
          message={
            <IntlMessages
              id="upgrade.globalWarning"
              values={{
                link: (
                  <Link to="/plans">
                    <strong>
                      <IntlMessages id="common.subscribeHere" />
                    </strong>
                  </Link>
                ),
              }}
            />
          }
          banner
          closable
        />
      );
    return null;
  }

  return (
    <TopbarWrapper>
      {renderFreeAlert()}
      <Header
        style={styling}
        className={
          isCollapsed ? 'isomorphicTopbar collapsed' : 'isomorphicTopbar'
        }
      >
        {dWidth <= 767 ? (
          <div className="isoLeft">
            {isCollapsed ? (
              <MenuUnfoldOutlined
                className="triggerBtn"
                onClick={handleToggle}
              />
            ) : (
              <MenuFoldOutlined className="triggerBtn" onClick={handleToggle} />
            )}
          </div>
        ) : null}
        <div className="searchView">
          <Input
            name="search"
            placeholder={messageArray['SEARCH ANYTHING']}
            allowClear
            prefix={
              <SearchOutlined style={{ fontSize: 20, marginRight: '10px' }} />
            }
          />
        </div>

        <ul className="isoRight">
          {/* Notification Icon */}
          <li className="isoNotification">
            <TopbarNotification />
          </li>
          <Divider type="vertical" style={{ height: '2.9em' }} />
          {/* User Account Popover */}
          <li className="isoUser">
            <TopbarUser />
          </li>
        </ul>
      </Header>
    </TopbarWrapper>
  );
}

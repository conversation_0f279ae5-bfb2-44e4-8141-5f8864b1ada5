/* eslint-disable react/jsx-no-bind */
/* eslint-disable global-require */
import React from 'react';
import { Link, useHistory } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { UserOutlined } from '@ant-design/icons';
import { Avatar, Tooltip } from 'antd';
import { isObject } from 'lodash';
import Popover from '@chill/components/uielements/popover';
import IntlMessages from '@chill/components/utility/intlMessages';
import authAction from '@chill/redux/auth/actions';
import Popconfirms from '@chill/components/Feedback/Popconfirm';
import accountSVG from '@chill/assets/images/account.svg';
import settingsSVG from '@chill/assets/images/settings.svg';
import logoutSVG from '@chill/assets/images/logout.svg';
import TopbarDropdownWrapper from './TopbarDropdown.styles';

const { logout } = authAction;

export default function TopbarUser() {
  const dispatch = useDispatch();
  const history = useHistory();

  // const { logout: logoutAuth0 } = useAuth0();

  const [visible, setVisibility] = React.useState(false);

  const uData = useSelector((state) => state.Auth.userData);
  const user = isObject(uData) ? uData : {};
  const imgUrl = isObject(user) && user.user_profile ? user.user_profile : '';

  function handleVisibleChange() {
    setVisibility((visiblePre) => !visiblePre);
  }

  // function logoutApp() {
  //   <div>
  //     <Popconfirms
  //       title={<IntlMessages id="Sure to delete?" />}
  //       okText={<IntlMessages id="Yes" />}
  //       cancelText={<IntlMessages id="No" />}
  //       onConfirm={() => {
  //         dispatch(logout());
  //         history.replace('/');
  //         // setIsIndex(null);
  //       }}
  //       onCancel={null}
  //     >
  //       {/* <DeleteOutlined style={{ paddingRight: 12, fontSize: 16 }} />
  //     <IntlMessages id="Delete Marketing" /> */}
  //     </Popconfirms>
  //     ;
  //   </div>;
  //   // logoutAuth0();
  //   // dispatch(logout());

  //   // Replace history to root
  //   // history.replace('/');
  // }

  const content = (
    <TopbarDropdownWrapper className="isoUserDropdown">
      {/* <Link
        onClick={handleVisibleChange}
        to="/dashboard/account-settings?active=basic"
        className="isoDropdownLink profileLink"
      >
        <Avatar src={imgUrl} size={40} icon={<UserOutlined />} />
        <div>
          <p>
            <strong>{getuName() || 'User'}</strong>
          </p>
          <p>
            <Tooltip placement="bottom" title={user.email}>
              {user.email || ''}
            </Tooltip>
          </p>
        </div>
      </Link> */}
      <Link
        onClick={handleVisibleChange}
        className="isoDropdownLink"
        to="/dashboard/account"
      >
        <img src={accountSVG} alt="account" />
        <IntlMessages id="topbar.account" />
      </Link>
      <Link
        onClick={handleVisibleChange}
        className="isoDropdownLink"
        to="/dashboard/settings"
      >
        <img src={settingsSVG} alt="settings" />
        <IntlMessages id="topbar.setting" />
      </Link>

      <div
        role="button"
        tabIndex="-1"
        className="isoDropdownLink"
        // onClick={logoutApp}
        // onKeyPress={logoutApp}
      >
        <Popconfirms
          title={<IntlMessages id="common.surelogout" />}
          okText={<IntlMessages id="common.yes" />}
          cancelText={<IntlMessages id="common.no" />}
          onConfirm={() => {
            dispatch(logout());
            history.replace('/');
            // setIsIndex(null);
          }}
          onCancel={null}
        >
          <img src={logoutSVG} alt="logout" style={{ marginRight: 12 }} />
          <IntlMessages id="topbar.logout" />
        </Popconfirms>
      </div>
    </TopbarDropdownWrapper>
  );

  return (
    <Popover
      content={content}
      trigger="click"
      visible={visible}
      onVisibleChange={handleVisibleChange}
      arrowPointAtCenter
      placement="bottomLeft"
    >
      {/* <div className="isoImgWrapper"> */}
      <div className="isoDropdownLink profileLink">
        <Avatar src={imgUrl} size={40} icon={<UserOutlined />} />
        <div className="userDetail">
          <div>
            <p>{user.full_name || 'User'}</p>
          </div>
          <p style={{ color: '#B2B2B2', fontSize: 13 }}>
            <Tooltip placement="bottom" title={user.user_type}>
              {user.user_type || '-'}
            </Tooltip>
          </p>
        </div>
      </div>
    </Popover>
  );
}

/* eslint-disable react/jsx-no-bind */
/* eslint-disable global-require */
/* eslint-disable react/no-array-index-key */
/* eslint-disable react/no-danger */
import React, { useState } from 'react';
import { Popover, Empty, Avatar } from 'antd';
import { Link } from 'react-router-dom';
import moment from 'moment';
import { isObject, isEmpty, isArray } from 'lodash';
import Scrollbar from '@chill/components/utility/customScrollBar';
import { useSelector } from 'react-redux';
import Button from '@chill/components/uielements/button';
import IntlMessages from '@chill/components/utility/intlMessages';
import notiSVG from '@chill/assets/images/notification.svg';
import TopbarDropdownWrapper from './TopbarDropdown.styles';

export default function TopbarNotification({ view }) {
  const [visible, setVisiblity] = useState(false);
  const customizedTheme = useSelector(
    (state) => state.ThemeSwitcher.topbarTheme,
  );
  const chatList = useSelector((state) => state.Chat.chatNotifications);
  const notiCount = chatList.length || 0;
  const mobile = view === 'MobileView';

  function handleVisibleChange() {
    setVisiblity((v) => !v);
  }

  const content = (
    <TopbarDropdownWrapper className="topbarMessage withImg">
      <div className="isoDropdownHeader">
        <h3>
          <IntlMessages id="sidebar.notification" />
        </h3>
      </div>
      <div className="isoDropdownBody">
        <Scrollbar style={{ height: 300 }}>
          {isArray(chatList) && !isEmpty(chatList) ? (
            chatList.map((massage) => {
              if (isObject(massage) && !isEmpty(massage)) {
                let msg = massage.message;
                if (massage.file_data && massage.file_url) {
                  const fData = JSON.parse(massage.file_data);
                  msg =
                    isObject(fData) && fData.fd ? `File: ${fData.fd}` : 'File';
                }
                return (
                  <Link
                    onClick={handleVisibleChange}
                    className="isoDropdownListItem"
                    to={{
                      pathname: `/dashboard/support`,
                      query: { latestMsg: massage },
                    }}
                  >
                    <div className="isoImgWrapper">
                      <Avatar src={massage.avtar_url} size={35} icon="user" />
                    </div>

                    <div className="isoListContent">
                      <div className="isoListHead">
                        <h5>{massage.name}</h5>
                        <span className="isoDate">
                          {moment(massage.createdAt).fromNow()}
                        </span>
                      </div>
                      <p>{msg}</p>
                    </div>
                  </Link>
                );
              }
              return null;
            })
          ) : (
            <div className="emptyContainer">
              <Empty description={<IntlMessages id="No Messages" />} />
              <Link onClick={handleVisibleChange} to="/dashboard/support">
                <Button className="viewAllBtn" type="primary" size="small">
                  <IntlMessages id="View All Chat" />
                </Button>
              </Link>
            </div>
          )}
        </Scrollbar>
      </div>
      {isArray(chatList) && !isEmpty(chatList) ? (
        <Link
          onClick={handleVisibleChange}
          className="isoViewAllBtn"
          to="/dashboard/support"
        >
          <IntlMessages id="topbar.viewAll" />
        </Link>
      ) : null}
    </TopbarDropdownWrapper>
  );

  return (
    <Popover
      content={content}
      trigger="click"
      visible={visible}
      onVisibleChange={handleVisibleChange}
      placement={mobile ? 'bottom' : 'bottomLeft'}
      arrowPointAtCenter
    >
      <div
        className="isoIconWrapper isoRightIcon"
        style={{ color: customizedTheme.textColor, padding: '8px' }}
      >
        <img src={notiSVG} alt="notification" />
        {/* <BellOutlined style={{ color: '#000' }} /> */}
        {notiCount ? (
          <span
            className="badge"
            style={{
              fontFamily: 'Inter',
              fontWeight: 600,
              // fontSize: '10px',
            }}
          >
            {notiCount}
          </span>
        ) : null}
      </div>
    </Popover>
  );
}

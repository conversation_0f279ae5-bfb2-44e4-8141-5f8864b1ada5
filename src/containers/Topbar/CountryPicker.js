/* eslint-disable eqeqeq */
import React from 'react';
import { Menu, Popover, Empty, Spin } from 'antd';
import { isArray, isEmpty, isObject } from 'lodash';
import IntlMessages from '@chill/components/utility/intlMessages';
import appActions from '@chill/redux/app/actions';
import AntIcon from '@chill/components/antdIcon';
import { useSelector, useDispatch } from 'react-redux';
import { useLocation, useHistory } from 'react-router';

export default function CountryPicker() {
  const history = useHistory();
  const loc = useLocation();
  const [visible, setVisiblity] = React.useState(false);
  const customizedTheme = useSelector(
    (state) => state.ThemeSwitcher.topbarTheme,
  );
  const { commonData, currentCountry } = useSelector((state) => state.App);
  const dispatch = useDispatch();

  const countryId = isObject(currentCountry) ? currentCountry.id : '';
  const countries =
    isObject(commonData) &&
    isArray(commonData.countries) &&
    !isEmpty(commonData.countries)
      ? commonData.countries
      : [];

  function handleVisibleChange() {
    setVisiblity((vis) => !vis);
  }

  function handleMenuClick(e) {
    if (e && e.key) {
      if (e.key === 'all') {
        dispatch(appActions.setCurrentCountry({ id: 'all' }));
      } else {
        const currentLang = countries.find((ct) => ct.id && ct.id == e.key);
        if (isObject(currentLang) && !isEmpty(currentLang)) {
          dispatch(appActions.setCurrentCountry(currentLang));
        }
      }
      // window.location.reload(false);
      const path = loc.pathname || '';
      if (path) {
        history.replace(`/dashboard/reloading`);
        setTimeout(() => {
          history.replace(path);
        });
      }
      handleVisibleChange();
    }
  }

  function renderCountries() {
    if (isEmpty(countries) && commonData.loading) {
      return <Spin spinning />;
    }
    if (!isEmpty(countries)) {
      return [{ id: 'all', name: 'All' }, ...countries].map((ct) => (
        <Menu.Item key={ct.id.toString()}>{ct.name}</Menu.Item>
      ));
    }
    return <Empty description={<IntlMessages id="No Countries" />} />;
  }

  const content = (
    <Menu
      selectedKeys={[countryId ? countryId.toString() : 'all']}
      className="langMenu"
      onClick={handleMenuClick}
    >
      {renderCountries()}
    </Menu>
  );

  return (
    <Popover
      content={content}
      trigger="click"
      visible={visible}
      onVisibleChange={handleVisibleChange}
      arrowPointAtCenter
      placement="bottom"
    >
      <div
        className="isoRightIcon"
        style={{ color: customizedTheme.textColor }}
      >
        <AntIcon name="globe" />
      </div>
    </Popover>
  );
}

import { Tooltip } from 'antd';
import React from 'react';
import IntlMessages from '@chill/components/utility/intlMessages';
import { useSelector } from 'react-redux';
import { QuestionCircleOutlined } from '@ant-design/icons';

function TopbarHelp() {
  const customizedTheme = useSelector(
    (state) => state.ThemeSwitcher.topbarTheme,
  );
  return (
    <a
      href="https://www.filesdna.com/WebHelp/"
      target="_blank"
      rel="noopener noreferrer"
    >
      <Tooltip title={<IntlMessages id="topbar.help" />}>
        <div
          className="isoIconWrapper isoRightIcon"
          style={{ color: customizedTheme.textColor }}
        >
          <QuestionCircleOutlined />
        </div>
      </Tooltip>
    </a>
  );
}

export default TopbarHelp;

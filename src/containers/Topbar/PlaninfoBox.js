import React from 'react';
import { isEmpty } from 'lodash';
import { Link } from 'react-router-dom';
import { RightCircleTwoTone } from '@ant-design/icons';
import IntlMessages from '@chill/components/utility/intlMessages';
import theme from '@chill/config/theme/default';

function PlaninfoBox(props) {
  const { planData } = props;

  if (!isEmpty(planData)) {
    return (
      <div className="planInfo">
        <Link to="/dashboard/account-settings?active=billing">
          <span>
            <strong>{planData.name}</strong>
            <small className="block">
              (
              {planData.is_free === 1 ? (
                <IntlMessages id="plans.free" />
              ) : (
                planData.plan_type
              )}
              )
            </small>
          </span>
          <RightCircleTwoTone twoToneColor={theme.colors.primaryColor} />
        </Link>
      </div>
    );
  }
  return null;
}

export default PlaninfoBox;

import { Empty } from 'antd';
import React from 'react';
import LinkBox from '@chill/components/LinkBox';
import IntlMessages from '@chill/components/utility/intlMessages';
import { PlusCircleOutlined } from '@ant-design/icons';
import { injectIntl } from 'react-intl';
import CardWidget from '../Card/CardWidget';

function SignInitial(props) {
  const { sObj = {}, isTeamUser, intl } = props;
  const { label, type, url, noTxt, addTxt } = sObj;

  /**
   * Renders sign content
   */
  function renderContent() {
    if (!url) {
      if (isTeamUser) {
        return (
          <Empty
            description={noTxt && <IntlMessages id={noTxt} />}
            className="w100"
          />
        );
      }
      return (
        <div className="rowAlignCenter w100">
          <div className="textCenter">
            <PlusCircleOutlined style={{ fontSize: 20 }} />
            <h3 className="ml5">
              <IntlMessages id={addTxt} />
            </h3>
          </div>
        </div>
      );
    }
    return null;
  }

  return (
    <LinkBox
      link={isTeamUser ? '' : '/dashboard/account-settings?active=signature'}
    >
      <CardWidget
        number={
          <img
            alt={
              label
                ? intl.formatMessage({
                    id: label,
                  })
                : 'Signature'
            }
            className={type}
            src={url}
          />
        }
        text={url && <IntlMessages id={label} />}
      >
        {renderContent()}
      </CardWidget>
    </LinkBox>
  );
}

export default injectIntl(SignInitial);

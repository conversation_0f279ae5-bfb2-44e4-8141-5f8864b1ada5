import React from 'react';
import IntlMessages from '@chill/components/utility/intlMessages';
import { StickerWidgetWrapper } from './StickerWidget.styles';

export default function ({
  fontColor,
  bgColor,
  icon,
  number,
  text,
  image,
  status,
  diffValue,
}) {
  const textColor = {
    color: fontColor,
  };
  const widgetStyle = {
    backgroundColor: bgColor,
    // width: '105%',
  };

  return (
    <StickerWidgetWrapper
      className="isoStickerWidget"
      style={{
        ...widgetStyle,
        boxShadow: '0px 0px 10px -4px #ccc',
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <div className="isoContentWrapper">
        <span className="isoEcoLable" style={textColor}>
          <IntlMessages id={text} />
        </span>
        <h4 className="isoEcoStatNumber">
          ${number.toLocaleString() || 0}
          <span
            className="isoEcoDiffNumber"
            style={{
              color: status === 'decrease' ? '#F04438' : '#15D175',
              marginLeft: 20,
            }}
          >
            {status === 'decrease' ? '-' : '+'}
            {diffValue}%
          </span>
        </h4>
      </div>
      {image ? (
        <div className="mainIconWrapper">
          <img
            src={image}
            alt="chat"
            style={{
              width: 24,
              height: 24,
              alignSelf: 'center',
            }}
          />
        </div>
      ) : (
        <div className="mainIconWrapper">
          <div className="isoEcoIconWrapper">{icon}</div>
        </div>
      )}
    </StickerWidgetWrapper>
  );
}

/* eslint-disable import/prefer-default-export */
import styled from 'styled-components';
import { borderRadius } from '@chill/lib/helpers/style_utils';

const StickerWidgetWrapper = styled.div`
  width: 100%;
  display: flex;
  align-items: stretch;
  overflow: hidden;
  box-shadow: 20px 10px 30px 10px #000000;
  // margin-top: 20px;
  ${borderRadius('5px')};
  box-shadow: 20px 10px 30px 10px #000000;
  .isoIconWrapper {
    // display: flex;
    padding-top: 20px;
    padding-left: 15px;
    align-items: flex-start;
    justify-content: center;
    width: 30px;
    // flex-shrink: 0;
    // background-color: rgba(0, 0, 0, 0.1);

    & span.anticon {
      font-size: 20px;
      color: #123456;
    }
  }

  .mainIconWrapper {
    display: flex;
    width: 50px;
    height: 45px;
    border-radius: 12px;
    background-color: #04b8ff;
    justify-content: center;
    align-content: center;
    margin-right: 15px;
  }
  .isoEcoIconWrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 20px;
    // flex-shrink: 0;
    // background-color: rgba(0, 0, 0, 0.1);
  }

  .isoContentWrapper {
    width: 100%;
    padding: 20px 15px 20px 20px;
    display: flex;
    flex-direction: column;

    .isoStatNumber {
      font-size: 18px;
      font-weight: 400 !important;
      line-height: 1.1;
      margin: 5px 0 5px;
    }

    .isoLabel {
      font-size: 12px;
      font-weight: 400;
      margin: 0;
      line-height: 1.2;
      opacity: 0.6;
      color: #a0aec0;
    }

    .isoEcoLable {
      & span {
        font-family: 'Inter';
        font-size: 12px;
        font-weight: 700;
        margin: 0;
        line-height: 18px;
        opacity: 0.6;
        color: #a0aec0;
      }
    }

    .isoEcoStatNumber {
      font-family: 'Inter';
      font-size: 18px;
      font-weight: 700 !important;
      line-height: 18px;
      margin: 5px 0 5px;
      color: #2d3748;
    }
    .isoEcoDiffNumber {
      font-family: 'Inter';
      font-size: 14px;
      font-weight: 700 !important;
      line-height: 18px;
      margin: 5px 0 5px;
    }
  }
`;

export { StickerWidgetWrapper };

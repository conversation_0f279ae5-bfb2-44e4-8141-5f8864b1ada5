import React from 'react';
import _ from 'lodash';

function Separator({ turns, isActive, circular }) {
  return (
    <div
      style={{
        // This needs to be equal to props.strokeWidth
        position: 'absolute',
        height: '70%',
        transform: `rotate(${turns}turn)`,
      }}
    >
      {circular ? (
        <div
          style={{
            background: isActive
              ? 'linear-gradient(90deg, #04B8FF, #0086FF)'
              : '#E9EDF0',
            width: '3px',
            height: '3px',
            borderRadius: '10px',
            marginTop: 25,
          }}
        />
      ) : (
        <div
          style={{
            background: isActive
              ? 'linear-gradient(90deg, #04B8FF, #0086FF)'
              : '#E9EDF0',
            width: '2px',
            borderRadius: '3px',
            height: '6%',
          }}
        />
      )}
    </div>
  );
}

function RadialSeparators(props) {
  const turns = 1 / props.count;
  return _.range(props.count).map((index) => {
    const isActive = index < (props.percentage / 100) * 100;
    return (
      <Separator
        turns={index * turns}
        isActive={isActive}
        circular={props.circular}
      />
    );
  });
}

export default RadialSeparators;

/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable react/jsx-props-no-spreading */
import React from 'react';
import { ReportWidgetWrapper } from './ReportWidget.styles';

export default function ReportWidget({
  label,
  details,
  children,
  widgetClassName,
  labelClsName,
  label2,
  labelClsName2,
  action,
  redirect = () => {},
  customStyle,
  analyticStyle,
  ...props
}) {
  return (
    <ReportWidgetWrapper
      className={`isoReportsWidget ${customStyle}`}
      {...props}
    >
      {label || action ? (
        <div className="rowSpaceBetween">
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              width: '100%',
              ...analyticStyle,
            }}
          >
            <h4 className={`isoWidgetLabel ${labelClsName || ''}`}>{label}</h4>
            {label2 ? (
              <h5
                className={`isoWidgetLabel ${labelClsName2 || ''}`}
                style={{ cursor: 'pointer', color: '#fff' }}
                onClick={redirect}
              >
                {label2}
              </h5>
            ) : null}
          </div>

          {action}
        </div>
      ) : null}

      <div className={`isoReportsWidgetBar ${widgetClassName || ''}`}>
        {children}
      </div>

      <p className="isoDescription">{details}</p>
    </ReportWidgetWrapper>
  );
}

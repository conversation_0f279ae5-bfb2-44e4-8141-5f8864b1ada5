/* eslint-disable import/prefer-default-export */
import styled from 'styled-components';
import { palette } from 'styled-theme';

const ReportWidgetWrapper = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 25px;
  background-color: #ffffff;
  border: 1px solid ${palette('border', 2)};

  .isoWidgetLabel {
    font-size: 15px;
    color: ${palette('text', 0)};
    font-weight: 400;
    line-height: 1.2;
    margin: 0 0 25px;

    &.mb15 {
      margin: 0 0 15px;
    }
  }

  .isoReportsWidgetBar {
    display: flex;
    flex-direction: column;
    margin-bottom: 25px;

    .isoSingleProgressBar {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    &.mb0 {
      margin-bottom: 0px;
    }
  }

  .isoDescription {
    font-size: 13px;
    color: ${palette('text', 2)};
    font-weight: 400;
    line-height: 1.5;
    margin: 0;
  }

  & .ant-statistic {
    padding: 7px 10px
    background-color: #f1f0f7;
    border-radius: 3px;

    & .ant-statistic-content {
      font-size: 20px;
    }
  }

  @media only screen and (max-width: 1440px) {
    padding: 20px;
  }

  @media (max-width: 1200px) {
    & .isoWidgetLabel {
      font-size: 14px;
    }
    & .ant-statistic {
      & .ant-statistic-title {
        font-size: 12px;
      }
      & .ant-statistic-content {
        font-size: 16px;
      }
    }
  }
  .titleText{
    color: #fff;
    font-size: 16px;
  }
  .headerText {
    & span {
      font-family: 'Inter';
      font-size: 14px;
      font-weight: 600;
      margin: 0px 0px 20px 0px;
      line-height: 17px;
      color: #15223C;
    },
  }
  .chartHeader{
    & span {
      font-family: 'Inter';
      font-size: 14px;
      font-weight: 500;
      margin: 0px 0px 20px 0px;
      line-height: 20px;
      color: #262D33;
    },
  }
  .weekHeader{
    & span {
      font-family: 'Inter';
      font-size: 14px;
      font-weight: 400;
      margin: 0px 0px 20px 0px;
      line-height: 20px;
      color: #262D33;
    },
  }
  .conversionText {
    & span {
      font-family: 'Inter';
      font-size: 15px;
      font-weight: 600;
      margin: 0px 0px 20px 0px;
      line-height: 18px;
      color: #000000;
    },
  }
`;

export { ReportWidgetWrapper };

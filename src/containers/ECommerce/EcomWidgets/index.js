/* eslint-disable react/no-array-index-key */
/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable react/jsx-no-bind */
/* eslint-disable no-plusplus */
/* eslint-disable no-param-reassign */
/* eslint-disable no-shadow */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable prefer-destructuring */
import React, { useEffect, useState } from 'react';
import {
  Row,
  Col,
  Spin,
  Avatar,
  Empty,
  Typography,
  Progress,
  Button,
} from 'antd';
import {
  ClockCircleOutlined,
  DollarCircleOutlined,
  UserOutlined,
  DownOutlined,
} from '@ant-design/icons';
import LayoutWrapper from '@chill/components/utility/layoutWrapper';
import basicStyle from '@chill/assets/styles/constants';
import IntlMessages from '@chill/components/utility/intlMessages';
import { isEmpty } from 'lodash';
import getApiData from '@chill/lib/helpers/apiHelper';
import { injectIntl } from 'react-intl';
import { useSelector } from 'react-redux';
import moment from 'moment';
import LinkBox from '@chill/components/LinkBox';
import { Column } from '@ant-design/charts';
import Popover from '@chill/components/uielements/popover';
import DatePicker from '@chill/components/uielements/datePicker';
import {
  CircularProgressbarWithChildren,
  buildStyles,
} from 'react-circular-progressbar';
import 'react-circular-progressbar/dist/styles.css';
import DataRangeIcon from '@chill/assets/images/DataRangeIcon.png';
import {
  BarChart,
  CartesianGrid,
  Cell,
  Label,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
  Bar,
  Legend,
  Rectangle,
  LabelList,
} from 'recharts';
// import ReactApexChart from 'react-apexcharts';
import WalletImage from '@chill/assets/images/wallet.svg';
import CartImag from '@chill/assets/images/Cart.svg';
import IsoWidgetsWrapper from './WidgetsWrapper';
import ReportsWidget from './Report/ReportWidget';
import StickerWidget from './Sticker/StickerWidget';
import {
  DashboardWrapper,
  WidgetWrapper,
  MessageSingle,
} from './Widgets.styles';
import { styles } from './config';
import RadialSeparators from './RadialSaperators';

/**
 *
 * @module ECommerce
 */

const { Text } = Typography;
function EComWidget() {
  // const { intl } = props;
  const { RangePicker } = DatePicker;

  const { rowStyle, colStyle } = basicStyle;

  const access = useSelector((state) => state.Auth.access);

  const accLoading = access.loading || false;

  // const sPer = Number(getStorageData(access));
  // const dashData = dData?.data || {};
  const [dData, setdData] = useState({ data: {}, loading: true });
  const [overViewData, setOverViewData] = useState({ data: {}, loading: true });
  const [topSelling, setTopSelling] = useState({ data: {}, loading: true });
  const [topMarkCamp, setTopMarkCamp] = useState({ data: {}, loading: true });
  const [overviewVisible, setOverViewVisibility] = useState(false);
  const [durationVisible, setDurationVisibility] = useState(false);

  const [userVisible, setUserVisible] = useState(false);
  const [duration, setDuration] = useState(
    <IntlMessages id="dashboard.thisweek" />,
  );
  const [isUser, setIsUser] = useState(
    <IntlMessages id="dashboard.lastweek" />,
  );
  const [cperiodType, setCPeriodType] = useState('this_week');
  const [rperiodType, setRPeriodType] = useState('week');
  const [overView, setOverView] = useState(
    <IntlMessages id="dashboard.custOverview" />,
  );
  const [state, setState] = React.useState({
    loading: false,
  });
  const [isRangePickerOpen, setIsRangePickerOpen] = useState(false);

  const [selectedProduct, setSelectedProduct] = useState(null);
  const brandLists = useSelector((st) => st.Auth.list);
  const [topProducts, setTopProducts] = useState([]);
  const [mostViewed, setMostViewedProducts] = useState([]);
  const [topMarketingCamp, setTopMarketingCamp] = useState([]);
  const [salesPerformanceData, setSalesPerformanceData] = useState([]);

  const [fromDate, setFromDate] = useState();
  const [toDate, setToDate] = useState();
  const [stEndDate, setStEndDate] = useState({
    stDate: fromDate,
    endDate: toDate,
  });
  const [dateRange, changeDateRange] = useState(null);

  const returnMomentDateRange = (start, finish) => {
    return [moment(start, 'YYYY-MM-DD'), moment(finish, 'YYYY-MM-DD')];
  };

  function onDateChange(e) {
    if (e) {
      changeDateRange(returnMomentDateRange(e[0], e[1]));
    } else {
      changeDateRange([]);
    }
    if (e != null) {
      const date1 = moment(e[0]).format('DD-MM-YYYY');
      const date2 = moment(e[1]).format('DD-MM-YYYY');
      setStEndDate({ stDate: date1, endDate: date2 });
    } else {
      setStEndDate({ stDate: fromDate, endDate: toDate });
    }
  }
  const handleDateClick = (e) => {
    setIsRangePickerOpen(!isRangePickerOpen);
  };
  const content = <RangePicker onChange={onDateChange} />;

  const TOP_BOXES = [
    {
      key: 'totalusers',
      text: 'ecom.totalSales',
      image: WalletImage,
      bgColor: '#ffffffff',
      count: 53000,
      diffValue: 0,
      status: 'decrease',
    },
    {
      key: 'weeklySession',
      text: 'eco.noofSold',
      image: CartImag,
      bgColor: '#ffffffff',
      count: 173000,
      diffValue: 0,
      status: 'decrease',
    },
  ];

  const [selArray, setSelArray] = useState(TOP_BOXES);

  const submissionBox = [
    {
      value: dData?.formSubmissionCnt || 0,
      title: <IntlMessages id="card.form" />,
      discription: <IntlMessages id="card.submissions" />,
    },
    {
      value: dData?.abandonedDataCnt || 0,
      title: <IntlMessages id="card.carts" />,
      discription: <IntlMessages id="card.abandoned" />,
    },
  ];

  const [submissionVal, setSubmissionVal] = useState(submissionBox);
  const [percentage, setPercentage] = useState(89);

  function overViewChange() {
    setOverViewVisibility((visiblePre) => !visiblePre);
  }

  function durationChange(type) {
    if (type === 'user') {
      setUserVisible((visiblePre) => !visiblePre);
    } else {
      setDurationVisibility((visiblePre) => !visiblePre);
    }
  }
  function disabledDate(current) {
    // Can not select days before today and today
    return current && current > moment().endOf('day');
  }

  useEffect(() => {
    const today = moment();
    const startOfWeek = today.clone().startOf('isoWeek'); // Monday
    const endOfWeek = today.clone().endOf('isoWeek');

    changeDateRange([startOfWeek, endOfWeek]);
  }, []);

  /** Function to fetch dashboard's data
   * @function getTopMarketingCamp
   * @param {object} data customer_overview, user_by_region
   */

  async function getTopMarketingCamp(data = {}) {
    try {
      data.start_end_date = stEndDate;
      const res = await getApiData(
        'e-commerce/marketing-campaign-list',
        data,
        'post',
      );
      if (res.success && !isEmpty(res.data)) {
        const data = {};
        setTopMarketingCamp(res?.data?.topMarketingCam);

        data.marketingCampaigns = res.data?.marketingCampaigns;
        setTopMarkCamp((pre) => ({ ...pre, data, loading: false }));
      } else {
        setTopMarkCamp((pre) => ({ ...pre, loading: false }));
      }
    } catch (err) {
      console.log('err ===', err);
      setTopMarkCamp((pre) => ({ ...pre, loading: false }));
    }
  }

  /** Function to fetch dashboard's data
   * @function getTopSellingProducts
   * @param {object} data customer_overview, user_by_region
   */

  async function getTopSellingProducts(data = {}) {
    try {
      data.start_end_date = stEndDate;
      const res = await getApiData(
        'e-commerce/product-selling-list',
        data,
        'post',
      );
      if (res.success && !isEmpty(res.data)) {
        const data = {};
        setTopProducts(res?.data?.topSellingProducts);
        data.topSellingProducts = res.data?.topSellingProducts;
        setTopSelling((pre) => ({ ...pre, data, loading: false }));
      } else {
        setTopSelling((pre) => ({ ...pre, loading: false }));
      }
    } catch (err) {
      console.log('err ===', err);
      setTopSelling((pre) => ({ ...pre, loading: false }));
    }
  }
  /** Function to fetch dashboard's data
   * @function getDashboardData
   * @param {object} data customer_overview, user_by_region
   */

  async function getDashboardData(data = {}) {
    try {
      const res = await getApiData('e-commerce/dashboard-data', data, 'post');
      if (res.success && !isEmpty(res.data)) {
        // setTopProducts(res?.data?.topSellingProducts);
        setMostViewedProducts(res?.data?.mostViewdProducts);
        // setTopMarketingCamp(res?.data?.topMarketingCam);
        const updatedArray = selArray.map((item) => {
          if (item.key === 'totalusers') {
            return {
              ...item,
              count: res.data?.totalSold,
              diffValue:
                res.data?.tileDetails?.totalSalesDetails
                  ?.cntDifferencePercentage,
              status: res.data?.tileDetails?.totalSalesDetails?.cntStatus,
            }; // Merge the object to update the existing item
          }
          return {
            ...item,
            count: res.data?.totalSales,
            diffValue:
              res.data?.tileDetails?.totalSoldDetails?.cntDifferencePercentage,
            status: res.data?.tileDetails?.totalSoldDetails?.cntStatus,
          };
        });
        setSelArray(updatedArray);

        const updatedSalesArray = submissionVal.map((item) => {
          if (item.title === 'Form') {
            return { ...item, value: res.data?.formSubmissionCnt }; // Merge the object to update the existing item
          }
          return { ...item, value: res.data?.abandonedDataCnt };
        });
        setSubmissionVal(updatedSalesArray);
        setdData({ data: res?.data, loading: false });
      } else {
        setdData({ data: {}, loading: false });
      }
    } catch (err) {
      console.log('err ===', err);
      setdData({ data: {}, loading: false });
    }
  }

  /** Function to fetch dashboard's data
   * @function getOverviewData
   * @param {object} data customer_overview, user_by_region
   */

  async function getOverviewData(data = {}) {
    // data.customer_overview = {
    //   period_type: cperiodType,
    // };
    data.period_type = cperiodType;
    data.user_by_region = {
      period_type: rperiodType,
      brand_id: selectedProduct,
    };
    data.start_end_date = stEndDate;
    try {
      const res = await getApiData(
        'e-commerce/product-overview-data',
        data,
        'post',
      );
      if (res.success && !isEmpty(res.data)) {
        const data = {};
        const customerOverview = res?.data?.productOverview
          ? res.data.productOverview
          : [];
        data.customerOverview = customerOverview;
        setOverViewData((pre) => ({ ...pre, data, loading: false }));
      } else {
        setOverViewData((pre) => ({ ...pre, loading: false }));
      }
    } catch (err) {
      console.log('err ===', err);
      setOverViewData((pre) => ({ ...pre, loading: false }));
    }
  }

  /** Function to fetch dashboard's data
   * @function getSalesPerformance
   */

  async function getSalesPerformance() {
    try {
      const res = await getApiData(
        `get-sales-performance-data?start_date=${
          stEndDate?.stDate || ''
        }&end_date=${stEndDate?.endDate || ''}`,
      );
      if (res.success) {
        setSalesPerformanceData(res?.salesPerformaceChart);
      } else {
        setSalesPerformanceData([]);
      }
    } catch (err) {
      setSalesPerformanceData([]);
      console.log('err ===', err);
    }
  }
  useEffect(() => {
    getDashboardData();
    getTopSellingProducts();
    getTopMarketingCamp();
    getSalesPerformance();
  }, []);

  useEffect(() => {
    setOverViewData((pre) => ({ ...pre, loading: true }));
    getOverviewData();
    getDashboardData({
      start_date: stEndDate?.stDate,
      end_date: stEndDate?.endDate,
    });
    getTopSellingProducts();
    getTopMarketingCamp();
    getSalesPerformance();
  }, [cperiodType, rperiodType, selectedProduct, stEndDate]);

  const customerOverviewContent = (
    <WidgetWrapper>
      <div className="popMainDiv">
        <div
          className="isoDropdownLink"
          onClick={() => {
            setOverView(<IntlMessages id="dashboard.custOverview" />);
            setOverViewVisibility(!overviewVisible);
          }}
          role="button"
          onKeyPress={() => {
            setOverView(<IntlMessages id="dashboard.custOverview" />);
            setOverViewVisibility(!overviewVisible);
          }}
          tabIndex="-1"
        >
          dashboard.custOverview 1
        </div>
      </div>
    </WidgetWrapper>
  );
  const config = {
    data:
      !isEmpty(overViewData.data) &&
      !isEmpty(overViewData.data.customerOverview)
        ? overViewData.data.customerOverview
        : [],
    xField: 'month',
    yField: 'dummyValue',
    // isGroup: true,
    // isStack: true,
    seriesField: 'type',
    groupField: 'name',
    shape: 'diamon',
    color: ['#80d8ff', '#2962ff'],
    legend: {
      position: 'bottom',
      shape: 'diamon',
      textStyle: {
        color: '#233238',
        fontSize: 14,
      },
    },
    yAxis: {
      grid: {
        line: null,
      },
      label: null,
    },

    label: null,
    xAxis: {
      label: {
        autoHide: true,
        autoRotate: false,
      },
      grid: {
        line: null,
      },
    },
    // meta: {
    //   day: { alias: 'day' },
    //   value: { alias: ' ' },
    // },
  };

  // static data for BarChart.
  const barData = [
    { label: 'Visitors', value: 12345, percent: 0.8 * 100 },
    { label: 'Leads', value: 642, percent: 0.7 * 100 },
    { label: 'Sign-ups', value: 335, percent: 0.5 * 100 },
    { label: 'Purchases', value: 172, percent: 0.3 * 100 },
  ];

  // static data for PieChart.
  const pieChartData = [
    { color: '#9C89FF' },
    { color: '#7386FF' },
    { color: '#3AD1A9' },
  ];

  // Render Customized label..
  const RADIAN = Math.PI / 180;
  const renderCustomizedLabel = ({
    cx,
    cy,
    midAngle,
    innerRadius,
    outerRadius,
    index,
  }) => {
    const data = dData?.data?.trafficSourceChart || [];
    const value = data[index].value;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    const labelWidth = 100;
    const labelHeight = 40;
    const labelX = x - labelWidth / 2;
    const labelY = y - labelHeight / 2;

    // Check if label overlaps and adjust position if needed
    const isLeft = x < cx;
    const isTop = y < cy;

    const adjustedX = isLeft ? labelX - 10 : labelX + 10; // Adjust horizontal position
    const adjustedY = isTop ? labelY - 10 : labelY + 10; // Adjust vertical position

    return (
      <g key={`label-${index}`}>
        <foreignObject
          x={adjustedX}
          y={adjustedY}
          width={labelWidth}
          height={labelHeight}
          style={{
            boxShadow: '5px 5px 5px 5px #0000',
            boxSizing: 'border-box',
            borderRadius: '8px',
          }}
        >
          <div
            xmlns="http://www.w3.org/1999/xhtml"
            style={{
              backgroundColor: 'white',
              padding: '5px',
              borderRadius: '8px',
              border: '1px solid rgba(217, 217, 217, 0.26)', // Set border color and width
              boxShadow: '2px 2px 8px rgba(0,0,0,0.5)', // Set box shadow
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              width: '100%',
              height: '100%',
            }}
          >
            <span
              style={{
                fontSize: '12px',
                fontFamily: 'Inter',
                fontWeight: '800',
                color: '#000000',
              }}
            >
              {value.toFixed(0)}%
            </span>
            <span
              style={{
                fontSize: '10px',
                fontFamily: 'Inter',
                fontWeight: '500',
                color: '#718096',
              }}
            >
              {data[index].name}
            </span>
          </div>
        </foreignObject>
      </g>
    );
  };

  // PIE Chart Center Label..
  const renderCenterLabel = ({ cx, cy }) => {
    return (
      <>
        <text
          x={cx}
          y="47%"
          textAnchor="middle"
          dominantBaseline="middle"
          fill="#000"
          style={{
            fontFamily: 'inter',
            fontSize: '16px',
            fontWeight: '800',
            fill: '#000000',
            lineHeight: '18px',
          }}
        >
          Traffic
        </text>
        <text
          x={cx}
          y="53%"
          textAnchor="middle"
          style={{
            fontFamily: 'inter',
            fontSize: '16px',
            fontWeight: '600',
            fill: '#718096',
          }}
        >
          Sources
        </text>
      </>
    );
  };

  const customerDurationContent = (
    <WidgetWrapper>
      <div className="popMainDiv">
        {durationVisible ? (
          <div
            className="isoDropdownLink"
            onClick={() => {
              const startOfWeek = moment().startOf('isoWeek');
              const endOfWeek = moment().endOf('isoWeek');

              changeDateRange([startOfWeek, endOfWeek]);

              setDuration(<IntlMessages id="dashboard.thisweek" />);
              setDurationVisibility(!durationVisible);
              setCPeriodType('this_week');
            }}
            role="button"
            onKeyPress={() => {
              const startOfWeek = moment().startOf('isoWeek');
              const endOfWeek = moment().endOf('isoWeek');

              changeDateRange([startOfWeek, endOfWeek]);
              setDuration(<IntlMessages id="dashboard.thisweek" />);
              setDurationVisibility(!durationVisible);
              setCPeriodType('this_week');
            }}
            tabIndex="-1"
          >
            <IntlMessages id="dashboard.thisweek" />
          </div>
        ) : null}
        <div
          className="isoDropdownLink"
          onClick={() => {
            if (userVisible) {
              const lastWeekStart = moment()
                .subtract(1, 'weeks')
                .startOf('isoWeek');
              const lastWeekEnd = moment()
                .subtract(1, 'weeks')
                .endOf('isoWeek');

              changeDateRange([lastWeekStart, lastWeekEnd]);
              setStEndDate({ stDate: '', endDate: '' });
              setIsUser(<IntlMessages id="dashboard.lastweek" />);
              setUserVisible(!userVisible);
              setRPeriodType('week');
            } else {
              const lastWeekStart = moment()
                .subtract(1, 'weeks')
                .startOf('isoWeek');
              const lastWeekEnd = moment()
                .subtract(1, 'weeks')
                .endOf('isoWeek');

              changeDateRange([lastWeekStart, lastWeekEnd]);
              setStEndDate({ stDate: '', endDate: '' });
              setDuration(<IntlMessages id="dashboard.lastweek" />);
              setDurationVisibility(!durationVisible);
              setCPeriodType('week');
            }
          }}
          role="button"
          onKeyPress={() => {
            if (userVisible) {
              setIsUser(<IntlMessages id="dashboard.lastweek" />);
              setUserVisible(!userVisible);
              setRPeriodType('week');
              const lastWeekStart = moment()
                .subtract(1, 'weeks')
                .startOf('isoWeek');
              const lastWeekEnd = moment()
                .subtract(1, 'weeks')
                .endOf('isoWeek');

              changeDateRange([lastWeekStart, lastWeekEnd]);
              setStEndDate({ stDate: '', endDate: '' });
            } else {
              const lastWeekStart = moment()
                .subtract(1, 'weeks')
                .startOf('isoWeek');
              const lastWeekEnd = moment()
                .subtract(1, 'weeks')
                .endOf('isoWeek');

              changeDateRange([lastWeekStart, lastWeekEnd]);
              setStEndDate({ stDate: '', endDate: '' });
              setDuration(<IntlMessages id="dashboard.lastweek" />);
              setDurationVisibility(!durationVisible);
              setCPeriodType('week');
            }
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.lastweek" />{' '}
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            if (userVisible) {
              const startOfMonth = moment().startOf('month');
              const endOfMonth = moment().endOf('month');

              changeDateRange([startOfMonth, endOfMonth]);
              setIsUser(<IntlMessages id="dashboard.thisMonth" />);
              setUserVisible(!userVisible);
              setRPeriodType('this_month');
            } else {
              const startOfMonth = moment().startOf('month');
              const endOfMonth = moment().endOf('month');

              changeDateRange([startOfMonth, endOfMonth]);

              setStEndDate({ stDate: null, endDate: null });
              setDuration(<IntlMessages id="dashboard.thisMonth" />);
              setDurationVisibility(!durationVisible);
              setCPeriodType('this_month');
            }
          }}
          role="button"
          onKeyPress={() => {
            if (userVisible) {
              const startOfMonth = moment().startOf('month');
              const endOfMonth = moment().endOf('month');

              changeDateRange([startOfMonth, endOfMonth]);
              setIsUser(<IntlMessages id="dashboard.thisMonth" />);
              setUserVisible(!userVisible);
              setRPeriodType('this_month');
            } else {
              const startOfMonth = moment().startOf('month');
              const endOfMonth = moment().endOf('month');

              changeDateRange([startOfMonth, endOfMonth]);
              setDuration(<IntlMessages id="dashboard.thisMonth" />);
              setDurationVisibility(!durationVisible);
              setCPeriodType('this_month');
            }
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.thisMonth" />
        </div>

        <div
          className="isoDropdownLink"
          onClick={() => {
            if (userVisible) {
              const lastMonthStart = moment()
                .subtract(1, 'months')
                .startOf('month');
              const lastMonthEnd = moment()
                .subtract(1, 'months')
                .endOf('month');

              changeDateRange([lastMonthStart, lastMonthEnd]);
              setIsUser(<IntlMessages id="dashboard.lastMonth" />);
              setUserVisible(!userVisible);
              setRPeriodType('month');
            } else {
              const lastMonthStart = moment()
                .subtract(1, 'months')
                .startOf('month');
              const lastMonthEnd = moment()
                .subtract(1, 'months')
                .endOf('month');

              changeDateRange([lastMonthStart, lastMonthEnd]);
              setDuration(<IntlMessages id="dashboard.lastMonth" />);
              setDurationVisibility(!durationVisible);
              setCPeriodType('month');
            }
          }}
          role="button"
          onKeyPress={() => {
            if (userVisible) {
              const lastMonthStart = moment()
                .subtract(1, 'months')
                .startOf('month');
              const lastMonthEnd = moment()
                .subtract(1, 'months')
                .endOf('month');

              changeDateRange([lastMonthStart, lastMonthEnd]);
              setIsUser(<IntlMessages id="dashboard.lastMonth" />);
              setUserVisible(!userVisible);
              setRPeriodType('month');
            } else {
              const lastMonthStart = moment()
                .subtract(1, 'months')
                .startOf('month');
              const lastMonthEnd = moment()
                .subtract(1, 'months')
                .endOf('month');

              changeDateRange([lastMonthStart, lastMonthEnd]);
              setDuration(<IntlMessages id="dashboard.lastMonth" />);
              setDurationVisibility(!durationVisible);
              setCPeriodType('month');
            }
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.lastMonth" />
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            if (userVisible) {
              const lastSixMonthsStart = moment()
                .subtract(6, 'months')
                .startOf('month');
              const lastSixMonthsEnd = moment()
                .subtract(1, 'months')
                .endOf('month');

              changeDateRange([lastSixMonthsStart, lastSixMonthsEnd]);
              setIsUser(<IntlMessages id="dashboard.lastSixMonth" />);
              setUserVisible(!userVisible);
              setRPeriodType('six_month');
            } else {
              const lastSixMonthsStart = moment()
                .subtract(6, 'months')
                .startOf('month');
              const lastSixMonthsEnd = moment()
                .subtract(1, 'months')
                .endOf('month');

              changeDateRange([lastSixMonthsStart, lastSixMonthsEnd]);
              setDuration(<IntlMessages id="dashboard.lastSixMonth" />);
              setDurationVisibility(!durationVisible);
              setCPeriodType('six_month');
            }
          }}
          role="button"
          onKeyPress={() => {
            if (userVisible) {
              const lastSixMonthsStart = moment()
                .subtract(6, 'months')
                .startOf('month');
              const lastSixMonthsEnd = moment()
                .subtract(1, 'months')
                .endOf('month');

              changeDateRange([lastSixMonthsStart, lastSixMonthsEnd]);
              setIsUser(<IntlMessages id="dashboard.lastSixMonth" />);
              setUserVisible(!userVisible);
              setRPeriodType('six_month');
            } else {
              const lastSixMonthsStart = moment()
                .subtract(6, 'months')
                .startOf('month');
              const lastSixMonthsEnd = moment()
                .subtract(1, 'months')
                .endOf('month');

              changeDateRange([lastSixMonthsStart, lastSixMonthsEnd]);
              setDuration(<IntlMessages id="dashboard.lastSixMonth" />);
              setDurationVisibility(!durationVisible);
              setCPeriodType('six_month');
            }
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.lastSixMonth" />
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            if (userVisible) {
              const lastYearStart = moment()
                .subtract(1, 'years')
                .startOf('year');
              const lastYearEnd = moment().subtract(1, 'years').endOf('year');

              changeDateRange([lastYearStart, lastYearEnd]);
              setIsUser(<IntlMessages id="dashboard.lastYear" />);
              setUserVisible(!userVisible);
              setRPeriodType('year');
            } else {
              const lastYearStart = moment()
                .subtract(1, 'years')
                .startOf('year');
              const lastYearEnd = moment().subtract(1, 'years').endOf('year');

              changeDateRange([lastYearStart, lastYearEnd]);
              setDuration(<IntlMessages id="dashboard.lastYear" />);
              setDurationVisibility(!durationVisible);
              setCPeriodType('year');
            }
          }}
          role="button"
          onKeyPress={() => {
            if (userVisible) {
              const lastYearStart = moment()
                .subtract(1, 'years')
                .startOf('year');
              const lastYearEnd = moment().subtract(1, 'years').endOf('year');

              changeDateRange([lastYearStart, lastYearEnd]);
              setIsUser(<IntlMessages id="dashboard.lastYear" />);
              setUserVisible(!userVisible);
              setRPeriodType('year');
            } else {
              const lastYearStart = moment()
                .subtract(1, 'years')
                .startOf('year');
              const lastYearEnd = moment().subtract(1, 'years').endOf('year');

              changeDateRange([lastYearStart, lastYearEnd]);
              setDuration(<IntlMessages id="dashboard.lastYear" />);
              setDurationVisibility(!durationVisible);
              setCPeriodType('year');
            }
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.lastYear" />
        </div>
      </div>
    </WidgetWrapper>
  );
  /**
   * Function to render Top 4 boxes
   */
  function renderTopBoxes() {
    return (
      <>
        {selArray.map((widget) => {
          // const val = getObjVal(dashData, widget.key);
          // const lnk = widget.link ? `/dashboard${widget.link}` : '';
          return (
            <Col
              lg={9}
              md={9}
              sm={24}
              xs={24}
              style={colStyle}
              key={widget.text}
            >
              <IsoWidgetsWrapper>
                <LinkBox link="">
                  <StickerWidget
                    number={widget.count}
                    text={widget.text}
                    icon={widget.icon}
                    fontColor={widget.fontColor}
                    bgColor={widget.bgColor}
                    diffValue={widget.diffValue}
                    status={widget.status}
                    image={widget.image}
                  />
                </LinkBox>
              </IsoWidgetsWrapper>
            </Col>
          );
        })}
        <Popover
          content={content}
          trigger="click"
          visible={isRangePickerOpen}
          onVisibleChange={handleDateClick}
        >
          <Button
            type="dashed"
            className="filterBtnTxt"
            style={{
              marginRight: '10px',
              display: 'flex',
              alignItems: 'center',
              border: 'none',
              borderRadius: '8px',
              background: '#00B7FF',
              color: '#FFFFFF',
              gap: '15px',
              fontWeight: 600,
              width: '160px',
              height: '50px',
              fontSize: '16px',
              marginTop: '20px',
            }}
          >
            <IntlMessages id="analytics.dataRange" />
            <img src={DataRangeIcon} alt="icon" />
          </Button>
        </Popover>
      </>
    );
  }

  const renderProduct = (product) => {
    return (
      <div style={{ margin: '10px 30px' }}>
        <div className="messageContentText">
          <div className="messageContentText msgUserDetail">
            <p
              style={{
                fontWeight: '500',
                fontSize: 14,
                paddingRight: 10,
              }}
            >
              {product?.title}
            </p>
          </div>
        </div>
        <div className="messageGravatar">
          <Progress
            percent={product?.price}
            format={(percent) => `$${percent}`}
            strokeColor="#1ab3ff"
            trailColor="#f5f6fa"
          />
        </div>
      </div>
    );
  };
  const renderMostViewProduct = (product) => {
    return (
      <div style={{ margin: '10px 30px' }}>
        <div className="messageContentText">
          <div className="messageContentText msgUserDetail">
            <p
              style={{
                fontWeight: '500',
                fontSize: 14,
                paddingRight: 10,
              }}
            >
              {product?.title}
            </p>
          </div>
        </div>
        <div className="messageGravatar">
          <Progress
            percent={product?.price}
            format={(percent) => `$${percent}`}
            strokeColor="#1ab3ff"
            trailColor="#f5f6fa"
          />
        </div>
      </div>
    );
  };
  const renderCamp = (camp) => {
    return (
      <div style={{ margin: '10px 30px' }}>
        <div className="messageContentText">
          <div className="messageContentText msgUserDetail">
            <p
              style={{
                fontWeight: '500',
                fontSize: 14,
                paddingRight: 10,
              }}
            >
              {camp?.title}
            </p>
          </div>
        </div>
        <div className="messageGravatar">
          <Progress
            percent={camp?.count}
            format={(percent) => `$${percent}`}
            strokeColor="#1ab3ff"
            trailColor="#f5f6fa"
          />
        </div>
      </div>
    );
  };

  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      return (
        <div
          style={{
            backgroundColor: '#fff',
            border: '1px solid #fff !important',
            padding: '2px',
          }}
        >
          <p>{`Value: ${payload[0].value}`}</p>
        </div>
      );
    }
    return null;
  };

  // Custom Legend Component..
  const CustomLegend = ({ payload }) => {
    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'flex-start',
        }}
      >
        {payload?.map((entry, index) => (
          <div
            key={`item-${entry?.dataKey}`}
            style={{
              display: 'flex',
              alignItems: 'center',
              margin: '5px 10px',
              justifyContent: 'center',
              alignSelf: 'center',
            }}
          >
            <div
              style={{
                width: '12px',
                height: '12px',
                borderRadius: '50%', // Circular shape
                backgroundColor: entry.color,
                marginRight: '5px',
              }}
            />
            <span
              style={{
                fontFamily: 'inter',
                fontWeight: '400',
                fontSize: '12px',
                color: '#4B5157',
              }}
            >
              {entry?.payload.label}
            </span>
          </div>
        ))}
      </div>
    );
  };

  // Custom Tooltip Component
  const CustomBarToolTips = ({ active, payload }) => {
    if (active && payload && payload.length) {
      return (
        <div
          style={{
            backgroundColor: '#fff',
            border: '1px solid #ccc',
            padding: '10px',
          }}
        >
          <p>{payload[0].payload.month}</p>
          <p
            style={{ color: payload[0].color }}
          >{`No. of sales: ${payload[0].payload.value_1}`}</p>{' '}
          {/* Change this to customize the label */}
          <p
            style={{ color: payload[1].color }}
          >{`No. of Products Sold: ${payload[0].payload.value_2}`}</p>{' '}
          {/* Display the value */}
        </div>
      );
    }
    return null;
  };

  // render Sales Performance Chart..
  const salesPerformanceChart = () => {
    return (
      <BarChart
        width={500}
        height={300}
        data={salesPerformanceData?.chart || []}
        margin={{
          top: 5,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        <XAxis dataKey="month" />
        <Tooltip
          content={CustomTooltip}
          wrapperStyle={{ outline: 'none', borderColor: 'red' }}
        />
        {/* Define the gradient */}
        <defs>
          <linearGradient id="colorPv" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#07D4BC" stopOpacity={1} />
            <stop offset="100%" stopColor="#00C8B1" stopOpacity={1} />
          </linearGradient>
        </defs>
        <Bar
          dataKey="value"
          fill="url(#colorPv)"
          radius={5}
          background={{ fill: '#eee', radius: 5 }}
        />
      </BarChart>
    );
  };

  return (
    <LayoutWrapper>
      <DashboardWrapper>
        <div
          style={{
            ...styles.wisgetPageStyle,
          }}
        >
          <Row
            style={{
              ...rowStyle,
              padding: '0px 20px 0px 20px',
              marginTop: '45px',
            }}
            gutter={[10, 10]}
          >
            {renderTopBoxes()}
          </Row>
          <Row
            style={{ ...rowStyle, padding: '0px 20px 0px 20px' }}
            gutter={[10, 10]}
            justify="start"
          >
            <Col xl={13} md={24} sm={24} xs={24}>
              <IsoWidgetsWrapper>
                <Spin spinning={overViewData.loading}>
                  <ReportsWidget
                    widgetClassName="mb0"
                    labelClsName="mb15"
                    style={{
                      borderRadius: 12,
                      boxShadow: '5px 5px 5px 5px #0000',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        marginBottom: 16,
                      }}
                    >
                      <Popover
                        content={customerOverviewContent}
                        trigger="click"
                        visible={overviewVisible}
                        onVisibleChange={overViewChange}
                        placement="bottomLeft"
                      >
                        <span
                          className="chartHeader"
                          style={{
                            marginBottom: 20,
                          }}
                        >
                          {overView}
                        </span>
                      </Popover>
                      <Popover
                        content={customerDurationContent}
                        trigger="click"
                        visible={durationVisible}
                        onVisibleChange={durationChange}
                        placement="bottomRight"
                      >
                        <p
                          className="weekHeader"
                          style={{
                            cursor: 'pointer',
                            fontWeight: '400',
                          }}
                        >
                          {duration}
                          <DownOutlined style={{ marginLeft: 5 }} />
                        </p>
                      </Popover>
                    </div>
                    <Spin spinning={accLoading}>
                      <ResponsiveContainer height={300}>
                        <BarChart
                          data={overViewData?.data?.customerOverview || []}
                          margin={{
                            top: 5,
                            right: 30,
                            left: 20,
                            bottom: 5,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis
                            dataKey="month"
                            style={{
                              fontFamily: 'inter',
                              fontSize: '12px',
                              lineHeight: '15px',
                            }}
                          />
                          <Tooltip content={<CustomBarToolTips />} />
                          <Legend content={<CustomLegend />} />
                          <Bar
                            dataKey="value_1"
                            fill="#0FB4FF"
                            radius={[10, 10, 0, 0]}
                            label="No. of sales"
                          />
                          <Bar
                            dataKey="value_2"
                            fill="#00D9C0"
                            label="No. of Products Sold"
                            radius={[10, 10, 0, 0]}
                          />
                        </BarChart>
                      </ResponsiveContainer>
                    </Spin>
                  </ReportsWidget>
                </Spin>
              </IsoWidgetsWrapper>
            </Col>
            <Col xl={11} md={24} sm={24} xs={24}>
              <IsoWidgetsWrapper>
                <Spin spinning={overViewData.loading}>
                  <ReportsWidget
                    widgetClassName="mb0"
                    labelClsName="mb15"
                    style={{
                      height: '100%',
                      borderRadius: 12,
                      boxShadow: '5px 5px 5px 5px #0000',
                    }}
                  >
                    <span
                      className="conversionText"
                      style={{ marginBottom: 20 }}
                    >
                      <IntlMessages
                        id={<IntlMessages id="title.conversionFunnel" />}
                      />
                    </span>
                    <Spin spinning={accLoading}>
                      {barData?.map((item, index) => {
                        return (
                          <Row key={`${index + 1}`} align="middle">
                            <Col span={24}>
                              <div
                                style={{
                                  position: 'relative',
                                  height: '40px', // Adjust height
                                  borderRadius: '8px', // Adjust border radius
                                  overflow: 'hidden',
                                  width: '100%',
                                  borderWidth: 2,
                                  borderColor:
                                    'linear-gradient(to right, rgba(186, 186, 186, 0.05), rgba(0, 134, 255, 0.6))',
                                  background:
                                    'linear-gradient(to right, rgba(4, 184, 255, 0.02), rgba(0, 134, 255, 0.03))',
                                }}
                              >
                                <span
                                  style={{
                                    fontFamily: 'Inter',
                                    fontWeight: '700',
                                    color: '#fff',
                                    position: 'absolute',
                                    padding: '10px 10px',
                                    fontSize: '14px',
                                  }}
                                >
                                  {item.label}
                                </span>
                                <div
                                  style={{
                                    width: `${item.percent}%`,
                                    height: '100%',
                                    background:
                                      'linear-gradient(to right,  #0B66B8, #1F9BCC)',
                                    borderRadius: '8px',
                                  }}
                                />
                              </div>
                              <Col
                                style={{
                                  fontSize: '20px',
                                  fontFamily: 'Inter',
                                  fontWeight: '700',
                                  textAlign: 'right',
                                  bottom: 35,
                                  right: 10,
                                }}
                              >
                                {item?.value.toLocaleString()}
                              </Col>
                            </Col>
                          </Row>
                        );
                      })}
                    </Spin>
                  </ReportsWidget>
                </Spin>
              </IsoWidgetsWrapper>
            </Col>
          </Row>
          <Row
            gutter={[10, 10]}
            style={{
              ...rowStyle,
              padding: '30px 20px 0px 20px',
            }}
          >
            <Col xl={8} md={24} sm={12} xs={24} style={colStyle}>
              <IsoWidgetsWrapper style={{ height: '100%' }}>
                <Spin spinning={dData.loading}>
                  <ReportsWidget
                    style={{
                      height: '100%',
                      borderRadius: 12,
                      boxShadow: '5px 5px 5px 5px #0000',
                      padding: 0,
                    }}
                    // label={<IntlMessages id="ecom.mostViewedProducts" />}
                    labelClsName="labelStyle"
                    widgetClassName="flex1"
                    className="campaignsList"
                  >
                    <span
                      className="headerText"
                      style={{
                        marginBottom: 20,
                      }}
                    >
                      <IntlMessages id="ecom.mostViewedProducts" />
                    </span>
                    <div
                      style={{
                        color: '#efefef',
                        width: '100%',
                        background: '#efefef',
                        height: 1,
                      }}
                    />
                    {mostViewed.length > 0 ? (
                      mostViewed.map(renderMostViewProduct)
                    ) : (
                      <Empty
                        style={{
                          alignContent: 'center',
                          alignItems: 'center',
                          marginTop: 10,
                        }}
                        description={<IntlMessages id="ecom.noProducts" />}
                      />
                    )}
                  </ReportsWidget>
                </Spin>
              </IsoWidgetsWrapper>
            </Col>
            <Col
              xl={8}
              md={24}
              sm={12}
              xs={24}
              style={colStyle}
              className="isoTrafficList"
            >
              <IsoWidgetsWrapper style={{ height: '100%' }}>
                <Spin spinning={topSelling.loading}>
                  <ReportsWidget
                    labelClsName="labelStyle"
                    className="campaignsList"
                    style={{
                      height: '100%',
                      borderRadius: 12,
                      boxShadow: '5px 5px 5px 5px #0000',
                      padding: 0,
                    }}
                    widgetClassName="flex1"
                  >
                    <span
                      className="headerText"
                      style={{
                        marginBottom: 20,
                      }}
                    >
                      <IntlMessages id="ecom.topSellingProducts" />
                    </span>
                    <div
                      style={{
                        color: '#efefef',
                        width: '100%',
                        background: '#efefef',
                        height: 1,
                      }}
                    />
                    {topProducts.length > 0 ? (
                      topProducts.map(renderProduct)
                    ) : (
                      <Empty
                        style={{
                          alignContent: 'center',
                          alignItems: 'center',
                          marginTop: 10,
                        }}
                        description={<IntlMessages id="ecom.noProducts" />}
                      />
                    )}
                  </ReportsWidget>
                </Spin>
              </IsoWidgetsWrapper>
            </Col>
            <Col xl={8} md={24} sm={12} xs={24} style={colStyle}>
              <IsoWidgetsWrapper style={{ height: '100%' }}>
                <Spin spinning={topMarkCamp.loading}>
                  <ReportsWidget
                    style={{
                      height: '100%',
                      borderRadius: 12,
                      boxShadow: '5px 5px 5px 5px #0000',
                      padding: 0,
                    }}
                    // label={<IntlMessages id="ecom.topMarketingCamp" />}
                    labelClsName="labelStyle"
                    widgetClassName="flex1"
                    className="campaignsList"
                  >
                    <span
                      className="headerText"
                      style={{
                        marginBottom: 20,
                      }}
                    >
                      <IntlMessages id="ecom.topMarketingCamp" />
                    </span>
                    <div
                      style={{
                        color: '#efefef',
                        width: '100%',
                        background: '#efefef',
                        height: 1,
                      }}
                    />
                    {topMarketingCamp.length > 0 ? (
                      topMarketingCamp.map(renderCamp)
                    ) : (
                      <Empty
                        style={{
                          alignContent: 'center',
                          alignItems: 'center',
                          marginTop: 10,
                        }}
                        description={<IntlMessages id="ecom.noCamp" />}
                      />
                    )}
                  </ReportsWidget>
                </Spin>
              </IsoWidgetsWrapper>
            </Col>
          </Row>
          <Row
            gutter={[10, 10]}
            style={{
              ...rowStyle,
              padding: '0px 20px 0px 20px',
            }}
          >
            <Col xl={8} md={24} sm={12} xs={24} style={colStyle}>
              <IsoWidgetsWrapper>
                <Spin spinning={dData.loading}>
                  <ReportsWidget
                    style={{
                      borderRadius: 25,
                      boxShadow: '5px 5px 5px 5px #0000',
                    }}
                    widgetClassName="mb0"
                    labelClsName="mb15"
                  >
                    <ResponsiveContainer width="100%" height={400}>
                      <PieChart>
                        <Pie
                          data={dData?.data?.trafficSourceChart || []}
                          cx="50%"
                          cy="50%"
                          label={renderCustomizedLabel}
                          outerRadius={150}
                          innerRadius={100}
                          fill="#8884d8"
                          dataKey="value"
                          paddingAngle={10}
                          cornerRadius={10}
                        >
                          {(dData?.data?.trafficSourceChart || []).map(
                            (entry, index) => (
                              <Cell
                                key={`ind-${index}`}
                                fill={pieChartData?.[index]?.color || '#8884d8'}
                              />
                            ),
                          )}
                        </Pie>
                        {renderCenterLabel({ cx: '50%', cy: '50%' })}
                      </PieChart>
                    </ResponsiveContainer>
                  </ReportsWidget>
                </Spin>
              </IsoWidgetsWrapper>
            </Col>
            <Col
              xl={8}
              md={24}
              sm={12}
              xs={24}
              style={colStyle}
              className="isoTrafficList"
            >
              <IsoWidgetsWrapper>
                <Spin spinning={topSelling.loading}>
                  <ReportsWidget
                    // label={<IntlMessages id="Sales Performance" />}
                    widgetClassName="mb0"
                    labelClsName="mb15"
                    style={{
                      borderRadius: 25,
                      boxShadow: '5px 5px 5px 5px #0000',
                    }}
                  >
                    <span
                      className="headerText"
                      style={{
                        marginBottom: 20,
                        textAlign: 'center',
                      }}
                    >
                      <IntlMessages id="title.salesPerformance" />
                    </span>
                    <ResponsiveContainer width="100%" height={370}>
                      {salesPerformanceChart()}
                    </ResponsiveContainer>
                    <Text
                      style={{
                        textAlign: 'center',
                        color: '#767676',
                        fontSize: 12,
                        fontFamily: 'Inter',
                        fontWeight: '400',
                        lineHeight: '14px',
                      }}
                    >
                      Sales performance is{' '}
                      <span
                        style={{
                          color: '#141B34',
                          fontWeight: '700',
                          fontFamily: 'Inter',
                          lineHeight: '14px',
                        }}
                      >
                        {salesPerformanceData?.percentChange || 0}{' '}
                        {salesPerformanceData?.trend}
                      </span>
                    </Text>
                  </ReportsWidget>
                </Spin>
              </IsoWidgetsWrapper>
            </Col>
            <Col xl={8} md={24} sm={12} xs={24} style={colStyle}>
              <IsoWidgetsWrapper>
                <Spin spinning={topMarkCamp.loading}>
                  <ReportsWidget
                    style={{
                      borderRadius: 25,
                      boxShadow: '5px 5px 5px 5px #0000',
                    }}
                    // label={<IntlMessages id="Goal Completion" />}
                    widgetClassName="mb0"
                    labelClsName="mb15"
                  >
                    <span
                      className="headerText"
                      style={{
                        marginBottom: 20,
                        textAlign: 'center',
                      }}
                    >
                      <IntlMessages id="sidebar.goalcompletion" />
                    </span>
                    <CircularProgressbarWithChildren
                      value={percentage}
                      strokeWidth={0}
                      styles={buildStyles({
                        pathColor: 'transparent', // Hide the circular progress path
                        trailColor: 'transparent', // Hide the trail
                        strokeLinecap: 'butt',
                      })}
                    >
                      <div
                        style={{
                          fontSize: '30px',
                          color: '#404040',
                          fontWeight: '700',
                          lineHeight: '37px',
                          fontFamily: 'Inter',
                        }}
                      >
                        {`${percentage}%`}
                      </div>
                      <div
                        style={{
                          fontSize: '14px',
                          color: '#404040',
                          marginTop: '3px',
                          fontWeight: '700',
                          lineHeight: '17px',
                          fontFamily: 'Inter',
                        }}
                      >
                        {/* Add your additional text here */}
                        Excellent
                      </div>
                      <RadialSeparators
                        count={100}
                        circular
                        percentage={percentage}
                      />
                      <RadialSeparators count={100} percentage={percentage} />
                    </CircularProgressbarWithChildren>
                  </ReportsWidget>
                </Spin>
              </IsoWidgetsWrapper>
            </Col>
          </Row>
        </div>
      </DashboardWrapper>
    </LayoutWrapper>
  );
}

export default injectIntl(EComWidget);

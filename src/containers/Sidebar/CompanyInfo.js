import React from 'react';
import { Progress, Spin, Tooltip } from 'antd';
import { isEmpty, isObject } from 'lodash';
import IntlMessages from '@chill/components/utility/intlMessages';
import theme from '@chill/config/theme/default';
import { CrownTwoTone } from '@ant-design/icons';

function CompanyInfo({ userData = {}, access = {} }) {
  const userAccess = isObject(access) ? access : {};
  const accLoading = userAccess.loading || false;
  const cSpace = isObject(userAccess.cloud_space) ? userAccess.cloud_space : {};
  const percentage = !isEmpty(cSpace)
    ? (100 * cSpace.getSizeOfDocumet) / cSpace.featuresSpace
    : 0;
  const per =
    percentage < 99 ? percentage.toPrecision(2) : percentage.toFixed(2);

  function renderCompanyName() {
    return (
      userAccess.company_name || (
        <IntlMessages
          id="common.helloUser"
          values={{
            name: userData.first_name || '@user',
          }}
        />
      )
    );
  }

  return (
    <div className="companyInfo">
      <Spin spinning={!!accLoading}>
        <Tooltip placement="topLeft" title={renderCompanyName()}>
          <h3>
            {accLoading ? (
              <IntlMessages id="common.loading" />
            ) : (
              renderCompanyName()
            )}{' '}
            {!accLoading && userAccess.view_team && (
              <CrownTwoTone
                className="roleIcon"
                twoToneColor={theme.colors.primaryColor}
              />
            )}
          </h3>
        </Tooltip>

        {!isEmpty(cSpace) && (
          <div className="mt5">
            <small>
              <IntlMessages
                id="common.storageUsed"
                values={{
                  used: cSpace.usedSize || 0,
                  total: cSpace.totalSpace || 0,
                }}
              />
            </small>
            <Tooltip
              placement="right"
              title={
                <IntlMessages
                  id="common.usedPer"
                  values={{
                    used: per || 0,
                  }}
                />
              }
            >
              <Progress
                percent={percentage}
                showInfo={false}
                strokeColor={
                  percentage > 90 ? 'red' : theme.colors.primaryColor
                }
                status="normal"
              />
            </Tooltip>
          </div>
        )}
      </Spin>
    </div>
  );
}

export default CompanyInfo;

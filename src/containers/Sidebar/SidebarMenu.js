/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
/* eslint-disable no-unused-expressions */
/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable no-restricted-globals */
import React from 'react';
import { Link, useRouteMatch } from 'react-router-dom';
import { useSelector } from 'react-redux';
import siteConfig from '@chill/config/site.config';
import Menu from '@chill/components/uielements/menu';
import IntlMessages from '@chill/components/utility/intlMessages';
import AntIcon from '@chill/components/antdIcon';
import { isEmpty, isObject } from 'lodash';
import DashSVG from '@chill/assets/images/sidebarIcons/dashboard.svg';
import DashFilledSVG from '@chill/assets/images/sidebarIcons/DashFilledSVG.svg';
import serviceSVG from '@chill/assets/images/sidebarIcons/serviceSVG.svg';
import serviceFiledSVG from '@chill/assets/images/sidebarIcons/serviceFiledSVG.svg';
import analyticsSVG from '@chill/assets/images/sidebarIcons/analytics.svg';
import analyticsFiledSVG from '@chill/assets/images/sidebarIcons/analyticsFiledSVG.svg';
import customersSVG from '@chill/assets/images/sidebarIcons/user-circle.png';
import customersFiledSVG from '@chill/assets/images/sidebarIcons/user-circle-filled.png';
import marketingSVG from '@chill/assets/images/sidebarIcons/marketing.svg';
import marketingFiledSVG from '@chill/assets/images/sidebarIcons/marketingFiledSVG.svg';
import productsSVG from '@chill/assets/images/sidebarIcons/products.svg';
import productsFiledSVG from '@chill/assets/images/sidebarIcons/productsFiledSVG.svg';
import devicesSVG from '@chill/assets/images/sidebarIcons/devices.svg';
import devicesFiledSVG from '@chill/assets/images/sidebarIcons/devicesFiledSVG.svg';
import goalSVG from '@chill/assets/images/sidebarIcons/goal.svg';
import goalFiledSVG from '@chill/assets/images/sidebarIcons/goalFiledSVG.svg';
// import faqSVG from '@chill/assets/images/sidebarIcons/faq.svg';
import strollersSVG from '@chill/assets/images/sidebarIcons/stroller.svg';
import blueExpandIcon from '@chill/assets/images/blue-up-arrow.png';
import downArrow from '@chill/assets/images/primary-down-arrow.png';
import strolerFiledSVG from '@chill/assets/images/sidebarIcons/strolerFiledSVG.svg';
import EcommerceSVG from '@chill/assets/images/sidebarIcons/ecommerce.svg';
import EcommerceFiledSVG from '@chill/assets/images/sidebarIcons/EcommerceFiledSVG.svg';

const { SubMenu } = Menu;

const stripTrailingSlash = (str) => {
  if (str.substr(-1) === '/') {
    return str.substr(0, str.length - 1);
  }
  return str;
};

function getChildKey(key = '') {
  let keyVar = key || '';

  if (key.indexOf('/') > -1) {
    keyVar = key.split('/').pop();
  }

  return keyVar;
}

export default React.memo(function SidebarMenu({
  singleOption,
  submenuStyle,
  submenuColor,
  ...rest
}) {
  const match = useRouteMatch();
  const url = stripTrailingSlash(match.url);
  const { key, label, children, img, type = '' } = singleOption;
  const { tabBadges } = useSelector((state) => state.Chat);
  const userData = useSelector((states) => states.Auth.userData);
  const uData = isObject(userData) ? { ...userData } : {};

  const isActive =
    location.pathname.includes(`${url}/${key}`) ||
    (children &&
      children.some((child) =>
        location.pathname.includes(`${url}/${child.key}`),
      )) ||
    (key === 'dashboard' && location.pathname === `${url}`);

  const getIcon = () => {
    const baseIcon = {
      dashboard: DashSVG,
      service: serviceSVG,
      analytics: analyticsSVG,
      customers: customersSVG,
      marketing: marketingSVG,
      products: productsSVG,
      devices: devicesSVG,
      goal: goalSVG,
      faq: DashSVG,
      strollers: strollersSVG,
      ecommerce: EcommerceSVG,
    };

    const filledIcon = {
      dashboard: DashFilledSVG,
      service: serviceFiledSVG,
      analytics: analyticsFiledSVG,
      customers: customersFiledSVG,
      marketing: marketingFiledSVG,
      products: productsFiledSVG,
      devices: devicesFiledSVG,
      goal: goalFiledSVG,
      faq: DashFilledSVG,
      strollers: strolerFiledSVG,
      ecommerce: EcommerceFiledSVG,
    };
    return isActive ? filledIcon[img] || baseIcon[img] : baseIcon[img];
  };

  const imageSVG = getIcon();

  function rendersMenu(d) {
    const cKey = getChildKey(d.key);
    const linkTo = d.withoutDashboard ? `/${d.key}` : `${url}/${d.key}`;
    return (d.key === 'strollers' && uData.brand_name !== 'bugaboo') ||
      (d?.contact_name && d.contact_name !== uData.contact_name) ? null : (
      <Menu.Item style={submenuStyle} key={cKey}>
        <Link style={submenuColor} to={linkTo}>
          <IntlMessages id={d.label} />
        </Link>
      </Menu.Item>
    );
  }

  if (children) {
    return (
      <SubMenu
        style={{ zIndex: 10 }}
        key={key}
        title={
          <span className="isoMenuHolder" style={submenuColor}>
            {img ? (
              <img className="iconImg" src={imageSVG} alt={label} />
            ) : (
              <AntIcon name={img} />
            )}
            <span className="nav-text">
              <IntlMessages id={label} />
            </span>
          </span>
        }
        expandIcon={({ isOpen }) =>
          !isOpen ? (
            <img src={downArrow} alt="collapse arrow" />
          ) : (
            <img src={blueExpandIcon} alt="expand arrow" />
          )
        }
        {...rest}
      >
        {children &&
          children.map((child) => {
            const lnk = child?.link || '';
            if (lnk) {
              return (
                <Menu.Item key={child.key} style={submenuStyle}>
                  <a href={lnk} {...(child.aProps && { ...child.aProps })}>
                    <IntlMessages id={child.label} />
                  </a>
                </Menu.Item>
              );
            }
            return rendersMenu(child);
          })}
      </SubMenu>
    );
  }

  if (type === 'link') {
    return (
      <Menu.Item key={key} {...rest}>
        <a
          target="_blank"
          rel="noopener noreferrer"
          href={`${siteConfig.apiUrl}${key}`}
          style={submenuColor}
        >
          <span className="isoMenuHolder" style={submenuColor}>
            {img ? (
              <img className="iconImg" src={imageSVG} alt={label} />
            ) : (
              <AntIcon name={img} />
            )}
            <span className="nav-text">
              <IntlMessages id={label} />
            </span>
          </span>
        </a>
      </Menu.Item>
    );
  }
  const rKey = key === 'dashboard' ? '' : `/${key}`;
  const activeBadge =
    isObject(tabBadges) && !isEmpty(tabBadges) ? tabBadges.active : 0;
  const openBadge =
    isObject(tabBadges) && !isEmpty(tabBadges) ? tabBadges.open : 0;
  const totalBadge = activeBadge + openBadge;

  return (
    <Menu.Item key={key} {...rest}>
      <Link to={`${url}${rKey}`}>
        <span className="isoMenuHolder" style={submenuColor}>
          {img ? (
            <img className="iconImg2" src={imageSVG} alt={label} />
          ) : (
            <AntIcon name={img} />
          )}
          <span className="nav-text">
            <IntlMessages id={label} />
          </span>
          {totalBadge > 0 && key === 'support' ? (
            <div className="tabBadgeStyle">{totalBadge}</div>
          ) : null}
        </span>
      </Link>
    </Menu.Item>
  );
});

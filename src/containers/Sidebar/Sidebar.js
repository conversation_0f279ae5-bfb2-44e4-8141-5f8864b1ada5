/* eslint-disable react/jsx-no-bind */
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Divider, Layout } from 'antd';
import { includes } from 'lodash';
import Scrollbars from '@chill/components/utility/customScrollBar';
import Menu from '@chill/components/uielements/menu';
import appActions from '@chill/redux/app/actions';
import Logo from '@chill/components/utility/logo';
import IntlMessages from '@chill/components/utility/intlMessages';
import options from './options';
import SidebarWrapper from './Sidebar.styles';
import SidebarMenu from './SidebarMenu';
import accessMenus, { upgradeScreens } from './config';
import PlaninfoBox from '../Topbar/PlaninfoBox';

const { Sider } = Layout;

const { changeOpenKeys, toggleCollapsed } = appActions;

export default function Sidebar() {
  const dispatch = useDispatch();
  const { view, openKeys, collapsed, openDrawer, current, height } =
    useSelector((state) => state.App);
  const customizedTheme = useSelector(
    (state) => state.ThemeSwitcher.sidebarTheme,
  );
  const { userData, access } = useSelector((state) => state.Auth);

  const planData = userData?.plan?.currentPlan ? userData.plan.currentPlan : {};

  const tabMobile = view === 'MobileView' || view === 'TabView';

  function handleClick() {
    if (tabMobile) {
      setTimeout(() => {
        dispatch(toggleCollapsed());
      }, 100);
    }
  }

  const getAncestorKeys = (key) => {
    const map = {
      sub3: ['sub2'],
    };
    return map[key] || [];
  };

  function onOpenChange(newOpenKeys) {
    const latestOpenKey = newOpenKeys.find(
      (key) => !(openKeys.indexOf(key) > -1),
    );
    const latestCloseKey = openKeys.find(
      (key) => !(newOpenKeys.indexOf(key) > -1),
    );
    let nextOpenKeys = [];
    if (latestOpenKey) {
      nextOpenKeys = getAncestorKeys(latestOpenKey).concat(latestOpenKey);
    }
    if (latestCloseKey) {
      nextOpenKeys = getAncestorKeys(latestCloseKey);
    }
    dispatch(changeOpenKeys(nextOpenKeys));
  }

  const isCollapsed = collapsed && !openDrawer;
  const mode = isCollapsed === true ? 'vertical' : 'inline';

  const styling = {
    backgroundColor: customizedTheme.backgroundColor,
    zIndex: 9,
  };

  const mainCustom =
    customizedTheme.themeName === 'themeCustom' ? ' customThemeSidebar' : '';

  return (
    <SidebarWrapper>
      <Sider
        trigger={null}
        collapsible
        collapsed={isCollapsed}
        width={276}
        className={`isomorphicSidebar${mainCustom}`}
        style={styling}
      >
        <Logo
          logo="/logo.png"
          // sLogo={settings?.small_logo}
          collapsed={isCollapsed}
        />
        <Scrollbars className="sidebarScroll" style={{ height: height - 70 }}>
          {/* {!isCollapsed && <CompanyInfo userData={userData} access={access} />} */}
          <Menu
            onClick={handleClick}
            className="isoDashboardMenu"
            mode={mode}
            openKeys={openKeys}
            selectedKeys={current}
            onOpenChange={onOpenChange}
          >
            <div
              className={`menuContentWrapper ${isCollapsed ? 'collapsed' : ''}`}
              style={{ flexGrow: 1 }}
            >
              <Menu.ItemGroup key="home" title={<IntlMessages id="Home" />}>
                {options?.map((singleOption) => {
                  const accessKey = accessMenus[singleOption.key] || '';
                  const showRoute = access[accessKey] || !accessKey;
                  const findUserType = singleOption.userType
                    ? includes(singleOption.userType, userData.user_type)
                    : '';

                  if (!findUserType) {
                    return null;
                  }

                  if (
                    showRoute ||
                    upgradeScreens.indexOf(singleOption.key) > -1
                  ) {
                    return (
                      <SidebarMenu
                        key={singleOption.key}
                        singleOption={singleOption}
                      />
                    );
                  }

                  return null;
                })}
              </Menu.ItemGroup>
            </div>
          </Menu>

          <Divider style={{ margin: '10px 0px' }} />

          {!isCollapsed && <PlaninfoBox planData={planData} />}
          <Menu style={{ padding: 0 }}>
            <Menu.Item key="logout" style={{ paddingLeft: '42px' }}>
              <span className="isoMenuHolder">
                <span className="nav-text">
                  <IntlMessages id="Log Out" />
                </span>
              </span>
            </Menu.Item>
          </Menu>
        </Scrollbars>
      </Sider>
    </SidebarWrapper>
  );
}

const options = [
  {
    key: 'dashboard',
    label: 'dashboard.title',
    img: 'dashboard',
    userType: [
      'chillbaby_admin',
      'brand_admin',
      'marketing_manager',
      'chillbaby_user',
      'manager',
    ],
  },
  {
    key: 'analytics',
    userType: [
      'chillbaby_admin',
      'brand_admin',
      'marketing_manager',
      'chillbaby_user',
      'manager',
    ],
    label: 'sidebar.analytics',
    img: 'analytics',
  },
  {
    key: 'marketing',
    userType: ['brand_admin', 'marketing_manager', 'manager'],
    label: 'sidebar.marketing',
    img: 'marketing',

    children: [
      {
        key: 'marketing-new',
        userType: ['brand_admin', 'marketing_manager', 'manager'],
        label: 'sidebar.campaignMarketing',
        img: 'marketingnew1',
      },
      {
        key: 'marketingnew1',
        userType: ['brand_admin', 'marketing_manager', 'manager'],
        label: 'sidebar.AIMarketing',
        img: 'marketingnew1',
      },
      {
        key: 'TMPlace',
        userType: ['brand_admin', 'marketing_manager', 'manager'],
        label: 'sidebar.marketplace',
        img: 'marketingnew1',
      },
      {
        key: 'FeedCampaigns',
        userType: ['brand_admin', 'marketing_manager', 'manager'],
        label: 'sidebar.feedcampaigns',
        img: 'marketingnew1',
      },
      // {
      //   key: 'marketingNew',
      //   userType: ['brand_admin', 'marketing_manager', 'manager'],
      //   label: 'AI Marketing 2',
      //   img: 'marketingNew',
      // },
      // {
      //   key: 'marketingnew1',
      //   userType: ['brand_admin', 'marketing_manager', 'manager'],
      //   label: 'AI Marketing 2',
      //   img: 'marketingnew1',
      // },
    ],
  },
  {
    key: 'ecommerce',
    userType: ['brand_admin'],
    label: 'sidebar.ecommerce',
    img: 'ecommerce',
  },
  {
    key: 'support',
    userType: ['brand_admin', 'customer_service', 'manager', 'chillbaby_admin'],
    label: 'sidebar.support',
    img: 'service',
  },
  {
    key: 'brand-management',
    userType: ['chillbaby_admin', 'chillbaby_user'],
    label: 'sidebar.brandManage',
    img: 'customers',
    children: [
      {
        key: 'brands',
        userType: ['chillbaby_admin', 'chillbaby_user'],
        label: 'sidebar.brands',
        img: 'customers',
      },
      {
        key: 'brand-integration',
        userType: ['chillbaby_admin', 'chillbaby_user'],
        label: 'sidebar.integrations',
        img: 'customers',
      },
    ],
  },
  // {
  //   key: 'brands',
  //   userType: ['chillbaby_admin', 'chillbaby_user'],
  //   label: 'sidebar.brands',
  //   img: 'customers',
  // },
  {
    key: 'customers',
    userType: [
      'brand_admin',
      'marketing_manager',
      'customer_service',
      'manager',
    ],
    label: 'sidebar.customer',
    img: 'customers',
  },
  {
    key: 'admin-accounting',
    userType: ['chillbaby_admin'],
    label: 'sidebar.accounting',
    img: 'marketing',
  },
  {
    key: 'products',
    userType: [
      'brand_admin',
      'marketing_manager',
      'customer_service',
      'manager',
    ],
    label: 'sidebar.products',
    img: 'products',
    children: [
      {
        key: 'products',
        userType: [
          'brand_admin',
          'marketing_manager',
          'customer_service',
          'manager',
        ],
        label: 'sidebar.productManage',
        img: 'products',
      },
      {
        key: 'product-category',
        userType: [
          'brand_admin',
          'marketing_manager',
          'customer_service',
          'manager',
        ],
        label: 'sidebar.product.category',
        img: 'products',
      },
    ],
  },
  // {
  //   key: 'device-management',
  //   userType: ['chillbaby_admin', 'brand_admin', 'manager'],
  //   label: 'sidebar.device',
  //   img: 'devices',
  // },
  {
    key: 'device-management',
    userType: ['chillbaby_admin', 'brand_admin', 'manager'],
    label: 'sidebar.device',
    img: 'devices',
    children: [
      {
        key: 'mainDevices',
        userType: ['chillbaby_admin', 'brand_admin', 'manager'],
        label: 'sidebar.mainDevice',
        img: 'products',
      },
      {
        key: 'devices-category',
        userType: ['chillbaby_admin', 'brand_admin', 'manager'],
        label: 'sidebar.deviceData',
        img: 'products',
      },
    ],
  },
  {
    key: 'goal-completion',
    userType: [
      'chillbaby_admin',
      'brand_admin',
      'chillbaby_user',
      'manager',
      'marketing_manager',
    ],
    label: 'sidebar.goalcompletion',
    img: 'goal',
  },
  {
    key: 'app-management',
    userType: ['chillbaby_admin', 'brand_admin', 'manager'],
    label: 'sidebar.appManagement',
    img: 'strollers',
    children: [
      {
        key: 'group-management',
        contact_name: 'Qeridoo Admin',
        label: 'sidebar.groupManagement',
        img: 'faq',
      },
      {
        key: 'trailer-management',
        contact_name: 'Qeridoo Admin',
        label: 'sidebar.trailerManagement',
        img: 'faq',
      },
      {
        key: 'maintenance',
        contact_name: 'Qeridoo Admin',
        label: 'sidebar.maintenance',
        img: 'faq',
      },
      {
        key: 'highlights',
        contact_name: 'Qeridoo Admin',
        label: 'sidebar.highlights',
        img: 'faq',
      },
      {
        key: 'gpx-management',
        contact_name: 'Qeridoo Admin',
        label: 'sidebar.gpxManagement',
        img: 'faq',
      },
      {
        key: 'faq',
        userType: ['chillbaby_admin', 'brand_admin', 'manager'],
        label: 'sidebar.faq',
        img: 'faq',
      },
      {
        key: 'strollers',
        userType: ['chillbaby_admin', 'brand_admin', 'manager'],
        label: 'sidebar.strollers',
        img: 'strollers',
      },
    ],
  },
];
export default options;

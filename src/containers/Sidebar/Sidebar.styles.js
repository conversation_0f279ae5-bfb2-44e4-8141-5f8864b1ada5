import styled from 'styled-components';
import { palette } from 'styled-theme';
import { transition, borderRadius } from '@chill/lib/helpers/style_utils';
import WithDirection from '@chill/lib/helpers/rtl';

const SidebarWrapper = styled.div`
  .isomorphicSidebar {
    z-index: 1000;
    background: ${palette('secondary', 0)};
    width: 280px;
    flex: 0 0 280px;

    .scrollarea {
      height: calc(100vh - 70px);
    }

    & .sidebarScroll {
      & > div {
        &:first-child {
          display: flex;
          flex-direction: column;
          margin-left: ${(props) =>
            props['data-rtl'] === 'rtl' ? '-15px' : '0px'};
          // margin-right: ${(props) =>
            props['data-rtl'] === 'rtl' ? '0px' : '-15px'} !important;
        }
      }

      & .companyInfo {
        margin-top: 5px;
        border-bottom: 1px solid #eee;
        width: auto;
        padding: 10px 20px;

        & h3 {
          font-weight: 550;
          font-size: 18px;
          color: ${palette('secondary', 2)};
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;

          & .roleIcon {
            font-size: 16px;
            // opacity: 0;
            // visibility: hidden;
            transition: all 0.3s;
          }
        }

        & .ant-progress-text {
          font-size: 13px;
          line-height: 14px;
        }

        // &:hover {
        //   & .roleIcon {
        //     opacity: 1;
        //     visibility: visible;
        //   }
        // }
      }

      & .planInfo {
        margin: 10px;
        margin-bottom: 20px;
        font-size: 16px;
        color: ${palette('primary', 0)};
        border: 1px solid ${palette('primary', 0)};
        // position: absolute;
        // bottom: 0;
        width: 210px;
        border-radius: 4px;
        background-color: #fff;

        & a {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 10px;

          & .anticon.anticon-right-circle {
            font-size: 20px;
          }
        }
      }
    }

    @media only screen and (max-width: 767px) {
      width: 240px !important;
      flex: 0 0 240px !important;
    }

    &.ant-layout-sider-collapsed {
      @media only screen and (max-width: 767px) {
        width: 0;
        min-width: 0 !important;
        max-width: 0 !important;
        flex: 0 0 0 !important;
      }
    }

    .isoLogoWrapper {
      height: 70px;
      // background: rgb(241, 243, 246);
      margin: 0;
      padding: 0 10px;
      text-align: center;
      overflow: hidden;
      /* NEW CUSTOM */
      display: flex;
      align-items: center;
      justify-content: center;
      border-right: 1px solid #f0f0f0;
      border-bottom: 1px solid #f0f0f0;
      ${borderRadius()};

      & img {
        max-width: 100%;
      }

      h3 {
        a {
          font-size: 21px;
          font-weight: 300;
          line-height: 70px;
          letter-spacing: 3px;
          text-transform: uppercase;
          color: ${palette('primary', 0)};
          display: block;
          text-decoration: none;
        }
      }
    }

    &.ant-layout-sider-collapsed {
      .isoLogoWrapper {
        padding: 0;

        h3 {
          a {
            font-size: 27px;
            font-weight: 500;
            letter-spacing: 0;
          }
        }
      }
    }

    .isoDashboardMenu {
      background: transparent;
      justify-content: center;
      flex: 1;
      @media (max-width: 1220px) {
      }
      // a {
      //   text-decoration: none;
      //   font-weight: 400;
      //   white-space: break-spaces;
      //   line-height: 1.2;
      // }

    //   .ant-menu-item {
    //     width: 100%;
    //     display: -ms-flexbox;
    //     display: flex;
    //     -ms-flex-align: center;
    //     align-items: center;
    //     padding: 0 24px;
    //     margin: 12px 0px;
    //   }

    //   .isoMenuHolder {
    //     display: flex;
    //     align-items: center;
    //   }

    //   img.iconImg {
    //     width: 20px;
    //     height: 20px;
    //     margin: ${(props) =>
      props['data-rtl'] === 'rtl' ? '0 0 0 15px' : 0};
    //     ${transition()};
    //   }
    //   img.iconImg2 {
    //     width: 20px;
    //     height: 20px;
    //     margin: ${(props) =>
      //       props['data-rtl'] === 'rtl' ? '0 0 0 15px' : '0 0 0 25px'};
      //     ${transition()};
      //   }
      //   .tabBadgeStyle {
      //     background-color: #ff0000;
      //     height: 18px;
      //     width: 18px;
      //     border-radius: 10px;
      //     display: flex;
      //     align-items: center;
      //     justify-content: center;
      //     margin-left: 8px;
      //     color: #fff !important;
      //     font-weight: 400;
      //     font-size: 13px;
      //   }

      //   .nav-text {
      //     font-size: 14px;

      //     font-family: 'Inter';
      //     font-weight: 600;
      //     margin-left: 15px;
      //     ${transition()};
      //   }

      //   .ant-menu-item-selected {
      //     & .iconImg {
      //     }
      //     & .iconImg2 {
      //         brightness(101%) contrast(102%);
      //     }
      //   }

      //   > li {
      //     &:hover {
      //       i,
      //       .nav-text {
      //         // color: #ffffff;
      //         color: #0086ff;
      //       }
      //     }
      //   }

      //   &.ant-menu-inline-collapsed {
      //     & .anticon {
      //       margin: ${(props) =>
      //         props['data-rtl'] === 'rtl' ? ' 0  0px 0px 5px' : ' 0 5px 0px 0px'};
      //     }
      //     & .ant-menu-item {
      //       justify-content: center;
      //     }
      //     & .ant-menu-submenu {
      //       & .ant-menu-submenu-title {
      //         justify-content: center;
      //       }
      //     }
      //   }
      // }

      // .menuContentWrapper {
      //   width: 100%;
      // }

      // .menuContentWrapper.collapsed {
      //   display: flex;
      //   flex-direction: column;
      //   align-items: center;
      //   justify-content: center;
      //   height: 100%;
      //   min-height: 100%;
      //   width: 100%;
      // }

      // .ant-menu-dark .ant-menu-inline.ant-menu-sub {
      //   background: ${palette('secondary', 5)};
      // }

      // .ant-menu-submenu-inline,
      // .ant-menu-submenu-vertical {
      //   > .ant-menu-submenu-title {
      //     width: 100%;
      //     display: flex;
      //     align-items: center;
      //     padding: 0 24px;

      //     > span {
      //       display: flex;
      //       align-items: center;
      //     }

      // .ant-menu-submenu-arrow {
      //   left: ${(props) =>
      props['data-rtl'] === 'rtl' ? '25px' : 'auto'};
        //   right: ${(props) =>
          props['data-rtl'] === 'rtl' ? 'auto' : '25px'};

        //   &:before,
        //   &:after {
        //     width: 8px;
        //     ${transition()};
        //   }

        //   &:before {
        //     transform: rotate(-45deg) translateX(3px);
        //   }

        //   &:after {
        //     transform: rotate(45deg) translateX(-3px);
        //   }

          ${
            '' /* &:after {
            content: '\f123';
            font-family: 'Ionicons' !important;
            font-size: 16px;
            color: inherit;
            left: ${props => (props['data-rtl'] === 'rtl' ? '16px' : 'auto')};
            right: ${props => (props['data-rtl'] === 'rtl' ? 'auto' : '16px')};
            ${transition()};
          } */
          };
        }

        // &:hover {
        //   .ant-menu-submenu-arrow {
        //     &:before,
        //     &:after {
        //       color: #ffffff;
        //     }
        //   }
        // }
      }


    // &.ant-layout-sider-collapsed {
    //   .nav-text {
    //     display: none;
    //   }

    //   .ant-menu-submenu-inline > {
    //     .ant-menu-submenu-title:after {
    //       display: none;
    //     }
    //   }

    //   .ant-menu-submenu-vertical {
    //     > .ant-menu-submenu-title:after {
    //       display: none;
    //     }

    //     .ant-menu-sub {
    //       background-color: transparent !important;

    //       .ant-menu-item {
    //         height: 35px;
    //       }
    //     }
    //   }
    }
  }
  /*-----------------------------------------------*/
  // CUSTOM SIDEBAR THEME
  /*-----------------------------------------------*/
  .customThemeSidebar {
    .ant-menu-submenu.ant-menu-submenu-inline {
      &.ant-menu-submenu-title {
        & * {
          color: inherit !important;
        }
      }
      .ant-menu-item-selected.ant-menu-item-only-child {
        border: none !important;
        height: 34px;
        border-radius: 12px;
      }
        
    }
      .ant-menu-submenu-selected {
         border: 1.5px solid;
         border-radius: 12px;
          background: #ECF4FF;

      }
         .ant-menu-submenu-open {
          //  border: 1px solid;
           background: none;
         border-radius: 12px;
         }
    .ant-menu-item:not(.ant-menu-item-selected),
    .ant-menu-submenu:not(.ant-menu-submenu-selected) {
      & * {
        // color: ${palette('secondary', 2)};
        color: #000;
        font-weight: 500;
      }
    }
    .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
      // background: linear-gradient(
      //   90deg,
      //   rgba(80, 135, 255, 1) 18%,
      //   rgba(41, 239, 196, 1) 70%
      // );
      box-shadow: 1px 1px 3px #f8f8fe;
      border: 1.5px solid !important;
      background: #ECF4FF;
      border-radius: 10px;
      height: 41px;
      font-weight: 500;
    }
    .ant-menu-item:after {
      display: none;
    }
    .ant-menu-item-selected a {
      color: #0086ff;
    }
    .ant-menu.ant-menu-sub.ant-menu-inline {
      background: #ffffff;
      & .ant-menu-item {
        // background-color: #ffffff;
      }
      & .ant-menu-item.ant-menu-item-selected {
        // background-color: ${palette('primary', 0)} !important;
      }
    }
    .ant-menu-submenu-inline {
      & .ant-menu-submenu-arrow::before,
      .ant-menu-submenu-arrow::after {
        background-color: ${palette('secondary', 2)} !important;
      }
    }
    .ant-menu-item.ant-menu-item-selected {
      // background-color: ${palette('primary', 0)} !important;
      & * {
        // color: #fff !important;
      }
    }
  }
`;

export default WithDirection(SidebarWrapper);

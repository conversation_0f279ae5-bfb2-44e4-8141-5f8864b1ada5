import styled from 'styled-components';
import WithDirection from '@chill/lib/helpers/rtl';

const ProductStyles = styled.div`
  .isoTabText {
    padding-left: 16px;
  }
  .isoTabRow {
    padding: 16px 16px 0px;
  }

  .main-content {
    padding: 21px;
    background-color: #ffffff;
    .title-col {
      width: 100%;
      .titleText {
        span {
          font-family: Inter;
          font-size: 20px;
          font-weight: 700;
          line-height: 36px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #242731;
        }
      }
    }
    .search-col {
      width: 100%;
    }
      .table-col {
    .table-pagnation {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px;
      background-color: #f7f7f8;
      border: 1px solid #d4d4d4;
      border-radius: 0px 0px 8px 8px;
      border-top: none;
      .removeRowBtn {
        width: 103px;
        display: flex;
        align-items: center;
        gap: 10px;
        span {
          font-family: Inter;
          font-size: 14px;
          font-weight: 400;
          line-height: 18px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #007aff;
        }
      }
      .gotoPagediv {
        display: flex;
        align-items: center;
        
        .goToText {
        padding-right: 24px;
          font-family: Inter;
          font-size: 14px;
          font-weight: 400;
          line-height: 18px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #9a9ea5;
        }
        .gotoInput {
          width: 32px;
          margin: 0 8px;
          border: none;
          background: transparent;
          border-bottom: 1px solid #9a9ea5;
          border-radius: 0px;
        }
        .gotoInput:focus {
          // border: none;
        }
      }
      .tabel-custom-pagination {
        .ant-pagination-disabled {
          .ant-pagination-item-link {
            border: none;
            background: none;
          }
        }
        .ant-pagination-item-active {
          background: none;
          border: none;
          a {
            border: none;
            background: none;
            font-family: Inter;
            font-size: 14px;
            font-weight: 500;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #007aff;
          }
        }

        .ant-pagination-prev,
        .ant-pagination-next {
          .ant-pagination-item-link {
            border: none;
            background: none;
          }
        }
        .ant-pagination-item {
          background: none;
          border: none;
          // a {
          //   border: none;
          //   background: none;
          //   font-family: Inter;
          //   font-size: 14px;
          //   font-weight: 400;
          //   text-align: left;
          //   text-underline-position: from-font;
          //   text-decoration-skip-ink: none;
          //   color: #242731;
          // }
        }
      }
    }
  }
  .isoTab > div > div::before {
    position: unset;
    border-bottom-color: red;
  }
  .isoTab > div > div > div > div > div {
    padding: 0px;
  }
  .isoTab > div > div > div > div {
    padding-bottom: 16px;
  }
  .isoTab > div > div > div > div > div {
    margin: 0px 46px 0px 0px;
    @media (min-width: 768px) and (max-width: 1024px) {
      margin: 0px 20px 0px 0px;
    }
  }
  .isoTab > div > div:nth-child(1) {
    margin: 0px;
  }
  .isoTab > div > div:nth-child(2) {
    display: none;
  }
  .horizontalDevider {
    margin: 0px;
  }
  .isoFilterView {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
  }
  .isoFilterIcon {
    display: flex;
    flex-direction: row;
  }
  .hiddenCol {
    padding-bottom: 16px;
    display: flex;
    justify-content: flex-end;
    @media (min-width: 767px) {
      display: none;
    }
  }
  .isoAddProduct {
    border-left: solid;
    border-left-width: 1px;
    border-left-color: #eee;
    @media (max-width: 767px) {
      display: none;
    }
  }
  .isoFilter1 {
    padding: 0px 16px;
    border-right: solid;
    border-right-width: 1px;
    border-right-color: #eee;
    @media (min-width: 768px) and (max-width: 1024px) {
      padding: 0px 7px;
    }
  }
  & .isoFilter2 {
    padding: 0px 16px;
    @media (min-width: 768px) and (max-width: 1024px) {
      padding: 0px 6px;
    }
  }
  .popMainDiv {
    display: flex;
    flex-direction: column;
    & < div < div < div {
      background-color: red;
    }
  }
  .isoDropdownLink {
    color: #000;
    padding: 8px 0px;
    cursor: pointer;
    span {
      font-family: Inter;
      font-size: 14px;
      font-weight: 400;
      line-height: 16.94px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #96aac1;
    }
  }
  .chartCol {
    background-color: #fff;
    text-align: center;
    display: flex;
    flex-direction: column;
    padding-top: 16px;
    border-radius: 14px;
  }
  .iconStyle {
    display: flex;
    align-items: center;
    height: 100%;
    margin-bottom: 20px;
  }
  .marketingMainDiv {
    align-items: flex-start;
  }
  .filterIcon {
    padding-left: 12px;
    padding-right: 12px;
    color: rgb(0, 0, 0);
    margin: 0px 12px;
    border-left: 1px solid #eee;
    border-right: 1px solid #eee;
  }
  .filterIcon1 {
    transform: rotate(90deg);
  }
  .iconPadding {
    display: flex;
    padding-right: 7px;
    @media (max-width: 281px) {
      display: none;
    }
  }
  .marketingText {
    font-weight: 400;
    text-transform: uppercase;
  }
  .hiddenCol {
    padding-bottom: 16px;
    display: flex;
    justify-content: flex-end;
    @media (min-width: 767px) {
      display: none;
    }
  }
  .isoTab {
    display: flex;
    justify-content: center;
    @media (max-width: 281px) {
      paxdding-right: 16px;
    }
  }
  .isoTab > div > div::before {
    position: unset;
  }
  .isoTab > div > div > div > div > div {
    padding: 0px;
  }
  .isoTab > div > div > div > div {
    padding-bottom: 20px;
  }
  .isoTab > div > div:nth-child(1) {
    margin: 0px;
  }
  .isoTab > div > div:nth-child(2) {
    display: none;
  }
  .usermanualstyle {
    display: flex;
    margin: 50px;
    background-color: 'green !important';
  }
  .test {
    background-color: 'red !important';
  }
  .isoAddNew {
    display: flex;
    justify-content: flex-end;
    @media (max-width: 767px) {
      display: none;
    }
  }
  .addNewUser {
    display: flex;
    justify-content: flex-end;
    cursor: pointer;
  }
  .chartStyle > div > svg > g > g:nth-child(2) {
    display: none;
  }
  .chartStyle > div > svg > g > g:nth-child(3) > text {
    dominant-baseline: text-before-edge;
  }

  .addNewBtn {
    padding-left: 4px;
    display: flex;
    justify-content: flex-end;
    cursor: pointer;
    gap: 5.98px;

    span {
      font-family: Inter;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      text-align: right;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #0086ff;
    }
     @media (max-width: 1440px) {
        gap: 2.98px;
        padding-left: 0px;
     } 
  }
  .secondCol {
    padding: 22px 12px !important;
    @media (max-width: 1023px) {
      padding: 22px 30px !important;
    }
  }
`;

export default WithDirection(ProductStyles);

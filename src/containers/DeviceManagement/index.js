/* eslint-disable react/button-has-type */
/* eslint-disable react/jsx-no-bind */
/* eslint-disable no-shadow */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable no-param-reassign */
/* eslint-disable global-require */
/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import React, { useState } from 'react';
import {
  Col,
  Row,
  Button,
  notification,
  Pagination,
  Typography,
  Input,
  Modal,
} from 'antd';
import { findIndex, isArray, isEmpty, isObject } from 'lodash';
import { useSelector } from 'react-redux';
import { SwapOutlined, DeleteOutlined } from '@ant-design/icons';
import theme from '@chill/config/theme/default';
import TableWrapper from '@chill/assets/styles/AntTables.styles';
import IntlMessages from '@chill/components/utility/intlMessages';
import getApiData from '@chill/lib/helpers/apiHelper';
import Popconfirms from '@chill/components/Feedback/Popconfirm';
import Notification from '@chill/components/Notification';
import editSVG from '@chill/assets/images/sidebarIcons/viewProduct.svg';
import SearchInput from '@chill/components/SearchInput';
import deleteIcon from '@chill/assets/images/delete-blue.png';
import rightArrow from '@chill/assets/images/right-arrow.png';
import threeDots from '@chill/assets/images/three-dots.png';
import threeDotsBlue from '@chill/assets/images/three-dots-blue.png';
import warningPng from '@chill/assets/images/warning.png';
import addIcon from '@chill/assets/images/add-square.png';
import { Popover } from 'react-tiny-popover';
import editActionDropdown from '@chill/assets/images/edit-action-dropdown.png';
import { MdArrowBackIosNew, MdArrowForwardIos } from 'react-icons/md';
import DeviceForm from './DeviceForm';
import DeviceStyles from './Device.styles';
import ConfirmDeleteModalStyles from '../DeviceData/ConfirmDeleteModal.styles';
/**
 *
 * @module DeviceManagement
 */

const { Text } = Typography;

const DeviceManagement = () => {
  const { language } = useSelector((state) => state.LanguageSwitcher);
  // const [actionMenu, setActionMenu] = React.useState(false);
  const [isIndex, setIsIndex] = React.useState(null);
  console.log(
    `hello ~ file: index.js:52 ~ DeviceManagement ~ isIndex:`,
    isIndex,
  );
  const [state, setState] = React.useState({
    initObj: {},
    visible: false,
  });
  const [deviceState, setDeviceState] = React.useState({
    devices: [],
    deviceLoad: false,
  });
  const [filter, setFilter] = React.useState({ pagination: {}, filters: {} });
  console.log(`hello ~ file: index.js:48 ~ DeviceManagement ~ filter:`, filter);
  const [filterMode, setFilterMode] = React.useState(false);
  console.log(
    `hello ~ file: index.js:49 ~ DeviceManagement ~ setFilterMode:`,
    setFilterMode,
  );
  const [sort, setSort] = React.useState(false);

  const [checkedArr, setCheckedArr] = React.useState({});

  const [isopenModal, setIsopenModal] = React.useState(false);
  const [gotoPage, setGotoPage] = React.useState(1);
  const [deleteLoader, setDeleteLoader] = React.useState(false);

  const userData = useSelector((state) => state.Auth.userData);
  const uData = isObject(userData) ? { ...userData } : {};
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);

  /**
   * Handles the visibility change of a popover.
   * @param {object} it - The item object containing the id.
   */
  const handleVisibleChange = (it) => {
    // Set the popover's open state based on the item's id
    setIsPopoverOpen(it?.id);
  };

  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  // this function for get devices
  /** this function for get devices
   * @function getDeviceList
   * @param {object} data sort || {}
   */
  async function getDeviceList(data = {}) {
    setDeviceState((p) => ({
      ...p,
      deviceLoad: true,
    }));
    if (sort) {
      data.sort = 'ASC';
    } else {
      data.sort = 'DESC';
    }

    try {
      const response = await getApiData('brand_devices/index', data, 'POST');
      if (response.success && isArray(response.data)) {
        setDeviceState((preState) => ({
          ...preState,
          devices: isArray(response.data) ? response.data : [],
          deviceLoad: false,
        }));
        const pagination =
          filter && filter.pagination ? { ...filter.pagination } : {};
        pagination.total = response.total_count || 0;
        pagination.pageSize = response.per_page || 10;
        pagination.current = response.page || 1;
        setFilter((f) => ({ ...f, pagination }));
      } else {
        setDeviceState((preState) => ({
          ...preState,
          deviceLoad: false,
        }));
      }
    } catch (err) {
      setDeviceState((preState) => ({
        ...preState,
        deviceLoad: false,
      }));
    }
  }

  React.useEffect(() => {
    const page =
      filter && filter.pagination && filter.pagination.current
        ? filter.pagination.current
        : 1;
    const flt = filter ? { ...filter } : {};
    getDeviceList({ page, ...flt.filters });
  }, [sort]);

  React.useEffect(() => {
    if (!filterMode) {
      getDeviceList({});
    }
  }, [filterMode]);

  // this function filters data and get updated list of device
  /** this function filters data and get updated list of device
   * @function fetchDataFilters
   * @param {object} data page
   */
  function fetchDataFilters() {
    const page =
      filter && filter.pagination && filter.pagination.current
        ? filter.pagination.current
        : 1;
    const filters = filter && filter.filters ? filter.filters : {};
    getDeviceList({ page, ...filters });
  }

  // this function for delete devices
  /** this function for delete devices, Only admin can delete
   * @function deleteDevice
   * @param {object} data device_id
   */
  async function deleteDevice() {
    const data = {
      device_id:
        isObject(state) &&
        isObject(state.initObj) &&
        !isEmpty(state.initObj) &&
        state.initObj.id,
    };
    const deviceAry = isArray(deviceState.devices) ? deviceState.devices : [];
    setDeviceState({
      deviceLoad: true,
    });
    try {
      const response = await getApiData(
        'brand_devices/remove-device',
        data,
        'POST',
      );
      let msgTitle = 'error';
      if (response.success) {
        msgTitle = 'success';
        fetchDataFilters();
      } else {
        setDeviceState((pre) => ({
          ...pre,
          devices: deviceAry,
          deviceLoad: false,
        }));
      }
      Notification(msgTitle, response.message);
    } catch (error) {
      console.log('error ===', error);
      setDeviceState((pre) => ({
        ...pre,
        devices: deviceAry,
        deviceLoad: false,
      }));
    }
  }

  // fun. for visible PopOver
  // function handleVisible() {
  //   setActionMenu((visiblePre) => !visiblePre);
  // }

  // this function for handle pagination
  function onChange(pagination) {
    // const pager = filter && filter.pagination ? { ...filter.pagination } : {};
    // pager.current = pagination.current;
    // setFilter((f) => ({ ...f, pagination: pager }));
    // getDeviceList({
    //   page: pagination.current,
    //   ...filter.filters,
    // });
    const pager = filter && filter.pagination ? { ...filter.pagination } : {};
    pager.current = pagination;
    setFilter((f) => ({ ...f, pagination: { ...pager } }));
    getDeviceList({
      page: pagination,
      ...filter.filters,
    });
    setSort(false);
  }

  // this function for update new device data
  /** this function for update new device data, Only Admin can edit
   * @function updateData
   * @param {object} data id
   */
  function updateData(data) {
    const deviceAry = isArray(deviceState.devices)
      ? [...deviceState.devices]
      : [];
    const edit = !isEmpty(state.initObj) && data.id;
    const dataIndex = findIndex(deviceAry, { id: data.id });
    if (edit && dataIndex > -1) {
      deviceAry[dataIndex] = data;
    } else {
      getDeviceList();
    }
    setDeviceState({
      devices: deviceAry,
    });
    setState({ initObj: {}, visible: false });
  }

  const textStyle = {
    fontFamily: 'Inter',
    fontSize: '14px',
    fontWeight: 400,
    lineHeight: '16.94px',
    textAlign: 'left',
    textUnderlinePosition: 'from-font',
    textDecorationSkipInk: 'none',
    color: '#96AAC1',
    span: {
      fontFamily: 'Inter',
    },
  };

  // Popover content or list
  const content = (item) => (
    <DeviceStyles>
      <div
        className="popMainDiv"
        style={{
          display: 'flex',
          flexDirection: 'column',
          padding: '0px 5px',
        }}
      >
        <div
          className="isCustomDropDown"
          onClick={() => {
            setState({ ...state, visible: true, view: true, initObj: item });
            setIsPopoverOpen(null);
            setIsIndex(null);
          }}
          style={{
            borderBottom: '0.5px solid #BBCCDE',
            padding: '10px 5px',
          }}
        >
          <img
            className="iconImg"
            src={editSVG}
            style={{ width: 19, height: 19, marginRight: 30, color: '#96AAC1' }}
            alt="noIcon"
          />
          <Text style={textStyle}>View Device</Text>
        </div>
        {uData.user_type === 'chillbaby_admin' ? (
          <>
            <div
              role="button"
              className="isCustomDropDown"
              onKeyPress=""
              tabIndex="-1"
              onClick={() => {
                setState({ ...state, visible: true, initObj: item });
                setIsIndex(null);
                setIsPopoverOpen(null);
              }}
              style={{
                borderBottom: '0.5px solid #BBCCDE',
                padding: '10px 5px',
              }}
            >
              <img
                src={editActionDropdown}
                alt=""
                style={{ paddingRight: 30 }}
              />
              <Text style={{ ...textStyle, color: '#007AFF' }}>
                Edit Device
              </Text>
            </div>
            <div
              className="isCustomDropDown"
              style={{
                padding: '10px 5px',
              }}
            >
              <Popconfirms
                title={<IntlMessages id="Sure to delete?" />}
                okText={<IntlMessages id="DELETE" />}
                cancelText={<IntlMessages id="No" />}
                onConfirm={() => {
                  deleteDevice();
                  setIsPopoverOpen(null);
                  setIsIndex(null);
                }}
                onCancel={null}
              >
                <DeleteOutlined
                  style={{ paddingRight: 30, fontSize: 19, color: '#96AAC1' }}
                />
                <IntlMessages id="device.delete" style={textStyle} />
              </Popconfirms>
            </div>
          </>
        ) : null}
      </div>
    </DeviceStyles>
  );

  // set item id in state

  const columns = [
    {
      title: <IntlMessages id="No" />,
      dataIndex: 'id',
      rowKey: 'id',
      width: 10,
      className: 'fullname-cell',
      render: (text, data, index) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="No" />
            </span>
            <span
              className="mobile-lbl-val"
              style={{
                fontFamily: 'Inter',
                fontSize: 16,
                fontWeight: 400,
                lineHeight: '24px',
                textAlign: 'left',
                textUnderlinePosition: 'from-font',
                textDecorationSkipInk: 'none',
                color: '#242731',
              }}
            >
              {index + 1 || '-'}
            </span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="device.nametitle" />,
      dataIndex: 'device_name',
      rowKey: 'device_name',
      width: 180,
      className: 'fullname-cell',
      render: (text, data) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="device.nametitle" />
            </span>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <img
                src={data.device_image}
                style={{
                  width: 30,
                  height: 30,
                  backgroundColor: '#eee',
                  marginRight: 12,
                  borderRadius: 30,
                }}
                alt=""
              />
              <span className="mobile-lbl-val">{text || '-'}</span>
            </div>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="device.bluetoothname" />,
      dataIndex: 'device_bluetooth_name',
      rowKey: 'device_bluetooth_name',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="device.bluetoothname" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="device.description" />,
      dataIndex: 'device_description',
      rowKey: 'device_description',
      width: 280,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="device.description" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="deviceData.latestFirmwareVrsn" />,
      dataIndex: 'device_latest_firmwareVrsn',
      rowKey: 'device_latest_firmwareVrsn',
      width: 280,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="deviceData.latestFirmwareVrsn" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="deviceData.userFirmware" />,
      dataIndex: 'device_userFirmware',
      rowKey: 'device_userFirmware',
      width: 280,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="deviceData.userFirmware" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="device.userCount" />,
      dataIndex: 'userCount',
      rowKey: 'userCount',
      width: 60,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="device.userCount" />
            </span>
            <span className="mobile-lbl-val">{text || '0'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Action" />,
      dataIndex: 'action',
      rowKey: 'action',
      className: 'fullname-cell',
      width: 50,
      render: (text, item) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Action" />
            </span>
            <span className="mobile-lbl-val">
              <Popover
                isOpen={isPopoverOpen === item.id}
                positions={['bottom', 'right']} // Preferred positions (adjust as needed)
                onClickOutside={() => setIsPopoverOpen(false)}
                content={
                  <div
                    style={{
                      border: '1px solid #BBCCDE66',
                      boxShadow: '0.2px 2px 7px 0px #0000000F',
                      marginRight: '70px',
                      width: 258.93,
                      borderRadius: 13,
                      backgroundColor: '#fff',
                      padding: '13px',
                    }}
                  >
                    {content(item)}
                  </div>
                }
              >
                <button
                  onClick={() => {
                    handleVisibleChange(item);
                  }}
                  style={{
                    backgroundColor: 'transparent',
                    border: 'none',
                    padding: 0,
                    cursor: 'pointer',
                  }}
                >
                  <img
                    src={isPopoverOpen === item.id ? threeDotsBlue : threeDots}
                    alt="actionIcon"
                    style={{
                      fontSize: 16,
                    }}
                  />
                </button>
              </Popover>
            </span>
          </div>
        );
      },
    },
  ];

  const brandName = {
    title: <IntlMessages id="th.brandname" />,
    dataIndex: 'brand_name',
    rowKey: 'brand_name',
    width: 180,
    className: 'fullname-cell',
    render: (text) => {
      return (
        <div>
          <span className="label">
            <IntlMessages id="th.brandname" />
          </span>
          <span className="mobile-lbl-val">{text || '-'}</span>
        </div>
      );
    },
  };

  if (uData.user_type === 'chillbaby_admin') {
    columns.splice(4, 0, brandName);
  }

  // Add device View
  const addNew = () => {
    return (
      <DeviceStyles style={{ width: '100%' }}>
        <div className="iconStyle marketingMainDiv">
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '5px',
              justifyContent: 'space-between',
              paddingLeft: '20px',
              width: '100%',
              marginTop: '13px',
            }}
          >
            <SwapOutlined
              className="filterIcon1"
              style={{
                fontSize: 24,
                color: sort ? theme.colors.primaryColor : '#000',
              }}
              onClick={() => {
                setSort(!sort);
              }}
              size={24}
            />
            {/* <FilterOutlined
                className="filterIcon"
                style={{
                  color: filterMode ? theme.colors.primaryColor : '#000',
                }}
                onClick={() => {
                  setFilterMode((pre) => !pre);
                  setFilter({ filters: {} });
                }}
              /> */}
            {uData.user_type === 'chillbaby_admin' ? (
              <div
                className="addNewBtn"
                style={{
                  color: theme.colors.primaryColor,
                  marginTop: '5px',
                }}
                onClick={() => setState({ visible: true })}
              >
                <IntlMessages id="device.addDevice" />
                <img src={addIcon} alt="addIcon" />
              </div>
            ) : null}
          </div>
        </div>
      </DeviceStyles>
    );
  };

  // this function renders search view
  function renderSearchBar() {
    return (
      <div className="inputSearch">
        <Row gutter={10}>
          <Col
            xs={24}
            sm={24}
            md={uData?.user_type === 'chillbaby_admin' ? 18 : 23}
            lg={uData?.user_type === 'chillbaby_admin' ? 19 : 23}
            xl={uData?.user_type === 'chillbaby_admin' ? 20 : 23}
            xxl={uData?.user_type === 'chillbaby_admin' ? 21 : 23}
          >
            <SearchInput
              onChange={(e) => {
                const val = e && e.target ? e.target.value : '';
                const flt = filter ? { ...filter } : {};
                flt.filters.search_value = val;
                setFilter(flt);
                getDeviceList(flt.filters);
              }}
              value={filter.search_value}
              allowClear
              placeholder={messageArray['common.Search']}
            />
          </Col>
          <Col
            xs={24}
            sm={24}
            md={uData?.user_type === 'chillbaby_admin' ? 6 : 1}
            lg={uData?.user_type === 'chillbaby_admin' ? 5 : 1}
            xl={uData?.user_type === 'chillbaby_admin' ? 4 : 1}
            xxl={uData?.user_type === 'chillbaby_admin' ? 3 : 1}
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              width: '100%',
            }}
          >
            {addNew()}
          </Col>
        </Row>
      </div>
    );
  }
  const { initObj, visible, view } = state;

  /**
   * Configuration object for row selection in the table
   * @type {Object}
   * @property {Function} onChange - Callback function that is triggered when row selection changes
   */
  const rowSelection = {
    /**
     * Callback function for handling changes in row selection
     * @param {Array} selectedRowKeys - Array of selected row keys
     * @param {Array} selectedRows - Array of selected row data
     */
    onChange: (selectedRowKeys, selectedRows) => {
      // Log selected row keys and data
      console.log(
        `selectedRowKeys: ${selectedRowKeys}`,
        'selectedRows: ',
        selectedRows,
      );

      // Map selected rows to their IDs and join them into a string
      const arr = selectedRows?.map((e) => e?.id).join(',');

      // Log the joined string of IDs
      console.log(`hello ~ file: index.js:640 ~ DeviceManagement ~ arr:`, arr);

      // Update state with the selected row keys and data
      setCheckedArr((p) => ({
        ...p,
        rowKeys: arr,
        rowarr: selectedRows,
      }));
    },
  };

  /**
   * Function to close the confirm delete modal
   * @function onCloseDeleteModal
   */
  const onCloseDeleteModal = () => {
    // set the state of the modal to false
    setIsopenModal(false);
  };

  /**
   * Function to delete multiple rows from the device list table
   * @function deleteRows
   */
  async function deleteRows() {
    // check if the user has selected any rows to delete
    if (isEmpty(checkedArr)) {
      // if not, show an info notification
      notification.open({
        type: 'info',
        message: 'Please check which rows you can delete.',
      });
    } else {
      // if yes, show a confirm modal
      try {
        // set the delete loader to true
        setDeleteLoader(true);
        // call the api to delete the selected rows
        const response = await getApiData(
          'brand_devices/multiple-devices-remove',
          {
            device_id: checkedArr?.rowKeys,
          },
          'POST',
        );
        // if the response is successful
        if (response.success) {
          // call the getDeviceList function to refresh the table
          getDeviceList();
          // close the confirm modal
          setIsopenModal(false);
          // show a success notification
          notification.open({
            type: 'success',
            message: response?.message,
          });
        } else {
          // if the response is not successful, show an error notification
          notification.open({
            type: 'error',
            message: 'Something went wrong',
          });
        }
      } catch (err) {
        // if there is an error, show an error notification
        notification.open({
          type: 'error',
          message: 'Something went wrong',
        });
      } finally {
        // set the delete loader to false
        setDeleteLoader(false);
      }
    }
  }

  return (
    <DeviceStyles>
      <Row className="main-content" gutter={[0, 5]}>
        <Col xs={24} className="title-col">
          <div className="titleText">
            <IntlMessages id="sidebar.mainDevice" />
          </div>
        </Col>

        {/* <Col sm={16} xs={16} className="hiddenCol">
          {addNew()}
        </Col> */}
        <Col
          xs={24}
          className="search-col"
          style={{
            marginBottom: '13px',
          }}
        >
          {renderSearchBar()}
        </Col>
        <Col xs={24} className="table-col">
          <TableWrapper
            loading={deviceState.deviceLoad}
            rowKey={(record) => record.id}
            dataSource={deviceState.devices}
            onChange={onChange}
            columns={columns}
            className="invoiceListTable"
            // pagination={filter.pagination || {}}
            pagination={false}
            // pagination={{ position: ['none'] }}
            showSorterTooltip={false}
            rowSelection={{
              type: 'checkbox',
              ...rowSelection,
            }}
          />
          <div className="table-pagnation">
            <Button
              type="text"
              className="removeRowBtn"
              onClick={() => {
                if (isEmpty(checkedArr)) {
                  notification.open({
                    type: 'info',
                    message: 'Please check which rows you can delete.',
                  });
                } else {
                  setIsopenModal(true);
                }
              }}
            >
              Remove Row(s) <img src={deleteIcon} alt="delete" />
            </Button>
            <Pagination
              defaultCurrent={filter?.pagination?.current}
              current={filter?.pagination?.current}
              onChange={(pgnation) => {
                onChange(pgnation);
              }}
              pageSize={filter?.pagination?.pageSize || 25}
              total={filter?.pagination?.total}
              showSizeChanger={false}
              showLessItems
              className="tabel-custom-pagination"
              nextIcon={<MdArrowForwardIos />}
              prevIcon={<MdArrowBackIosNew />}
              // showLessItems={wWidth <= 425}
            />
            <div className="gotoPagediv">
              <Text className="goToText">Go to page</Text>
              <Input
                type="number"
                min={1}
                max={filter?.pagination?.total}
                defaultValue={filter?.pagination?.current}
                onChange={(e) => {
                  const val = e && e.target ? Number(e.target.value) : '';
                  setGotoPage(val);
                }}
                className="gotoInput"
              />
              <Button
                type="text"
                onClick={() => {
                  const pager =
                    filter && filter.pagination ? { ...filter.pagination } : {};
                  pager.current = gotoPage;
                  setFilter((f) => ({ ...f, pagination: { ...pager } }));
                  getDeviceList({
                    page: gotoPage,
                    ...filter.filters,
                  });
                }}
                style={{
                  paddingLeft: '12px',
                }}
              >
                <img src={rightArrow} alt="arrow" />
              </Button>
            </div>
          </div>
        </Col>
      </Row>
      <DeviceForm
        initialValues={initObj}
        visible={visible}
        view={view}
        onClose={(type, data) => {
          if (type === 'success') {
            updateData(data);
          } else {
            setState({ initObj: {}, visible: false });
          }
        }}
      />
      <Modal
        open={isopenModal}
        width={740}
        onCancel={() => {
          onCloseDeleteModal();
        }}
        footer={false}
        closable={false}
        className="confirmDeleteModal"
      >
        <ConfirmDeleteModalStyles>
          <div className="deleteModal">
            {/* Warning image and text */}
            <div className="warningImgdiv">
              <img src={warningPng} alt="warningPng" />
            </div>
            <div className="warningDescDiv">
              {/* Warning description text */}
              <div className="warningDiv">
                <Text className="warningText">Warning!</Text>
                <Text className="warningDesc">
                  You’re about to delete {checkedArr?.rowarr?.length} rows, this
                  may cause loss of important data!
                </Text>
              </div>
              {/* Buttons for cancel and confirm deletion */}
              <div className="buttonDiv">
                {/* Cancel button */}
                <Button
                  className="cancelBtn"
                  onClick={() => {
                    onCloseDeleteModal();
                  }}
                >
                  Cancel
                </Button>
                {/* Confirm deletion button */}
                <Button
                  className="deleteBtn"
                  loading={deleteLoader}
                  onClick={() => deleteRows()}
                >
                  Confirm Deletion
                </Button>
              </div>
            </div>
          </div>
        </ConfirmDeleteModalStyles>
      </Modal>
    </DeviceStyles>
  );
};

export default DeviceManagement;

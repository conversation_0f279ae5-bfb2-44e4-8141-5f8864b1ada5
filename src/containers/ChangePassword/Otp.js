/* eslint-disable react/no-array-index-key */
/* eslint-disable react/button-has-type */
/* eslint-disable react/no-unescaped-entities */
import React, { useEffect, useRef, useState } from 'react';
import { Form, Input, Button, Row, Col, notification } from 'antd';
import styled from 'styled-components';
import getApiData from '@chill/lib/helpers/apiHelper';

// Wrapper for centering the OTP form vertically and horizontally
const Wrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 57vh;
  background-color: #fff;
`;

// Container for the form with padding and responsive styling
const FormContainer = styled.div`
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  max-width: 430px;
  width: 100%;
`;

// Styled Title for the form
const Title = styled.h4`
  margin-bottom: 19px;
  color: #1e1e1e;
  font-family: Inter;
  font-size: 20px;
  font-weight: 600;
  line-height: 20px;
  letter-spacing: -0.5px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
`;

// Subtitle for the form to give instructions
const SubText = styled.p`
  color: #899db3;
  margin-bottom: 31px;

  font-family: Inter;
  font-size: 15px;
  font-weight: 500;
  // line-height: 15px;
  letter-spacing: -0.5px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
`;

// Styled container for OTP input fields
const OTPContainer = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;

  .opt-input {
    width: 56px;
    height: 56px;
    border-radius: 12px;
    text-align: center;
    font-size: 24px;
    font-weight: 500;
    border: 2px solid #b0bcc8;
  }
  .opt-input:focus {
    width: 56px;
    height: 56px;
    border: 2px solid #007aff;
  }
`;

// Resend link styling
const ResendLink = styled.p`
  margin-top: 36px;
  color: #899db3;

  font-family: Inter;
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  letter-spacing: -0.5px;
  text-align: center;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;

  .resend-btn {
    color: #04b8ff;
    padding: 0px;
    cursor: pointer;
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;

    span {
      font-family: Inter;
      font-size: 16px;
      font-weight: 600;
      line-height: 20px;
      letter-spacing: -0.5px;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      text-decoration: underline;
    }
    span:hover {
      color: #04b8ff;
    }
  }
  .resend-btn:hover {
    color: #04b8ff;
  }
`;

const Otp = (props) => {
  const { changePwdObj, onNextStep } = props;
  // State to manage OTP inputs
  const [otp, setOtp] = useState(Array(5).fill('')); // Initialize an array of 5 empty strings

  const [loader, setLoader] = useState(false);

  const inputRefs = useRef(otp.map(() => React.createRef())); // Create refs for each input field
  const [finalSendOtp, setFinalSendOtp] = useState(''); // State to store the final OTP after all fields are filled

  // Function to handle form submission
  const handleFinish = async (values) => {
    console.log('OTP Submitted:', values);

    try {
      setLoader(true);

      const res = await getApiData(
        'verify-otp',
        {
          user_email: changePwdObj?.email,
          otp_code: finalSendOtp,
        },
        'POST',
      );
      if (res.success) {
        notification.open({
          type: 'success',
          message: res.message,
        });
        onNextStep();
        // handleSubmit(data);
      } else {
        notification.error({
          message: res.message,
        });
      }
      // setSubmitting(false);
    } catch (err) {
      notification.error({
        message: 'Something went wrong. Try later',
      });
      // setSubmitting(false);
    } finally {
      setLoader(false);
    }
  };

  // Function to handle input changes in OTP fields
  const handleChange = (index, value) => {
    const newOtp = [...otp]; // Create a copy of the current OTP state
    newOtp[index] = value.trim().substring(0, 1); // Update the current index with the entered value (1 digit max)

    setOtp(newOtp); // Update the OTP state
    const finalOtp = newOtp.join(''); // Combine the array into a single string

    if (finalOtp?.length === 5) {
      // If all 5 fields are filled
      setFinalSendOtp(finalOtp); // Update the final OTP state
      // otpSuccessFull(finalOtp); // Uncomment and implement this function if required
    }

    // Automatically focus on the next input field if the current value is valid
    if (value.trim() !== '' && index < inputRefs.current.length - 1) {
      inputRefs.current[index + 1].current.focus();
    }
  };

  // Handle "Backspace" key to move focus to the previous field
  const handleKeyDown = (e, index) => {
    if (e.key === 'Backspace' && index > 0 && otp[index] === '') {
      e.preventDefault(); // Prevent default backspace behavior
      inputRefs.current[index - 1].current.focus(); // Focus the previous field
    }
  };

  // Focus the first input field when the component mounts
  useEffect(() => {
    inputRefs.current[0].current.focus();
  }, []);

  return (
    <Wrapper>
      <FormContainer>
        {/* Form Title */}
        <Title>Check your email</Title>
        {/* Instructions for the user */}
        <SubText>
          We sent a reset link to{' '}
          <strong
            style={{
              color: '#545454',
              fontFamily: 'Inter',
              fontSize: 15,
              fontWeight: 500,
              letterSpacing: '-0.5px',
              textAlign: 'left',
              textUnderlinePosition: 'from-font',
              textDecorationSkipInk: 'none',
            }}
          >
            {changePwdObj?.email}
          </strong>
          <br />
          Enter the 5-digit code sent in the email
        </SubText>
        <Form layout="vertical" onFinish={handleFinish}>
          {/* OTP Input Fields */}
          <OTPContainer>
            {otp.map((value, index) => (
              <Form.Item
                key={`otp${index}`}
                rules={[
                  { required: true, message: 'This field is required.' },
                  { pattern: /^[0-9]$/, message: 'Must be a single digit.' },
                ]}
              >
                <Input
                  ref={inputRefs.current[index]} // Associate the current ref
                  maxLength={1} // Limit input to 1 character
                  value={value} // Bind the input value to the OTP state
                  onChange={(e) => handleChange(index, e.target.value)} // Handle changes
                  onKeyDown={(e) => handleKeyDown(e, index)} // Handle key presses
                  className="opt-input" // Apply custom styling
                  type="number" // Only allow numbers
                />
              </Form.Item>
            ))}
          </OTPContainer>
          {/* Submit Button */}
          <Row gutter={[0, 16]}>
            <Col span={24}>
              <Button
                loading={loader}
                style={{
                  width: '100%',
                  borderRadius: '12px',
                  fontWeight: 700,
                  fontSize: '16px',
                  fontFamily: 'Inter',
                  backgroundColor:
                    finalSendOtp?.trim() === '' ? '#95AAC1' : '#007AFF', // Disable button if OTP is incomplete
                  height: '56px',
                  borderColor: 'transparent',
                  color: '#fff',
                }}
                htmlType="submit"
              >
                <span
                  style={{
                    fontFamily: 'Inter',
                    fontSize: '16px',
                    fontWeight: 700,
                    lineHeight: '20px',
                    letterSpacing: '-0.5px',
                    textAlign: 'left',
                    textUnderlinePosition: 'from-font',
                    textDecorationSkipInk: 'none',
                  }}
                >
                  Verify Code
                </span>
              </Button>
            </Col>
          </Row>
        </Form>
        {/* Resend Email Link */}
        <ResendLink>
          Haven't received the code yet?{' '}
          <Button className="resend-btn" type="text">
            Resend email
          </Button>
        </ResendLink>
      </FormContainer>
    </Wrapper>
  );
};

export default Otp;

import { Col, Row, Typography } from 'antd';
import React from 'react';
import styled from 'styled-components';
import successIcon from '@chill/assets/images/success-icon.png';

const Wrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 57vh;
  background-color: #fff;
  .successRow {
    width: 100%;
    .successCol {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      gap: 10px;
      text-align: center;
      .successText {
        margin-top: 20px;
        font-size: 24px;
        font-weight: 500;
        color: #04b8ff;
        font-family: 'Inter';
        line-height: 29.05px;
        max-width: 375px;
      }
      .successDesc {
        margin-top: 5px;
        font-size: 16px;
        font-weight: 400;
        color: #575c64;
        line-height: 19.36px;
        font-family: 'Inter';
        max-width: 328px;
      }
    }
  }
`;

const { Text } = Typography;

/**
 * PasswordChangeSuccess
 *
 * This component is used to display the success message after the
 * user changes their password.
 *
 * @returns {JSX.Element} The JSX element to display the success message
 */
function PasswordChangeSuccess() {
  return (
    <Wrapper>
      <Row className="successRow">
        <Col className="successCol" xs={24}>
          <img src={successIcon} alt="Success icon" />
          {/* Success message */}
          <Text xs={24} className="successText">
            Password Changed Successfully{' '}
          </Text>
          {/* Success description */}
          <Text xs={24} className="successDesc">
            Your password has been updated to the requested new one.
          </Text>
        </Col>
      </Row>
    </Wrapper>
  );
}

export default PasswordChangeSuccess;

/* eslint-disable react/jsx-no-bind */
/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
import { Row, Col, Form, Button, notification } from 'antd';
import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import getApiData from '@chill/lib/helpers/apiHelper';
import IntlMessages from '@chill/components/utility/intlMessages';
import { InputPassword } from '@chill/components/uielements/input';
import LineeyeIcon from '@chill/assets/images/line-eye.svg';
import { AiOutlineEye } from 'react-icons/ai';
import ChangePasswordStyle from './ChangePassword.styles';
import EmailForm from './Email';
import Otp from './Otp';
import PasswordChangeSuccess from './PasswordChangeSuccess';

/**
 *
 * @module ChangePassword
 */

export default function ChangePassword() {
  const [form] = Form.useForm();
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);
  const [loader, setLoader] = useState(false);
  const [pageView, setPageView] = useState(1);
  const [changePwdObj, setChangePwdObj] = useState({});
  console.log(
    `hello ~ file: index.js:25 ~ ChangePassword ~ setPageView:`,
    setPageView,
    changePwdObj,
  );

  /** this function use for change old password to new password
   * @function onFinish
   * @param {object} data current_password, new_password, confirmPassword
   */

  async function onFinish(data) {
    try {
      setLoader(true);
      const res = await getApiData('user/change-password', data, 'POST');
      if (res.success) {
        setPageView(4);

        setTimeout(() => {
          setPageView(1);
        }, 2000);
        notification.open({
          type: 'success',
          message: res.message,
        });
        // handleSubmit(data);
      } else {
        notification.error({
          message: res.message,
        });
      }
      // setSubmitting(false);
    } catch (err) {
      // setSubmitting(false);
      notification.error({
        message: messageArray['err.wenWrong'],
      });
    } finally {
      setLoader(false);
    }
    form.resetFields();
  }
  const passReg = new RegExp(
    '^(((?=.*[a-z])(?=.*[A-Z]))((?=.*[a-z])(?=.*[0-9]))(?=.*[!@#$%^&*])((?=.*[A-Z])(?=.*[0-9])))(?=.{8,})',
  );

  const handlePageView = () => {
    if (pageView === 1) {
      return (
        <EmailForm
          setChangePwdObj={setChangePwdObj}
          onNextStep={() => {
            setPageView(2);
          }}
        />
      );
    }
    if (pageView === 2) {
      return (
        <Otp
          setChangePwdObj={setChangePwdObj}
          changePwdObj={changePwdObj}
          onNextStep={() => {
            setPageView(3);
          }}
        />
      );
    }
    if (pageView === 3) {
      return (
        <ChangePasswordStyle>
          <Row className="isoRightColRow">
            <Col xs={24} className="isoRightHeader">
              <text className="isoRightHeaderTitle">
                <IntlMessages id="Set a new password" />
              </text>
              <text className="isoRightHeaderdesc">
                <IntlMessages id="Create a new password. Ensure it differs from the previous one for security" />
              </text>
            </Col>
            <Col xs={24} className="form-main-col">
              <div className="inner-div">
                <Form layout="vertical" form={form} onFinish={onFinish}>
                  <Form.Item
                    label={<IntlMessages id="Password" />}
                    name="current_password"
                    rules={[
                      {
                        required: true,
                        message: <IntlMessages id="req.password" />,
                      },
                    ]}
                  >
                    <InputPassword
                      placeholder="Enter your current password"
                      // type="large"
                      style={{
                        // Styling for the input field
                        height: 56,
                        borderRadius: '12px',
                        border: '2px solid #D2D9E1',
                        // borderWidth: '2px 0px 0px 0px',
                      }}
                      inputStyle={{
                        color: '#B0BCC8 !important',
                        fontSize: '16px',
                        fontFamily: 'Inter',
                        fontWeight: 800,
                      }}
                      iconRender={(visible) =>
                        visible ? (
                          <AiOutlineEye
                            style={{
                              width: '22px',
                              height: '22px',
                              color: '#B0BCC8',
                            }}
                          /> // Custom hidden eye icon
                        ) : (
                          <img
                            src={LineeyeIcon}
                            alt="eye"
                            style={{
                              width: '20.01px',
                              height: '18.33px',
                              color: '#B0BCC8',
                            }}
                          /> // Custom visible eye icon
                        )
                      }
                    />
                  </Form.Item>
                  <Form.Item
                    className="formLabel"
                    label={<IntlMessages id="New Password" />}
                    name="new_password"
                    rules={[
                      {
                        required: true,
                        message: <IntlMessages id="req.newPassword" />,
                      },
                      () => ({
                        validator(rule, value) {
                          const errorMsg = <IntlMessages id="err.password" />;
                          if (value) {
                            if (passReg.test(value)) {
                              return Promise.resolve();
                            }
                            return Promise.reject(errorMsg);
                          }
                          return Promise.resolve();
                        },
                      }),
                    ]}
                  >
                    <InputPassword
                      placeholder="Enter your new password"
                      // type="large"
                      style={{
                        // Styling for the input field
                        height: 56,
                        borderRadius: '12px',
                        border: '2px solid #D2D9E1',
                        // borderWidth: '2px 0px 0px 0px',
                      }}
                      inputStyle={{
                        color: '#B0BCC8 !important',
                        fontSize: '16px',
                        fontFamily: 'Inter',
                        fontWeight: 800,
                      }}
                      iconRender={(visible) =>
                        visible ? (
                          <AiOutlineEye
                            style={{
                              width: '22px',
                              height: '22px',
                              color: '#B0BCC8',
                            }}
                          /> // Custom hidden eye icon
                        ) : (
                          <img
                            src={LineeyeIcon}
                            alt="eye"
                            style={{
                              width: '20.01px',
                              height: '18.33px',
                              color: '#B0BCC8',
                            }}
                          /> // Custom visible eye icon
                        )
                      }
                    />
                  </Form.Item>
                  <Form.Item
                    className="formLabel"
                    label="Confirm Password"
                    name="confirmPassword"
                    rules={[
                      {
                        required: true,
                        message: <IntlMessages id="req.confirmPassword" />,
                      },
                      ({ getFieldValue }) => ({
                        validator(rule, value) {
                          if (
                            !value ||
                            getFieldValue('new_password') === value
                          ) {
                            return Promise.resolve();
                          }
                          return Promise.reject(
                            <IntlMessages id="err.bothPssd" />,
                          );
                        },
                      }),
                    ]}
                  >
                    <InputPassword
                      style={{
                        // Styling for the input field
                        height: 56,
                        borderRadius: '12px',
                        border: '2px solid #D2D9E1',
                        // borderWidth: '2px 0px 0px 0px',
                      }}
                      inputStyle={{
                        color: '#B0BCC8 !important',
                        fontSize: '16px',
                        fontFamily: 'Inter',
                        fontWeight: 800,
                      }}
                      placeholder="Re-enter password"
                      type="large"
                      iconRender={(visible) =>
                        visible ? (
                          <AiOutlineEye
                            style={{
                              width: '22px',
                              height: '22px',
                              color: '#B0BCC8',
                            }}
                          /> // Custom hidden eye icon
                        ) : (
                          <img
                            src={LineeyeIcon}
                            alt="eye"
                            style={{
                              width: '20.01px',
                              height: '18.33px',
                              color: '#B0BCC8',
                            }}
                          /> // Custom visible eye icon
                        )
                      }
                    />
                  </Form.Item>
                  <Row className="isoRightColRow" gutter={12}>
                    <Col
                      className="isoRightFormBtn"
                      xs={24}
                      sm={24}
                      md={24}
                      lg={24}
                      xl={24}
                    >
                      <Form.Item>
                        <Button
                          loading={loader}
                          // className="changeBtn"
                          style={{
                            // Styling for the button

                            width: '100%',
                            borderRadius: '12px',
                            fontWeight: 400,
                            fontSize: '16px',
                            fontFamily: 'Inter',
                            backgroundColor: '#95AAC1',
                            height: '56px',
                            borderColor: 'transparent',
                            color: '#fff',
                          }}
                          type="primary"
                          htmlType="submit"
                        >
                          <span
                            style={{
                              fontFamily: 'Inter',
                              lineHeight: '19.36px',
                            }}
                          >
                            Save Changes
                          </span>
                        </Button>
                      </Form.Item>
                    </Col>
                  </Row>
                </Form>
              </div>
            </Col>
          </Row>
        </ChangePasswordStyle>
      );
    }
    if (pageView === 4) {
      return <PasswordChangeSuccess />;
    }
    return null;
  };
  return (
    <div style={{ backgroundColor: '#fff', height: '100%' }}>
      {handlePageView()}
    </div>
  );
}

// export default ChangePassword;

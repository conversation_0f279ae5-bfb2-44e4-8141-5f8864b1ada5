import React, { useState } from 'react';
import { Form, Input, Button, Row, Col, notification } from 'antd';
import styled from 'styled-components';
import getApiData from '@chill/lib/helpers/apiHelper';

const Wrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 57vh;
  background-color: #fff;
`;

const FormContainer = styled.div`
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  //   box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  //   text-align: center;
  max-width: 430px;
  width: 100%;

  .ant-input::placeholder {
    color: #b0bcc8;
    font-family: Inter;
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: -0.5px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
  }

  .ant-form-item-label {
    label {
      color: #2a2a2a;
      font-family: Inter;
      font-size: 16px;
      font-weight: 600;
      line-height: 20px;
      text-transform: none;
      letter-spacing: -0.5px;
      span {
        text-transform: none;
      }
    }
  }
`;

const Title = styled.h4`
  margin-bottom: 30px;
  color: #899db3;

  font-family: Inter;
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  letter-spacing: -0.5px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
`;

/**
 * @description EmailForm component
 * @returns {React.ReactElement} EmailForm element
 */
const EmailForm = (props) => {
  const { setChangePwdObj = () => {}, onNextStep = () => {} } = props;
  /**
   * @description Input value of the email field
   * @type {string}
   */
  const [inputVal, setInputVal] = useState('');
  const [loader, setLoader] = useState(false);

  /**
   * @description Function to be called when the form is submitted
   * @param {object} values - The values of the form fields
   */
  const handleSubmit = async (values) => {
    console.log('Form Values:', values);
    try {
      setLoader(true);
      const res = await getApiData(
        'send-verification-otp',
        {
          user_email: values?.email,
        },
        'POST',
      );
      if (res.success) {
        setChangePwdObj((prev) => ({ ...prev, email: values?.email }));
        notification.open({
          type: 'success',
          message: res.message,
        });
        onNextStep();

        // handleSubmit(data);
      } else {
        notification.error({
          message: res.message,
        });
      }
      // setSubmitting(false);
    } catch (err) {
      notification.error({
        message: 'Something went wrong. Try later',
      });
      // setSubmitting(false);
    } finally {
      setLoader(false);
    }
  };

  return (
    <Wrapper>
      <FormContainer>
        <Title>Please enter your email ID to reset the password</Title>
        <Form
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            email: '',
          }}
        >
          <Row gutter={[0, 16]}>
            <Col span={24}>
              <Form.Item
                label="Your Email"
                name="email"
                rules={[
                  {
                    required: true,
                    message: 'Please enter your email!',
                  },
                  {
                    type: 'email',
                    message: 'Please enter a valid email!',
                  },
                ]}
              >
                <Input
                  onChange={(e) => setInputVal(e.target.value)}
                  style={{
                    // Styling for the input field
                    height: 56,
                    borderRadius: '12px',
                    border: '2px solid #D2D9E1',
                    // borderWidth: '2px 0px 0px 0px',
                  }}
                  placeholder="Enter your email"
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Button
                loading={loader}
                style={{
                  // Styling for the button
                  width: '100%',
                  borderRadius: '12px',
                  fontWeight: 700,
                  fontSize: '16px',
                  fontFamily: 'Inter',
                  backgroundColor:
                    inputVal?.trim() === '' ? '#95AAC1' : '#007AFF',
                  height: '56px',
                  borderColor: 'transparent',
                  color: '#fff',
                }}
                htmlType="submit"
              >
                <span
                  style={{
                    fontFamily: 'Inter',
                    fontSize: '16px',
                    fontWeight: 700,
                    lineHeight: '20px',
                    letterSpacing: '-0.5px',
                    textAlign: 'left',
                    textUnderlinePosition: 'from-font',
                    textDecorationSkipInk: 'none',
                  }}
                >
                  Send Verification Code
                </span>
              </Button>
            </Col>
          </Row>
        </Form>
      </FormContainer>
    </Wrapper>
  );
};

export default EmailForm;

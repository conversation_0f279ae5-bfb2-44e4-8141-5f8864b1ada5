import styled from 'styled-components';
import WithDirection from '@chill/lib/helpers/rtl';

const changePassword = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 57vh;
  background-color: #fff;
  .isoLeftCol {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    padding: 20px 24px;
  }
  & .isoLeftColUName {
    display: flex;
    flex-direction: column;
    padding-left: 10px;
  }
  & .settingText {
    color: #000;
    border-bottom: solid;
    padding-bottom: 22px;
    border-color: #1e90ff;
    border-width: 3px;
    @media (max-width: 405px) {
      padding-bottom: 44px;
    }
  }
  & .userName {
    font-size: 18px;
    font-weight: 400;
  }
  & .isoLeftColRow {
    padding: 20px 24px 10px;
    display: flex;
    flex-direction: column;
  }
  & .isoLeftColRowText {
    padding-bottom: 16px;
  }
  & .isoLeftColIP {
    padding: 0px 24px 20px;
    display: flex;
    flex-direction: column;
  }
  & .isoRightCol {
    padding: 10px 24px 0px;
  }
  & .isoRightHeader {
    display: flex;
    text-align: center;
    flex-direction: column;
    padding-top: 20px;
  }
  & .isoRightChangeText {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
  }
  & .isoRightFormBtn {
    text-align: right;
    margin-bottom: 16px;
    align-self: flex-end;
  }
  & .isoRightHeaderdesc {
    padding-bottom: 11px;
    font-size: 13px;
    font-weight: 500;
    color: #95aac1;
    font-family: 'Inter';
    span {
      font-family: 'Inter';
      line-height: 10px;
    }
  }
  & .form-main-col {
    // display: flex;
    // justify-content: center;
    // align-items: center;
    // max-width: 430px;

    .ant-input::placeholder {
      color: #b0bcc8;
      font-family: Inter;
      font-size: 16px;
      font-weight: 500;
      line-height: 20px;
      letter-spacing: -0.5px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
    }

    .ant-form-item-label {
      label {
        text-transform: none;
        span {
          text-transform: none;
        }
      }
    }
    text-transform: none;
    .ant-form-item-required {
      span {
        font-family: Inter;
        font-size: 16px;
        text-transform: none;

        font-weight: 600;
        line-height: 20px;
        letter-spacing: -0.5px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
      }
    }
  }
  & .inner-div {
    width: 100%;
    max-width: 430px;
    margin: 0 auto;
    .ant-form-item-label {
      label {
        color: #2a2a2a;
        font-family: Inter;
        font-size: 16px;
        font-weight: 600;
        letter-spacing: -0.5px;
        line-height: 20px;
      }
    }
  }
  & .isoRightHeaderTitle {
    padding-bottom: 11px;
    font-size: 16px;
    font-weight: 600;
    color: #1e1e1e;
    span {
      font-family: Inter;
      font-size: 16px;
      font-weight: 600;
      line-height: 20px;
      letter-spacing: -0.5px;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
    }
  }
  & .formLabel {
    margin-top: 14px;
  }
  & .grayText {
    color: #808080;
  }
  & .plusAddUser {
    color: #1e90ff;
    display: flex;
    flex-direction: row-reverse;
    justify-content: end;
    & > span {
      font-size: 18px;
      margin-top: 1px;
      margin-right: 6px;
      @media (max-width: 405px) {
        display: none;
      }
    }
  }
  & .horizontalDevider {
    margin: 0;
  }
  & .isoRightColRow {
    max-width: 610px;
    padding-top: 20px;
  }
  & .changePasswordText {
    font-size: 16px;
    font-weight: 500;
    color: #000;
  }
  & .changeBtn {
    border-radius: 10px;
    font-weight: 500;
    border: 0px;
    background: linear-gradient(90deg, #039be5 38%, #03a9f4 90%);
    &:hover {
      background: linear-gradient(90deg, #039be5 38%, #03a9f4 90%);
    }
  }
  & .selectImg > span {
    font-size: 12px;
    color: #1e90ff;
    width: 22px;
    height: 22px;
    position: absolute;
    z-index: 1;
    right: 0px;
    bottom: 0px;
    background: #f5fffa;
    border-radius: 50px;
    padding: 5px;
  }
  .relativeClass {
    position: relative;
  }
`;

export default WithDirection(changePassword);

/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable  react/jsx-no-bind */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
/* eslint-disable no-unused-expressions */
/* eslint-disable react/destructuring-assignment */

import React, { useEffect, useState } from 'react';
import {
  Pie<PERSON><PERSON>,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
} from 'recharts';
import useWindowSize from '@chill/lib/hooks/useWindowSize';
import './FeedCampaigns.css';

import {
  Col,
  Row,
  Card,
  Avatar,
  Dropdown,
  Space,
  Divider,
  Switch,
  Menu,
  Slider,
  Button,
  Progress,
  Table,
  Spin,
  Form,
  Input,
  Select,
} from 'antd';
import { Link, useHistory } from 'react-router-dom';
import {
  StarFilled,
  EllipsisOutlined,
  MenuOutlined,
  AppstoreFilled,
  SettingFilled,
  DownOutlined,
  CopyOutlined,
  RiseOutlined,
  FallOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  RightOutlined,
  LoadingOutlined,
  FilterOutlined,
} from '@ant-design/icons';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { useSelector } from 'react-redux';
import ContentLoader from 'react-content-loader';
import babyS from '@chill/assets/images/babyStrollerSee.png';
import FC2 from '@chill/assets/images/FC2.jpg';
import FC3 from '@chill/assets/images/FC3.jpg';
import FC4 from '@chill/assets/images/FC4.jpg';
import TP5 from '@chill/assets/images/TP5.jpg';
import getApiData from '@chill/lib/helpers/apiHelper';
import expandIcon from '@chill/assets/images/up-arrow.png';
import downArrow from '@chill/assets/images/down-arrow.png';
import { isArray, isEmpty } from 'lodash';

const cardObjD = {
  id: 1,
  title: 'Keeping baby cool',
  price: '$180',
  price1: '360',
  price2: '30',
  price3: '$60',
  description: 'Estimated Sold QTY :',
  description1: 'People in this audience:',
  description3: 'Value of the campaign:',
  imageSrc: babyS,
  // 'https://img.freepik.com/free-photo/cute-baby-boy-scooter-autumnal-park_1303-29193.jpg?w=900&t=st=1681296208~exp=1681296808~hmac=db2387c4656a1f6311d599ef3bfe7bcc87e651a820791a3584fcace0b25a9af0',
  icon1: <StarFilled />,
  icon2: <EllipsisOutlined />,
};
// Array of card data
const cardData = [
  {
    id: 1,
    title: 'Keeping baby cool',
    price: '$180',
    price1: '360',
    price2: '3789',
    price3: '$60',
    description: 'Estimated Sold QTY :',
    description1: 'People in this audience:',
    description3: 'Value of the campaign:',
    imageSrc: babyS,
    // 'https://img.freepik.com/free-photo/cute-baby-boy-scooter-autumnal-park_1303-29193.jpg?w=900&t=st=1681296208~exp=1681296808~hmac=db2387c4656a1f6311d599ef3bfe7bcc87e651a820791a3584fcace0b25a9af0',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
  {
    id: 2,
    title: 'Top tips for sleep!',
    price: '$270',
    price1: '360',
    price2: '4578',
    price3: '$60',
    description: 'Estimated Sold QTY :',
    description1: 'People in this audience:',
    description3: 'Value of the campaign:',
    imageSrc: FC2,
    // 'https://img.freepik.com/free-photo/small-girl-skate_1303-9646.jpg?size=626&ext=jpg&ga=GA1.1.1186299328.1674125467&semt=ais',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
  {
    id: 3,
    title: 'When’s the right time to change car seats',
    price: '$90',
    price1: '360',
    price2: '6754',
    price3: '$60',
    description: 'Estimated Sold QTY :',
    description1: 'People in this audience:',
    description3: 'Value of the campaign:',
    imageSrc: FC3,
    // 'https://img.freepik.com/free-photo/funny-kid-rain-boots-playing-rain-park_1157-37683.jpg?size=626&ext=jpg&ga=GA1.2.1186299328.1674125467&semt=ais',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
  {
    id: 4,
    title: 'Travelling with young children',
    price: '$261',
    price1: '360',
    price2: '4567',
    price3: '$60',
    description: 'Estimated Sold QTY :',
    description1: 'People in this audience:',
    description3: 'Value of the campaign:',
    imageSrc: FC4,
    // 'https://img.freepik.com/premium-photo/happy-child-with-robot-funny-kid-playing-home-success-creative-innovation-technology-concept_411285-9771.jpg?size=626&ext=jpg&ga=GA1.1.1186299328.1674125467&semt=ais',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
  {
    id: 5,
    title: 'Playtime advice',
    price: '$180',
    price1: '360',
    price2: '4325',
    price3: '$60',
    description: 'Estimated Sold QTY :',
    description1: 'People in this audience:',
    description3: 'Value of the campaign:',
    imageSrc: TP5,
    // 'https://img.freepik.com/free-photo/daughter-helping-packing-boxes_23-2148569347.jpg?size=626&ext=jpg&ga=GA1.1.1186299328.1674125467&semt=ais',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
  {
    id: 6,
    title: 'Riding a bike',
    price: '$180',
    price1: '360',
    price2: '5645',
    price3: '$60',
    description: 'Estimated Sold QTY :',
    description1: 'People in this audience:',
    description3: 'Value of the campaign:',
    imageSrc:
      'https://img.freepik.com/premium-photo/happy-little-girl-protective-hat-riding-her-bike-outdoors-sunny-day-near-forest_146671-41396.jpg?size=626&ext=jpg&ga=GA1.1.1186299328.1674125467&semt=ais',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
  {
    id: 7,
    title: 'Outdoor play',
    price: '$180',
    price1: '360',
    price2: '6000',
    price3: '$60',
    description: 'Estimated Sold QTY :',
    description1: 'People in this audience:',
    description3: 'Value of the campaign:',
    imageSrc:
      'https://img.freepik.com/free-photo/young-boy-having-fun-playground_23-2149490452.jpg?size=626&ext=jpg&ga=GA1.1.1186299328.1674125467&semt=robertav1_2_sidr',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
];

export default function FeedCampaigns() {
  // Initialize window size hook for real-time responsive behavior
  const windowSize = useWindowSize({ throttleMs: 100 });

  // Responsive helper functions
  const getResponsiveCardWidth = (windowWidth) => {
    if (windowWidth < 768) return '100%'; // Mobile: full width
    if (windowWidth < 1024) return '48%'; // Tablet: 2 columns
    return '23%'; // Desktop: 4 columns
  };

  const getResponsiveCardHeight = (windowWidth) => {
    if (windowWidth < 768) return 'auto'; // Mobile: auto height
    if (windowWidth < 1024) return '300px'; // Tablet: fixed height
    return '280px'; // Desktop: compact height
  };

  const getResponsiveImageHeight = (windowWidth) => {
    if (windowWidth < 768) return '200px'; // Mobile: larger image
    if (windowWidth < 1024) return '140px'; // Tablet: medium image
    return '117px'; // Desktop: compact image
  };

  const getResponsivePopupWidth = (windowWidth) => {
    if (windowWidth < 768) return '90%'; // Mobile: almost full width
    if (windowWidth < 1024) return '70%'; // Tablet: 70% width
    return 'auto'; // Desktop: auto width
  };

  const getResponsiveFilterWidth = (windowWidth) => {
    if (windowWidth < 768) return '100%'; // Mobile: full width overlay
    if (windowWidth < 1024) return '30%'; // Tablet: 30% width
    return '25%'; // Desktop: 25% width
  };

  const getResponsiveMainWidth = (windowWidth, showFilter) => {
    if (windowWidth < 768) return '100%'; // Mobile: always full width
    if (windowWidth < 1024) return showFilter ? '70%' : '100%'; // Tablet: 70% when filter shown
    return showFilter ? '75%' : '100%'; // Desktop: 75% when filter shown
  };

  // Array of menus for Dropdowm
  const history = useHistory();
  const items = [
    {
      label: 'Value',
      key: '0',
      value: 'value',
    },
    {
      label: 'Audience size',
      key: '1',
      value: 'audienceSize',
    },
    {
      label: 'Conversion rate',
      key: '3',
      value: 'conversionRate',
    },
    {
      label: 'Quantity',
      key: '4',
      value: 'quantity',
    },
  ];

  const [showPopup, setShowPopup] = useState(false);
  const [cardObj, setCardObj] = useState(cardObjD);
  const [productPromo, setProductPromo] = useState(false);
  const [showPopupCircle, setShowPopupCircle] = useState(false);
  const { Option } = Select;
  const [productTypeArr, setProductTypeArr] = React.useState([]);
  const [relInfo, setRel] = useState('');
  const [pageInfo, setPageInfo] = useState('');
  const [previousPageInfo, setPreviousPageInfo] = useState('');
  const [showValues, setShowValues] = useState(false);
  const [selectedBrand, setselectedBrand] = useState();
  const [showFilter, setShowFilter] = useState(false);
  const [shopifyProductState, setShopifyProductState] = React.useState({
    products: [],
    maxView: [],
    maxClick: [],
    maxShare: [],
    productLoad: false,
    productData: {},
  });
  const [shopifyFilter, setshopifyFilter] = React.useState({
    pagination: { simple: true },
    filters: {},
  });
  const togglePopup = (value) => {
    setTimeout(() => {
      history.push({
        pathname: '/dashboard/dummyAdCamp',
        state: {
          image: cardObj?.imageSrc,
          name: value?.name,
        }, // your data array of objects
      });
    }, 5000);

    setShowPopupCircle(!showPopupCircle);
  };
  const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

  const onFinish = (values) => {
    console.log('Form data:', values);
    setShowPopup(false);

    togglePopup(values);
    setProductPromo(false);
    setShowValues(false);
  };
  // store the state of selected icon
  const [selectedIcon, setSelectedIcon] = useState('AppstoreFilled');

  // const [selectedDropdownItem, setSelectedDropdownItem] = useState(items[0]);

  // store the state of sliderRange
  const [sliderRange, setSliderRange] = useState([6, 9]);
  const onAfterChange1 = (value) => {
    console.log('onAfterChange: ', value);
    setSliderRange(value);
  };

  const onChange1 = (value) => {
    console.log('onChange: ', value);
    setSliderRange(value);
  };
  // store the state of selected options and keys
  const { SubMenu } = Menu;
  const [openKeys, setOpenKeys] = useState();
  const [selectedOptions, setSelectedOptions] = useState({});
  const [emailChecked, setEmailChecked] = useState(false);

  const [imageLoader, setImageLoader] = useState(false);
  const [title1Loader, setTitle1loader] = useState(false);

  const onOpenChange = (keys) => {
    setOpenKeys(keys);
  };
  // function for selecting menu items
  const handleOptionSelect = (event, key) => {
    setSelectedOptions({ ...selectedOptions, [key]: event.target.checked });
  };

  const handleApplyClick = () => {
    localStorage.setItem('selectedOptions', JSON.stringify(selectedOptions));
  };
  // function for checkbox clicked
  const onChange = (checked) => {
    setEmailChecked(checked);
    console.log(`switch to ${checked}`);
  };
  const fromAI = localStorage.getItem('fromAI');

  const handleGenerateCampaign = () => {
    setShowPopup(!showPopup);
    history.push('/dashboard/dummyAd');
  };
  const [gender, setGender] = useState();
  const [language, setLanguage] = useState();
  const [languageOptions, setLanguageOptions] = useState([]);
  const [selectedGenders, setSelectedGenders] = useState([]);
  const fetchLanguages = async () => {
    try {
      const response = await getApiData('getLanguageList', {}, 'POST');
      console.log(response.data);
      if (response.success) {
        console.log(response.data);
        setLanguageOptions(response.data);
      }
    } catch (error) {
      console.error('Error fetching images:', error);
    }
  };

  useEffect(() => {
    // Fetch images from the API
    fetchLanguages();
  }, []);

  const handleGenderChange = (selectedVlaues) => {
    setSelectedGenders(selectedVlaues);
  };
  const handleLanguageSelect = () => {};

  // const [cards, setCards] = useState(cardData);

  // function handleDragEnd(result) {
  //   if (!result.destination) return;
  //   const item = Array.from(cards);
  //   const [reorderedItem] = item.splice(result.source.index, 1);
  //   item.splice(result.destination.index, 0, reorderedItem);
  //   setCards(item);
  // }

  // to store the state of carddata
  const [cards, setCards] = useState(cardData);
  // functions for handling drag and drop
  function handleDragEnd(result) {
    if (!result.destination) return;

    const { source, destination } = result;

    if (source.index === destination.index) return;

    const newCards = [...cards];
    const [draggedCard] = newCards.splice(source.index, 1);

    newCards.splice(destination.index, 0, draggedCard);

    setCards(newCards);
  }

  function onChangeBrandSelect(value) {
    console.log(`selected ${value}`);
    console.log('Brand List v ==>', value);
    setselectedBrand(value);
    setShowValues(true);
  }
  function onBlur() {
    console.log('blur');
  }

  function onFocus() {
    console.log('focus');
  }

  function onSearch(val) {
    console.log('search:', val);
  }

  // this function for get shopify products
  /** this function for get shopify products
   * @function getShopifyProductList
   * @param {object} data type, sort
   */
  async function getShopifyProductList(data = {}) {
    console.log('e-commerce/product-list-------called-', data);

    // call this api

    setShopifyProductState((p) => ({
      ...p,
      productLoad: true,
    }));

    // if (data.product_tag || data.sort_obj) {
    // eslint-disable-next-line no-param-reassign
    data.collection_id = 1; // selectedCollectionId;
    // }
    // data.limit = 10;
    // set limit == pagination.pageSize, for previous, set rel, when change per page cahnge limit
    // data = {
    //   limit: 25,
    // };
    // if (sort) {
    //   data.sort = 'ASC';
    // } else {
    //   data.sort = 'DESC';
    // }
    try {
      const response = await getApiData(
        'e-commerce/product-list',
        data,
        'POST',
      );
      if (response.success && response.data) {
        console.log('e-commerce/product-list--------', response.data);
        if (response.page_info_next) {
          setPreviousPageInfo(pageInfo);
          setPageInfo(response.page_info_next);
        }
        if (response.page_info_previous) {
          setPreviousPageInfo(response.page_info_previous);
        }
        if (response.rel) {
          console.log('rel------response-', response.rel);
          setRel(response.rel);
        }
        if (isArray(response?.filter_for)) {
          console.log('------filter_for----', response.filter_for);
          if (!('product_tag' in data)) {
            setProductTypeArr(response?.filter_for);
          }
        }
        if (isArray(response?.data)) {
          setShopifyProductState((preState) => ({
            ...preState,
            products: isArray(response?.data) ? response?.data : [],
            maxView: isArray(response.product_analytics?.maxViewed)
              ? response?.product_analytics?.maxViewed
              : [],
            maxClick: isArray(response.product_analytics?.maxClicked)
              ? response?.product_analytics?.maxClicked
              : [],
            maxShare: isArray(response.product_analytics?.maxShared)
              ? response?.product_analytics?.maxShared
              : [],
            productLoad: false,
            productData: response,
          }));
          const pagination =
            shopifyFilter && shopifyFilter.pagination
              ? { ...shopifyFilter.pagination }
              : {};
          pagination.total = response.total_count || 0;
          pagination.pageSize = 10;
          pagination.current = response.page || 1;
          setshopifyFilter((f) => ({ ...f, pagination }));
          console.log('pagination---no response-----', pagination);
        }
      } else {
        console.log('e-commerce/product-list---no response-----', response);
        Notification('error', response.message);
        setShopifyProductState((preState) => ({
          ...preState,
          productLoad: false,
        }));
      }
    } catch (err) {
      console.log('e-commerce/product-list--err------', err);

      setShopifyProductState((preState) => ({
        ...preState,
        productLoad: false,
      }));
    }
  }
  React.useEffect(() => {
    getShopifyProductList();
  }, []);
  return (
    <div
      className="responsiveGrid"
      style={{
        display: 'flex',
        flexDirection: windowSize.width < 768 ? 'column' : 'row',
      }}
    >
      <div
        style={{
          width: getResponsiveMainWidth(windowSize.width, showFilter),
          transition: 'width 0.3s ease',
        }}
      >
        <div className="widgetSpacing" style={{ background: '#fff' }}>
          <div
            className="headerControls"
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              paddingRight: windowSize.width < 768 ? '16px' : '20px',
              paddingLeft: windowSize.width < 768 ? '16px' : '0',
              paddingTop: windowSize.width < 768 ? '16px' : '0',
              paddingBottom: windowSize.width < 768 ? '16px' : '0',
              flexWrap: windowSize.width < 768 ? 'wrap' : 'nowrap',
              gap: windowSize.width < 768 ? '12px' : '0',
            }}
          >
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                width: '100%',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  gap: windowSize.width < 768 ? '16px' : '25px',
                  alignItems: 'center',
                }}
              >
                <AppstoreFilled
                  className="touch-target"
                  style={{
                    width: windowSize.width < 768 ? '32px' : '24px',
                    height: windowSize.width < 768 ? '32px' : '24px',
                    fontSize: windowSize.width < 768 ? '32px' : '25px',
                    color:
                      selectedIcon === 'AppstoreFilled' ? '#2A77F4' : '#A9ABBC',
                    marginLeft: windowSize.width < 768 ? '0' : '23px',
                    marginTop: windowSize.width < 768 ? '0' : '28px',
                    cursor: 'pointer',
                    minWidth: '44px',
                    minHeight: '44px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: windowSize.width < 768 ? '8px' : '0',
                  }}
                  onClick={() => setSelectedIcon('AppstoreFilled')}
                />
                <MenuOutlined
                  className="touch-target"
                  style={{
                    width: windowSize.width < 768 ? '32px' : '24px',
                    fontSize: windowSize.width < 768 ? '32px' : '23px',
                    height: windowSize.width < 768 ? '32px' : '24px',
                    color:
                      selectedIcon === 'MenuOutlined' ? '#2A77F4' : '#A9ABBC',
                    marginLeft: windowSize.width < 768 ? '0' : '25px',
                    marginTop: windowSize.width < 768 ? '0' : '28px',
                    cursor: 'pointer',
                    minWidth: '44px',
                    minHeight: '44px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: windowSize.width < 768 ? '8px' : '0',
                  }}
                  onClick={() => setSelectedIcon('MenuOutlined')}
                />
              </div>
              <div style={{ position: 'relative' }}>
                <FilterOutlined
                  className="touch-target"
                  style={{
                    width: windowSize.width < 768 ? '32px' : '24px',
                    height: windowSize.width < 768 ? '32px' : '24px',
                    fontSize: windowSize.width < 768 ? '32px' : '25px',
                    marginLeft: windowSize.width < 768 ? '0' : '23px',
                    marginTop: windowSize.width < 768 ? '0' : '28px',
                    cursor: 'pointer',
                    color: showFilter ? '#2A77F4' : '#A9ABBC',
                    minWidth: '44px',
                    minHeight: '44px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: windowSize.width < 768 ? '8px' : '0',
                  }}
                  onClick={() => setShowFilter((prevState) => !prevState)}
                />
                {/* Mobile close button for filter */}
                {windowSize.width < 768 && showFilter && (
                  <CloseCircleOutlined
                    style={{
                      position: 'absolute',
                      top: '8px',
                      right: '8px',
                      fontSize: '20px',
                      color: '#2A77F4',
                      cursor: 'pointer',
                      zIndex: 1001,
                    }}
                    onClick={() => setShowFilter(false)}
                  />
                )}
              </div>
            </div>
            {/* <div>
              <p
                style={{
                  // marginLeft: '550px',
                  marginTop: '20px',
                  fontFamily: 'Rubik',
                  fontSize: '14px',
                  color: '#A9ABBC',
                  marginBottom: '-5px',
                }}
              >
                sort
              </p>
              <Dropdown
                menu={{
                  items,
                  // onClick: handleDropdownClick, // Pass the click handler to the menu // Pass the click handler to the menu
                }}
                trigger={['click']}
              >
                <Space
                  style={{
                    // marginLeft: '620px',
                    color: '#0A0606',
                    fontSize: '14px',
                    fontFamily: 'Rubik',
                    marginTop: '10px',
                  }}
                >
                  By Activity
                  <DownOutlined />
                </Space>
              </Dropdown>
            </div> */}
          </div>
          <div>
            <div
              className="chartContainer"
              style={{
                height: '100%',
                width: '100%',
                paddingTop: windowSize.width < 768 ? '20px' : '29.86px',
                paddingLeft: windowSize.width < 768 ? '16px' : '10px',
                paddingRight: windowSize.width < 768 ? '16px' : '10px',
                paddingBottom: windowSize.width < 768 ? '20px' : '10px',
                overflowX: 'hidden',
                overflowY: windowSize.width < 768 ? 'auto' : 'hidden',
                display: selectedIcon === 'AppstoreFilled' ? 'block' : 'none',
              }}
            >
              <h4
                style={{
                  marginBottom: windowSize.width < 768 ? '20px' : '10px',
                  fontSize: windowSize.width < 768 ? '18px' : '16px',
                  fontWeight: '500',
                }}
              >
                My Feed Campaigns :
              </h4>
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="cards">
                  {(provided) => (
                    <div ref={provided.innerRef} {...provided.droppableProps}>
                      <div
                        style={{
                          display: 'grid',
                          gridTemplateColumns:
                            windowSize.width < 768
                              ? '1fr'
                              : windowSize.width < 1024
                              ? 'repeat(2, 1fr)'
                              : 'repeat(4, 1fr)',
                          gap: windowSize.width < 768 ? '16px' : '12px',
                          padding: windowSize.width < 768 ? '0' : '0 10px',
                        }}
                      >
                        {cards.map((card, index) => (
                          <Draggable
                            key={card.id}
                            draggableId={card.id.toString()}
                            index={index}
                          >
                            {(dragProvided) => (
                              <div
                                ref={dragProvided.innerRef}
                                {...dragProvided.draggableProps}
                                {...dragProvided.dragHandleProps}
                                style={{
                                  width: '100%',
                                  ...dragProvided.draggableProps.style,
                                }}
                              >
                                {/* <Link to="/dashboard/card"> */}
                                <Card
                                  style={{
                                    width: '100%',
                                    height: getResponsiveCardHeight(
                                      windowSize.width,
                                    ),
                                    borderRadius: '12px',
                                    cursor: 'pointer',
                                    transition:
                                      'transform 0.2s ease, box-shadow 0.2s ease',
                                  }}
                                  hoverable
                                  cover={
                                    <img
                                      style={{
                                        width: '100%',
                                        height: getResponsiveImageHeight(
                                          windowSize.width,
                                        ),
                                        borderTopLeftRadius: '12px',
                                        borderTopRightRadius: '12px',
                                        objectFit: 'cover',
                                      }}
                                      alt="example"
                                      src={card.imageSrc}
                                    />
                                  }
                                  onClick={() => {
                                    setShowPopup(true);
                                    setCardObj(card);
                                  }}
                                >
                                  <div
                                    style={{
                                      marginTop: '-15px',
                                      marginLeft: '-15px',
                                      marginRight: '-15px',
                                      padding:
                                        windowSize.width < 768
                                          ? '16px'
                                          : '12px',
                                    }}
                                  >
                                    <div
                                      style={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        alignItems: 'flex-start',
                                        flexWrap:
                                          windowSize.width < 768
                                            ? 'wrap'
                                            : 'nowrap',
                                        gap:
                                          windowSize.width < 768 ? '8px' : '0',
                                      }}
                                    >
                                      <h4
                                        style={{
                                          color: '#2A77F4',
                                          fontSize:
                                            windowSize.width < 768
                                              ? '16px'
                                              : '14px',
                                          fontFamily: 'rubik',
                                          letterSpacing: '0px',
                                          fontWeight: '500',
                                          margin: 0,
                                          lineHeight: '1.4',
                                        }}
                                      >
                                        {card.title}
                                      </h4>
                                      {/* <h4
                                        style={{
                                          fontSize: '14px',
                                          fontFamily: 'Rubik',
                                        }}
                                      >
                                        {card.price}
                                      </h4> */}
                                    </div>
                                    {/* <div
                                      style={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        color: '#A9ABBC',
                                        fontSize: '12px',
                                        fontFamily: 'Rubik',
                                        marginTop: '8px',
                                      }}
                                    >
                                      {card.description}
                                      <h4>{card.price1}</h4>
                                    </div> */}
                                    <div
                                      style={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        color: '#A9ABBC',
                                        fontSize: '12px',
                                        fontFamily: 'Rubik',
                                        marginTop: '8px',
                                      }}
                                    >
                                      {card.description1}
                                      <h4>{card.price2}</h4>
                                    </div>

                                    <div
                                      style={{
                                        display: 'flex',
                                        justifyContent: 'flex-end',
                                      }}
                                    >
                                      <div
                                        style={{
                                          fontSize: '16px',
                                          color: '#FFC107',
                                          marginLeft: '105px',
                                          marginTop: '5px',
                                        }}
                                      >
                                        {card.icon1}
                                      </div>
                                      <div
                                        style={{
                                          fontSize: '20px',
                                          color: '#A9ABBC',
                                          marginLeft: '5px',
                                          marginTop: '5px',
                                        }}
                                      >
                                        {card.icon2}
                                      </div>
                                    </div>
                                  </div>
                                </Card>
                                {/* </Link> */}
                              </div>
                            )}
                          </Draggable>
                        ))}

                        {/* <Col span={12} md={6}>
                          <Card
                            hoverable
                            style={{
                              width: '14vw',
                              // height: '38vh',
                              borderRadius: '12px',
                              border: '6px solid #7070701A',
                            }}
                          >
                            <CopyOutlined
                              style={{
                                flex: 1,
                                fontSize: '40px',
                                color: '#A9ABBC',
                                marginTop: '60px',
                                marginLeft: '40px',
                                justifyContent: 'center',
                                alignItems: 'center',
                              }}
                            />
                            <h5
                              style={{
                                color: 'A9ABBC',
                                fontFamily: 'Rubik',
                                fontSize: '12px',
                                textAlign: 'center',
                              }}
                            >
                              Make a copy
                            </h5>
                            <div style={{ height: 80 }} />
                          </Card>
                        </Col> */}
                      </div>

                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            </div>
          </div>
          <center>
            <h1 style={{ padding: '20px' }}>
              *There is no Products in this Campaign
            </h1>
          </center>
        </div>
      </div>
      <div
        style={{
          width:
            windowSize.width < 768
              ? '100%'
              : getResponsiveFilterWidth(windowSize.width),
          display: showFilter ? 'block' : 'none',
          position: windowSize.width < 768 ? 'fixed' : 'relative',
          top: windowSize.width < 768 ? '0' : 'auto',
          left: windowSize.width < 768 ? '0' : 'auto',
          right: windowSize.width < 768 ? '0' : 'auto',
          bottom: windowSize.width < 768 ? '0' : 'auto',
          zIndex: windowSize.width < 768 ? 1000 : 'auto',
          backgroundColor:
            windowSize.width < 768 ? 'rgba(0, 0, 0, 0.5)' : 'transparent',
          transition: 'all 0.3s ease',
        }}
      >
        <div
          style={{
            width: windowSize.width < 768 ? '85%' : '100%',
            marginLeft: windowSize.width < 768 ? 'auto' : '15px',
            marginRight: windowSize.width < 768 ? '0' : 'auto',
            background: '#fff',
            height: windowSize.width < 768 ? '100vh' : '100%',
            padding: windowSize.width < 768 ? '24px' : '20px',
            maxHeight: windowSize.width < 768 ? '100vh' : '80vh',
            overflowX: 'hidden',
            overflowY: 'scroll',
            borderRadius: windowSize.width < 768 ? '0' : '0',
            boxShadow:
              windowSize.width < 768 ? '0 0 20px rgba(0, 0, 0, 0.3)' : 'none',
            position: 'relative',
          }}
        >
          {/* Mobile close button */}
          {windowSize.width < 768 && (
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '20px',
                paddingBottom: '16px',
                borderBottom: '1px solid #f0f0f0',
              }}
            >
              <h3
                style={{
                  margin: 0,
                  fontSize: '18px',
                  fontWeight: '600',
                  color: '#2A77F4',
                }}
              >
                Filters
              </h3>
              <CloseCircleOutlined
                style={{
                  fontSize: '24px',
                  color: '#2A77F4',
                  cursor: 'pointer',
                  minWidth: '44px',
                  minHeight: '44px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
                onClick={() => setShowFilter(false)}
              />
            </div>
          )}
          <div>
            <Divider
              style={{ display: windowSize.width < 768 ? 'none' : 'block' }}
            />
            {/* <h1
              style={{
                marginTop: '-10px',
                fontFamily: 'Rubik',
                fontSize: '14px',
                color: '0A0606',
              }}
            >
              Filter
            </h1> */}
            <div
              style={{
                display: 'flex',
                marginTop: '10px',
                justifyContent: 'space-between',
                padding: '0px 22px',
              }}
            >
              <h1
                style={{
                  fontWeight: 700,
                  fontSize: '14px',
                }}
              >
                Email Notification
              </h1>
              <Switch
                checked={emailChecked}
                onChange={onChange}
                style={{
                  // width: '3vw',
                  backgroundColor: emailChecked ? '#04B8FF' : '#718096',
                }}
              />
            </div>
            <Divider />

            <Menu
              mode="inline"
              openKeys={openKeys}
              onOpenChange={onOpenChange}
              style={{
                width: '100%',
              }}
            >
              <SubMenu
                className="custom-submenu"
                key="sub1"
                title="Audience size"
                expandIcon={({ isOpen }) =>
                  isOpen ? (
                    <img
                      src={downArrow}
                      alt="collapse arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  ) : (
                    <img
                      src={expandIcon}
                      alt="expand arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  )
                }
              >
                <Menu.Item
                  key="1"
                  style={{
                    color: '#A9ABBC',
                    marginLeft: '-20px',
                  }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '0.412')}
                    style={{ marginRight: '10px' }}
                  />
                  2(8)
                </Menu.Item>

                <Menu.Item
                  key="2"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '0.413')}
                    style={{ marginRight: '10px' }}
                  />
                  4(125)
                </Menu.Item>
                <Menu.Item
                  key="3"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '0.414')}
                    style={{ marginRight: '10px' }}
                  />
                  6(529)
                </Menu.Item>
              </SubMenu>

              <Divider style={{ marginTop: '8px' }} />

              <SubMenu
                className="custom-submenu"
                key="sub2"
                title="Conversion rate"
                expandIcon={({ isOpen }) =>
                  isOpen ? (
                    <img
                      src={downArrow}
                      alt="collapse arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  ) : (
                    <img
                      src={expandIcon}
                      alt="expand arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  )
                }
              >
                <Menu.Item
                  key="5"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '5%')}
                    style={{ marginRight: '10px' }}
                  />
                  5%
                </Menu.Item>
                <Menu.Item
                  key="6"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '6%')}
                    style={{ marginRight: '10px' }}
                  />
                  6%
                </Menu.Item>
              </SubMenu>
              <Divider style={{ marginTop: '8px' }} />

              <SubMenu
                className="custom-submenu"
                key="sub3"
                title="QTY"
                expandIcon={({ isOpen }) =>
                  isOpen ? (
                    <img
                      src={downArrow}
                      alt="collapse arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  ) : (
                    <img
                      src={expandIcon}
                      alt="expand arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  )
                }
              >
                <Menu.Item
                  key="7"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '81')}
                    style={{ marginRight: '10px' }}
                  />
                  81
                </Menu.Item>
                <Menu.Item
                  key="8"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '85')}
                    style={{ marginRight: '10px' }}
                  />
                  85
                </Menu.Item>
              </SubMenu>
              <Divider style={{ marginTop: '8px' }} />

              <SubMenu
                className="custom-submenu"
                key="sub4"
                title="Language"
                expandIcon={({ isOpen }) =>
                  isOpen ? (
                    <img
                      src={downArrow}
                      alt="collapse arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  ) : (
                    <img
                      src={expandIcon}
                      alt="expand arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  )
                }
              >
                <Menu.Item
                  key="9"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, 'english')}
                    style={{ marginRight: '10px' }}
                  />
                  English
                </Menu.Item>
                <Menu.Item
                  key="10"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, 'Spanish')}
                    style={{ marginRight: '10px' }}
                  />
                  Spanish
                </Menu.Item>
              </SubMenu>
              <Divider style={{ marginTop: '8px' }} />
              <SubMenu
                className="custom-submenu"
                key="sub5"
                title="Location"
                expandIcon={({ isOpen }) =>
                  isOpen ? (
                    <img
                      src={downArrow}
                      alt="collapse arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  ) : (
                    <img
                      src={expandIcon}
                      alt="expand arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  )
                }
              >
                <Menu.Item
                  key="11"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, 'USA')}
                    style={{ marginRight: '10px' }}
                  />
                  USA
                </Menu.Item>
                <Menu.Item
                  key="12"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, 'UK')}
                    style={{ marginRight: '10px' }}
                  />
                  UK
                </Menu.Item>
              </SubMenu>
              <Divider style={{ marginTop: '8px' }} />

              <SubMenu
                className="custom-submenu"
                key="sub9"
                title="Products owned"
                expandIcon={({ isOpen }) =>
                  isOpen ? (
                    <img
                      src={downArrow}
                      alt="collapse arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  ) : (
                    <img
                      src={expandIcon}
                      alt="expand arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  )
                }
              >
                <Menu.Item
                  key="13"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '4,2,6')}
                    style={{ marginRight: '10px' }}
                  />
                  4,2,6
                </Menu.Item>
                <Menu.Item
                  key="14"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '5,2,6')}
                    style={{ marginRight: '10px' }}
                  />
                  5,2,6
                </Menu.Item>
              </SubMenu>
              <Divider style={{ marginTop: '8px' }} />

              <SubMenu
                className="custom-submenu"
                key="sub6"
                title="Child age range"
                expandIcon={({ isOpen }) =>
                  isOpen ? (
                    <img
                      src={downArrow}
                      alt="collapse arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  ) : (
                    <img
                      src={expandIcon}
                      alt="expand arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  )
                }
              >
                <Slider
                  style={{ marginTop: '-8px' }}
                  trackStyle={{ backgroundColor: '#2A77F4' }}
                  railStyle={{ boxShadow: '0px 0px 6px #00000029' }}
                  range
                  defaultValue={[6, 9]}
                  onChange={onChange1}
                  onAfterChange={onAfterChange1}
                />
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'left',
                    color: '#040606',
                    opacity: 0.5,
                  }}
                >
                  <span>{sliderRange[0]}</span>
                  <span>-</span>
                  <span>{sliderRange[1]}+</span>
                </div>
              </SubMenu>
              <Divider style={{ marginTop: '8px' }} />
            </Menu>
            <div
              style={{
                flex: 1,
                width: '100%',
                hegith: '100%',
              }}
            >
              <center>
                <Button
                  style={{
                    background: '#09295D',
                    borderRadius: '4px',
                    color: '#fff',
                    paddingLeft: '70px',
                    paddingRight: '70px',
                  }}
                  onClick={handleApplyClick}
                >
                  Apply
                </Button>
              </center>
            </div>
          </div>
        </div>
        {showPopup && (
          <div
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: windowSize.width < 768 ? 'flex-start' : 'center',
              alignContent: 'center',
              zIndex: 9999,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              padding: windowSize.width < 768 ? '20px 16px' : '20px',
              overflowY: 'auto',
            }}
          >
            <div
              style={{
                backgroundColor: 'white',
                padding:
                  windowSize.width < 768
                    ? '24px 16px 16px 16px'
                    : '50px 50px 10px 50px',
                borderRadius: windowSize.width < 768 ? '16px' : '5px',
                boxShadow:
                  windowSize.width < 768
                    ? '0 10px 40px rgba(0, 0, 0, 0.3)'
                    : '0 0 10px rgba(0, 0, 0, 0.3)',
                position: 'absolute',
                zIndex: 999,
                display: 'flex',
                alignItems: 'center',
                flexDirection: 'column',
                width: getResponsivePopupWidth(windowSize.width),
                maxWidth: windowSize.width < 768 ? '100%' : 'none',
                maxHeight: windowSize.width < 768 ? '90vh' : 'none',
                marginTop: windowSize.width < 768 ? '20px' : '0',
                marginBottom: windowSize.width < 768 ? '20px' : '0',
                overflowY: windowSize.width < 768 ? 'auto' : 'visible',
              }}
            >
              <Button
                type="ai"
                htmlType="submit"
                className="touch-target"
                style={{
                  marginTop: windowSize.width < 768 ? -16 : -30,
                  border: 'none',
                  color: '#2A77F4',
                  background: 'transparent',
                  alignSelf: 'flex-end',
                  minWidth: '44px',
                  minHeight: '44px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: windowSize.width < 768 ? '12px' : '8px',
                }}
                onClick={() => {
                  setShowPopup(false);
                  setProductPromo(false);
                  setShowValues(false);
                }}
              >
                <CloseCircleOutlined
                  style={{
                    fontSize: windowSize.width < 768 ? '24px' : '20px',
                  }}
                />
              </Button>
              <div
                style={{
                  backgroundColor: 'white',

                  borderRadius: '5px',

                  display: 'flex',
                  justifyContent: 'center',
                  // alignItems: 'center',
                  alignContent: 'center',
                  flexDirection: 'row',
                }}
              >
                <Card
                  style={{
                    width: '14vw',
                    height: '35vh',
                    borderRadius: '12px',
                  }}
                  hoverable
                  cover={
                    <img
                      style={{
                        width: '14vw',
                        height: '180px',
                        borderTopLeftRadius: '12px',
                        borderTopRightRadius: '12px',
                        objectFit: 'cover',
                      }}
                      alt="example"
                      src={cardObj.imageSrc}
                    />
                  }
                >
                  <div
                    style={{
                      marginTop: '-15px',
                      marginLeft: '-15px',
                      marginRight: '-15px',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between ',
                      }}
                    >
                      <h4
                        style={{
                          color: '#2A77F4',
                          fontSize: '14px',
                          fontFamily: 'rubik',
                          letterSpacing: '0px',
                        }}
                      >
                        {cardObj.title}
                      </h4>
                      {showValues && (
                        <h4
                          style={{
                            fontSize: '14px',
                            fontFamily: 'Rubik',
                          }}
                        >
                          {cardObj.price}
                        </h4>
                      )}
                    </div>
                    {showValues && (
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          color: '#A9ABBC',
                          fontSize: '12px',
                          fontFamily: 'Rubik',
                          marginTop: '8px',
                        }}
                      >
                        {cardObj.description}
                      </div>
                    )}
                    {/* <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        color: '#A9ABBC',
                        fontSize: '12px',
                        fontFamily: 'Rubik',
                        marginTop: '8px',
                      }}
                    >
                      {cardObj.description1}
                      <h4>{cardObj.price2}</h4>
                    </div> */}

                    {/* <div
                      style={{
                        display: 'flex',
                        justifyContent: 'flex-end',
                      }}
                    >
                      <div
                        style={{
                          fontSize: '16px',
                          color: '#FFC107',
                          marginLeft: '105px',
                          marginTop: '5px',
                        }}
                      >
                        {cardObj.icon1}
                      </div>
                      <div
                        style={{
                          fontSize: '20px',
                          color: '#A9ABBC',
                          marginLeft: '5px',
                          marginTop: '5px',
                        }}
                      >
                        {cardObj.icon2}
                      </div>
                    </div> */}
                  </div>
                </Card>
                <div
                  style={{
                    flexDirection: 'column',
                    marginLeft: '10px',
                  }}
                >
                  <p
                    style={{
                      fontSize: '15px',
                      color: '#2A77F4',
                      marginBottom: 10,
                      fontWeight: 'bold',
                    }}
                  >
                    Create content :
                  </p>
                  <Form onFinish={onFinish} style={{ minWidth: '500px' }}>
                    {/* Text Input */}
                    <Form.Item
                      // label="Name"
                      style={{ marginBottom: 20 }}
                      name="name"
                      rules={[
                        { required: true, message: 'Please enter title' },
                      ]}
                    >
                      <Input placeholder="Headline text" />
                    </Form.Item>
                    {/* Text Area */}
                    <Form.Item name="description" style={{ marginBottom: 20 }}>
                      <Input.TextArea
                        placeholder="Prompt for the campaign..."
                        autoSize={{ minRows: 4, maxRows: 6 }}
                      />
                    </Form.Item>

                    {/* Switch */}
                    <Form.Item
                      style={{ marginBottom: '7px' }}
                      label="Include product promotion"
                      name="status"
                      valuePropName="checked"
                    >
                      <Switch
                        onChange={(val) => {
                          setProductPromo(val);

                          if (val && selectedBrand) {
                            setShowValues(true);
                          } else {
                            setShowValues(false);
                          }
                        }}
                      />
                    </Form.Item>

                    {/* Dropdown (Select) Input */}
                    {productPromo && (
                      <Form.Item
                        className="formLabel"
                        // label="Select Category"
                        name="category"
                      >
                        <Select
                          showSearch
                          placeholder="Shopify products"
                          optionFilterProp="children"
                          onChange={onChangeBrandSelect}
                          onFocus={onFocus}
                          onBlur={onBlur}
                          onSearch={onSearch}
                          filterOption={(input, option) =>
                            option.children
                              .toLowerCase()
                              .indexOf(input.toLowerCase()) >= 0
                          }
                        >
                          {shopifyProductState?.products &&
                            shopifyProductState.products.map((data) => (
                              <Option value={data.id}>{data.title}</Option>
                            ))}
                        </Select>
                      </Form.Item>
                    )}

                    <Form.Item
                      style={{
                        alignSelf: 'flex-end',
                        position: 'absolute',
                        right: 40,
                        bottom: 10,
                      }}
                    >
                      <Button
                        type="ai"
                        htmlType="submit"
                        style={{
                          background:
                            'linear-gradient(138deg, rgb(20, 135, 255) 8%, rgb(41, 239, 196) 90%)',
                          borderRadius: '4px',
                          textAlign: 'center',
                          font: 'normal normal medium 14px/17px Rubik',
                          border: 'none',
                          color: '#2A77F4',

                          height: '40px',
                        }}
                      >
                        <p
                          style={{
                            color: '#fff',
                            textAlign: 'center',
                            font: 'normal normal medium 14px/17px Rubik',
                          }}
                        >
                          Generate AI campaign creatives
                        </p>
                      </Button>
                    </Form.Item>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <Form.Item>
                        <Select
                          placeholder="All Genders"
                          mode="multiple"
                          optionFilterProp="children"
                          size="small"
                          style={{
                            minWidth: '15vw',
                          }}
                          onChange={handleGenderChange}
                          maxTagCount="responsive"
                          value={selectedGenders}
                          filterOption={(input, option) =>
                            (option?.label ?? '')
                              .toLowerCase()
                              .includes(input.toLowerCase())
                          }
                          options={[
                            {
                              value: 'male',
                              label: 'Male',
                            },
                            {
                              value: 'female',
                              label: 'Female',
                            },
                          ]}
                        />
                      </Form.Item>
                      <Form.Item>
                        <Select
                          size="small"
                          mode="multiple"
                          style={{
                            minWidth: '15vw',
                          }}
                          placeholder="All Languages"
                          onChange={handleLanguageSelect}
                          maxTagCount="responsive"
                          value={language}
                        >
                          {languageOptions.map((option) => (
                            <Option key={option.id} value={option.id}>
                              {option.lang_name}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </div>
                  </Form>
                </div>
              </div>
              <div style={{ height: 80 }} />
            </div>

            {/* Style the black opacity background */}
            <div
              style={{
                content: '',
                position: 'fixed',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                backgroundColor: 'black',
                opacity: 0.7 /* Adjust the opacity level as needed */,
              }}
            />
          </div>
        )}
        {showPopupCircle && (
          <div
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              alignContent: 'center',
              zIndex: 9999,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              padding: windowSize.width < 768 ? '20px 16px' : '20px',
            }}
          >
            <div
              style={{
                backgroundColor: 'white',
                padding: windowSize.width < 768 ? '32px 24px' : '50px',
                borderRadius: windowSize.width < 768 ? '16px' : '5px',
                boxShadow:
                  windowSize.width < 768
                    ? '0 10px 40px rgba(0, 0, 0, 0.3)'
                    : '0 0 10px rgba(0, 0, 0, 0.3)',
                position: 'absolute',
                zIndex: 999,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flexDirection: 'column',
                width: windowSize.width < 768 ? '90%' : 'auto',
                maxWidth: windowSize.width < 768 ? '400px' : 'none',
              }}
            >
              <p
                style={{
                  fontSize: windowSize.width < 768 ? '18px' : '20px',
                  color: '#2A77F4',
                  marginBottom: windowSize.width < 768 ? '32px' : '50px',
                  textAlign: 'center',
                  lineHeight: '1.4',
                }}
              >
                Our AI is working its magic…
              </p>
              <Spin
                indicator={antIcon}
                style={{
                  alignSelf: 'center',
                  fontSize: windowSize.width < 768 ? '28px' : '24px',
                }}
              />
            </div>
            {/* Style the black opacity background */}
            <div
              style={{
                content: '',
                position: 'fixed',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                backgroundColor: 'black',
                opacity: 0.7 /* Adjust the opacity level as needed */,
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
}

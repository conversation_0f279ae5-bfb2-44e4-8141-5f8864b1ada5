import styled from 'styled-components';

const MarketingNew1Wrapper = styled.div(() => ({
  //   padding: '30px 30px 50px 30px',
  display: 'flex',
  background: '#FAFBFC',
  '& .ai-marketing-left': {
    background: '#FAFBFC',
    '& .header-marketing': {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: '36px',
      '& .header-btns': {
        display: 'flex',
        justifyContent: 'flex-end',
        alignItems: 'center',
        width: '100%',
      },
    },
    '& .box-withoverview': {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      '& .overViewCol': {
        padding: '15px 30px',
        borderRadius: '12px',
      },
      '& .boxRowStyle': {
        width: '100%',
        '& .innerBoxCol': {
          width: '100%',
          '& .boxesStyle': {
            padding: '12px 13px 12px 21px',
            height: 85,
            borderRadius: 12,
            boxShadow: '0px 8px 32px 0px #3326AE14',
            backgroundColor: '#FFFFFF',

            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            '& .leftBoxDiv': {
              '& .innerLeftDiv': {
                display: 'flex',
                flexDirection: 'column',
                gap: '3px',
                '& .label': {
                  '& span': {
                    fontFamily: 'Inter',
                    fontSize: '14px',
                    fontWeight: 500,
                    lineHeight: '22px',
                    textAlign: 'left',
                    textUnderlinePosition: 'from-font',
                    textDecorationSkipInk: 'none',
                    color: '#8E95A9',
                  },
                },
                '& .label-val': {
                  fontFamily: 'Inter',
                  fontSize: '20px',
                  fontWeight: 600,
                  lineHeight: '40px',
                  textAlign: 'left',
                  textUnderlinePosition: 'from-font',
                  textDecorationSkipInk: 'none',
                  color: '#1C2A53',
                },
              },
            },
            '& .rightBoxDiv': {
              display: 'flex',
              flexDirection: 'row',
              gap: '11px',
              '& .innerRightDiv': {
                display: 'flex',
                flexDirection: 'column',
                gap: '3px',
                '& .label': {
                  fontFamily: 'Inter',
                  fontSize: '14px',
                  fontWeight: 700,
                  lineHeight: '22px',
                  textAlign: 'center',
                  textUnderlinePosition: 'from-font',
                  textDecorationSkipInk: 'none',
                },
                '& .label-val': {
                  fontFamily: 'Inter',
                  fontSize: '20px',
                  fontWeight: 600,
                  lineHeight: '40px',
                  textAlign: 'left',
                  textUnderlinePosition: 'from-font',
                  textDecorationSkipInk: 'none',
                  color: '#1C2A53',
                },
              },
            },
          },
        },
      },
      '& .overViewContainer': {
        height: '100%',
        width: '100%',
        padding: '20px 17px 20px 15px',
        borderRadius: '12px',
        // boxShadow:
        //   '2px 1px 10px -10px #ccc, 0px 1px 40px 0px #0D89CF33 inset, 0px 4px 18px 0px #083B584D inset, 0px 98px 100px -48px #00A1FD4D inset, 0px -82px 68px -64px #0E4E724D inset, 0px 7px 11px -4px #FFFFFF inset, 0px 39px 56px -36px #FFFFFF80 inset',
        // backdropFilter: 'blur(25px)',

        textAlign: 'center',
        '& .overViewText': {
          maxWidth: '345px',
          fontFamily: 'Inter',
          fontSize: '11px',
          fontWeight: 400,
          lineHeight: '15.4px',
          textUnderlinePosition: 'from-font',
          textDecorationSkipInk: 'none',
          color: '#FFFFFF',
        },
      },
    },

    '& .cardDetails': {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',

      '& .cardLabel': {
        fontFamily: 'Inter',
        fontSize: '10px',
        fontWeight: 500,
        lineHeight: '22px',
        textAlign: 'left',
        textUnderlinePosition: 'from-font',
        textDecorationSkipInk: 'none',
        color: '#8E95A9',
      },
      '& .cardValue': {
        fontFamily: 'Inter',
        fontSize: 10,
        fontWeight: 700,
        lineHeight: '22px',
        textAlign: 'right',
        textUnderlinePosition: 'from-font',
        textDecorationSkipInk: 'none',
        color: '#8E95A9',
      },
    },
    '& .ant-pagination-disabled .ant-pagination-item-link': {
      border: 'none',
      background: 'none',
    },
    '& .ant-pagination-item-active': {
      background: 'none',
      border: 'none',
      '& a': {
        border: 'none',
        background: 'none',
        fontFamily: 'Inter',
        fontSize: 14,
        fontWeight: 500,
        textAlign: 'left',
        textUnderlinePosition: 'from-font',
        textDecorationSkipInk: 'none',
        color: '#007aff',
      },
    },
    '& .ant-pagination-prev .ant-pagination-item-link, & .ant-pagination-next .ant-pagination-item-link':
      {
        border: 'none',
        background: 'none',
      },
    '& .ant-pagination-item': {
      background: 'none',
      border: 'none',
      // '& a': {
      //   border: 'none',
      //   background: 'none',
      //   fontFamily: 'Inter',
      //   fontSize: 14,
      //   fontWeight: 400,
      //   textAlign: 'left',
      //   textUnderlinePosition: 'from-font',
      //   textDecorationSkipInk: 'none',
      //   color: '#242731',
      // },
    },
  },
  '& .right-title-content': {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    '& .title': {
      fontFamily: 'Inter',
      fontSize: 16,
      fontWeight: 700,
      lineHeight: '24px',
      letterSpacing: '0.1px',
      textAlign: 'left',
      textUnderlinePosition: 'from-font',
      textDecorationSkipInk: 'none',
      color: '#252B42',
    },
  },
  '.ant-menu-inline, .ant-menu-vertical, .ant-menu-vertical-left': {
    borderRight: 'none',
  },
  '& .custom-menu': {
    '& .custom-submenu': {
      '& .ant-menu-submenu-title': {
        padding: '0px !important',
        '& .ant-menu-title-content': {
          fontFamily: 'Inter',
          fontSize: 14,
          fontWeight: 700,
          lineHeight: '24px',
          letterSpacing: '0.10000000149011612px',
          textAlign: 'left',
          textUnderlinePosition: 'from-font',
          textDecorationSkipInk: 'none',
          color: '#252B42',
        },
      },
    },
  },
  '& .ai-filter-checkbox': {
    margin: '10px 13px 0 0',
    '& .ant-checkbox': {
      '& .ant-checkbox-inner': {
        width: 25,
        height: 25,
        borderRadius: '5px',
        border: '1px solid #DCDCDC',
        background: '#FFFFFF',
      },
    },
    '& .ant-checkbox-checked': {
      '& .ant-checkbox-inner': {
        // background: '#007AFF',
        // border: '1px solid #007AFF',
        borderRadius: '5px',
        '&::after': {
          // border: '1.5px solid #141B34',
          borderColor: '#141B34',
          marginLeft: 2,
        },
      },
    },
  },
  '& .custom-slider': {
    '& .ant-slider-track-1': {
      height: 6,
      background: 'linear-gradient(90deg, #04B8FF 0%, #00D9C0 100%)',
    },
    '& .ant-slider-rail': {
      height: 6,
      borderRadius: '20px',
    },
  },
}));

export default MarketingNew1Wrapper;

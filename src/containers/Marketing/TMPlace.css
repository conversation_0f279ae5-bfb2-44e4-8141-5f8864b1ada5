/* TMPlace Responsive Styles */

/* Base responsive grid container */
.responsiveGrid {
  display: flex;
  width: 100%;
  min-height: 100vh;
}

/* Widget spacing for consistent padding */
.widgetSpacing {
  padding: 16px;
}

/* Header controls responsive styling */
.headerControls {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #fff;
}

/* Chart container responsive styling */
.chartContainer {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

/* Mobile breakpoint (< 768px) */
@media (max-width: 767px) {
  .responsiveGrid {
    flex-direction: column;
  }
  
  .widgetSpacing {
    padding: 12px;
  }
  
  .headerControls {
    padding: 16px;
    flex-wrap: wrap;
    gap: 12px;
  }
  
  .chartContainer {
    margin: 8px 0;
    border-radius: 6px;
  }
  
  /* Mobile-specific card grid */
  .mobile-card-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 16px;
  }
  
  /* Mobile touch targets */
  .touch-target {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* Mobile form elements */
  .mobile-form-input {
    min-height: 44px;
    font-size: 16px; /* Prevents zoom on iOS */
  }
  
  .mobile-form-button {
    min-height: 48px;
    width: 100%;
    font-size: 16px;
    font-weight: 500;
  }
  
  /* Mobile popup adjustments */
  .mobile-popup {
    width: 90% !important;
    max-width: 100% !important;
    max-height: 90vh !important;
    overflow-y: auto;
    padding: 20px !important;
  }
  
  /* Mobile filter sidebar */
  .mobile-filter {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100vh !important;
    z-index: 1000 !important;
    background-color: rgba(0,0,0,0.5) !important;
  }
  
  .mobile-filter-content {
    width: 80% !important;
    height: 100vh !important;
    background: #fff !important;
    padding: 16px !important;
    overflow-y: auto !important;
  }
}

/* Tablet breakpoint (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .responsiveGrid {
    flex-direction: row;
  }
  
  .widgetSpacing {
    padding: 16px;
  }
  
  .headerControls {
    padding: 18px;
  }
  
  .chartContainer {
    margin: 12px 0;
    border-radius: 8px;
  }
  
  /* Tablet card grid */
  .tablet-card-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 16px;
  }
  
  /* Tablet form elements */
  .tablet-form-input {
    min-height: 40px;
    font-size: 14px;
  }
  
  .tablet-form-button {
    min-height: 44px;
    font-size: 14px;
  }
  
  /* Tablet popup adjustments */
  .tablet-popup {
    width: 70% !important;
    padding: 30px !important;
  }
}

/* Desktop breakpoint (>= 1024px) */
@media (min-width: 1024px) {
  .responsiveGrid {
    flex-direction: row;
  }
  
  .widgetSpacing {
    padding: 20px;
  }
  
  .headerControls {
    padding: 20px;
  }
  
  .chartContainer {
    margin: 16px 0;
    border-radius: 8px;
  }
  
  /* Desktop card grid */
  .desktop-card-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    padding: 20px 10px;
  }
  
  /* Desktop form elements */
  .desktop-form-input {
    min-height: 36px;
    font-size: 14px;
  }
  
  .desktop-form-button {
    min-height: 40px;
    font-size: 14px;
  }
  
  /* Desktop popup adjustments */
  .desktop-popup {
    width: auto !important;
    padding: 50px !important;
  }
}

/* Utility classes for responsive behavior */
.flex-responsive {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.grid-responsive {
  display: grid;
  gap: 12px;
}

.text-responsive {
  font-size: clamp(14px, 2.5vw, 18px);
  line-height: 1.4;
}

.spacing-responsive {
  margin: clamp(8px, 2vw, 16px) 0;
  padding: clamp(8px, 2vw, 16px);
}

/* Animation for smooth transitions */
.responsive-transition {
  transition: all 0.3s ease-in-out;
}

/* Focus states for accessibility */
.touch-target:focus,
.mobile-form-input:focus,
.tablet-form-input:focus,
.desktop-form-input:focus {
  outline: 2px solid #2A77F4;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .headerControls,
  .chartContainer,
  .widgetSpacing {
    border: 1px solid #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .responsive-transition {
    transition: none;
  }
}

/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable  react/jsx-no-bind */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
/* eslint-disable no-unused-expressions */
// eslint-disable-next-line
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable  jsx-a11y/no-static-element-interactions */
/* eslint-disable react/jsx-no-bind */
/* eslint-disable no-shadow */
/* eslint-disable no-param-reassign */
/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable  no-restricted-globals */

import { Row, Col, InputNumber, Space, Select, Image, Form } from 'antd';
import React, { useState } from 'react';
import BigPoster from '@chill/assets/images/big poster.svg';
import Bottomlign from '@chill/assets/images/bottom align.svg';
import TopAlign from '@chill/assets/images/top align.svg';
import MiddleAlign from '@chill/assets/images/middle align.svg';
import Custom from '@chill/assets/images/Custom.svg';
import FullScreen from '@chill/assets/images/full screen.svg';
import Facebook from '@chill/assets/images/facebook.svg';
import Instagram from '@chill/assets/images/instagram.svg';
import Standerd from '@chill/assets/images/standard.svg';
import RotateLeft from '@chill/assets/images/rotate left.svg';
import IntlMessages from '@chill/components/utility/intlMessages';

const ArtBoardSizePop = ({ onCardSelect }, prop) => {
  const [selectedCard, setSelectedCard] = useState(null);
  const [isClicked, setIsClicked] = useState(false);
  const [isClicked1, setIsClicked1] = useState(false);
  const [isCustomCard, setIsCustomCard] = useState(false);
  const [width, setWidth] = useState(280); // Default width value
  const [height, setHeight] = useState(720); // Default height value

  const cards = [
    {
      id: '1',
      icon: <Image preview={false} src={Standerd} />,
      description: 'Standard',
      description1: '720 X 720 px',
    },
    {
      id: '2',
      icon: <Image preview={false} src={FullScreen} />,
      description: 'Full screen',
      description1: '1280 X 720 px',
    },
    {
      id: '3',
      icon: <Image preview={false} src={TopAlign} />,
      description: 'Top alignment',
      description1: '280 X 720 px',
    },
    {
      id: '4',
      icon: <Image preview={false} src={MiddleAlign} />,
      description: 'Centre',
      description1: '280 X 720 px',
    },
    {
      id: '5',
      icon: <Image preview={false} src={Bottomlign} />,
      description: 'Bottom alignment',
      description1: '280 X 720 px',
    },
    {
      id: '6',
      icon: <Image preview={false} src={Custom} />,
      description: 'Custom',
      // description1: "16'' X 20''",
    },
    // {
    //   id: '7',
    //   icon: <Image preview={false} src={BigPoster} />,
    //   description: 'Poster Big',
    //   description1: "24'' X 36''",
    // },
    // {
    //   id: '8',
    //   icon: <Image preview={false} src={Instagram} />,
    //   description: 'Instagram Post',
    //   description1: '1080 X 1080 px',
    // },
    // {
    //   id: '9',
    //   icon: <Image preview={false} src={Facebook} />,
    //   description: 'Facebook Post',
    //   description1: '1200 X 1200 px',
    // },
  ];

  const onChangeWidth = (value) => {
    setWidth(value);
    localStorage.setItem('customWidth', value);
  };

  const onChangeHeight = (value) => {
    setHeight(value);
    localStorage.setItem('customHeight', value);
  };

  const onChange = (value) => {
    console.log('changed value', value);
  };

  const handleChange = (value) => {
    console.log(`selected ${value}`);
    localStorage.setItem('selectedPlacement', value);
  };

  const handleCardClick = (id) => {
    const clickedCard = cards.find((card) => card.id === id);
    if (clickedCard) {
      setSelectedCard(clickedCard.id);
      onCardSelect(clickedCard.description);
      setIsCustomCard(clickedCard.id === '6');

      if (clickedCard.description1) {
        const [newWidth, newHeight] = clickedCard.description1.split(' X ');
        setWidth(parseInt(newWidth, 10));
        setHeight(parseInt(newHeight, 10));
      }
    }
  };

  const handleClick = () => {
    setIsClicked(true);
    setIsClicked1(false);
  };

  const handleClick1 = () => {
    setIsClicked(false);
    setIsClicked1(true);
  };

  const {
    state,
    setState = () => {},
    propIntl,
    initVal,
    submitUser = () => {},
    segmentList,
    form,
  } = prop;

  const { Option } = Select;

  function onChangeSelect(value) {
    console.log(`selected ${value}`);
    localStorage.setItem('selectedTrigger', value);
    setState((p) => ({ ...p, sendTo: value }));
    let count = 0;
    console.log('log ==>', value);
    if (value) {
      count = segmentList && find(segmentList, (o) => o.id === value);
    }
    setState((p) => ({
      ...p,
      userCount: count?.userCount ? count.userCount : 0,
    }));
  }

  function onBlur() {
    console.log('blur');
  }

  function onFocus() {
    console.log('focus');
  }

  function onSearch(val) {
    console.log('search:', val);
  }

  const audienceType = localStorage.getItem('audienceType');

  return (
    <div>
      <div style={{ display: 'flex' }}>
        <div style={{ flex: '0 0 55%' }}>
          <h6
            style={{
              fontSize: '14px',
              textAlign: 'left',
              color: '#0A0606',
            }}
          >
            ARTBOARD SIZE PRESETS
          </h6>
          <Row gutter={[8, 24]} style={{ marginTop: '20px' }}>
            {cards.map((card) => (
              <Col key={card.id} span={12}>
                <button
                  type="submit"
                  style={{
                    border: 'none',
                    background: 'none',
                    padding: '0',
                  }}
                  onClick={() => handleCardClick(card.id)}
                >
                  <div
                    style={{
                      height: '20vh',
                      width: '13vw',
                      background:
                        selectedCard === card.id ? '#ffffff' : '#EFEFEF',
                      borderRadius: '4px',
                      alignItems: 'center',
                      justifyContent: 'center',
                      display: 'flex',
                      flexDirection: 'column',
                      cursor: 'pointer',
                      userselect: 'none',
                      boxShadow:
                        selectedCard === card.id
                          ? '0px 4px 8px rgba(0, 0, 0, 0.2)'
                          : 'none',
                      border:
                        selectedCard === card.id ? '1px solid #EFEFEF' : 'none',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                      }}
                    >
                      <p
                        style={{
                          filter:
                            selectedCard === card.id
                              ? 'sepia(100%) hue-rotate(200deg) saturate(10000%) brightness(80%)'
                              : 'brightness(0) saturate(100%) invert(79%) sepia(7%) hue-rotate(231deg) brightness(102%) contrast(101%)',

                          letterSpacing: '0px',
                          fontSize: '20px',
                          color:
                            selectedCard === card.id ? '#007AFF' : 'inherit',
                        }}
                      >
                        {card.icon}
                      </p>

                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                        }}
                      >
                        <h5
                          style={{
                            font: 'Rubik',
                          }}
                        >
                          {card.description}
                        </h5>
                        <h6
                          style={{
                            color:
                              selectedCard === card.id ? '#007AFF' : '#A9ABBC',
                          }}
                        >
                          {card.description1}
                        </h6>
                      </div>
                    </div>
                  </div>
                </button>
              </Col>
            ))}
          </Row>
        </div>
        <div style={{ flex: '0 0 45%' }}>
          <h6
            style={{
              fontSize: '14px',
              textAlign: 'left',
              color: '#0A0606',
            }}
          >
            ARTBOARD SIZE PRESETS
          </h6>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              marginTop: '20px',
            }}
          >
            <div>
              <h5
                style={{
                  fontSize: '14px',
                  color: '#0A0606',
                }}
              >
                WIDTH
              </h5>
              <Space>
                <InputNumber
                  disabled={!isCustomCard}
                  min={1}
                  max={10000}
                  // defaultValue={200}
                  value={width} // Use the width state here
                  onChange={onChangeWidth}
                  style={{
                    width: '12vw',
                    borderRadius: '0px',
                    color: '#C4C4C4',
                    marginTop: '8px',
                  }}
                />
              </Space>
            </div>
            <div>
              <h5
                style={{
                  fontSize: '14px',
                  color: '#0A0606',
                }}
              >
                HEIGHT
              </h5>
              <Space>
                <InputNumber
                  disabled={!isCustomCard}
                  min={1}
                  max={10000}
                  // defaultValue={380}
                  value={height} // Use the height state here
                  onChange={onChangeHeight}
                  style={{
                    width: '12vw',
                    borderRadius: '0px',
                    color: '#C4C4C4',
                    marginTop: '8px',
                  }}
                />
              </Space>
            </div>
          </div>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              marginTop: '20px',
            }}
          >
            {/* <div>
              <h5
                style={{
                  fontSize: '14px',
                  color: '#0A0606',
                }}
              >
                UNITS
              </h5>
              <Space>
                <InputNumber
                  disabled
                  min={1}
                  max={100000}
                  defaultValue="Pixel (px)"
                  onChange={onChange}
                  style={{
                    width: '12vw',
                    borderRadius: '0px',
                    color: '#C4C4C4',
                    marginTop: '8px',
                  }}
                />
              </Space>
            </div>
            <div>
              <h5
                style={{
                  fontSize: '14px',
                  color: '#0A0606',
                }}
              >
                DPI
              </h5>
              <Space>
                <InputNumber
                  disabled
                  min={1}
                  max={100000}
                  defaultValue={72}
                  onChange={onChange}
                  style={{
                    width: '12vw',
                    borderRadius: '0px',
                    color: '#C4C4C4',
                    marginTop: '8px',
                  }}
                />
              </Space>
            </div> */}
          </div>
          <div style={{ marginTop: '20px' }}>
            <h5
              style={{
                color: '#0A0606',
                fontSize: '14px',
                letterSpacing: '0',
                opacity: '1',
              }}
            >
              ORIENTATION
            </h5>
            <div style={{ display: 'flex', gap: '15px' }}>
              <div
                style={{
                  borderRadius: '4px',
                  background: isClicked ? '#007AFF' : '#fff',
                  height: '6.5vh',
                  width: '3vw',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginTop: '15px',
                  cursor: 'pointer',
                }}
                onClick={handleClick}
              >
                <Image
                  src={RotateLeft}
                  preview={false}
                  style={{
                    fontSize: '20px',
                    transform: 'rotate(90deg)',
                    filter: isClicked
                      ? ' brightness(0) saturate(100%) invert(100%)'
                      : 'none',
                  }}
                />
              </div>
              <div
                style={{
                  borderRadius: '4px',
                  background: isClicked1 ? '#007AFF' : '#fff',
                  height: '6.5vh',
                  width: '3vw',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginTop: '15px',
                  cursor: 'pointer',
                }}
                onClick={handleClick1}
              >
                <Image
                  src={RotateLeft}
                  preview={false}
                  style={{
                    fontSize: '20px',
                    filter: isClicked1
                      ? ' brightness(0) saturate(100%) invert(100%) '
                      : 'none',
                  }}
                />
              </div>
            </div>
          </div>
          <div style={{ marginTop: '20px' }}>
            {audienceType === 'in_app_message' ? (
              <div>
                <h5
                  style={{
                    color: '#0A0606',
                    fontSize: '14px',
                    letterSpacing: '0',
                    opacity: '1',
                  }}
                >
                  PLACEMENT
                </h5>
                <Space style={{ marginTop: '8px' }}>
                  <Select
                    style={{
                      width: 300,
                    }}
                    onChange={handleChange}
                    options={[
                      {
                        value: 'Opening screen',
                        label: 'Opening screen',
                      },
                      {
                        value: 'Home screen',
                        label: 'Home screen',
                      },
                      {
                        value: 'Dashboard',
                        label: 'Dashboard',
                      },
                      {
                        value: 'Child profile',
                        label: 'Child profile',
                      },
                      {
                        value: 'Settings',
                        label: 'Settings',
                      },
                    ]}
                  />
                </Space>
              </div>
            ) : null}
            <h5
              style={{
                color: '#0A0606',
                fontSize: '14px',
                letterSpacing: '0',
                opacity: '1',

                textTransform: 'uppercase',
                marginTop: '20px',
              }}
            >
              <IntlMessages id="marketing.triggers" />
            </h5>

            <Select
              style={{ width: 300, display: 'block', marginTop: '8px' }}
              showSearch
              placeholder={<IntlMessages id="marketing.messageTriggers" />}
              optionFilterProp="children"
              onChange={onChangeSelect}
              onFocus={onFocus}
              onBlur={onBlur}
              onSearch={onSearch}
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              allowClear
            >
              <Option value="alertsBased">Alerts based</Option>
              <Option value="environmentBased">Environment based</Option>
              <Option value="demographicsBased">Demographics based</Option>
              <Option value="productBased">Product based</Option>
            </Select>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ArtBoardSizePop;

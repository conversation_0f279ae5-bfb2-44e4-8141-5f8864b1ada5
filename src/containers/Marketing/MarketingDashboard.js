/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable react/jsx-no-bind */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
/* eslint-disable no-unused-expressions */
import React, { useEffect, useState } from 'react';
import { Row, Col, Spin, Button, Image } from 'antd';
import moment from 'moment';
import Chart from 'react-google-charts';
import { PDFDownloadLink, PDFViewer } from '@react-pdf/renderer';
import LayoutWrapper from '@chill/components/utility/layoutWrapper';
import basicStyle from '@chill/assets/styles/constants';
import IntlMessages from '@chill/components/utility/intlMessages';
import { isArray, isEmpty, isObject, isNull, toNumber } from 'lodash';
import getApiData from '@chill/lib/helpers/apiHelper';
import qs from 'query-string';
import { useLocation } from 'react-router-dom';
import { injectIntl } from 'react-intl';
import { useSelector } from 'react-redux';
import theme from '@chill/config/theme/default';
import LinkBox from '@chill/components/LinkBox';
import {
  DownloadOutlined,
  DownOutlined,
  LeftCircleOutlined,
} from '@ant-design/icons';
import { Column } from '@ant-design/charts';
import Popover from '@chill/components/uielements/popover';
import Notification from '@chill/components/Notification';
import MarketingReport from '@chill/components/MarketingReport';
import exchangeSVG from '@chill/assets/images/exchange.svg';
import speechBubleSVG from '@chill/assets/images/speech-bubble.svg';
import goalSVG from '@chill/assets/images/goal.svg';
import percentSVG from '@chill/assets/images/percentage.svg';
import IsoWidgetsWrapper from '../Widgets/WidgetsWrapper';
import IsoWidgetBox from '../Widgets/WidgetBox';
import ReportsWidget from '../Widgets/Report/ReportWidget';
import StickerWidget from '../Widgets/Sticker/StickerWidget';
import {
  MarketingDashboardWrapper,
  WidgetWrapper,
} from '../Widgets/Widgets.styles';
import { styles } from '../Widgets/config';

function Widgets({ visible, setDashboardVisible, initialValues }) {
  // const { intl } = props;
  const location = useLocation();
  const pObj = qs.parse(location.search || '');
  const isTeamUser = pObj.user_id && pObj.email;

  const { rowStyle, colStyle } = basicStyle;

  const access = useSelector((state) => state.Auth.access);
  const accLoading = access.loading || false;
  const [overviewVisible, setOverViewVisibility] = useState(false);
  const [durationVisible, setDurationVisibility] = useState(false);
  const [userVisible, setUserVisible] = useState(false);
  const [duration, setDuration] = useState(
    <IntlMessages id="dashboard.thisweek" />,
  );
  const [overView, setOverView] = useState('OUTCOME STATISTICS');
  const [campaignData, setCampaignData] = useState({});
  const [pageLoad, setPageLoad] = useState(true);
  const { language } = useSelector((st) => st.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);
  const [cperiodType, setCPeriodType] = useState('this_week');

  const marketingBoxes = [
    {
      key: 'sentMessages',
      text: 'marketing.sentMessages',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(campaignData) ? campaignData.getSentMessages : 0,
      img: speechBubleSVG,
    },
    {
      key: 'totalClicks',
      text: 'marketing.totalClicks',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(campaignData) ? campaignData.totalClicks : 0,
      img: goalSVG,
    },
    {
      key: 'totalShares',
      text: 'marketing.totalShares',
      link: '/contacts',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(campaignData) ? campaignData.totalShare : 0,
      img: percentSVG,
    },
    {
      key: 'totalSales',
      text: 'marketing.totalSales',
      link: '/manage-tags',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(campaignData) ? campaignData.sale : 0,
      img: exchangeSVG,
    },
    {
      key: 'conversionRate',
      text: 'marketing.conversionRate',
      link: '/manage-tags',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(campaignData) ? campaignData.rate : 0,
      img: exchangeSVG,
    },
  ];

  function overViewChange() {
    setOverViewVisibility((visiblePre) => !visiblePre);
  }

  function durationChange(type) {
    if (type === 'user') {
      setUserVisible((visiblePre) => !visiblePre);
    } else {
      setDurationVisibility((visiblePre) => !visiblePre);
    }
  }

  /**
   * Function to fetch campaign data
   */
  async function getCampaignDetail() {
    try {
      const response = await getApiData(
        'campaigns/get-campaign-data',
        {
          type: 'campaign',
          campaign_id: initialValues.id,
          period_type: cperiodType,
        },
        'POST',
      );
      if (
        response.success &&
        isObject(response.data) &&
        !isEmpty(response.data)
      ) {
        setCampaignData(response.data);
        setPageLoad(false);
      } else {
        setPageLoad(false);
        Notification('error', response.message || messageArray['err.wenWrong']);
      }
    } catch (error) {
      console.log('error ===', error);
      setPageLoad(false);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  useEffect(() => {
    if (visible) {
      getCampaignDetail();
    }
  }, [visible, cperiodType]);

  const customerOverviewContent = (
    <WidgetWrapper>
      <div className="popMainDiv">
        <div
          className="isoDropdownLink"
          onClick={() => {
            setOverView('OUTCOME STATISTICS');
            setOverViewVisibility(!overviewVisible);
          }}
          role="button"
          onKeyPress={() => {
            setOverView('OUTCOME STATISTICS');
            setOverViewVisibility(!overviewVisible);
          }}
          tabIndex="-1"
        >
          OUTCOME STATISTICS
        </div>
        {/* <div
          className="isoDropdownLink"
          onClick={() => {
            setOverView('Overview 1');
            setOverViewVisibility(!overviewVisible);
          }}
          role="button"
          onKeyPress={() => {
            setOverView('Overview 1');
            setOverViewVisibility(!overviewVisible);
          }}
          tabIndex="-1"
        >
          Overview 1
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setOverView('Overview 2');
            setOverViewVisibility(!overviewVisible);
          }}
          role="button"
          onKeyPress={() => {
            setOverView('Overview 2');
            setOverViewVisibility(!overviewVisible);
          }}
          tabIndex="-1"
        >
          Overview 2
        </div> */}
      </div>
    </WidgetWrapper>
  );

  const config = {
    data: (!isEmpty(campaignData) && campaignData?.outComeChartData) || [],
    xField: 'month',
    yField: 'value',
    isGroup: true,
    isStack: true,
    seriesField: 'type',
    groupField: 'name',
    shape: 'diamon',
    color: ['#80d8ff', '#e57373', '#2962ff'],
    legend: {
      position: 'bottom',
      shape: 'diamon',
      textStyle: {
        color: '#233238',
        fontSize: 14,
      },
    },
    yAxis: {
      grid: {
        line: null,
      },
      label: null,
    },
  };

  const customerDurationContent = (
    <WidgetWrapper>
      <div className="popMainDiv">
        <div
          className="isoDropdownLink"
          onClick={() => {
            setDuration(<IntlMessages id="dashboard.thisweek" />);
            setDurationVisibility(!durationVisible);
            setCPeriodType('this_week');
          }}
          role="button"
          onKeyPress={() => {
            setDuration(<IntlMessages id="dashboard.thisweek" />);
            setDurationVisibility(!durationVisible);
            setCPeriodType('this_week');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.thisweek" />
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setDuration('Last Week');
            setDurationVisibility(!durationVisible);
            setCPeriodType('week');
          }}
          role="button"
          onKeyPress={() => {
            setDuration('Last Week');
            setDurationVisibility(!durationVisible);
            setCPeriodType('week');
          }}
          tabIndex="-1"
        >
          Last Week
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setDuration('Last Month');
            setDurationVisibility(!durationVisible);
            setCPeriodType('month');
          }}
          role="button"
          onKeyPress={() => {
            setDuration('Last Month');
            setDurationVisibility(!durationVisible);
            setCPeriodType('month');
          }}
          tabIndex="-1"
        >
          Last Month
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setDuration('Last 6 Month');
            setDurationVisibility(!durationVisible);
            setCPeriodType('six_month');
          }}
          role="button"
          onKeyPress={() => {
            setDuration('Last 6 Month');
            setDurationVisibility(!durationVisible);
            setCPeriodType('six_month');
          }}
          tabIndex="-1"
        >
          Last 6 Months
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setDuration('Last Year');
            setDurationVisibility(!durationVisible);
            setCPeriodType('year');
          }}
          role="button"
          onKeyPress={() => {
            setDuration('Last Year');
            setDurationVisibility(!durationVisible);
            setCPeriodType('year');
          }}
          tabIndex="-1"
        >
          Last Year
        </div>
      </div>
    </WidgetWrapper>
  );
  /**
   * Function to render Top 4 boxes
   */
  function renderTopBoxes() {
    return (
      <Row style={rowStyle} gutter={0} justify="start">
        {marketingBoxes.map((widget) => {
          // const val = getObjVal(dashData, widget.key);
          // const lnk = widget.link ? `/dashboard${widget.link}` : '';
          return (
            <Col
              lg={6}
              md={6}
              sm={12}
              xs={24}
              style={colStyle}
              key={widget.text}
            >
              <IsoWidgetsWrapper>
                <LinkBox link="">
                  <StickerWidget
                    number={widget.count}
                    text={<IntlMessages id={widget.text} />}
                    icon={widget.icon}
                    fontColor={widget.fontColor}
                    bgColor={widget.bgColor}
                    image={widget.img}
                  />
                </LinkBox>
              </IsoWidgetsWrapper>
            </Col>
          );
        })}
      </Row>
    );
  }

  console.log('initialValues ==>', initialValues);
  if (visible) {
    return (
      <LayoutWrapper>
        <MarketingDashboardWrapper>
          <div
            className="thisAc"
            style={{ paddingTop: 16, alignItems: 'center', display: 'flex' }}
          >
            <div className="iconStyle">
              <span style={{ cursor: 'pointer' }}>
                <div
                  onClick={() => {
                    setDashboardVisible(false);
                  }}
                  role="button"
                  tabIndex={-1}
                  onKeyPress={() => {
                    setDashboardVisible(false);
                  }}
                  className="createSegmentP"
                  style={{
                    color: theme.colors.primaryColor,
                    display: 'flex',
                  }}
                >
                  <p className="iconPadding" style={{ paddingRight: '10px' }}>
                    <LeftCircleOutlined />
                  </p>
                  <IntlMessages id="common.goBack" />
                </div>
              </span>
            </div>
            <PDFDownloadLink
              document={
                <MarketingReport
                  initialValues={initialValues}
                  campaignData={campaignData}
                  language={language}
                  visible={visible}
                />
              }
              fileName={`Marketing Report(${moment().format(
                'DD MMM YYYY',
              )}).pdf`}
            >
              {({ blob, url, loading, error }) => (
                <Button type="primary" htmlType="submit" className="changeBtn">
                  <DownloadOutlined
                    style={{
                      fontSize: window.innerWidth > 281 ? 20 : 15,
                      marginRight: window.innerWidth > 280 ? 10 : 0,
                    }}
                  />
                  <IntlMessages id="common.report" />
                </Button>
              )}
            </PDFDownloadLink>
            {/* <PDFViewer width={500} height={500}>
              <MarketingReport
                initialValues={initialValues}
                campaignData={campaignData}
                language={language}
                visible={visible}
              />
            </PDFViewer> */}
          </div>
          <Spin spinning={pageLoad}>
            <div style={styles.wisgetPageStyle}>
              <Row gutter={0} style={{ ...rowStyle }}>
                <Col sm={24} lg={16} md={24} xl={16}>
                  {renderTopBoxes()}
                  <Row style={{ ...rowStyle }} gutter={0} justify="start">
                    <Col lg={24} md={24} sm={24} xs={24} style={colStyle}>
                      <IsoWidgetsWrapper style={{ height: '100%' }}>
                        <ReportsWidget
                          widgetClassName="mb0"
                          labelClsName="mb15"
                          style={{
                            height: '100%',
                            borderRadius: 25,
                            boxShadow: '5px 5px 5px 5px #0000',
                          }}
                        >
                          <div
                            style={{
                              display: 'flex',
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                              marginBottom: 16,
                            }}
                          >
                            <Popover
                              content={customerOverviewContent}
                              trigger="click"
                              visible={overviewVisible}
                              onVisibleChange={overViewChange}
                              placement="bottomLeft"
                            >
                              <h4
                                style={{
                                  cursor: 'pointer',
                                  fontWeight: '400',
                                  fontSize:
                                    window.innerWidth <= 400 ? '12px' : '15px',
                                }}
                              >
                                {overView}
                                <DownOutlined
                                  style={{
                                    marginLeft:
                                      window.innerWidth <= 400 ? 5 : 10,
                                    fontSize:
                                      window.innerWidth <= 400 ? 12 : 15,
                                  }}
                                />
                              </h4>
                            </Popover>
                            <Popover
                              content={customerDurationContent}
                              trigger="click"
                              visible={durationVisible}
                              onVisibleChange={durationChange}
                              placement="bottomRight"
                            >
                              <p
                                style={{
                                  cursor: 'pointer',
                                  fontSize:
                                    window.innerWidth <= 400 ? '12px' : '15px',
                                }}
                              >
                                {duration}{' '}
                                <DownOutlined style={{ marginLeft: 5 }} />
                              </p>
                            </Popover>
                          </div>
                          <Spin spinning={accLoading}>
                            <Column
                              {...config}
                              style={{
                                height: 200,
                                width: '100%',
                                marginTop: 20,
                              }}
                            />
                          </Spin>
                        </ReportsWidget>
                      </IsoWidgetsWrapper>
                    </Col>
                  </Row>
                </Col>
                <Col lg={8} xl={8} md={24} sm={24} xs={24} style={colStyle}>
                  {/* <Col lg={8} md={24} sm={12} xs={24} style={colStyle}> */}
                  <IsoWidgetsWrapper style={{ height: '100%' }}>
                    <ReportsWidget
                      label={<IntlMessages id="analytics.operatingSystem" />}
                      style={{
                        height: '100%',
                        borderRadius: 25,
                        boxShadow: '5px 5px 5px 5px #0000',
                      }}
                      widgetClassName="flex1"
                    >
                      <div
                        style={{
                          width: '100%',
                          alignItems: 'center',
                          textAlign: 'center',
                          justifyContent: 'center',
                          display: 'flex',
                        }}
                      >
                        <Chart
                          width="300px"
                          height="300px"
                          chartType="PieChart"
                          loader={
                            <div>
                              <IntlMessages id="LoadingChart" />
                            </div>
                          }
                          data={
                            isObject(campaignData) &&
                            !isEmpty(campaignData) &&
                            isArray(campaignData.operatingSystemChart)
                              ? campaignData.operatingSystemChart
                              : []
                          }
                          options={{
                            pieHole: 0.8,
                            legend: { position: 'bottom' },
                            pieSliceText: 'none',
                            slices: {
                              0: { color: '#1172EC' },
                              1: { color: '#EF7D7A' },
                            },
                            chartArea: {
                              width: '100%',
                              height: '80%',
                            },
                          }}
                        />
                      </div>
                    </ReportsWidget>
                  </IsoWidgetsWrapper>
                  {/* </Col> */}
                </Col>
              </Row>
              <Row style={rowStyle} gutter={0} justify="start">
                <Col lg={24} md={24} sm={24} xs={24} style={colStyle}>
                  <IsoWidgetsWrapper
                    style={{
                      height: '100%',
                    }}
                  >
                    <ReportsWidget
                      style={{
                        overflow: 'hidden',
                        height: '100%',
                        borderRadius: 25,
                        boxShadow: '5px 5px 5px 5px #0000',
                      }}
                    >
                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          marginBottom: 16,
                        }}
                      >
                        <h4
                          style={{
                            fontWeight: '400',
                            fontSize:
                              window.innerWidth <= 400 ? '14px' : '15px',
                          }}
                        >
                          <IntlMessages id="DETAILS" />
                        </h4>
                      </div>
                      <Row style={{ marginTop: 20 }}>
                        <Col
                          sm={24}
                          lg={16}
                          md={16}
                          xl={16}
                          xs={24}
                          style={{ paddingRight: '40px' }}
                        >
                          <Row className="marginWrapper">
                            <Col sm={12} lg={12} md={12} xl={12} xs={12}>
                              <p className="padding title">
                                <IntlMessages id="CAMPAIGNNAME" />
                              </p>
                              <p className="padding subTitle">
                                {initialValues.campaign_name}
                              </p>
                            </Col>
                            <Col sm={12} lg={12} md={12} xl={12} xs={12}>
                              <p className="padding title">
                                <IntlMessages id="CAMPAIGNTITLE" />
                              </p>
                              <p className="padding subTitle">
                                {initialValues.post_title}
                              </p>
                            </Col>
                          </Row>
                          <Row className="marginWrapper">
                            <Col sm={24} lg={24} md={24} xl={24} xs={24}>
                              <p className="padding title">
                                <IntlMessages id="DESCRIPTION" />
                              </p>
                              <p className="padding description">
                                {initialValues.description}
                              </p>
                            </Col>
                          </Row>
                          <Row className="marginWrapper">
                            <Col sm={24} lg={12} md={12} xl={12} xs={24}>
                              <p className="padding title">
                                <IntlMessages id="common.tags" />
                              </p>
                              <div style={{ display: 'flex' }}>
                                {/* {initialValues.campaign_tags} */}
                                {!isEmpty(initialValues) &&
                                  isArray(initialValues.campaign_tags) &&
                                  initialValues.campaign_tags.map((data) => {
                                    return (
                                      <div
                                        className="topLinkCountWrapper"
                                        style={{ marginRight: 5 }}
                                      >
                                        {data}
                                      </div>
                                    );
                                  })}
                              </div>
                            </Col>
                            <Col
                              sm={24}
                              lg={12}
                              md={12}
                              xl={12}
                              xs={24}
                              style={{
                                marginTop:
                                  window.innerWidth <= 480 ? '15px' : '',
                              }}
                            >
                              <p className="padding title">
                                <IntlMessages id="CAMPAIGNTYPE" />
                              </p>
                              <p className="padding subTitle">
                                {initialValues.campaign_type}
                              </p>
                            </Col>
                          </Row>
                          <Row className="marginWrapper">
                            <Col sm={24} lg={8} md={8} xl={8} xs={24}>
                              <p className="padding title">
                                <IntlMessages id="marketing.SEGMENTGROUP" />
                              </p>
                              <p className="padding subTitle">{`${
                                initialValues.segment_name
                              } (${initialValues.user_count || 0})`}</p>
                            </Col>
                            <Col
                              sm={12}
                              lg={8}
                              md={8}
                              xl={8}
                              xs={12}
                              style={{
                                marginTop:
                                  window.innerWidth <= 480 ? '15px' : '',
                              }}
                            >
                              <p className="padding title">
                                <IntlMessages id="antTable.title.startDate" />
                              </p>
                              <p className="padding subTitle">
                                {initialValues.start_date}
                              </p>
                            </Col>
                            <Col
                              sm={12}
                              lg={8}
                              md={8}
                              xl={8}
                              xs={12}
                              style={{
                                marginTop:
                                  window.innerWidth <= 480 ? '15px' : '',
                              }}
                            >
                              <p className="padding title">
                                <IntlMessages id="antTable.title.endDate" />
                              </p>
                              <p className="padding subTitle">
                                {initialValues.end_date}
                              </p>
                            </Col>
                          </Row>
                        </Col>
                        <Col sm={24} lg={8} md={8} xl={8} xs={24}>
                          <IsoWidgetsWrapper
                            style={{
                              height: '100%',
                            }}
                          >
                            <IsoWidgetBox
                              style={{
                                borderRadius: 25,
                                boxShadow: '5px 5px 5px 5px #0000',
                                backgroundColor: '#ddd',
                                color: '#fff',
                              }}
                            >
                              {/* <div className="previewText">
                                <Row>
                                  <div className="postCircle" />
                                  <p
                                    className="padding title"
                                    style={{ color: '#000' }}
                                  >
                                    Brand Name
                                  </p>
                                </Row>
                                <p className="padding subTitle">
                                  {initialValues.post_title}
                                </p>
                                <p className="padding message">
                                  {initialValues.description}
                                </p>
                              </div> */}
                              {initialValues.campaign_type ===
                              'in_app_message' ? (
                                <div
                                  className="previewText"
                                  style={{ padding: '10px 0px 10px' }}
                                >
                                  <div style={{ width: '100%' }}>
                                    <p
                                      className="title"
                                      style={{
                                        fontSize: 16,
                                        textAlign: 'center',
                                        width: '100%',
                                        color: '#000',
                                        padding: 6,
                                      }}
                                    >
                                      {initialValues.post_title || 'Heading'}
                                    </p>
                                  </div>
                                  <Image
                                    src={initialValues.post_file}
                                    style={{ width: '100%', height: '10%' }}
                                  />
                                  <div>
                                    {initialValues.button_info.map(
                                      (data, index) => (
                                        // <div key={data?.id} className="btnCenter">
                                        <Button
                                          className="previewBtn"
                                          style={{
                                            fontSize: toNumber(
                                              data.button_text_size,
                                            ), // || state.buttonFontSize,
                                            backgroundColor:
                                              data.background_color, // || state.backColor,
                                            color: data.button_text_color, // || state.buttonTextColor,
                                            textAlign: data.button_text_align, // || state.buttonTextAlign,
                                            width: '92%',
                                            borderRadius: 10,
                                            alignSelf: 'center',
                                            marginInline: 12,
                                          }}
                                        >
                                          <IntlMessages
                                            id={`${
                                              data.button_text || 'Button'
                                            }`}
                                          />
                                        </Button>
                                        // </div>
                                      ),
                                    )}
                                  </div>
                                </div>
                              ) : initialValues.campaign_type ===
                                'push_message' ? (
                                <div
                                  className="previewText"
                                  style={{ padding: '10px 0px 10px' }}
                                >
                                  <div
                                    className="pushPreviewText"
                                    style={{ width: '100%' }}
                                  >
                                    <Row
                                      style={{
                                        marginBottom: 10,
                                        paddingLeft: 10,
                                        paddingTop: 10,
                                        alignItems: 'flex-start',
                                      }}
                                    >
                                      {!isNull(initialValues.icon_url) ? (
                                        <img
                                          src={initialValues.icon_url}
                                          style={{
                                            width: 30,
                                            height: 30,
                                            backgroundColor: '#eee',
                                            marginRight: 12,
                                            borderRadius: 30,
                                          }}
                                          alt=""
                                        />
                                      ) : (
                                        <div className="postCircle" />
                                      )}
                                      <p
                                        className="title"
                                        style={{
                                          width: '86%',
                                          paddingLeft: 12,
                                        }}
                                      >
                                        {/* {type === 'feed'
                                        ? state.postTitle
                                        : state.postTitlePushMsg ||
                                          'Post Title'} */}
                                        {initialValues.post_title}
                                      </p>
                                    </Row>
                                    {/* {initialValues.media_type === 'video' ? (
                                      <video
                                        key={
                                          initialValues.post_file
                                            ? initialValues.post_file
                                            : ''
                                        }
                                        width="320"
                                        height="240"
                                        controls
                                      >
                                        <source
                                          src={initialValues.post_file} // fileObj?.name}
                                          type={initialValues.post_file}
                                        />
                                      </video>
                                    ) : ( */}
                                    <Image
                                      src={initialValues.post_file}
                                      className="imagePreviewStyle"
                                      style={{ width: '100%' }}
                                    />
                                    {/* )} */}
                                    <p
                                      className="padding subTitle paddingTop"
                                      style={{
                                        width: '86%',
                                        paddingLeft: 10,
                                        paddingBottom: 5,
                                      }}
                                    >
                                      {/* {type === 'feed'
                                      ? state.postSubTitle
                                      : state.postSubTitlePushMsg ||
                                        'Post Sub Title'} */}
                                      {initialValues.post_subtitle}
                                    </p>
                                    <p
                                      className="padding message"
                                      style={{
                                        width: '86%',
                                        paddingLeft: 10,
                                        // paddingBottom: 10,
                                        color: '#d3d3d3',
                                      }}
                                    >
                                      {/* {type === 'feed'
                                      ? state.message
                                      : state.messagePushMsg || 'Post Message'} */}

                                      {initialValues.message}
                                    </p>
                                    <div>
                                      {initialValues.button_info.map(
                                        (data, index) => (
                                          <div className="btnCenter">
                                            <Button
                                              className="previewBtn"
                                              type="primary"
                                              style={{
                                                fontSize: toNumber(
                                                  data.button_text_size,
                                                ), // || state.buttonFontSize,
                                                backgroundColor:
                                                  data.background_color, // || state.backColor,
                                                color: data.button_text_color, // || state.buttonTextColor,
                                                textAlign:
                                                  data.button_text_align, // || state.buttonTextAlign,
                                                width: '92%',
                                                borderRadius: 10,
                                                alignSelf: 'center',
                                                marginInline: 12,
                                                marginTop: 10,
                                              }}
                                            >
                                              <IntlMessages
                                                id={`${
                                                  data.button_text || 'Button'
                                                }`}
                                              />
                                            </Button>
                                          </div>
                                        ),
                                      )}
                                    </div>
                                  </div>
                                </div>
                              ) : (
                                <div className="previewText">
                                  <Row>
                                    <div className="postCircle" />
                                    <p
                                      className="padding title"
                                      style={{ color: '#000' }}
                                    >
                                      Brand Name
                                    </p>
                                  </Row>
                                  <p className="padding subTitle">
                                    {initialValues.post_title}
                                  </p>
                                  <p className="padding message">
                                    {initialValues.description}
                                  </p>
                                </div>
                              )}
                            </IsoWidgetBox>
                          </IsoWidgetsWrapper>
                        </Col>
                      </Row>
                    </ReportsWidget>
                  </IsoWidgetsWrapper>
                </Col>
              </Row>
            </div>
          </Spin>
        </MarketingDashboardWrapper>
      </LayoutWrapper>
    );
  }
  return true;
}

export default injectIntl(Widgets);

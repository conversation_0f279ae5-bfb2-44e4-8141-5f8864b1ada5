/* eslint-disable react/jsx-no-bind */
/* eslint-disable no-param-reassign */
/* eslint-disable no-lonely-if */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-expressions */
/* eslint-disable jsx-a11y/interactive-supports-focus */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
/* eslint-disable react/jsx-props-no-spreading */
import React, { useEffect, useState } from 'react';
import { Col, Input, Row, Select, Upload, Modal } from 'antd';
import { useSelector } from 'react-redux';
import IntlMessages from '@chill/components/utility/intlMessages';
import Form from '@chill/components/uielements/form';
import {
  BottomViewWrapper,
  FormWrapper,
} from '@chill/assets/styles/drawerFormStyles';
import {
  PaperClipOutlined,
  PlusCircleOutlined,
  MinusCircleOutlined,
} from '@ant-design/icons';
import theme from '@chill/config/theme/default';
import TextArea from 'antd/lib/input/TextArea';
import { checkImage, getBase64 } from '@chill/lib/helpers/utility';
import { find, isArray, isEmpty, isObject } from 'lodash';
import getApiData from '@chill/lib/helpers/apiHelper';
import Notification from '@chill/components/Notification';
import moment from 'moment';
import { useHistory } from 'react-router';
import CButton from '@chill/components/uielements/CButton';
import RenderInAppPurchase from './RenderInAppPurchase';
import PostPreviewTab from './PostPreviewTab';

export default function RenderData({
  type,
  state,
  setState = () => {},
  propIntl,
  initVal,
  segmentList,
  // categoryList,
  // setCategoryList,
  buttonLoader,
  setButtonLoader = () => {},
}) {
  const [form] = Form.useForm();
  const { btnCnt, attachmentypeArr } = state;
  const { language } = useSelector((st) => st.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);
  const [testedUsers, setTestedUsers] = useState([]);
  const [testUserModal, setTestUserModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const userData = useSelector((states) => states.Auth.userData);
  const uData = isObject(userData) ? { ...userData } : {};
  const [categoryList, setCategoryList] = useState();

  const handleChange = (info) => {
    let fileList = [...info.fileList];
    console.log('file lisr ===', fileList);
    fileList = fileList?.slice(-2);
    fileList = fileList?.map((file) => {
      if (file.response) {
        file.url = file.response.url;
      }
      return file;
    });

    setState((p) => ({ ...p, file: fileList }));
  };

  const { Option } = Select;
  // on click handler for navigation to campaign list

  const history = useHistory();

  const handleClick = () => {
    history.push('/dashboard/marketingNew');
  };

  // this function for get post data
  async function getUsersTestPost() {
    try {
      const response = await getApiData('getUserForTestPost', {}, 'POST');
      if (
        response.success &&
        isArray(response.data) &&
        response.data.length > 0
      ) {
        setTestedUsers(response.data);
      }
    } catch (error) {
      console.log('error ===', error);
      Notification('error', messageArray['err.wenWrong']);
    }
  }
  // this function for get category
  /** this function for get category
   * @function getCategoryList
   *
   */
  async function getCategoryList() {
    try {
      const response = await getApiData('campaigns-category/index', {}, 'POST');
      if (response.success) {
        setCategoryList(response.data);
      } else {
        setCategoryList(response.data);
      }
    } catch (err) {
      console.log('er=====', err);
      setCategoryList((preState) => ({
        ...preState,
      }));
    }
  }
  useEffect(() => {
    getCategoryList();
  }, [state]);

  useEffect(() => {
    getCategoryList();
    getUsersTestPost();
  }, []);

  useEffect(() => {
    if (state.type === initVal?.campaign_type) {
      setState({
        ...state,
        attachmentsUrl: initVal?.attchments,
        attachments: initVal?.attchments,
        selectfileUrl: initVal?.post_file,
        btnCnt: initVal?.button_info,
        postTitle: initVal?.post_title,
        fontSize: initVal?.text_info?.text_size,
        textAlign: initVal?.text_info?.text_align,
        textColor: initVal?.text_info?.text_color,
        iconUrl: initVal?.icon_url,
        message: initVal?.message,
        postTitlePushMsg: initVal?.post_title,
        postTitleInAppMsg: initVal?.text_info?.text_name,
        messageType: initVal?.message_position,
        messagePushMsg: initVal?.message,
        postSubTitle: initVal?.post_subtitle,
        postSubTitlePushMsg: initVal?.post_subtitle,
        fileUrl: initVal?.post_file,
        file: initVal?.post_file,
        fileName: initVal?.file_name,
        startDate: initVal?.start_date,
        endDate: initVal?.end_date,
      });
    }
  }, [state?.drawerVisible]);

  function onChangeSelect(value) {
    console.log(`selected ${value}`);
    setState((p) => ({ ...p, sendTo: value }));

    let count = 0;
    console.log('log ==>', value);
    if (value) {
      count = segmentList && find(segmentList, (o) => o.id === value);
    }
    setState((p) => ({
      ...p,
      userCount: count?.userCount ? count.userCount : 0,
    }));
  }

  function onChangeCatSelect(value) {
    console.log(`selected ${value}`);
    setState((p) => ({ ...p, sendTo: value }));
    console.log('categoryList v ==>', value);

    // deleteCampCategory(value);
  }
  function onChangeSelectattachments(value) {
    console.log(`selected ${value}`);
    setState((p) => ({ ...p, attachmentType: value }));
  }
  function onBlur() {
    console.log('blur');
  }

  function onFocus() {
    console.log('focus');
  }

  function onSearch(val) {
    console.log('search:', val);
  }
  const beforeUpload = (file) => {
    const res = checkImage(file);
    if (res.status) {
      setState((p) => ({ ...p, fileName: file?.name }));

      getBase64(file, (imageUrl) => {
        setState((p) => ({
          ...p,
          selectfileUrl: imageUrl, // state.type !== 'push_message' ? fileT : imageUrl,
          loading: false,
        }));
        console.log('imageUrl ==>', imageUrl);
      });
    } else {
      Notification('error', res.message);
    }
    setState((p) => ({ ...p, fileUrl: file, loading: false }));

    return false;
  };

  const props = {
    // action: 'https://www.mocky.io/v2/5cc8019d300000980a055e76',
    onChange: handleChange,
    multiple: false,
  };
  async function addMarketing(values, vType = '') {
    const obj = values;
    obj.campaign_type = state.type;
    if (state.type === 'push_message') {
      Object.assign(obj, { button_array: state.btnCnt });
      Object.assign(obj, { button_text: state.btnText });
    }
    const objInApp = values;
    const end_date = moment(obj.end_date).format('YYYY-MM-DD');
    obj.end_date = end_date;
    const start_date = moment(obj.start_date).format('YYYY-MM-DD');
    obj.start_date = start_date;
    obj.message_type = state.messageType;
    objInApp.button = JSON.stringify(state.btnCnt || obj.button_array);
    objInApp.button =
      state.btnCnt && state.type === 'push_message'
        ? JSON.stringify(obj.button_array)
        : state.btnCnt && state.type === 'in_app_message'
        ? JSON.stringify(state.btnCnt)
        : [];
    objInApp.text = JSON.stringify({
      text_name: values.text1 || obj.text_name,
      text_size: state.fontSize || obj.text_size,
      text_align: state.textAlign,
      text_color: state.textColor,
    });

    const formData = new FormData();
    state.type === ('push_message' || 'in_app_message')
      ? Object.keys(objInApp).map((k) => formData.append(k, objInApp[k]))
      : Object.keys(obj).map((k) => formData.append(k, obj[k]));

    formData.append('post_file', obj.attachments || state.fileUrl);
    if (state.type !== 'feed_post') {
      formData.append('attachments', state.fileUrl);
    }

    console.log('addMarketing -> obj', obj, values, formData);
    if (vType === 'testDevice') {
      try {
        const response = await getApiData(
          'campaigns/send-test-device',
          formData,
          'POST',
          {},
          true,
        );

        if (response.success) {
          Notification('success', response.message);
        } else {
          Notification('error', response.message);
        }
        setButtonLoader(false);
      } catch (error) {
        console.log(error);
        setButtonLoader(false);
      }
    } else {
      try {
        const response = await getApiData(
          'campaigns/add-campaign',
          formData,
          'POST',
          {},
          true,
        );
        if (response.success) {
          Notification('success', response.message);
          form.resetFields();
          setState((p) => ({
            ...p,
            drawerVisible: false,
            btnCnt: [],
            fileUrl: {},
          }));
        } else {
          Notification('error', response.message);
        }
        setState((p) => ({
          ...p,
          btnCnt: [],
        }));
        setButtonLoader(false);
      } catch (error) {
        console.log(error);
        setButtonLoader(false);
        // Notification('error', messageArray['err.wenWrong']);
      }
    }
  }

  function validate(values, vType = '') {
    let valid = true;
    console.log('jjjjjjj===', values);
    const obj = values;
    obj.attachments = initVal.attachments || '';
    if (state.type === 'feed_post') {
      delete obj.attachments;
    } else {
      if (isEmpty(state.fileUrl)) {
        Notification('error', messageArray['common.uploadImg']);
        valid = false;
      }
    }

    if (vType === 'testDevice') {
      obj.user_id = selectedUser;
    }

    if (valid) {
      setButtonLoader(true);
      addMarketing(obj, vType);
    }
  }
  const btnObj = {
    id: btnCnt?.length + 1,
    button_text: initVal?.button_text || state.btnText || 'Button',
    button_text_color: initVal.button_text_color || '#000',
    button_text_size: initVal.button_text_size || '16',
    button_text_align: initVal.button_text_align || 'center',
    background_color: initVal.background_color || '#1172EC',
    button_url: initVal.button_url || state.buttonUrl || '',
  };

  return (
    <Row gutter={12}>
      {state.type === 'in_app_message' ? (
        <Col xl={16} xs={24} sm={24} md={12} className="colHeight">
          <RenderInAppPurchase
            state={state}
            setState={setState}
            propIntl={propIntl}
            initVal={initVal}
            segmentList={segmentList}
            form={form}
            buttonLoader={buttonLoader}
          />
        </Col>
      ) : (
        <Col
          xl={16}
          xs={24}
          sm={24}
          md={12}
          style={{
            paddingTop: 16,
            paddingRight: 24,
            overflowY: 'scroll',
            height: '100vh',
          }}
        >
          <FormWrapper>
            <Form
              requiredMark={false}
              form={form}
              onFinish={validate}
              layout="vertical"
              initialValues={{ ...initVal, attachments: initVal?.post_file }}
              style={{ paddingBottom: 200 }}
            >
              <Row
                gutter={[12, 20]}
                style={{
                  marginBottom: 20,
                }}
              >
                <Col xs={24}>
                  <Form.Item
                    label={
                      <p className="titleStyle">
                        <IntlMessages id="CAMPAIGNNAME" />
                      </p>
                    }
                    name="campaign_name"
                    rules={[
                      {
                        required: true,
                        message: <IntlMessages id="req.campaignName" />,
                      },
                      {
                        whitespace: true,
                        message: <IntlMessages id="err.blankSpace" />,
                      },
                    ]}
                  >
                    <Input
                      onChange={(val) => {
                        const { value } = val.target;
                        setState((p) => ({
                          ...p,
                          campaignName: value,
                        }));
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item
                    label={
                      <p className="titleStyle">
                        <IntlMessages id="CAMPAIGNTAGS" />
                      </p>
                    }
                    name="campaign_tags"
                    rules={[
                      {
                        required: true,
                        message: <IntlMessages id="req.campaignTags" />,
                      },
                    ]}
                  >
                    <Select mode="tags" />
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item
                    label={
                      <p className="titleStyle">
                        <IntlMessages id="DESCRIPTION" />
                      </p>
                    }
                    name="description"
                    rules={[
                      {
                        required: true,
                        message: <IntlMessages id="req.description" />,
                      },
                      {
                        whitespace: true,
                        message: <IntlMessages id="err.blankSpace" />,
                      },
                    ]}
                  >
                    <Input
                      onChange={(val) => {
                        const { value } = val.target;
                        setState((p) => ({
                          ...p,
                          campaignDesc: value,
                        }));
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col xl={24} md={24} xs={24} sm={24}>
                  <Form.Item
                    label={
                      <p className="titleStyle">
                        <IntlMessages id="marketing.sendTo" />
                      </p>
                    }
                    rules={[
                      {
                        required: true,
                        message: <IntlMessages id="req.customer" />,
                      },
                    ]}
                    name="segment_id"
                  >
                    <Select
                      showSearch
                      placeholder={
                        <IntlMessages id="marketing.customerSegment" />
                      }
                      optionFilterProp="children"
                      onChange={onChangeSelect}
                      onFocus={onFocus}
                      onBlur={onBlur}
                      onSearch={onSearch}
                      filterOption={(input, option) =>
                        option.children
                          .toLowerCase()
                          .indexOf(input.toLowerCase()) >= 0
                      }
                    >
                      {segmentList &&
                        segmentList.map((data) => (
                          <Option value={data.id}>{data.segment_name}</Option>
                        ))}
                    </Select>
                  </Form.Item>
                </Col>
                {/* <div className="iconStyle">
                <span style={{ cursor: 'pointer' }}>
                  <div
                    onClick={() => {
                      setState((p) => ({
                        ...p,
                        visible: true,
                        drawerType: 'add',
                      }));
                    }}
                    role="button"
                    tabIndex={-1}
                    onKeyPress={() => {
                      setState((p) => ({
                        ...p,
                        visible: true,
                        drawerType: 'add',
                      }));
                    }}
                    className="createSegmentP"
                    style={{
                      color: theme.colors.primaryColor,
                      display: 'flex',
                    }}
                  >
                    <p className="iconPadding" style={{ paddingRight: '10px' }}>
                      <PlusCircleOutlined />
                    </p>
                    <IntlMessages id="customer.addSegment" />
                  </div>
                </span>
                {state.sendTo ? (
                  <div
                    className="createSegmentP"
                    style={{
                      color: 'red',
                      display: 'flex',
                      marginLeft: 20,
                      fontSize: 12,
                    }}
                  >
                    {`There are "${state?.userCount}" people that will be added to this segment`}
                  </div>
                ) : null}
              </div> */}
                {state.type === 'feed_post' &&
                uData.brand_name === 'bugaboo' ? (
                  <>
                    <Col xl={24} md={24} xs={24} sm={24}>
                      <Form.Item
                        label={
                          <p className="titleStyle">
                            <IntlMessages id="marketing.categories" />
                          </p>
                        }
                        rules={[
                          {
                            required: true,
                            message: <IntlMessages id="req.category" />,
                          },
                        ]}
                        name="category_id"
                      >
                        <Select
                          showSearch
                          placeholder={
                            <IntlMessages id="marketing.chooseCategory" />
                          }
                          optionFilterProp="children"
                          onChange={onChangeCatSelect}
                          onFocus={onFocus}
                          onBlur={onBlur}
                          onSearch={onSearch}
                          filterOption={(input, option) =>
                            option.children
                              .toLowerCase()
                              .indexOf(input.toLowerCase()) >= 0
                          }
                        >
                          {categoryList &&
                            categoryList.map((data) => (
                              <Option value={data.id}>
                                {data.category_name}
                              </Option>
                            ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <div className="iconStyle">
                      <span style={{ cursor: 'pointer' }}>
                        <Row>
                          <div
                            onClick={() => {
                              setState((p) => ({
                                ...p,
                                visible: true,
                                drawerType: 'category',
                              }));
                            }}
                            role="button"
                            tabIndex={-1}
                            onKeyPress={() => {
                              setState((p) => ({
                                ...p,
                                visible: true,
                                drawerType: 'category',
                              }));
                            }}
                            className="createSegmentP"
                            style={{
                              color: theme.colors.primaryColor,
                              display: 'flex',
                            }}
                          >
                            <p
                              className="iconPadding"
                              style={{ paddingRight: '10px' }}
                            >
                              <PlusCircleOutlined />
                            </p>
                            <IntlMessages id="customer.addCategory" />
                          </div>
                          <div
                            onClick={() => {
                              setState((p) => ({
                                ...p,
                                visible: true,
                                drawerType: 'category-delete',
                              }));
                            }}
                            role="button"
                            tabIndex={-1}
                            onKeyPress={() => {
                              setState((p) => ({
                                ...p,
                                visible: true,
                                drawerType: 'category-delete',
                              }));
                            }}
                            className="createSegmentP"
                            style={{
                              color: 'red',
                              display: 'flex',
                              marginLeft: 20,
                            }}
                          >
                            <p
                              className="iconPadding"
                              style={{ paddingRight: '10px' }}
                            >
                              <MinusCircleOutlined />
                            </p>
                            <IntlMessages id="DeleteCategory" />
                          </div>
                        </Row>
                      </span>
                    </div>
                  </>
                ) : null}
                {/* {state.type === 'push_message' && (
                <Row gutter={12}>
                  <Col xs={24}>
                    <Form.Item
                      label={
                        <p className="titleStyle">
                          <IntlMessages id="marketing.appName" />
                        </p>
                      }
                      name="app_name"
                      rules={[
                        {
                          required: true,
                          message: <IntlMessages id="req.appName" />,
                        },
                        {
                          whitespace: true,
                          message: <IntlMessages id="err.blankSpace" />,
                        },
                      ]}
                    >
                      <Input
                        onChange={(val) => {
                          const { value } = val.target;
                          setState((p) => ({
                            ...p,
                            appName: value,
                          }));
                        }}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              )} */}
                <Col xs={24}>
                  <Form.Item
                    label={
                      <p className="titleStyle">
                        <IntlMessages id="marketing.postTitle" />
                      </p>
                    }
                    name="post_title"
                    rules={[
                      {
                        required: true,
                        message: <IntlMessages id="req.postTitle" />,
                      },
                      {
                        whitespace: true,
                        message: <IntlMessages id="err.blankSpace" />,
                      },
                    ]}
                  >
                    <Input
                      onChange={(val) => {
                        const { value } = val.target;
                        if (type === 'feed') {
                          setState((p) => ({
                            ...p,
                            postTitle: value,
                          }));
                        } else {
                          setState((p) => ({
                            ...p,
                            postTitlePushMsg: value,
                          }));
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item
                    label={
                      <p className="titleStyle">
                        <IntlMessages id="marketing.postSubTitle" />
                      </p>
                    }
                    name="post_subtitle"
                    rules={[
                      {
                        required: true,
                        message: <IntlMessages id="req.postSubTitle" />,
                      },
                      {
                        whitespace: true,
                        message: <IntlMessages id="err.blankSpace" />,
                      },
                    ]}
                  >
                    <Input
                      onChange={(val) => {
                        const { value } = val.target;
                        if (type === 'feed') {
                          setState((p) => ({
                            ...p,
                            postSubTitle: value,
                          }));
                        } else {
                          setState((p) => ({
                            ...p,
                            postSubTitlePushMsg: value,
                          }));
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item
                    label={
                      <p className="titleStyle">
                        <IntlMessages id="marketing.message" />
                      </p>
                    }
                    name="message"
                    rules={[
                      {
                        whitespace: true,
                        message: <IntlMessages id="err.blankSpace" />,
                      },
                    ]}
                  >
                    <TextArea
                      rows={4}
                      onChange={(val) => {
                        const { value } = val.target;
                        if (type === 'feed') {
                          setState((p) => ({
                            ...p,
                            message: value,
                          }));
                        } else {
                          setState((p) => ({
                            ...p,
                            messagePushMsg: value,
                          }));
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item
                    label={
                      <p className="titleStyle">
                        <IntlMessages id="marketing.attachments" />
                      </p>
                    }
                    name="attachments"
                    rules={[
                      {
                        required: true,
                        message: <IntlMessages id="req.attachmentsUrl" />,
                      },
                    ]}
                  >
                    <Upload
                      {...props}
                      fileList={[state?.file]}
                      beforeUpload={beforeUpload}
                      showUploadList={false}
                      multiple={false}
                    >
                      <p className="attachments" style={{ cursor: 'pointer' }}>
                        <PaperClipOutlined />
                        {state.file && state.fileName ? (
                          <p>{state?.fileName}</p>
                        ) : (
                          <IntlMessages id="marketing.attachFiles" />
                        )}
                      </p>
                    </Upload>
                    {/* )} */}
                  </Form.Item>
                </Col>
              </Row>
              {state.type !== 'push_message' && (
                <Row
                  gutter={[12, 20]}
                  style={{
                    marginBottom: 20,
                  }}
                >
                  <Col xs={24}>
                    <div className="upload">
                      <Form.Item
                        label={
                          <p className="titleStyle">
                            <IntlMessages id="marketing.launchUrl" />
                          </p>
                        }
                        name="launch_url"
                      >
                        <Input
                          placeholder="Video link or HTTP Link"
                          onChange={(val) => {
                            const { value } = val.target;
                            setState((p) => ({
                              ...p,
                              launchUrl: value,
                            }));
                          }}
                        />
                      </Form.Item>
                    </div>
                  </Col>
                </Row>
              )}
              {state.type === 'push_message' && (
                <>
                  <Row
                    gutter={[12, 20]}
                    style={{
                      marginBottom: 20,
                    }}
                  >
                    <Col xs={24}>
                      <div className="upload">
                        <Form.Item
                          label={
                            <p className="titleStyle">
                              <IntlMessages id="marketing.icon" />
                            </p>
                          }
                          name="icon_url"
                        >
                          <Input
                            placeholder="Enter Icon url"
                            onChange={(val) => {
                              const { value } = val.target;
                              setState((p) => ({
                                ...p,
                                iconUrl: value,
                              }));
                            }}
                          />
                        </Form.Item>
                      </div>
                    </Col>
                  </Row>
                  <Row
                    gutter={[12, 20]}
                    style={{
                      marginBottom: 20,
                    }}
                  >
                    <Col xs={24}>
                      {/* <div className="upload"> */}
                      <Form.Item
                        label={
                          <p className="titleStyle">
                            <IntlMessages id="marketing.button" />
                          </p>
                        }
                        // name="button_text"
                      >
                        <Input
                          placeholder="Enter Button Text"
                          value={state.btnText}
                          onChange={(val) => {
                            const { value } = val.target;
                            setState((p) => ({
                              ...p,
                              btnText: value,
                            }));
                          }}
                        />
                      </Form.Item>
                      {/* </div> */}
                    </Col>
                  </Row>
                  <Row
                    gutter={[12, 20]}
                    style={{
                      marginBottom: 20,
                    }}
                  >
                    <Col xs={24}>
                      <Form.Item
                        label={
                          <p className="titleStyle">
                            <IntlMessages id="marketing.buttonUrl" />
                          </p>
                        }
                        // name="button_url"
                        rules={[
                          // {
                          //   required: true,
                          //   message: <IntlMessages id="req.buttonUrl" />,
                          // },
                          {
                            whitespace: true,
                            message: <IntlMessages id="err.blankSpace" />,
                          },
                        ]}
                      >
                        <Input
                          placeholder="Enter Button Url"
                          value={state.buttonUrl}
                          onChange={(val) => {
                            const { value } = val.target;
                            setState((p) => ({
                              ...p,
                              buttonUrl: value,
                            }));
                          }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row
                    gutter={[12, 20]}
                    style={{
                      marginBottom: 20,
                    }}
                  >
                    <Col xs={24} md={24} xl={24}>
                      <div className="iconStyle">
                        <span style={{ cursor: 'pointer' }}>
                          <div
                            onClick={() => {
                              // const data = state.btnText || 'Button';
                              btnCnt.push(btnObj);
                              setState((p) => ({
                                ...p,
                                btnCnt: state.btnCnt,
                                btnText: '',
                                buttonUrl: '',
                              }));
                            }}
                            role="button"
                            tabIndex={-1}
                            onKeyPress={() => {
                              btnCnt.push(btnObj);
                              setState((p) => ({
                                ...p,
                                btnCnt: state.btnCnt,
                                btnText: '',
                                buttonUrl: '',
                              }));
                            }}
                            className="createSegmentP"
                            style={{
                              color: theme.colors.primaryColor,
                              display: 'flex',
                            }}
                          >
                            <p
                              className="iconPadding"
                              style={{ paddingRight: '10px' }}
                            >
                              <PlusCircleOutlined />
                            </p>
                            <IntlMessages id="marketing.addButton" />
                          </div>
                        </span>
                      </div>
                    </Col>
                  </Row>
                </>
              )}
              {state.type !== 'push_message' ? (
                <Row
                  gutter={[12, 20]}
                  style={{
                    marginBottom: 20,
                  }}
                >
                  <Col xl={24} md={24} xs={24} sm={24}>
                    <Form.Item
                      label={
                        <p className="titleStyle">
                          <IntlMessages id="marketing.AttachmentsType" />
                        </p>
                      }
                      rules={[
                        {
                          required: true,
                          message: (
                            <IntlMessages id="req.selectAttachmentsType" />
                          ),
                        },
                      ]}
                      name="attachment_type"
                    >
                      {/* <Checkbox.Group
                        style={{ width: '100%' }}
                        onChange={onChangeSelectattachments}
                      >
                        <Row>
                          <Col span={8}>
                            <Checkbox value="Video_link">
                              <IntlMessages id="marketing.videolink" />
                            </Checkbox>
                          </Col>
                          <Col span={8}>
                            <Checkbox value="Webpage">
                              <IntlMessages id="marketing.Webpage" />
                            </Checkbox>
                          </Col>
                        </Row>
                      </Checkbox.Group> */}
                      <Select
                        allowClear
                        placeholder={
                          <IntlMessages id="marketing.selectAttachmentsType" />
                        }
                        optionFilterProp="children"
                        onChange={onChangeSelectattachments}
                        onFocus={onFocus}
                        onBlur={onBlur}
                      >
                        {state.attachmentypeArr &&
                          attachmentypeArr.map((data) => (
                            <Option value={data}>{data}</Option>
                          ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              ) : null}
            </Form>
          </FormWrapper>
        </Col>
      )}
      <Col
        xl={8}
        xs={24}
        sm={24}
        md={12}
        style={{ backgroundColor: '#eee', paddingTop: 8, paddingBottom: 8 }}
      >
        <p className="titleStyle titlePadding">
          <IntlMessages id="marketing.postPreview" />
        </p>
        <div className="mobileView">
          <div className="mainPreview" style={{ overflow: 'hidden' }}>
            <div className="previewUpperPart1">
              <div className="icon1" />
              <div className="icon2" />
            </div>
            <div className="icon3View">
              <div className="icon3" />
            </div>
            <PostPreviewTab state={state} setState={setState} type={type} />
          </div>
        </div>
      </Col>

      <FormWrapper>
        <BottomViewWrapper className="bottomBtnWrapper">
          <Form.Item className="btn-form">
            <CButton
              type="primary"
              htmlType="submit"
              loading={buttonLoader}
              disabled={buttonLoader}
              style={{
                marginRight: 16,
              }}
              onClick={() => {
                form?.validateFields().then((values) => {
                  validate(values);
                  // changeStatus({ ...values, id: initVal.id });
                });
              }}
            >
              <IntlMessages id="common.save" />
            </CButton>
          </Form.Item>
          <Form.Item className="btn-form">
            <CButton
              type="primary"
              htmlType="submit"
              loading={buttonLoader}
              disabled={buttonLoader}
              style={{
                marginRight: 16,
              }}
              onClick={handleClick}
            >
              <IntlMessages id="Next" />
            </CButton>
                
          </Form.Item>
          <Form.Item className="btn-form">
            <div
              onClick={() => setTestUserModal(true)}
              role="button"
              style={{
                cursor: 'pointer',
                borderWidth: '0px',
                color: '#5087FF',
                fontWeight: '500',
              }}
            >
              <IntlMessages id="marketing.sendToTest" />
            </div>
          </Form.Item>
        </BottomViewWrapper>
      </FormWrapper>
      <Modal
        title={<IntlMessages id="selectUser" />}
        visible={testUserModal}
        onOk={() => {
          setTestUserModal(false);
          form.validateFields().then((values) => {
            validate(values, 'testDevice');
          });
        }}
        onCancel={() => {
          setTestUserModal(false);
          setSelectedUser(null);
        }}
      >
        <Select
          style={{ width: '100%' }}
          onChange={(val) => setSelectedUser(val)}
          value={selectedUser}
        >
          {testedUsers.map((item) => (
            <Select.Option key={`testedUsers${item.id}`} value={item.user_id}>
              {item.full_name}
            </Select.Option>
          ))}
        </Select>
      </Modal>
    </Row>
  );
}

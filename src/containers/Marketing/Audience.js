/* eslint-disable react/destructuring-assignment */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable no-unused-vars */
/* eslint-disable global-require */
/* eslint-disable no-unneeded-ternary */
/* eslint-disable no-irregular-whitespace */
/* eslint-disable no-use-before-define */
/* eslint-disable no-shadow */
/* eslint-disable no-debugger */

import React, { useState, useEffect } from 'react';

import { Tabs, Button, Space, Select, Progress } from 'antd';

import getApiData from '@chill/lib/helpers/apiHelper';
import Notification from '@chill/components/Notification';
import {
  GoogleMap,
  useLoadScript,
  useJsApiLoader,
  Marker,
} from '@react-google-maps/api';
import { useHistory } from 'react-router-dom';

import { isObject } from 'lodash-es';
import CreateNewAudienceTab from './CreateNewAudienceTab'; // For create new audience tab
import UseSaveAud from './UseSaveAud';
import UpdateAudTab from './UpdateAudTab';
import { countries, productDetails } from './AudienceData';

const { TabPane } = Tabs;

const Audience = () => {
  const [age, setAge] = useState([0, 52]);
  const [height, setHeight] = useState([35, 60]);
  const [weight, setWeight] = useState([0, 25]);
  const [gender, setGender] = useState('');
  const [language, setLanguage] = useState([]);
  const [childrens, setChildren] = useState([]);
  const [airQuality, setAirQuality] = useState([6, 150]);
  const [weightUnit, setWeightUnit] = useState('kg');
  const [temperatureUnit, setTemperatureUnit] = useState('F');
  const [temperature, setTemperature] = useState([20, 68]);
  const [userTitle, setUserTitle] = useState('');
  const [isCheckedInclude, setIsCheckedInclude] = useState([]);
  const [isCheckedExclude, setIsCheckedExclude] = useState([]);
  const [saveValue, setSaveValue] = useState('yes');
  const [saveCampaign, setSaveCampaign] = useState('awareness');
  const [activeTab, setActiveTab] = useState('1');
  const [editId, setEditId] = useState(null);

  const [age1, setAge1] = useState([]);
  const [gender1, setGender1] = useState('');
  const [language1, setLanguage1] = useState([]);
  const [childrens1, setChildren1] = useState([]);
  const [height1, setHeight1] = useState([]);
  const [airQuality1, setAirQuality1] = useState([]);
  const [weightUnit1, setWeightUnit1] = useState('kg');
  const [temperatureUnit1, setTemperatureUnit1] = useState('F');
  const [weight1, setWeight1] = useState([]);
  const [temperature1, setTemperature1] = useState([]);
  const [userTitle1, setUserTitle1] = useState('');
  const [isCheckedInclude1, setIsCheckedInclude1] = useState([]);
  const [isCheckedExclude1, setIsCheckedExclude1] = useState([]);

  const [searchValueDetails, setSearchValueDetails] = useState('');
  const [productsDetails, setProductsDetails] = useState([]);
  const [product, setProduct] = useState({});
  const [savedProduct, setSavedProduct] = useState({});
  const [savedCampaign, setSavedCampaign] = useState('');
  const defaultSelectedCountry = countries.find(
    (country) => country.name === 'United States',
  );

  const [selectedCountries, setSelectedCountries] = useState([
    defaultSelectedCountry,
  ]);
  const [propCountry, setPropCountry] = useState(['United States']);
  const [savedCountry, setSavedCountry] = useState([]);
  const [optimizedCriteria, setOptimizedCriteria] = useState(false);
  const [savedOptCriteria, setSavedOptCriteria] = useState(0);
  const [audienceCount, setAudienceCount] = useState(0);

  const [updateCount, setUpdateCount] = useState(0);
  const [accuracy, setAccuracy] = useState(false);
  const [accuracyBtn, setAccuracyBtn] = useState(false);
  const [accuracy1, setAccuracy1] = useState(false);
  const [accuracyBtn1, setAccuracyBtn1] = useState(true);
  const [actualAccuracy, setActualAccuracy] = useState();
  const [actualAccuracy1, setActualAccuracy1] = useState();
  const history = useHistory();

  // Getting the audienceType from thr localStorage

  const audienceType = localStorage.getItem('audienceType');
  // Handling the save text based on yes or no selection
  const { isLoaded } = useLoadScript({
    googleMapsApiKey: process.env.REACT_APP_GOOGLE_API_KEY,
  });
  const apiCall = async () => {
    const countDataa = {
      campaign_type: saveCampaign,
      age: {
        min: age[0],
        max: age[1],
        units: 'months',
      },
      aqi: {
        min: airQuality[0],
        max: airQuality[1],
        units: 'AQI',
      },
      gender,
      height: {
        min: height[0],
        max: height[1],
        units: 'cms',
      },
      languages: language,
      locations: propCountry,
      no_of_children: childrens,

      weight: {
        min: Math.round(weight[0] * 10) / 10,
        max: Math.round(weight[1] * 10) / 10,
        units: weightUnit,
      },
      temperature: {
        min: Math.round(temperature[0] * 10) / 10,
        max: Math.round(temperature[1] * 10) / 10,
        units: temperatureUnit,
      },
    };
    if (isCheckedInclude.length > 0 || isCheckedExclude.length > 0) {
      countDataa.productsOwned = {
        condition: isCheckedInclude.length > 0 ? 'include' : 'exclude',
        ids: isCheckedInclude.length > 0 ? isCheckedInclude : isCheckedExclude,
      };
    }
    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('thi is weight api responce', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count; // Extract the count from the API response
        console.log('this is set count Data', countData);
        setAudienceCount(countData); // Set the count in the state
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  useEffect(() => {
    apiCall();
  }, []);

  useEffect(() => {
    if (accuracy === true && saveCampaign === 'sales') {
      setOptimizedCriteria(true);
      console.log('&&&&&&&&&&', accuracy);
    } else {
      setOptimizedCriteria(false);
    }
  }, [accuracy]);

  const handleSaveValueChange = (value) => {
    setSaveValue(value);
  };

  const onSaveCampaignChange = (awareness) => {
    setSaveCampaign(awareness);
  };

  useEffect(() => {
    if (saveCampaign === 'sales') {
      setAge([0, 0]);
      setTemperature([0, 0]);
      setAirQuality([0, 0]);
      setWeight([0, 0]);
      setHeight([0, 0]);
      setAccuracy(false);
      setAccuracyBtn(false);
      setAudienceCount(0);
      setIsCheckedExclude([]);
      setIsCheckedInclude([]);
      setGender('');
      setLanguage([]);
      setChildren([]);
      setSelectedCountries([defaultSelectedCountry]);
      setTemperatureUnit('F');
      setWeightUnit('kg');
      // setCountry(countries[105]);
      // setSelectedCountryLatLng({ lat: 55.3781, lng: -3.436 });
    } else {
      setAge([0, 52]);
      setTemperature([20, 68]);
      setAirQuality([6, 150]);
      setWeight([0, 25]);
      setHeight([35, 60]);
      setProduct([]);
      setIsCheckedExclude([]);
      setIsCheckedInclude([]);
      setGender('');
      setLanguage([]);
      setChildren([]);
      setSelectedCountries([defaultSelectedCountry]);
      setTemperatureUnit('F');
      setWeightUnit('kg');
      // setCountry(countries[105]);
      // setSelectedCountryLatLng({ lat: 55.3781, lng: -3.436 });
    }
  }, [saveCampaign]);

  const handleTabChange = async (key) => {
    if (key === '1') {
      // When switching to the first tab, close the third tab
      setEditId(null);
      setSelectedCountries([defaultSelectedCountry]);
      setPropCountry(['United States']);
      // setCountry(countries[105]);
      // setSelectedCountryLatLng({ lat: 55.3781, lng: -3.436 });
      setSaveCampaign('awareness');

      const countDataa = {
        campaign_type: 'awareness',
        age: {
          min: 0,
          max: 52,
          units: 'months',
        },
        aqi: {
          min: 6,
          max: 150,
          units: 'AQI',
        },
        gender,
        height: {
          min: 35,
          max: 60,
          units: 'cms',
        },
        languages: language,
        locations: propCountry,
        no_of_children: childrens,
        weight: {
          min: 0,
          max: 25,
          units: 'kg',
        },
        temperature: {
          min: 20,
          max: 68,
          units: 'F',
        },
      };

      if (isCheckedInclude.length > 0 || isCheckedExclude.length > 0) {
        countDataa.productsOwned = {
          condition: isCheckedInclude.length > 0 ? 'include' : 'exclude',
          ids:
            isCheckedInclude.length > 0 ? isCheckedInclude : isCheckedExclude,
        };
      }

      try {
        const response = await getApiData(
          'audience/get-count',
          countDataa,
          'POST',
        );
        console.log('thi is Tab chnage api responce', response);
        console.log('this is count Data', countDataa);
        if (response.success) {
          const countData = response.count; // Extract the count from the API response
          setAudienceCount(countData); // Set the count in the state
        }
      } catch (countError) {
        console.log('Count error:', countError);
        // Handle the error from the get-count API
      }
    }
    setActiveTab(key);
  };

  const criteriaTitle = `${userTitle}_H${height[0]}-${height[1]}_W${
    Math.round(weight[0] * 10) / 10
  }-${Math.round(weight[1] * 10) / 10}_T${
    Math.round(temperature[0] * 10) / 10
  }-${Math.round(temperature[1] * 10) / 10}`;

  const criteriaTitle1 = `${userTitle1}_H${height1[0]}-${height1[1]}_W${
    Math.round(weight1[0] * 10) / 10
  }-${Math.round(weight1[1] * 10) / 10}_T${
    Math.round(temperature1[0] * 10) / 10
  }-${Math.round(temperature1[1] * 10) / 10}`;

  // Handling the next button for navigating to design studio & post audience data api & updating audience

  const handleButton = async () => {
    console.log(audienceType);
    if (saveValue === 'yes' && editId && activeTab === '3') {
      if (propCountry.length === 0) {
        Notification('error', 'Please select a location');
        return;
      }
      const data = {
        name: userTitle1,
        title: criteriaTitle1,
        campaign_type: savedCampaign,
        product:
          savedProduct && Object.keys(savedProduct).length > 0
            ? savedProduct.product_name
            : '',
        product_id:
          savedProduct && Object.keys(savedProduct).length > 0
            ? savedProduct.product_id
            : null,
        parameters: {
          age: {
            min: age1[0],
            max: age1[1],
            units: 'months',
          },
          gender: gender1,
          languages: language1,
          weight: {
            min: Math.round(weight1[0] * 10) / 10,
            max: Math.round(weight1[1] * 10) / 10,
            units: weightUnit1,
          },

          children: childrens1,
          aqi: {
            min: airQuality1[0],
            max: airQuality1[1],
            units: 'AQI',
          },
          height: {
            min: height1[0],
            max: height1[1],
            units: 'cms',
          },
          temperature: {
            min: Math.round(temperature1[0] * 10) / 10,
            max: Math.round(temperature1[1] * 10) / 10,
            units: temperatureUnit1,
          },
          productsOwned: {
            include: isCheckedInclude1,
            exclude: isCheckedExclude1,
          },
          locations: propCountry,
        },
      };
      // localStorage.setItem('audienceData', JSON.stringify(data));
      try {
        const response = await getApiData(
          `audience/update-criteria/${editId}`,
          data,
          'POST',
        );

        if (response.success && isObject(response.data)) {
          console.log(response.data);
        }
      } catch (error) {
        console.log(error);
      }
      history.push({
        pathname: '/design-studio',
        aud_info: {
          name: userTitle1,
          c_name: criteriaTitle1,
          aud_count: updateCount,
        },
      });
    } else if (saveValue === 'yes') {
      let isError = false;
      if (userTitle === '') {
        Notification('error', 'User title is required');
        isError = true;
      }
      if (age[0] === 0 && age[1] === 0) {
        Notification('error', 'Please select the valid age');
        isError = true;
      }
      if (height[0] === 0 && height[1] === 0) {
        Notification('error', 'Please select the valid height');
        isError = true;
      }
      if (weight[0] === 0 && weight[1] === 0) {
        Notification('error', 'Please select the valid weight');
        isError = true;
      }
      if (propCountry.length === 0) {
        Notification('error', 'Please select a location');
        isError = true;
      }
      if (product.length === 0 && saveCampaign === 'sales') {
        Notification('error', 'Please select a product');
        isError = true;
      }
      if (isError) {
        return;
      }
      const data = {
        name: userTitle,
        title: criteriaTitle,
        campaign_type: saveCampaign,
        product: product.product_name,
        product_id: product.id,
        optimized_criteria: optimizedCriteria,
        parameters: {
          age: {
            min: age[0],
            max: age[1],
            units: 'months',
          },
          gender,
          languages: language,
          weight: {
            min: Math.round(weight[0] * 10) / 10,
            max: Math.round(weight[1] * 10) / 10,
            units: weightUnit,
          },

          children: childrens,
          aqi: {
            min: airQuality[0],
            max: airQuality[1],
            units: 'AQI',
          },
          height: {
            min: height[0],
            max: height[1],
            units: 'cms',
          },
          temperature: {
            min: Math.round(temperature[0] * 10) / 10,
            max: Math.round(temperature[1] * 10) / 10,
            units: temperatureUnit,
          },
          productsOwned: {
            include: isCheckedInclude,
            exclude: isCheckedExclude,
          },
          locations: propCountry,
        },
      };
      // localStorage.setItem('audienceData', JSON.stringify(data));
      try {
        const response = await getApiData(
          'audience/add-criteria',
          data,
          'POST',
        );
        console.log('data', data);
        if (response.success && isObject(response.data)) {
          console.log(response.data);
        }
      } catch (error) {
        console.log(error);
      }

      history.push({
        pathname: '/design-studio',
        aud_info: {
          name: userTitle,
          c_name: criteriaTitle,
          aud_count: audienceCount,
        },
      });
    } else {
      history.push('/design-studio');
    }
  };

  const [dataLoaded, setDataLoaded] = useState(false);

  const handleEditClick = async (id) => {
    try {
      // Make a GET request to retrieve the data for the specific ID
      const response = await getApiData(`audience/${id}`, {}, 'GET');
      if (response.success && isObject(response.data)) {
        const { data } = response; // Destructure the 'data' object

        setEditId(id);
        setActiveTab('3');
        console.log('locations@@@@@@', data);
        // Set the retrieved data to the form fields in Create New Audience tab
        setUserTitle1(data[0].name);
        setLanguage1(data[0].parameters.languages);
        setWeight1([
          data[0].parameters.weight.min,
          data[0].parameters.weight.max,
        ]);
        setWeightUnit1(data[0].parameters.weight.units);
        setAge1([data[0].parameters.age.min, data[0].parameters.age.max]);
        setGender1(data[0].parameters.gender);
        setChildren1(data[0].parameters.children);
        setHeight1([
          data[0].parameters.height.min,
          data[0].parameters.height.max,
        ]);
        setAirQuality1([
          data[0].parameters.aqi.min,
          data[0].parameters.aqi.max,
        ]);
        setTemperature1([
          data[0].parameters.temperature.min,
          data[0].parameters.temperature.max,
        ]);
        setTemperatureUnit1(data[0].parameters.temperature.units);
        setIsCheckedInclude1(data[0].parameters.productsOwned.include);
        setIsCheckedExclude1(data[0].parameters.productsOwned.exclude);

        const productName = data[0].product;
        const prodItem = productsDetails.filter(
          (item) => item.product_name === productName,
        );
        setSavedProduct(prodItem[0]);

        setSavedCampaign(data[0].campaign_type);

        setSavedOptCriteria(data[0].optimized_criteria);

        const countryName = data[0].parameters.locations;
        const selectedCountryObjects = countries.filter((country) =>
          countryName.includes(country.name),
        );

        setSelectedCountries(selectedCountryObjects);
        setSavedCountry(countryName);
        setDataLoaded(true);
      }
    } catch (error) {
      console.log('Failed to fetch data for editing:', error);
    }
  };

  useEffect(() => {
    if (dataLoaded === true) {
      handleCountData();
      setDataLoaded(false);
    }
  }, [dataLoaded]);

  const handleCountData = async () => {
    const updatedHeight = height1;
    const updatedWeight = weight1;
    const updatedGender = gender1;
    const updatedAgeRange = age1;
    const updatedLanguage = language1;
    const updatedTemperature = temperature1;
    const updatedAqi = airQuality1;
    const updatedProdOwned = isCheckedInclude1;
    const updatedChildren = childrens1;
    const updatedCountry = savedCountry;
    const updatedOptCriteria = savedOptCriteria;
    const updatedProduct = savedProduct;
    const updatedCamp = savedCampaign;

    if (
      updatedOptCriteria === 1 &&
      savedProduct &&
      Object.keys(savedProduct).length > 0
    ) {
      const countData = {
        age: {
          accuracy_weightage: updatedProduct.age.accuracy_weightage,
          max_recommended_range: updatedProduct.age.max_recommended_range,
          min_recommended_range: updatedProduct.age.min_recommended_range,
          max_manufacturer_range: updatedProduct.age.max_manufacturer_range,
          min_manufacturer_range: updatedProduct.age.min_manufacturer_range,
        },
        height: {
          accuracy_weightage: updatedProduct.height.accuracy_weightage,
          max_recommended_range: updatedProduct.height.max_recommended_range,
          min_recommended_range: updatedProduct.height.min_recommended_range,
          max_manufacturer_range: updatedProduct.height.max_manufacturer_range,
          min_manufacturer_range: updatedProduct.height.min_manufacturer_range,
        },
        weight: {
          accuracy_weightage: updatedProduct.weight.accuracy_weightage,
          max_recommended_range: updatedProduct.weight.max_recommended_range,
          min_recommended_range: updatedProduct.weight.min_recommended_range,
          max_manufacturer_range: updatedProduct.weight.max_manufacturer_range,
          min_manufacturer_range: updatedProduct.weight.min_manufacturer_range,
        },
        temperature: {
          accuracy_weightage: updatedProduct.temperature.accuracy_weightage,
          max_recommended_range:
            updatedProduct.temperature.max_recommended_range,
          min_recommended_range:
            updatedProduct.temperature.min_recommended_range,
          max_manufacturer_range:
            updatedProduct.temperature.max_manufacturer_range,
          min_manufacturer_range:
            updatedProduct.temperature.min_manufacturer_range,
        },
        aqi: {
          accuracy_weightage: updatedProduct.aqi.accuracy_weightage,
          max_recommended_range: updatedProduct.aqi.max_recommended_range,
          min_recommended_range: updatedProduct.aqi.min_recommended_range,
          max_manufacturer_range: updatedProduct.aqi.max_manufacturer_range,
          min_manufacturer_range: updatedProduct.aqi.min_manufacturer_range,
        },
      };

      try {
        const response = await getApiData(
          'audience/get-sales-accuracy',
          countData,
          'POST',
        );
        console.log('this is product api responce', response);
        console.log('this is count data', countData);
        if (response.success) {
          const totalCount = response.count; // Extract the count from the API response
          setUpdateCount(totalCount); // Use the functional update approach here
          console.log('This is audience Count:', totalCount); // Log the updated audience count here
          const totalAccuracy = response.overall_accuracy;
          setActualAccuracy1(totalAccuracy);
        }
      } catch (countError) {
        console.log('Count error:', countError);
        // Handle the error from the get-count API
      }
    } else {
      const countData = {
        campaign_type: updatedCamp,
        age: {
          min: updatedAgeRange[0],
          max: updatedAgeRange[1],
          units: 'months',
        },
        aqi: {
          min: updatedAqi[0],
          max: updatedAqi[1],
          units: 'AQI',
        },
        gender: updatedGender,
        height: {
          min: updatedHeight[0],
          max: updatedHeight[1],
          units: 'cms',
        },
        languages: updatedLanguage,
        locations: updatedCountry,
        no_of_children: updatedChildren,

        weight: {
          min: Math.round(updatedWeight[0] * 10) / 10,
          max: Math.round(updatedWeight[1] * 10) / 10,
          units: weightUnit1,
        },
        temperature: {
          min: updatedTemperature[0],
          max: updatedTemperature[1],
          units: temperatureUnit1,
        },
      };
      if (updatedProdOwned.length > 0 || isCheckedExclude1.length > 0) {
        countData.productsOwned = {
          condition: updatedProdOwned.length > 0 ? 'include' : 'exclude',
          ids:
            updatedProdOwned.length > 0 ? isCheckedInclude1 : isCheckedExclude1,
        };
      }

      try {
        const response = await getApiData(
          'audience/get-count',
          countData,
          'POST',
        );
        console.log('this is weight api response', response);
        console.log('this is count Data', countData);
        if (response.success) {
          const totalCount = response.count; // Extract the count from the API response
          console.log('this is set count Data', totalCount);
          setUpdateCount(totalCount); // Set the count in the state
        }
      } catch (countError) {
        console.log('Count error:', countError);
        // Handle the error from the get-count API
      }
    }
  };

  // Styling for map

  const containerStyle = {
    height: '25%',
    margin: '0 auto',
  };

  const options = {
    styles: [
      {
        featureType: 'all',
        elementType: 'all',
        stylers: [
          { hue: '#c0e6e9' }, // Change the hue of the map to a light pink color
          { saturation: -20 }, // Decrease the saturation of the map by 20%
        ],
      },
    ],
  };

  const countryOpts = countries.map((item) => {
    return { value: item.name, label: item.name };
  });

  useEffect(() => {
    // This effect will run whenever the audienceCount state updates
    console.log('this is audience count test', audienceCount);
  }, [audienceCount]); // Add audienceCount as a dependency for the effect

  // Countries Selector Handler

  const handleCountryOptions = async (selectedNames) => {
    const selectedCountryObjects = countries.filter((country) =>
      selectedNames.includes(country.name),
    );
    const mapCountry = selectedNames;
    setPropCountry(mapCountry);
    console.log('this is selected country', mapCountry);
    setSelectedCountries(selectedCountryObjects);

    setAccuracy(false);
    setAccuracyBtn(true);
    if (editId) {
      const countDataa = {
        campaign_type: savedCampaign,
        age: {
          min: age1[0],
          max: age1[1],
          units: 'months',
        },
        aqi: {
          min: airQuality1[0],
          max: airQuality1[1],
          units: 'AQI',
        },
        gender: gender1,
        height: {
          min: height1[0],
          max: height1[1],
          units: 'cms',
        },
        languages: language1,
        locations: mapCountry,
        no_of_children: childrens1,

        weight: {
          min: Math.round(weight1[0] * 10) / 10,
          max: Math.round(weight1[1] * 10) / 10,
          units: weightUnit1,
        },
        temperature: {
          min: Math.round(temperature1[0] * 10) / 10,
          max: Math.round(temperature1[1] * 10) / 10,
          units: temperatureUnit1,
        },
      };
      if (isCheckedInclude1.length > 0 || isCheckedExclude1.length > 0) {
        countDataa.productsOwned = {
          condition: isCheckedInclude1.length > 0 ? 'include' : 'exclude',
          ids:
            isCheckedInclude1.length > 0
              ? isCheckedInclude1
              : isCheckedExclude1,
        };
      }

      try {
        const response = await getApiData(
          'audience/get-count',
          countDataa,
          'POST',
        );
        console.log('thi is weight api responce', response);
        console.log('this is count Data', countDataa);
        if (response.success) {
          const countData = response.count; // Extract the count from the API response
          setUpdateCount(countData); // Set the count in the state
        }
      } catch (countError) {
        console.log('Count error:', countError);
        // Handle the error from the get-count API
      }
    } else {
      const countDataa = {
        campaign_type: saveCampaign,
        age: {
          min: age[0],
          max: age[1],
          units: 'months',
        },
        aqi: {
          min: airQuality[0],
          max: airQuality[1],
          units: 'AQI',
        },
        gender,
        height: {
          min: height[0],
          max: height[1],
          units: 'cms',
        },
        languages: language,
        locations: mapCountry,
        no_of_children: childrens,
        weight: {
          min: Math.round(weight[0] * 10) / 10,
          max: Math.round(weight[1] * 10) / 10,
          units: weightUnit,
        },
        temperature: {
          min: temperature[0],
          max: temperature[1],
          units: temperatureUnit,
        },
      };

      if (isCheckedInclude.length > 0 || isCheckedExclude.length > 0) {
        countDataa.productsOwned = {
          condition: isCheckedInclude.length > 0 ? 'include' : 'exclude',
          ids:
            isCheckedInclude.length > 0 ? isCheckedInclude : isCheckedExclude,
        };
      }
      try {
        const response = await getApiData(
          'audience/get-count',
          countDataa,
          'POST',
        );
        console.log('thi is country api responce', response);
        console.log('this is count Data', countDataa);
        if (response.success) {
          const countData = response.count; // Extract the count from the API response
          const NewCount = countData;
          console.log('this is new count:', NewCount);
          setAudienceCount(NewCount);
        }
      } catch (countError) {
        console.log('Count error:', countError);
        // Handle the error from the get-count API
      }
    }
  };

  const isDataEmpty = Object.keys(product).length === 0;
  // Optimze Handler
  const optimizeHandler = async () => {
    if (product.length < 0) {
      Notification('error', 'Select a product');
    } else {
      setHeight([
        product.height.min_recommended_range,
        product.height.max_recommended_range,
      ]);
      setWeight([
        product.weight.min_recommended_range,
        product.weight.max_recommended_range,
      ]);
      setAge([
        product.age.min_recommended_range,
        product.age.max_recommended_range,
      ]);
      setTemperature([
        product.temperature.min_recommended_range,
        product.temperature.max_recommended_range,
      ]);
      setAirQuality([
        product.aqi.min_recommended_range,
        product.aqi.max_recommended_range,
      ]);
    }

    setAccuracy(true);

    const countData = {
      age: {
        accuracy_weightage: product.age.accuracy_weightage,
        max_recommended_range: product.age.max_recommended_range,
        min_recommended_range: product.age.min_recommended_range,
        max_manufacturer_range: product.age.max_manufacturer_range,
        min_manufacturer_range: product.age.min_manufacturer_range,
      },
      height: {
        accuracy_weightage: product.height.accuracy_weightage,
        max_recommended_range: product.height.max_recommended_range,
        min_recommended_range: product.height.min_recommended_range,
        max_manufacturer_range: product.height.max_manufacturer_range,
        min_manufacturer_range: product.height.min_manufacturer_range,
      },
      weight: {
        accuracy_weightage: product.weight.accuracy_weightage,
        max_recommended_range: product.weight.max_recommended_range,
        min_recommended_range: product.weight.min_recommended_range,
        max_manufacturer_range: product.weight.max_manufacturer_range,
        min_manufacturer_range: product.weight.min_manufacturer_range,
      },
      temperature: {
        accuracy_weightage: product.temperature.accuracy_weightage,
        max_recommended_range: product.temperature.max_recommended_range,
        min_recommended_range: product.temperature.min_recommended_range,
        max_manufacturer_range: product.temperature.max_manufacturer_range,
        min_manufacturer_range: product.temperature.min_manufacturer_range,
      },
      aqi: {
        accuracy_weightage: product.aqi.accuracy_weightage,
        max_recommended_range: product.aqi.max_recommended_range,
        min_recommended_range: product.aqi.min_recommended_range,
        max_manufacturer_range: product.aqi.max_manufacturer_range,
        min_manufacturer_range: product.aqi.min_manufacturer_range,
      },
    };

    try {
      const response = await getApiData(
        'audience/get-sales-accuracy',
        countData,
        'POST',
      );
      console.log('this is product api responce', response);
      console.log('this is count data', countData);
      if (response.success) {
        const totalCount = response.count; // Extract the count from the API response
        setAudienceCount(totalCount); // Use the functional update approach here
        console.log('This is audience Count:', totalCount); // Log the updated audience count here
        const totalAccuracy = response.overall_accuracy;
        setActualAccuracy(totalAccuracy);
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  // Optimze Handler
  const optimizeHandler1 = async () => {
    setHeight1([
      savedProduct.height.min_recommended_range,
      savedProduct.height.max_recommended_range,
    ]);
    setWeight1([
      savedProduct.weight.min_recommended_range,
      savedProduct.weight.max_recommended_range,
    ]);
    setAge1([
      savedProduct.age.min_recommended_range,
      savedProduct.age.max_recommended_range,
    ]);
    setTemperature1([
      savedProduct.temperature.min_recommended_range,
      savedProduct.temperature.max_recommended_range,
    ]);
    setAirQuality1([
      savedProduct.aqi.min_recommended_range,
      savedProduct.aqi.max_recommended_range,
    ]);

    setAccuracy1(true);

    const countData = {
      age: {
        accuracy_weightage: savedProduct.age.accuracy_weightage,
        max_recommended_range: savedProduct.age.max_recommended_range,
        min_recommended_range: savedProduct.age.min_recommended_range,
        max_manufacturer_range: savedProduct.age.max_manufacturer_range,
        min_manufacturer_range: savedProduct.age.min_manufacturer_range,
      },
      height: {
        accuracy_weightage: savedProduct.height.accuracy_weightage,
        max_recommended_range: savedProduct.height.max_recommended_range,
        min_recommended_range: savedProduct.height.min_recommended_range,
        max_manufacturer_range: savedProduct.height.max_manufacturer_range,
        min_manufacturer_range: savedProduct.height.min_manufacturer_range,
      },
      weight: {
        accuracy_weightage: savedProduct.weight.accuracy_weightage,
        max_recommended_range: savedProduct.weight.max_recommended_range,
        min_recommended_range: savedProduct.weight.min_recommended_range,
        max_manufacturer_range: savedProduct.weight.max_manufacturer_range,
        min_manufacturer_range: savedProduct.weight.min_manufacturer_range,
      },
      temperature: {
        accuracy_weightage: savedProduct.temperature.accuracy_weightage,
        max_recommended_range: savedProduct.temperature.max_recommended_range,
        min_recommended_range: savedProduct.temperature.min_recommended_range,
        max_manufacturer_range: savedProduct.temperature.max_manufacturer_range,
        min_manufacturer_range: savedProduct.temperature.min_manufacturer_range,
      },
      aqi: {
        accuracy_weightage: savedProduct.aqi.accuracy_weightage,
        max_recommended_range: savedProduct.aqi.max_recommended_range,
        min_recommended_range: savedProduct.aqi.min_recommended_range,
        max_manufacturer_range: savedProduct.aqi.max_manufacturer_range,
        min_manufacturer_range: savedProduct.aqi.min_manufacturer_range,
      },
    };

    try {
      const response = await getApiData(
        'audience/get-sales-accuracy',
        countData,
        'POST',
      );
      console.log('this is product api responce', response);
      console.log('this is count data', countData);
      if (response.success) {
        const totalCount = response.count; // Extract the count from the API response
        setUpdateCount(totalCount); // Use the functional update approach here
        console.log('This is audience Count:', totalCount); // Log the updated audience count here
        const totalAccuracy = response.overall_accuracy;
        setActualAccuracy1(totalAccuracy);
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  // const center = {
  //   lat: selectedCountryLatLng.lat,
  //   lng: selectedCountryLatLng.lng,
  // };

  return (
    <div
      style={{
        margin: 20,
        display: 'flex',
        flexDirection: 'row',
        gap: 20,
        flex: 1,
        height: '100vh',
      }}
    >
      <div
        style={{
          flex: 1,
          backgroundColor: '#fff',
          padding: 20,

          overflowY: 'scroll',
        }}
      >
        <div
          style={{
            display: 'flex',
            justifyItems: 'space-between',
            alignItems: 'center',
          }}
        >
          <div>
            <div
              style={{
                fontSize: 14,
                fontWeight: 'bold',
                letterSpacing: 1,
                opacity: 1,
                color: '#0a0606',
                display: 'flex',
              }}
            >
              <p>AUDIENCE</p>
            </div>
            <div style={{ display: 'flex', flexDirection: 'row' }}>
              <p
                style={{
                  color: '#0a0606',
                  letterSpacing: 0.7,
                  opacity: 0.5,
                  fontSize: 11,
                }}
              >
                Define who you want to see your Ads.
              </p>
              <p
                style={{
                  color: '#2a77f4',
                  opacity: 1,
                  fontWeight: 'bold',
                  fontSize: 11,
                }}
              >
                Learn More
              </p>
            </div>
          </div>
        </div>

        <div>
          <Tabs
            style={{ marginTop: 10, fontSize: 12 }}
            centered
            activeKey={activeTab}
            onChange={handleTabChange}
          >
            {/* Create new audience tab & use saved audience tab */}
            <TabPane
              tab={
                <span style={{ fontSize: '12px' }}>CREATE NEW AUDIENCE</span>
              }
              key="1"
            >
              {activeTab === '1' && (
                <CreateNewAudienceTab
                  onSaveValueChange={handleSaveValueChange}
                  onSaveCampaignChange={onSaveCampaignChange}
                  age={age}
                  setAge={setAge}
                  gender={gender}
                  setGender={setGender}
                  language={language}
                  setLanguage={setLanguage}
                  childrens={childrens}
                  setChildren={setChildren}
                  height={height}
                  setHeight={setHeight}
                  airQuality={airQuality}
                  setAirQuality={setAirQuality}
                  weight={weight}
                  setWeight={setWeight}
                  weightUnit={weightUnit}
                  setWeightUnit={setWeightUnit}
                  temperature={temperature}
                  setTemperature={setTemperature}
                  temperatureUnit={temperatureUnit}
                  setTemperatureUnit={setTemperatureUnit}
                  userTitle={userTitle}
                  setUserTitle={setUserTitle}
                  criteriaTitle={criteriaTitle}
                  isCheckedInclude={isCheckedInclude}
                  setIsCheckedInclude={setIsCheckedInclude}
                  isCheckedExclude={isCheckedExclude}
                  setIsCheckedExclude={setIsCheckedExclude}
                  product={product}
                  setProduct={setProduct}
                  propCountry={propCountry}
                  setAudienceCount={setAudienceCount}
                  setActualAccuracy={setActualAccuracy}
                  setProductsDetails={setProductsDetails}
                  productsDetails={productsDetails}
                  setAccuracy={setAccuracy}
                  setAccuracyBtn={setAccuracyBtn}
                />
              )}
            </TabPane>
            <TabPane
              tab={<span style={{ fontSize: '12px' }}>USE SAVED AUDIENCE</span>}
              key="2"
            >
              {activeTab === '2' && (
                <UseSaveAud handleEditClick={handleEditClick} />
              )}
            </TabPane>
            {editId && (
              <TabPane
                tab={
                  <span
                    style={{ fontSize: '12px', textTransform: 'uppercase' }}
                  >
                    {userTitle1}
                  </span>
                }
                key="3"
              >
                {activeTab === '3' && (
                  <UpdateAudTab
                    onSaveValueChange={handleSaveValueChange}
                    savedCampaign={savedCampaign}
                    savedProduct={savedProduct}
                    setSavedProduct={setSavedProduct}
                    age1={age1}
                    setAge1={setAge1}
                    gender1={gender1}
                    setGender1={setGender1}
                    language1={language1}
                    setLanguage1={setLanguage1}
                    childrens1={childrens1}
                    setChildren1={setChildren1}
                    height1={height1}
                    setHeight1={setHeight1}
                    airQuality1={airQuality1}
                    setAirQuality1={setAirQuality1}
                    weightUnit1={weightUnit1}
                    setWeightUnit1={setWeightUnit1}
                    temperatureUnit1={temperatureUnit1}
                    setTemperatureUnit1={setTemperatureUnit1}
                    weight1={weight1}
                    setWeight1={setWeight1}
                    temperature1={temperature1}
                    setTemperature1={setTemperature1}
                    userTitle1={userTitle1}
                    setUserTitle1={setUserTitle1}
                    criteriaTitle1={criteriaTitle1}
                    isCheckedInclude1={isCheckedInclude1}
                    setIsCheckedInclude1={setIsCheckedInclude1}
                    isCheckedExclude1={isCheckedExclude1}
                    setIsCheckedExclude1={setIsCheckedExclude1}
                    searchValueDetails={searchValueDetails}
                    setSearchValueDetails={setSearchValueDetails}
                    setUpdateCount={setUpdateCount}
                    propCountry={propCountry}
                    setAccuracy1={setAccuracy1}
                    setActualAccuracy1={setActualAccuracy1}
                    editId={editId}
                  />
                )}
              </TabPane>
            )}
          </Tabs>
        </div>
      </div>

      <div
        style={{
          flex: 1,
          backgroundColor: '#fff',
          padding: 20,
        }}
      >
        <p
          style={{
            color: '#0a0606',
            fontWeight: 'bold',
            fontSize: 12,
            marginBottom: 5,
          }}
        >
          LOCATIONS
        </p>

        {/* <div
          style={{
            marginTop: 10,
            padding: 8,
            backgroundColor: '#ededed',
          }}
        >
          <div
            style={{
              color: '#0A0606',
              fontSize: 11,
              opacity: 1,
              marginBottom: 5,
              fontWeight: 'bold',
            }}
          >
            <p>UNITED KINGDOM</p>
          </div>
          <div
            style={{
              backgroundColor: '#fff',
              padding: 4,
              display: 'flex',
              borderRadius: 2,
              opacity: 1,
              flexDirection: 'row',
            }}
          >
            <div>
              <EnvironmentFilled
                style={{
                  fontSize: 11,
                  marginRight: 5,
                  color: 'green',
                }}
              />
            </div>
            <div
              style={{
                color: '#0A0606',
                fontWeight: 'bold',
                fontSize: 11,
                opacity: 1,
                marginTop: 4,
              }}
            >
              <p>UNITED KINGDOM</p>
            </div>
          </div>
        </div> */}
        {/* Dropdown and search above the map */}
        <div
          style={{
            backgroundColor: '#fff',
            margin: '0px 2px 0px 2px',
            padding: 5,
            display: 'flex',
          }}
        >
          <Select
            placeholder="Select Countries"
            mode="multiple"
            optionFilterProp="children"
            size="small"
            style={{
              width: '100%',
            }}
            showSearch
            onChange={handleCountryOptions}
            value={selectedCountries.map((country) => country.name)}
            // value={country.country || undefined}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={countryOpts}
          />

          {/* <div
            style={{
              display: 'flex',
              alignItems: 'center',
              width: '100%',
            }}
          >
            <SearchOutlined
              style={{ fontSize: 14, marginLeft: 20, border: 'none' }}
            />
            <Input
              placeholder="SEARCH LOCATION"
              bordered={false}
              size="small"
              style={{ width: '100%' }}
              suffix={
                <span
                  style={{
                    color: '#0A0606',
                    opacity: 1,
                    letterSpacing: 1.2,
                    fontSize: 10,

                    fontWeight: 'bold',
                  }}
                >
                  BROWSE
                </span>
              }
            />
          </div > */}
        </div>
        {!isLoaded ? (
          <h1>Loading...</h1>
        ) : (
          <GoogleMap
            mapContainerStyle={containerStyle}
            center={{
              lat: defaultSelectedCountry.lat,
              lng: defaultSelectedCountry.lng,
            }}
            zoom={1}
          >
            {selectedCountries.map((country) => (
              <Marker
                key={country.id}
                position={{ lat: country.lat, lng: country.lng }}
              />
            ))}
          </GoogleMap>
        )}
        {/* <Image src={GoogleMap} preview={false} /> */}
        <div style={{ marginTop: 25 }}>
          <p
            style={{
              fontSize: 12,

              opacity: 1,

              fontWeight: 'bold',

              color: '#0A0606',
            }}
          >
            AUDIENCE INFO
          </p>
          <p
            style={{
              opacity: 1,
              color: '#000000',
              fontSize: 12,
            }}
          >
            <span style={{ fontSize: 12, fontWeight: 'bold' }}>
              {editId ? updateCount : audienceCount}
            </span>{' '}
            Customers In Audience
          </p>
        </div>
        {/* save text */}
        {saveValue === 'yes' && (
          <p
            style={{
              color: '#0A0606',
              fontSize: 12,
              marginTop: 20,
            }}
          >
            * Selected criteria will be saved with the title of the audience
          </p>
        )}
        {saveCampaign === 'sales' && !editId && accuracy && (
          <div
            style={{
              margin: '20px 0',
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            <p
              style={{
                fontSize: 12,
                opacity: 1,
                fontWeight: 'bold',
                color: '#0A0606',
                margin: '20px 0',
              }}
            >
              Estimate the accuracy of the audience
            </p>
            <Space wrap style={{ marginLeft: '20px' }}>
              <Progress
                type="circle"
                percent={actualAccuracy}
                format={(percent) => (percent === 100 ? '100%' : `${percent}%`)}
              />
            </Space>
          </div>
        )}
        {saveCampaign === 'sales' && !editId && accuracyBtn && !isDataEmpty && (
          <Button
            type="save"
            onClick={optimizeHandler}
            style={{
              background:
                'transparent linear-gradient(100deg, #2F95F7 0%, #46B6F8 100%) 0% 0% no-repeat padding-box',
              borderRadius: 10,
              color: '#fff',
              opacity: 1,
              margin: '20px',
              width: '150px',
            }}
          >
            Get Optimize Reach
          </Button>
        )}
        {savedCampaign === 'sales' && editId && accuracy1 && (
          <div
            style={{
              margin: '20px 0',
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            <p
              style={{
                fontSize: 12,
                opacity: 1,
                fontWeight: 'bold',
                color: '#0A0606',
                margin: '20px 0',
              }}
            >
              Estimate the accuracy of the audience
            </p>
            <Space wrap style={{ marginLeft: '20px' }}>
              <Progress
                type="circle"
                percent={actualAccuracy1}
                format={(percent) => (percent === 100 ? '100%' : `${percent}%`)}
              />
            </Space>
          </div>
        )}
        {savedCampaign === 'sales' && editId && accuracyBtn1 && (
          <Button
            type="save"
            onClick={optimizeHandler1}
            style={{
              background:
                'transparent linear-gradient(100deg, #2F95F7 0%, #46B6F8 100%) 0% 0% no-repeat padding-box',
              borderRadius: 10,
              color: '#fff',
              opacity: 1,
              margin: '20px',
              width: '150px',
            }}
          >
            Get Optimize reach
          </Button>
        )}
        <div
          style={{
            marginTop: 20,
            display: 'flex',
            justifyContent: 'flex-end',
          }}
        >
          <div>
            <Button
              type="save"
              onClick={handleButton}
              style={{
                background:
                  'transparent linear-gradient(100deg, #2F95F7 0%, #46B6F8 100%) 0% 0% no-repeat padding-box',

                borderRadius: 10,
                color: '#fff',

                opacity: 1,
              }}
            >
              Next
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Audience;

/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable prefer-destructuring */
/* eslint-disable no-shadow */
/* eslint-disable no-plusplus */
/* eslint-disable no-else-return */
/* eslint-disable react/no-array-index-key */
/* eslint-disable  react/jsx-no-bind  */
/* eslint-disable no-unused-vars */
/* eslint-disable no-irregular-whitespace */

import React, { useState, useEffect, useRef } from 'react';
import {
  Input,
  Slider,
  TreeSelect,
  Select,
  Radio,
  Checkbox,
  Tabs,
  Image,
  Empty,
} from 'antd';
import getApiData from '@chill/lib/helpers/apiHelper';
import { isArray } from 'lodash-es';
import { InfoCircleFilled, SearchOutlined } from '@ant-design/icons';

const { Option } = Select;
const { TabPane } = Tabs;
const { TreeNode } = TreeSelect;
const treeData = [
  {
    title: 'Demographics',
    value: 'Demographics',
    children: [
      {
        title: 'Education',
        value: 'Education',
        children: [
          {
            title: 'Field of study',
            value: 'Field of study',
            children: [
              {
                title: 'Chemistry',
                value: 'Chemistry',
              },
            ],
          },
        ],
      },
      {
        title: 'Financial',
        value: 'Financial',
        children: [
          {
            title: 'Budget',
            value: 'Budget',
          },
        ],
      },
      {
        title: 'Life events',
        value: 'Life events',
        children: [
          {
            title: 'Buying a home',
            value: 'Buying a home',
          },
        ],
      },
      {
        title: 'Parents',
        value: 'Parents',
        children: [
          {
            title: 'Authoritative parenting',
            value: 'Authoritative parenting',
          },
        ],
      },
      {
        title: 'Relationship',
        value: 'Relationship',
        children: [
          {
            title: 'Effective',
            value: 'Effective',
          },
        ],
      },
      {
        title: 'Work',
        value: 'Work',
        children: [
          {
            title: 'Building',
            value: 'Building',
          },
        ],
      },
    ],
  },
  {
    title: 'Interests',
    value: 'Interests',
    children: [
      {
        title: 'Painting',
        value: 'Painting',
      },
      {
        title: 'Writing',
        value: 'Writing',
      },
    ],
  },
  {
    title: 'Behaviors',
    value: 'Behaviors',
    children: [
      {
        title: 'Usage',
        value: 'Usage',
        children: [
          {
            title: 'Frequency',
            value: 'Frequency',
          },
          {
            title: 'Length of use',
            value: 'Length of use',
          },
        ],
      },
      {
        title: 'Travel',
        value: 'Travel',
        children: [
          {
            title: 'International',
            value: 'International',
          },
          {
            title: 'Domestic',
            value: 'Domestic',
          },
        ],
      },
    ],
  },
  {
    title: 'Environment',
    value: 'Environment',
    children: [
      {
        title: 'Type of Location',
        value: 'Type of Location',
        children: [
          {
            title: 'City',
            value: 'City',
          },
          {
            title: 'Beach',
            value: 'Beach',
          },
          {
            title: 'Mountains',
            value: 'Mountains',
          },
          {
            title: 'Countryside',
            value: 'Countryside',
          },
        ],
      },
    ],
  },
  {
    title: 'Product',
    value: 'Product',
    children: [
      {
        title: 'Ownership',
        value: 'Ownership',
        children: [
          {
            title: 'Length of time owned',
            value: 'Length of time owned',
          },
        ],
      },
    ],
  },
  {
    title: 'Engagement',
    value: 'Engagement',
    children: [
      {
        title: 'Response levels',
        value: 'Response levels',
        children: [
          {
            title: 'High',
            value: 'High',
          },
          {
            title: 'Low',
            value: 'Low',
          },
          {
            title: 'Medium',
            value: 'Medium',
          },
        ],
      },
      {
        title: 'purchasing behaviours',
        value: 'purchasing behaviours',
        children: [
          {
            title: 'Frequent',
            value: 'Frequent',
          },
          {
            title: 'Low',
            value: 'Low',
          },
        ],
      },
    ],
  },
];

function CreateNewAudienceTab({
  onSaveValueChange,
  onSaveCampaignChange,
  age,
  setAge,
  gender,
  setGender,
  language,
  setLanguage,
  childrens,
  setChildren,
  height,
  setHeight,
  airQuality,
  setAirQuality,
  weight,
  setWeight,
  weightUnit,
  setWeightUnit,
  temperature,
  setTemperature,
  temperatureUnit,
  setTemperatureUnit,
  userTitle,
  setUserTitle,
  criteriaTitle,
  isCheckedInclude,
  setIsCheckedInclude,
  isCheckedExclude,
  setIsCheckedExclude,
  product,
  setProduct,
  propCountry,
  setAudienceCount,
  setActualAccuracy,
  setProductsDetails,
  productsDetails,
  setAccuracy,
  setAccuracyBtn,
  editId,
}) {
  const updatedCountry = propCountry;
  const [saveValue, setSaveValue] = useState('yes');
  const [selectedValues, setSelectedValues] = useState([]);
  const [nodePaths, setNodePaths] = useState([]);
  const [campaignChange, setCampaignChange] = useState('awareness');
  const [images, setImages] = useState([]);

  const [languageOptions, setLanguageOptions] = useState([]);

  // Handling save text based on yes or no options in audience tab

  useEffect(() => {
    onSaveValueChange(saveValue);
  }, [saveValue, onSaveValueChange]);

  useEffect(() => {
    onSaveCampaignChange(campaignChange);
  }, [campaignChange, onSaveCampaignChange]);

  const handleRadioChange = (e) => {
    setSaveValue(e.target.value);
  };

  const handleRadioBtnChange = async (e) => {
    setCampaignChange(e.target.value);

    const camp = e.target.value;
    if (camp === 'awareness') {
      const countDataa = {
        campaign_type: camp,
        age: {
          min: 0,
          max: 52,
          units: 'months',
        },
        aqi: {
          min: 6,
          max: 150,
          units: 'AQI',
        },
        gender: '',
        height: {
          min: 35,
          max: 60,
          units: 'cms',
        },
        languages: [],
        locations: ['United States'],
        no_of_children: [],

        weight: {
          min: 0,
          max: 25,
          units: 'kg',
        },
        temperature: {
          min: 20,
          max: 68,
          units: 'F',
        },
      };
      // if (isCheckedInclude.length > 0 || isCheckedExclude.length > 0) {
      //   countDataa.productsOwned = {
      //     condition: isCheckedInclude.length > 0 ? 'include' : 'exclude',
      //     ids:
      //       isCheckedInclude.length > 0 ? isCheckedInclude : isCheckedExclude,
      //   };
      // }
      try {
        const response = await getApiData(
          'audience/get-count',
          countDataa,
          'POST',
        );
        console.log('thi is weight api responce', response);
        console.log('this is count Data', countDataa);
        if (response.success) {
          const countData = response.count; // Extract the count from the API response
          console.log('this is set count Data', countData);
          setAudienceCount(countData); // Set the count in the state
        }
      } catch (countError) {
        console.log('Count error:', countError);
        // Handle the error from the get-count API
      }
    }
  };

  // Handling user title and criteria title

  const handleUserTitleChange = (e) => {
    const value = e.target.value;
    const regex = /^[^\d]*$/; // Regex to match any character except numbers

    if (regex.test(value)) {
      setUserTitle(value);
    }
  };

  // Handling weight slider

  const handleWeightChange = (value) => {
    setAccuracy(false);
    setAccuracyBtn(true);
    setWeight(value);
  };

  const handleWeightUnitChange = (e) => {
    const checked = e.target.value === 'lbs';
    const newWeight = checked
      ? [weight[0] * 2.2, weight[1] * 2.2]
      : [weight[0] / 2.2, weight[1] / 2.2];
    setWeightUnit(checked ? 'lbs' : 'kg');
    setWeight(newWeight);
  };

  const weightRadioGroupProps = {
    value: weightUnit,
    onChange: handleWeightUnitChange,
  };

  const onAfterWeightChange = async (value) => {
    console.log('onAfterChange value:', value);
    const updatedWeight = value;
    console.log('updatedWeight:', updatedWeight);

    setWeight(updatedWeight, () => {
      console.log('weight after setHeight:', weight);
    });

    const countDataa = {
      campaign_type: campaignChange,
      age: {
        min: age[0],
        max: age[1],
        units: 'months',
      },
      aqi: {
        min: airQuality[0],
        max: airQuality[1],
        units: 'AQI',
      },
      gender,
      height: {
        min: height[0],
        max: height[1],
        units: 'cms',
      },
      languages: language,
      locations: updatedCountry,
      no_of_children: childrens,
      weight: {
        min: Math.round(updatedWeight[0] * 10) / 10,
        max: Math.round(updatedWeight[1] * 10) / 10,
        units: weightUnit,
      },
      temperature: {
        min: Math.round(temperature[0] * 10) / 10,
        max: Math.round(temperature[1] * 10) / 10,
        units: temperatureUnit,
      },
    };
    if (isCheckedInclude.length > 0 || isCheckedExclude.length > 0) {
      countDataa.productsOwned = {
        condition: isCheckedInclude.length > 0 ? 'include' : 'exclude',
        ids: isCheckedInclude.length > 0 ? isCheckedInclude : isCheckedExclude,
      };
    }
    console.log('this is count Data', countDataa);

    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('thi is weight api responce', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count; // Extract the count from the API response
        setAudienceCount(countData); // Set the count in the state
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  const weightSliderProps = {
    range: true,
    min: 0,
    max: weightUnit === 'kg' ? 50 : 110,
    step: 0.1,
    onChange: handleWeightChange,
    onAfterChange: onAfterWeightChange,
    value: weight,
  };

  const weightUnitLabel = weightUnit === 'kg' ? '(kgs)' : '(lbs)';
  const weightUnitLabel1 = weightUnit === 'kg' ? 'kgs' : 'lbs';

  // Handling temperature slider

  const handleTemperatureChange = (value) => {
    setAccuracy(false);
    setAccuracyBtn(true);
    setTemperature(value);
  };

  const handleTemperatureUnitChange = (e) => {
    const checked = e.target.value === 'C';
    const newTemperature = checked
      ? [(temperature[0] - 32) / 1.8, (temperature[1] - 32) / 1.8]
      : [temperature[0] * 1.8 + 32, temperature[1] * 1.8 + 32];
    setTemperatureUnit(checked ? 'C' : 'F');
    setTemperature(newTemperature);
  };

  const temperatureRadioGroupProps = {
    value: temperatureUnit,
    onChange: handleTemperatureUnitChange,
  };

  const onAfterTemperatureChange = async (value) => {
    console.log('onAfterChange value:', value);
    const updatedTemperature = value;
    console.log('updatedWeight:', updatedTemperature);
    setTemperature(updatedTemperature, () => {
      console.log('temperature after setHeight:', temperature);
    });
    const countDataa = {
      campaign_type: campaignChange,
      age: {
        min: age[0],
        max: age[1],
        units: 'months',
      },
      aqi: {
        min: airQuality[0],
        max: airQuality[1],
        units: 'AQI',
      },
      gender,
      height: {
        min: height[0],
        max: height[1],
        units: 'cms',
      },
      languages: language,
      locations: updatedCountry,
      no_of_children: childrens,
      weight: {
        min: Math.round(weight[0] * 10) / 10,
        max: Math.round(weight[1] * 10) / 10,
        units: weightUnit,
      },
      temperature: {
        min: Math.round(updatedTemperature[0] * 10) / 10,
        max: Math.round(updatedTemperature[1] * 10) / 10,
        units: temperatureUnit,
      },
    };

    if (isCheckedInclude.length > 0 || isCheckedExclude.length > 0) {
      countDataa.productsOwned = {
        condition: isCheckedInclude.length > 0 ? 'include' : 'exclude',
        ids: isCheckedInclude.length > 0 ? isCheckedInclude : isCheckedExclude,
      };
    }

    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('thi is weight api responce', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count; // Extract the count from the API response
        setAudienceCount(countData); // Set the count in the state
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  const temperatureSliderProps = {
    range: true,
    min: -40,
    max: temperatureUnit === 'F' ? 150 : 75,
    step: 0.1,
    onChange: handleTemperatureChange,
    onAfterChange: onAfterTemperatureChange,
    value: temperature,
  };

  const temperatureUnitLabel = temperatureUnit === 'C' ? '(°C)' : '(F)';

  // Handling Age slider

  const onAfterChange1 = async (value) => {
    console.log('onAfterChange value:', value);
    const updatedAge = value;
    console.log('updatedAge:', updatedAge);
    setAge(updatedAge, () => {
      console.log('Age after setHeight:', age);
    });
    const countDataa = {
      campaign_type: campaignChange,
      age: {
        min: updatedAge[0],
        max: updatedAge[1],
        units: 'months',
      },
      aqi: {
        min: airQuality[0],
        max: airQuality[1],
        units: 'AQI',
      },
      gender,
      height: {
        min: height[0],
        max: height[1],
        units: 'cms',
      },
      languages: language,
      locations: updatedCountry,
      no_of_children: childrens,
      weight: {
        min: Math.round(weight[0] * 10) / 10,
        max: Math.round(weight[1] * 10) / 10,
        units: weightUnit,
      },
      temperature: {
        min: Math.round(temperature[0] * 10) / 10,
        max: Math.round(temperature[1] * 10) / 10,
        units: temperatureUnit,
      },
    };

    if (isCheckedInclude.length > 0 || isCheckedExclude.length > 0) {
      countDataa.productsOwned = {
        condition: isCheckedInclude.length > 0 ? 'include' : 'exclude',
        ids: isCheckedInclude.length > 0 ? isCheckedInclude : isCheckedExclude,
      };
    }

    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('thi is weight api responce', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count; // Extract the count from the API response
        setAudienceCount(countData); // Set the count in the state
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  const onChange1 = (value) => {
    console.log('onChange: ', value);
    setAccuracy(false);
    setAccuracyBtn(true);
    setAge(value);
  };

  // Handling Height slider

  const onAfterChange2 = async (value) => {
    console.log('onAfterChange value:', value);
    const updatedHeight = value;
    console.log('updatedHeight:', updatedHeight);

    setHeight(updatedHeight, () => {
      console.log('height after setHeight:', height);
    });
    const countDataa = {
      campaign_type: campaignChange,
      age: {
        min: age[0],
        max: age[1],
        units: 'months',
      },
      aqi: {
        min: airQuality[0],
        max: airQuality[1],
        units: 'AQI',
      },
      gender,
      height: {
        min: updatedHeight[0],
        max: updatedHeight[1],
        units: 'cms',
      },
      languages: language,
      locations: updatedCountry,
      no_of_children: childrens,
      weight: {
        min: Math.round(weight[0] * 10) / 10,
        max: Math.round(weight[1] * 10) / 10,
        units: weightUnit,
      },
      temperature: {
        min: Math.round(temperature[0] * 10) / 10,
        max: Math.round(temperature[1] * 10) / 10,
        units: temperatureUnit,
      },
    };
    if (isCheckedInclude.length > 0 || isCheckedExclude.length > 0) {
      countDataa.productsOwned = {
        condition: isCheckedInclude.length > 0 ? 'include' : 'exclude',
        ids: isCheckedInclude.length > 0 ? isCheckedInclude : isCheckedExclude,
      };
    }

    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('thi is weight api responce', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count; // Extract the count from the API response
        setAudienceCount(countData); // Set the count in the state
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  const onChange2 = (value) => {
    console.log('onChange: ', value);
    setAccuracy(false);
    setAccuracyBtn(true);
    setHeight(value);
  };

  // Handling Air quality slider

  const onAfterChange5 = async (value) => {
    console.log('onAfterChange value:', value);
    const updatedAqi = value;
    console.log('updatedaqi:', updatedAqi);
    setAirQuality(updatedAqi, () => {
      console.log('aqi after setaqi:', airQuality);
    });
    const countDataa = {
      campaign_type: campaignChange,
      age: {
        min: age[0],
        max: age[1],
        units: 'months',
      },
      aqi: {
        min: updatedAqi[0],
        max: updatedAqi[1],
        units: 'AQI',
      },
      gender,
      height: {
        min: height[0],
        max: height[1],
        units: 'cms',
      },
      languages: language,
      locations: updatedCountry,
      no_of_children: childrens,
      weight: {
        min: Math.round(weight[0] * 10) / 10,
        max: Math.round(weight[1] * 10) / 10,
        units: weightUnit,
      },
      temperature: {
        min: Math.round(temperature[0] * 10) / 10,
        max: Math.round(temperature[1] * 10) / 10,
        units: temperatureUnit,
      },
    };
    if (isCheckedInclude.length > 0 || isCheckedExclude.length > 0) {
      countDataa.productsOwned = {
        condition: isCheckedInclude.length > 0 ? 'include' : 'exclude',
        ids: isCheckedInclude.length > 0 ? isCheckedInclude : isCheckedExclude,
      };
    }

    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('thi is weight api responce', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count; // Extract the count from the API response
        setAudienceCount(countData); // Set the count in the state
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  const onChange5 = (value) => {
    console.log('onChange: ', value);
    setAccuracy(false);
    setAccuracyBtn(true);
    setAirQuality(value);
  };

  // Handling gender dropdown

  const handleGenderChange = async (option) => {
    console.log('onAfterChange value:', option);
    const updatedGender = option;
    console.log('updatedGender:', updatedGender);
    setGender(updatedGender, () => {
      console.log('gender after setgender:', gender);
    });
    setAccuracy(false);
    setAccuracyBtn(true);
    const countDataa = {
      campaign_type: campaignChange,
      age: {
        min: age[0],
        max: age[1],
        units: 'months',
      },
      aqi: {
        min: airQuality[0],
        max: airQuality[1],
        units: 'AQI',
      },
      gender: updatedGender,
      height: {
        min: height[0],
        max: height[1],
        units: 'cms',
      },
      languages: language,
      locations: updatedCountry,
      no_of_children: childrens,
      weight: {
        min: Math.round(weight[0] * 10) / 10,
        max: Math.round(weight[1] * 10) / 10,
        units: weightUnit,
      },
      temperature: {
        min: Math.round(temperature[0] * 10) / 10,
        max: Math.round(temperature[1] * 10) / 10,
        units: temperatureUnit,
      },
    };
    if (isCheckedInclude.length > 0 || isCheckedExclude.length > 0) {
      countDataa.productsOwned = {
        condition: isCheckedInclude.length > 0 ? 'include' : 'exclude',
        ids: isCheckedInclude.length > 0 ? isCheckedInclude : isCheckedExclude,
      };
    }

    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('thi is weight api responce', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count; // Extract the count from the API response
        setAudienceCount(countData); // Set the count in the state
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  // Handling children dropdown

  const handleChildrenSelect = async (option) => {
    console.log('onAfterChange value:', option);
    const updatedChildren = option;
    console.log('updatedaqi:', updatedChildren);
    setChildren(updatedChildren, () => {
      console.log('gender after setgender:', childrens);
    });
    setAccuracy(false);
    setAccuracyBtn(true);
    const countDataa = {
      campaign_type: campaignChange,
      age: {
        min: age[0],
        max: age[1],
        units: 'months',
      },
      aqi: {
        min: airQuality[0],
        max: airQuality[1],
        units: 'AQI',
      },
      gender,
      height: {
        min: height[0],
        max: height[1],
        units: 'cms',
      },
      languages: language,
      locations: updatedCountry,
      no_of_children: updatedChildren,
      weight: {
        min: Math.round(weight[0] * 10) / 10,
        max: Math.round(weight[1] * 10) / 10,
        units: weightUnit,
      },
      temperature: {
        min: Math.round(temperature[0] * 10) / 10,
        max: Math.round(temperature[1] * 10) / 10,
        units: temperatureUnit,
      },
    };
    if (isCheckedInclude.length > 0 || isCheckedExclude.length > 0) {
      countDataa.productsOwned = {
        condition: isCheckedInclude.length > 0 ? 'include' : 'exclude',
        ids: isCheckedInclude.length > 0 ? isCheckedInclude : isCheckedExclude,
      };
    }

    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('thi is weight api responce', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count; // Extract the count from the API response
        setAudienceCount(countData); // Set the count in the state
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  const childOptions = [
    { label: '1', value: '1' },
    { label: '2', value: '2' },
    { label: '3', value: '3' },
    { label: '4', value: '4' },
    { label: '5', value: '5' },
    { label: '6', value: '6' },
  ];

  // Handling Language dropdown

  const handleLanguageSelect = async (value) => {
    console.log('onAfterChange value:', value);
    const updatedLanguage = value;
    console.log('updatelanguage:', updatedLanguage);
    setLanguage(updatedLanguage, () => {
      console.log('gender after setgender:', language);
    });
    setAccuracy(false);
    setAccuracyBtn(true);

    const countDataa = {
      campaign_type: campaignChange,
      age: {
        min: age[0],
        max: age[1],
        units: 'months',
      },
      aqi: {
        min: airQuality[0],
        max: airQuality[1],
        units: 'AQI',
      },
      gender,
      height: {
        min: height[0],
        max: height[1],
        units: 'cms',
      },
      languages: updatedLanguage,
      locations: updatedCountry,
      no_of_children: childrens,
      weight: {
        min: Math.round(weight[0] * 10) / 10,
        max: Math.round(weight[1] * 10) / 10,
        units: weightUnit,
      },
      temperature: {
        min: Math.round(temperature[0] * 10) / 10,
        max: Math.round(temperature[1] * 10) / 10,
        units: temperatureUnit,
      },
    };

    if (isCheckedInclude.length > 0 || isCheckedExclude.length > 0) {
      countDataa.productsOwned = {
        condition: isCheckedInclude.length > 0 ? 'include' : 'exclude',
        ids: isCheckedInclude.length > 0 ? isCheckedInclude : isCheckedExclude,
      };
    }

    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('thi is weight api responce', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count; // Extract the count from the API response
        setAudienceCount(countData); // Set the count in the state
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  const fetchLanguages = async () => {
    try {
      const response = await getApiData('getLanguageList', {}, 'POST');
      console.log(response.data);
      if (response.success) {
        console.log(response.data);
        setLanguageOptions(response.data);
      }
    } catch (error) {
      console.error('Error fetching images:', error);
    }
  };

  const fetchImages = async () => {
    try {
      const response = await getApiData('product/get-brand-products', 'GET');
      console.log(response.data);
      if (response.success && isArray(response.data)) {
        console.log(response.data);
        setImages(response.data);
        setProductsDetails(response.data);
      }
    } catch (error) {
      console.error('Error fetching images:', error);
    }
  };

  useEffect(() => {
    fetchImages();
    fetchLanguages();
  }, []);

  const handleIncludeImageClick = async (id) => {
    const newIncludeIds = isCheckedInclude.includes(id)
      ? isCheckedInclude.filter((item) => item !== id)
      : [...isCheckedInclude, id];

    const newExcludeIds = isCheckedExclude.filter((item) => item !== id);

    setIsCheckedInclude(newIncludeIds);
    setIsCheckedExclude(newExcludeIds);
    setAccuracy(false);
    setAccuracyBtn(true);
    const countDataa = {
      campaign_type: campaignChange,
      age: {
        min: age[0],
        max: age[1],
        units: 'months',
      },
      aqi: {
        min: airQuality[0],
        max: airQuality[1],
        units: 'AQI',
      },
      gender,
      height: {
        min: height[0],
        max: height[1],
        units: 'cms',
      },
      languages: language,
      locations: updatedCountry,
      no_of_children: childrens,
      weight: {
        min: Math.round(weight[0] * 10) / 10,
        max: Math.round(weight[1] * 10) / 10,
        units: weightUnit,
      },
      temperature: {
        min: Math.round(temperature[0] * 10) / 10,
        max: Math.round(temperature[1] * 10) / 10,
        units: temperatureUnit,
      },
    };

    if (newIncludeIds.length > 0 || newExcludeIds.length > 0) {
      countDataa.productsOwned = {
        condition: newIncludeIds.length > 0 ? 'include' : 'exclude',
        ids: newIncludeIds.length > 0 ? newIncludeIds : newExcludeIds,
      };
    }

    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('this is weight api response', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count;
        setAudienceCount(countData);
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  const handleExcludeImageClick = async (id) => {
    const newExcludeIds = isCheckedExclude.includes(id)
      ? isCheckedExclude.filter((item) => item !== id)
      : [...isCheckedExclude, id];

    const newIncludeIds = isCheckedInclude.filter((item) => item !== id);

    setIsCheckedInclude(newIncludeIds);
    setIsCheckedExclude(newExcludeIds);
    setAccuracy(false);
    setAccuracyBtn(true);
    const countDataa = {
      campaign_type: campaignChange,
      age: {
        min: age[0],
        max: age[1],
        units: 'months',
      },
      aqi: {
        min: airQuality[0],
        max: airQuality[1],
        units: 'AQI',
      },
      gender,
      height: {
        min: height[0],
        max: height[1],
        units: 'cms',
      },
      languages: language,
      locations: updatedCountry,
      no_of_children: childrens,
      weight: {
        min: Math.round(weight[0] * 10) / 10,
        max: Math.round(weight[1] * 10) / 10,
        units: weightUnit,
      },
      temperature: {
        min: Math.round(temperature[0] * 10) / 10,
        max: Math.round(temperature[1] * 10) / 10,
        units: temperatureUnit,
      },
    };

    if (newIncludeIds.length > 0 || newExcludeIds.length > 0) {
      countDataa.productsOwned = {
        condition: newIncludeIds.length > 0 ? 'include' : 'exclude',
        ids: newIncludeIds.length > 0 ? newIncludeIds : newExcludeIds,
      };
    }

    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('this is weight api response', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count;
        setAudienceCount(countData);
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  const handleIncludeCheckboxClick = async (id) => {
    const newIncludeIds = isCheckedInclude.includes(id)
      ? isCheckedInclude.filter((item) => item !== id)
      : [...isCheckedInclude, id];

    const newExcludeIds = isCheckedExclude.filter((item) => item !== id);

    setIsCheckedInclude(newIncludeIds);
    setIsCheckedExclude(newExcludeIds);
    setAccuracy(false);
    setAccuracyBtn(true);
    const countDataa = {
      campaign_type: campaignChange,
      age: {
        min: age[0],
        max: age[1],
        units: 'months',
      },
      aqi: {
        min: airQuality[0],
        max: airQuality[1],
        units: 'AQI',
      },
      gender,
      height: {
        min: height[0],
        max: height[1],
        units: 'cms',
      },
      languages: language,
      locations: updatedCountry,
      no_of_children: childrens,
      weight: {
        min: Math.round(weight[0] * 10) / 10,
        max: Math.round(weight[1] * 10) / 10,
        units: weightUnit,
      },
      temperature: {
        min: Math.round(temperature[0] * 10) / 10,
        max: Math.round(temperature[1] * 10) / 10,
        units: temperatureUnit,
      },
    };

    if (newIncludeIds.length > 0 || newExcludeIds.length > 0) {
      countDataa.productsOwned = {
        condition: newIncludeIds.length > 0 ? 'include' : 'exclude',
        ids: newIncludeIds.length > 0 ? newIncludeIds : newExcludeIds,
      };
    }

    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('this is weight api response', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count;
        setAudienceCount(countData);
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  const handleExcludeCheckboxClick = async (id) => {
    const newExcludeIds = isCheckedExclude.includes(id)
      ? isCheckedExclude.filter((item) => item !== id)
      : [...isCheckedExclude, id];

    const newIncludeIds = isCheckedInclude.filter((item) => item !== id);

    setIsCheckedInclude(newIncludeIds);
    setIsCheckedExclude(newExcludeIds);
    setAccuracy(false);
    setAccuracyBtn(true);
    const countDataa = {
      campaign_type: campaignChange,
      age: {
        min: age[0],
        max: age[1],
        units: 'months',
      },
      aqi: {
        min: airQuality[0],
        max: airQuality[1],
        units: 'AQI',
      },
      gender,
      height: {
        min: height[0],
        max: height[1],
        units: 'cms',
      },
      languages: language,
      locations: updatedCountry,
      no_of_children: childrens,
      weight: {
        min: Math.round(weight[0] * 10) / 10,
        max: Math.round(weight[1] * 10) / 10,
        units: weightUnit,
      },
      temperature: {
        min: Math.round(temperature[0] * 10) / 10,
        max: Math.round(temperature[1] * 10) / 10,
        units: temperatureUnit,
      },
    };

    if (newIncludeIds.length > 0 || newExcludeIds.length > 0) {
      countDataa.productsOwned = {
        condition: newIncludeIds.length > 0 ? 'include' : 'exclude',
        ids: newIncludeIds.length > 0 ? newIncludeIds : newExcludeIds,
      };
    }

    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('this is weight api response', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count;
        setAudienceCount(countData);
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  const includeTabImages = images.filter(
    (image) => !isCheckedExclude.includes(image.id),
  );

  const excludeTabImages = images.filter(
    (image) => !isCheckedInclude.includes(image.id),
  );

  // Handling Detailed targeted search and options

  const getNodePath = (data, targetValue, currentPath = []) => {
    for (let i = 0; i < data.length; i++) {
      const node = data[i];
      const path = [...currentPath, node.title];

      if (node.value === targetValue) {
        return path;
      }

      if (node.children) {
        const foundPath = getNodePath(node.children, targetValue, path);
        if (foundPath) {
          return foundPath;
        }
      }
    }

    return null;
  };

  const isEqualPaths = (path1, path2) => {
    if (path1.length !== path2.length) {
      return false;
    }

    for (let i = 0; i < path1.length; i++) {
      if (path1[i] !== path2[i]) {
        return false;
      }
    }

    return true;
  };

  const handleSelect = (value, node) => {
    if (!node.children) {
      const nodePath = getNodePath(treeData, node.value);
      if (selectedValues.includes(value)) {
        setSelectedValues(selectedValues.filter((val) => val !== value));
        setNodePaths(nodePaths.filter((path) => !isEqualPaths(path, nodePath)));
      } else {
        setSelectedValues([...selectedValues, value]);
        setNodePaths([...nodePaths, nodePath]);
      }
    }
  };

  const renderTreeNodes = (data) => {
    return data.map((node) => {
      const title = (
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}
        >
          <span>{node.title}</span>
          <span>
            {[
              'Demographics',
              'Interests',
              'Behaviors',
              'Environment',
              'Product',
              'Engagement',
            ].includes(node.title) && (
              <InfoCircleFilled style={{ opacity: 0.5 }} />
            )}
          </span>
        </div>
      );

      return (
        <TreeNode
          title={title}
          value={node.value}
          key={node.value}
          style={{
            borderBottom: '1px solid #e8e8e8',

            width: '100%',
            padding: 5,
          }}
        >
          {node.children && renderTreeNodes(node.children)}
        </TreeNode>
      );
    });
  };

  // eslint-disable-next-line no-shadow
  // const getExpectedReach = (age, height, weight) => {
  //   const [minAge, maxAge] = [age[0], age[1]];
  //   const [minHeight, maxHeight] = [height[0], height[1]];
  //   const [minWeight, maxWeight] = [weight[0], weight[1]];

  //   const occAge = age[1] - age[0];
  //   const occHeight = height[1] - height[0];
  //   const occWeight = weight[1] - weight[0];
  //   const totalOccPer = 100 - ((occAge + occHeight + occWeight) / 350) * 100;

  //   return totalOccPer;
  // };

  const handleProductChange = async (name) => {
    const prodItem = productsDetails.filter(
      (item) => item.product_name === name,
    )[0];
    setProduct(prodItem);
    setHeight([
      prodItem.height.min_recommended_range,
      prodItem.height.max_recommended_range,
    ]);
    setWeight([
      prodItem.weight.min_recommended_range,
      prodItem.weight.max_recommended_range,
    ]);
    setAge([
      prodItem.age.min_recommended_range,
      prodItem.age.max_recommended_range,
    ]);
    setTemperature([
      prodItem.temperature.min_recommended_range,
      prodItem.temperature.max_recommended_range,
    ]);
    setAirQuality([
      prodItem.aqi.min_recommended_range,
      prodItem.aqi.max_recommended_range,
    ]);
    setWeightUnit(prodItem.weight.units);
    setTemperatureUnit(prodItem.temperature.units);
    setProduct(prodItem);
    // setExpectedReach(expReach);
    setAccuracy(true);
    setAccuracyBtn(true);
    setIsCheckedInclude(prodItem.products_owned || []);
    setIsCheckedExclude(prodItem.products_not_owned || []);
    const countData = {
      age: {
        accuracy_weightage: prodItem.age.accuracy_weightage,
        max_recommended_range: prodItem.age.max_recommended_range,
        min_recommended_range: prodItem.age.min_recommended_range,
        max_manufacturer_range: prodItem.age.max_manufacturer_range,
        min_manufacturer_range: prodItem.age.min_manufacturer_range,
      },
      height: {
        accuracy_weightage: prodItem.height.accuracy_weightage,
        max_recommended_range: prodItem.height.max_recommended_range,
        min_recommended_range: prodItem.height.min_recommended_range,
        max_manufacturer_range: prodItem.height.max_manufacturer_range,
        min_manufacturer_range: prodItem.height.min_manufacturer_range,
      },
      weight: {
        accuracy_weightage: prodItem.weight.accuracy_weightage,
        max_recommended_range: prodItem.weight.max_recommended_range,
        min_recommended_range: prodItem.weight.min_recommended_range,
        max_manufacturer_range: prodItem.weight.max_manufacturer_range,
        min_manufacturer_range: prodItem.weight.min_manufacturer_range,
      },
      temperature: {
        accuracy_weightage: prodItem.temperature.accuracy_weightage,
        max_recommended_range: prodItem.temperature.max_recommended_range,
        min_recommended_range: prodItem.temperature.min_recommended_range,
        max_manufacturer_range: prodItem.temperature.max_manufacturer_range,
        min_manufacturer_range: prodItem.temperature.min_manufacturer_range,
      },
      aqi: {
        accuracy_weightage: prodItem.aqi.accuracy_weightage,
        max_recommended_range: prodItem.aqi.max_recommended_range,
        min_recommended_range: prodItem.aqi.min_recommended_range,
        max_manufacturer_range: prodItem.aqi.max_manufacturer_range,
        min_manufacturer_range: prodItem.aqi.min_manufacturer_range,
      },
    };

    try {
      const response = await getApiData(
        'audience/get-sales-accuracy',
        countData,
        'POST',
      );
      console.log('this is product api responce', response);
      console.log('this is count data', countData);
      if (response.success) {
        const totalCount = response.count; // Extract the count from the API response
        setAudienceCount(totalCount); // Use the functional update approach here
        console.log('This is audience Count:', totalCount); // Log the updated audience count here
        const totalAccuracy = response.overall_accuracy;
        setActualAccuracy(totalAccuracy);
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };
  const productOptions = productsDetails.map((item) => {
    return { value: item.product_name, label: item.product_name };
  });

  return (
    <div>
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          marginTop: 5,
        }}
      >
        <p style={{ color: '#0a0606', fontWeight: 'bold' }}>
          TITLE OF AUDIENCE CRITERIA
        </p>
      </div>
      <div style={{ marginTop: 5 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Input
            placeholder="User Title"
            type="text"
            size="small"
            value={userTitle}
            onChange={handleUserTitleChange}
            required
            style={{
              width: '15vw',
            }}
          />
          <div style={{ display: 'flex', flexDirection: 'row', width: '15vw' }}>
            <p
              style={{
                marginRight: 15,
                fontWeight: 'bold',
                marginTop: 3,
              }}
            >
              SAVE AUDIENCE:
            </p>
            <Radio.Group
              size="small"
              onChange={handleRadioChange}
              value={saveValue}
            >
              <Radio.Button
                value="yes"
                style={{
                  backgroundColor: saveValue === 'yes' ? '#2a77f4' : '',
                  color: saveValue === 'yes' ? 'white' : '',
                  borderRadius: '10px 0px 0px 10px',
                }}
              >
                {' '}
                Yes
              </Radio.Button>
              <Radio.Button
                value="no"
                style={{
                  backgroundColor: saveValue === 'no' ? '#2a77f4' : '',
                  color: saveValue === 'no' ? 'white' : '',
                  borderRadius: '0px 10px 10px 0px',
                }}
              >
                {' '}
                No
              </Radio.Button>
            </Radio.Group>
          </div>
        </div>
        {userTitle && (
          <Input
            style={{ marginTop: 10 }}
            bordered={false}
            size="small"
            value={userTitle ? criteriaTitle : ''}
            readOnly
          />
        )}
      </div>
      <p style={{ marginTop: 10, color: '#0a0606', fontWeight: 'bold' }}>
        TYPE OF CAMPAIGN
      </p>
      <div
        style={{
          marginTop: 10,
          display: 'flex',
          alignItems: 'center',
        }}
      >
        <Radio.Group onChange={handleRadioBtnChange} value={campaignChange}>
          <Radio value="awareness">Awareness campaign</Radio>
          <Radio value="sales">Sales campaign</Radio>
        </Radio.Group>
      </div>
      {campaignChange === 'awareness' ? (
        <>
          {/* Age slider */}
          <p style={{ color: '#0a0606', fontWeight: 'bold', marginTop: 10 }}>
            AGE
            <span style={{ color: '#0a0606', fontWeight: 'normal' }}>
              (months)
            </span>
          </p>
          <div style={{ display: 'flex' }}>
            <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
            <Slider
              range
              min={0}
              max={150}
              // defaultValue={[4, 20]}
              value={age}
              onChange={onChange1}
              onAfterChange={onAfterChange1}
              trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
              railStyle={{ boxShadow: '0px 0px 6px #00000029', height: 5 }}
              style={{ width: '100%' }}
            />
            <span style={{ margin: '5px 0px 0px 5px' }}>150</span>
          </div>

          <span
            style={{
              color: '#040606',
              opacity: 0.5,
            }}
          >{`${age[0]} - ${age[1]} months`}</span>

          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              marginTop: 10,
            }}
          >
            {/* Gender dropdown */}
            <div>
              <p
                style={{
                  color: '#0a0606',
                  fontWeight: 'bold',
                  marginBottom: 5,
                }}
              >
                GENDER
              </p>
              <Select
                placeholder="All Genders"
                optionFilterProp="children"
                size="small"
                style={{
                  minWidth: '15vw',
                }}
                onChange={handleGenderChange}
                value={gender || undefined}
                filterOption={(input, option) =>
                  (option?.label ?? '')
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                options={[
                  {
                    value: 'male',
                    label: 'Male',
                  },
                  {
                    value: 'female',
                    label: 'Female',
                  },
                  {
                    value: 'others',
                    label: 'Others',
                  },
                ]}
              />
            </div>
            {/* Languages dropdown */}
            <div>
              <p
                style={{
                  color: '#0a0606',
                  fontWeight: 'bold',
                  marginBottom: 5,
                }}
              >
                LANGUAGES
              </p>
              <Select
                size="small"
                mode="multiple"
                style={{
                  minWidth: '15vw',
                }}
                placeholder="All Languages"
                onChange={handleLanguageSelect}
                maxTagCount="responsive"
                value={language}
                filterOption={(inputValue, option) =>
                  option.children
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
              >
                {languageOptions.map((option) => (
                  <Option key={option.id} value={option.id}>
                    {option.lang_name}
                  </Option>
                ))}
              </Select>
            </div>
          </div>
          {/* Children dropdown */}
          <div>
            <p
              style={{
                color: '#0a0606',
                fontWeight: 'bold',
                marginBottom: 5,
                marginTop: 10,
              }}
            >
              CHILDREN
            </p>
            <Select
              size="small"
              mode="multiple"
              style={{
                minWidth: '15vw',
              }}
              placeholder="Select no of Children"
              onChange={handleChildrenSelect}
              value={childrens}
            >
              {childOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </div>
          {/* Height Slider */}
          <p style={{ color: '#0a0606', fontWeight: 'bold', marginTop: 10 }}>
            HEIGHT
            <span style={{ color: '#0a0606', fontWeight: 'normal' }}>
              (cms)
            </span>
          </p>
          <div style={{ display: 'flex' }}>
            <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
            <Slider
              trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
              railStyle={{ boxShadow: '0px 0px 6px #00000029', height: 5 }}
              range
              step={0.1}
              min={0}
              max={150}
              value={height}
              onChange={onChange2}
              onAfterChange={onAfterChange2}
              style={{ width: '100%' }}
            />
            <span style={{ margin: '5px 0px 0px 0px' }}>150</span>
          </div>
          <span
            style={{
              color: '#040606',
              opacity: 0.5,
            }}
          >{`${height[0]} - ${height[1]} cms`}</span>

          {/* Weight Slider */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              marginTop: 10,
            }}
          >
            <div style={{ display: 'flex' }}>
              <p
                style={{ color: '#0a0606', fontWeight: 'bold', marginRight: 5 }}
              >
                WEIGHT
              </p>
              <span>{weightUnitLabel}</span>
            </div>
            <div
              style={{
                marginRight: 13,
              }}
            >
              <Radio.Group {...weightRadioGroupProps} size="small">
                <Radio.Button
                  value="kg"
                  style={{
                    backgroundColor: weightUnit === 'kg' ? '#2a77f4' : '',
                    color: weightUnit === 'kg' ? 'white' : '',
                    borderRadius: '10px 0px 0px 10px',
                  }}
                >
                  kgs
                </Radio.Button>
                <Radio.Button
                  value="lbs"
                  style={{
                    backgroundColor: weightUnit === 'lbs' ? '#2a77f4' : '',
                    color: weightUnit === 'lbs' ? 'white' : '',
                    borderRadius: '0px 10px 10px 0px',
                  }}
                >
                  lbs
                </Radio.Button>
              </Radio.Group>
            </div>
          </div>
          <div style={{ display: 'flex' }}>
            <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
            <Slider
              {...weightSliderProps}
              trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
              railStyle={{ boxShadow: '0px 0px 6px #00000029', height: 5 }}
              style={{ width: '100%' }}
              defaultValue={[0, 40]}
              value={[weight[0], weight[1]]}
            />
            <span style={{ margin: '5px 0px 0px 3px' }}>
              {weightUnit === 'kg' ? 50 : 110}
            </span>
          </div>
          <span style={{ color: '#040606', opacity: 0.5 }}>{`${
            Math.round(weight[0] * 10) / 10
          } - ${Math.round(weight[1] * 10) / 10} ${weightUnitLabel1}`}</span>

          {/* Temperature Slider */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              marginTop: 10,
            }}
          >
            <div style={{ display: 'flex' }}>
              <p
                style={{ color: '#0a0606', fontWeight: 'bold', marginRight: 5 }}
              >
                TEMPERATURE
              </p>
              <span>{temperatureUnitLabel}</span>
            </div>
            <div
              style={{
                marginRight: 13,
              }}
            >
              <Radio.Group {...temperatureRadioGroupProps} size="small">
                <Radio.Button
                  value="F"
                  style={{
                    backgroundColor: temperatureUnit === 'F' ? '#2a77f4' : '',
                    color: temperatureUnit === 'F' ? 'white' : '',
                    width: '2.7vw',
                    textAlign: 'center',
                    borderRadius: '10px 0px 0px 10px',
                  }}
                >
                  F
                </Radio.Button>
                <Radio.Button
                  value="C"
                  style={{
                    backgroundColor: temperatureUnit === 'C' ? '#2a77f4' : '',
                    color: temperatureUnit === 'C' ? 'white' : '',
                    width: '2.7vw',
                    textAlign: 'center',
                    borderRadius: '0px 10px 10px 0px',
                  }}
                >
                  °C
                </Radio.Button>
              </Radio.Group>
            </div>
          </div>
          <div style={{ display: 'flex' }}>
            <span style={{ margin: '5px 5px 0px 0px' }}>-40</span>
            <Slider
              {...temperatureSliderProps}
              trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
              railStyle={{ boxShadow: '0px 0px 6px #00000029', height: 5 }}
              style={{ width: '100%' }}
            />
            <span style={{ margin: '5px 0px 0px 0px' }}>
              {temperatureUnit === 'F' ? 150 : 75}
            </span>
          </div>
          <span style={{ color: '#040606', opacity: 0.5 }}>{`${
            Math.round(temperature[0] * 10) / 10
          } - ${
            Math.round(temperature[1] * 10) / 10
          } ${temperatureUnit}`}</span>

          {/* Air Quality Slider */}
          <p style={{ color: '#0a0606', fontWeight: 'bold', marginTop: 10 }}>
            AIR QUALITY
            <span style={{ color: '#0a0606', fontWeight: 'normal' }}>
              {' '}
              (AQI)
            </span>
          </p>
          <div style={{ display: 'flex' }}>
            <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
            <Slider
              trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
              railStyle={{ boxShadow: '0px 0px 6px #00000029', height: 5 }}
              min={0}
              max={1000}
              range
              value={airQuality}
              onChange={onChange5}
              onAfterChange={onAfterChange5}
              style={{ width: '100%' }}
            />
            <span style={{ margin: '5px 0px 0px 0px' }}>1000</span>
          </div>
          <span
            style={{
              color: '#040606',
              opacity: 0.5,
            }}
          >{`${airQuality[0]} - ${airQuality[1]} AQI`}</span>

          {/* Products owned Section Images & Checkbox */}
          <div style={{ marginTop: 10, marginBottom: 10 }}>
            <p
              style={{
                color: '#0a0606',
                fontWeight: 'bold',
              }}
            >
              PRODUCTS
            </p>
            <Tabs defaultActiveKey="1">
              <TabPane tab="Owned" key="1">
                {includeTabImages.length === 0 ? (
                  <Empty />
                ) : (
                  <div
                    style={{
                      display: 'flex',
                      overflowX: 'scroll',
                      marginTop: 10,
                      gap: 10,
                    }}
                  >
                    {includeTabImages.map((image) => (
                      <div
                        style={{
                          position: 'relative',
                          display: 'inline-block',
                          border: '1px solid #e2e2e2',
                          borderRadius: 5,
                        }}
                      >
                        <Image
                          style={{
                            width: '23vh',
                            height: '23vh',
                            borderRadius: 6,
                          }}
                          src={image.product_image}
                          preview={false}
                          onClick={() => handleIncludeImageClick(image.id)}
                        />
                        <p
                          style={{
                            'max-width': '23vh',
                            'text-overflow': 'ellipsis',
                            'white-space': 'nowrap',
                            'text-align': 'center',
                            overflow: 'hidden',
                          }}
                        >
                          {image.product_name}
                        </p>
                        <Checkbox
                          checked={isCheckedInclude.includes(image.id)}
                          style={{
                            position: 'absolute',
                            top: 4,
                            right: 6,
                            zIndex: 1,
                          }}
                          onClick={() => handleIncludeCheckboxClick(image.id)}
                        />
                      </div>
                    ))}
                  </div>
                )}
              </TabPane>
              <TabPane tab="Not Owned" key="2">
                {excludeTabImages.length === 0 ? (
                  <Empty />
                ) : (
                  <div
                    style={{
                      display: 'flex',
                      marginTop: 10,
                      gap: 10,
                      overflowX: 'scroll',
                    }}
                  >
                    {excludeTabImages.map((image) => (
                      <div
                        style={{
                          position: 'relative',
                          display: 'inline-block',
                          border: '1px solid #e2e2e2',
                          borderRadius: 5,
                        }}
                      >
                        <Image
                          style={{
                            width: '23vh',
                            height: '23vh',
                            borderRadius: 6,
                          }}
                          src={image.product_image}
                          preview={false}
                          onClick={() => handleExcludeImageClick(image.id)}
                        />
                        <p
                          style={{
                            'max-width': '23vh',
                            'text-overflow': 'ellipsis',
                            'white-space': 'nowrap',
                            'text-align': 'center',
                            overflow: 'hidden',
                          }}
                        >
                          {image.product_name}
                        </p>
                        <Checkbox
                          checked={isCheckedExclude.includes(image.id)}
                          style={{
                            position: 'absolute',
                            top: 4,
                            right: 6,
                            zIndex: 1,
                          }}
                          onClick={() => handleExcludeCheckboxClick(image.id)}
                        />
                      </div>
                    ))}
                  </div>
                )}
              </TabPane>
            </Tabs>
          </div>

          {/* Detailed Targeted section with Search & suggestions */}
          <p style={{ color: '#0a0606', fontWeight: 'bold', marginTop: 10 }}>
            DETAILED TARGETED
          </p>
          <div style={{ display: 'flex' }}>
            <p
              style={{
                color: '#0a0606',
                letterspacing: 0.7,
                opacity: 0.5,
              }}
            >
              Include people who match
            </p>
            <InfoCircleFilled
              style={{ marginLeft: 6, opacity: 0.5, marginTop: 3 }}
            />
          </div>
          <TreeSelect
            showSearch
            style={{ width: '100%', borderRadius: 5 }}
            dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
            placeholder={
              <span>
                <SearchOutlined style={{ marginRight: 8 }} />
                Add demographics, interests, or behaviors
              </span>
            }
            allowClear
            multiple
            size="small"
            showArrow={false}
            treeExpandAction="click"
            value={selectedValues}
            onSelect={handleSelect}
            onDeselect={handleSelect}
          >
            {renderTreeNodes(treeData)}
          </TreeSelect>
          {selectedValues.length > 0 && (
            <div
              style={{
                padding: 10,
                backgroundColor: '#efefef',
                borderRadius: '0px  0px 5px 5px',
              }}
            >
              {selectedValues.map((value, index) => (
                <div key={value}>
                  {nodePaths[index] && (
                    <p>
                      {nodePaths[index]
                        .slice(0, -1)
                        .map((node, i) => (
                          <span key={i} style={{ color: '#1890ff' }}>
                            {node}
                          </span>
                        ))
                        .reduce((prev, curr) => [prev, ' > ', curr])}
                    </p>
                  )}
                  <div
                    style={{
                      backgroundColor: '#fff',
                      padding: '5px 3px',
                      margin: '5px 0px',
                      borderRadius: 5,
                    }}
                  >
                    <p>{value}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </>
      ) : (
        <div>
          {/* New Version for Sales campaign */}
          {/* New product select dropdown */}
          <Select
            placeholder="Select Any Product"
            optionFilterProp="children"
            size="lg"
            style={{
              width: '100%',
              marginTop: '20px',
            }}
            onChange={handleProductChange}
            value={product.product_name || undefined}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={productOptions}
          />
          <>
            {/* Age slider */}
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                marginTop: 10,
              }}
            >
              {/* Gender dropdown */}
              <div>
                <p
                  style={{
                    color: '#0a0606',
                    fontWeight: 'bold',
                    marginBottom: 5,
                  }}
                >
                  GENDER
                </p>
                <Select
                  placeholder="All Genders"
                  optionFilterProp="children"
                  size="small"
                  style={{
                    minWidth: '15vw',
                  }}
                  onChange={handleGenderChange}
                  value={gender || undefined}
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={[
                    {
                      value: 'male',
                      label: 'Male',
                    },
                    {
                      value: 'female',
                      label: 'Female',
                    },
                    {
                      value: 'others',
                      label: 'Others',
                    },
                  ]}
                />
              </div>
              {/* Languages dropdown */}
              <div>
                <p
                  style={{
                    color: '#0a0606',
                    fontWeight: 'bold',
                    marginBottom: 5,
                  }}
                >
                  LANGUAGES
                </p>
                <Select
                  size="small"
                  mode="multiple"
                  style={{
                    minWidth: '15vw',
                  }}
                  placeholder="All Languages"
                  onChange={handleLanguageSelect}
                  maxTagCount="responsive"
                  value={language}
                  filterOption={(inputValue, option) =>
                    option.children
                      .toLowerCase()
                      .indexOf(inputValue.toLowerCase()) >= 0
                  }
                >
                  {languageOptions.map((option) => (
                    <Option key={option.id} value={option.id}>
                      {option.lang_name}
                    </Option>
                  ))}
                </Select>
              </div>
            </div>
            {/* Children dropdown */}
            <div>
              <p
                style={{
                  color: '#0a0606',
                  fontWeight: 'bold',
                  marginBottom: 5,
                  marginTop: 10,
                }}
              >
                CHILDREN
              </p>
              <Select
                size="small"
                mode="multiple"
                style={{
                  minWidth: '15vw',
                }}
                placeholder="Select no of Children"
                onChange={handleChildrenSelect}
                value={childrens}
              >
                {childOptions.map((option) => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </div>
            <p style={{ color: '#0a0606', fontWeight: 'bold', marginTop: 10 }}>
              AGE
              <span style={{ color: '#0a0606', fontWeight: 'normal' }}>
                {' '}
                (months)
              </span>
            </p>
            <div style={{ display: 'flex' }}>
              <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
              <Slider
                range
                min={0}
                max={150}
                // defaultValue={[4, 20]}
                value={age}
                onChange={onChange1}
                onAfterChange={onAfterChange1}
                trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
                railStyle={{ boxShadow: '0px 0px 6px #00000029', height: 5 }}
                style={{ width: '100%' }}
              />
              <span style={{ margin: '5px 0px 0px 5px' }}>150</span>
            </div>
            <span
              style={{
                color: '#040606',
                opacity: 0.5,
              }}
            >{`${age[0]} - ${age[1]} months`}</span>

            {/* Height Slider */}
            <p style={{ color: '#0a0606', fontWeight: 'bold', marginTop: 10 }}>
              HEIGHT
              <span style={{ color: '#0a0606', fontWeight: 'normal' }}>
                {' '}
                (cms)
              </span>
            </p>
            <div style={{ display: 'flex' }}>
              <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
              <Slider
                trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
                railStyle={{ boxShadow: '0px 0px 6px #00000029', height: 5 }}
                range
                step={0.1}
                min={0}
                max={150}
                // defaultValue={[50, 100]}
                value={height}
                onChange={onChange2}
                onAfterChange={onAfterChange2}
                style={{ width: '100%' }}
              />
              <span style={{ margin: '5px 0px 0px 0px' }}>150</span>
            </div>
            <span
              style={{
                color: '#040606',
                opacity: 0.5,
              }}
            >{`${height[0]} - ${height[1]} cms`}</span>

            {/* Weight Slider */}
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginTop: 10,
              }}
            >
              <div style={{ display: 'flex' }}>
                <p
                  style={{
                    color: '#0a0606',
                    fontWeight: 'bold',
                    marginRight: 5,
                  }}
                >
                  WEIGHT
                </p>
                <span>{weightUnitLabel}</span>
              </div>
              <div
                style={{
                  marginRight: 13,
                }}
              >
                <Radio.Group {...weightRadioGroupProps} size="small">
                  <Radio.Button
                    value="kg"
                    style={{
                      backgroundColor: weightUnit === 'kg' ? '#2a77f4' : '',
                      color: weightUnit === 'kg' ? 'white' : '',
                      borderRadius: '10px 0px 0px 10px',
                    }}
                  >
                    kgs
                  </Radio.Button>
                  <Radio.Button
                    value="lbs"
                    style={{
                      backgroundColor: weightUnit === 'lbs' ? '#2a77f4' : '',
                      color: weightUnit === 'lbs' ? 'white' : '',
                      borderRadius: '0px 10px 10px 0px',
                    }}
                  >
                    lbs
                  </Radio.Button>
                </Radio.Group>
              </div>
            </div>
            <div style={{ display: 'flex' }}>
              <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
              <Slider
                {...weightSliderProps}
                trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
                railStyle={{ boxShadow: '0px 0px 6px #00000029', height: 5 }}
                style={{ width: '100%' }}
              />
              <span style={{ margin: '5px 0px 0px 3px' }}>
                {weightUnit === 'kg' ? 50 : 110}
              </span>
            </div>
            <span style={{ color: '#040606', opacity: 0.5 }}>{`${
              Math.round(weight[0] * 10) / 10
            } - ${Math.round(weight[1] * 10) / 10} ${weightUnitLabel1}`}</span>

            {/* Temperature Slider */}
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginTop: 10,
              }}
            >
              <div style={{ display: 'flex' }}>
                <p
                  style={{
                    color: '#0a0606',
                    fontWeight: 'bold',
                    marginRight: 5,
                  }}
                >
                  TEMPERATURE
                </p>
                <span>{temperatureUnitLabel}</span>
              </div>
              <div
                style={{
                  marginRight: 13,
                }}
              >
                <Radio.Group {...temperatureRadioGroupProps} size="small">
                  <Radio.Button
                    value="F"
                    style={{
                      backgroundColor: temperatureUnit === 'F' ? '#2a77f4' : '',
                      color: temperatureUnit === 'F' ? 'white' : '',
                      width: '2.7vw',
                      textAlign: 'center',
                      borderRadius: '10px 0px 0px 10px',
                    }}
                  >
                    F
                  </Radio.Button>
                  <Radio.Button
                    value="C"
                    style={{
                      backgroundColor: temperatureUnit === 'C' ? '#2a77f4' : '',
                      color: temperatureUnit === 'C' ? 'white' : '',
                      width: '2.7vw',
                      textAlign: 'center',
                      borderRadius: '0px 10px 10px 0px',
                    }}
                  >
                    °C
                  </Radio.Button>
                </Radio.Group>
              </div>
            </div>
            <div style={{ display: 'flex' }}>
              <span style={{ margin: '5px 5px 0px 0px' }}>-40</span>
              <Slider
                {...temperatureSliderProps}
                trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
                railStyle={{ boxShadow: '0px 0px 6px #00000029', height: 5 }}
                style={{ width: '100%' }}
              />
              <span style={{ margin: '5px px 0px 0px' }}>
                {temperatureUnit === 'F' ? 150 : 75}
              </span>
            </div>
            <span style={{ color: '#040606', opacity: 0.5 }}>{`${
              Math.round(temperature[0] * 10) / 10
            } - ${
              Math.round(temperature[1] * 10) / 10
            } ${temperatureUnit}`}</span>

            {/* Air Quality Slider */}
            <p style={{ color: '#0a0606', fontWeight: 'bold', marginTop: 10 }}>
              AIR QUALITY
              <span style={{ color: '#0a0606', fontWeight: 'normal' }}>
                {' '}
                (AQI)
              </span>
            </p>
            <div style={{ display: 'flex' }}>
              <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
              <Slider
                trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
                railStyle={{ boxShadow: '0px 0px 6px #00000029', height: 5 }}
                min={0}
                max={1000}
                range
                value={airQuality}
                onChange={onChange5}
                onAfterChange={onAfterChange5}
                style={{ width: '100%' }}
              />
              <span style={{ margin: '5px 0px 0px 0px' }}>1000</span>
            </div>
            <span
              style={{
                color: '#040606',
                opacity: 0.5,
              }}
            >{`${airQuality[0]} - ${airQuality[1]} AQI`}</span>

            {/* Products owned Section Images & Checkbox */}
            <div style={{ marginTop: 10, marginBottom: 10 }}>
              <p
                style={{
                  color: '#0a0606',
                  fontWeight: 'bold',
                }}
              >
                PRODUCTS
              </p>
              <Tabs defaultActiveKey="1">
                <TabPane tab="Owned" key="1">
                  {includeTabImages.length === 0 ? (
                    <Empty />
                  ) : (
                    <div
                      style={{
                        display: 'flex',
                        overflowX: 'scroll',
                        marginTop: 10,
                        gap: 10,
                      }}
                    >
                      {includeTabImages.map((image) => (
                        <div
                          style={{
                            position: 'relative',
                            display: 'inline-block',
                            border: '1px solid #e2e2e2',
                            borderRadius: 5,
                          }}
                        >
                          <Image
                            style={{
                              width: '23vh',
                              height: '23vh',
                              borderRadius: 6,
                            }}
                            src={image.product_image}
                            preview={false}
                            onClick={() => handleIncludeImageClick(image.id)}
                          />
                          <p
                            style={{
                              'max-width': '23vh',
                              'text-overflow': 'ellipsis',
                              'white-space': 'nowrap',
                              'text-align': 'center',
                              overflow: 'hidden',
                            }}
                          >
                            {image.product_name}
                          </p>
                          <Checkbox
                            checked={isCheckedInclude.includes(image.id)}
                            style={{
                              position: 'absolute',
                              top: 4,
                              right: 6,
                              zIndex: 1,
                            }}
                            onClick={() => handleIncludeCheckboxClick(image.id)}
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </TabPane>
                <TabPane tab="Not Owned" key="2">
                  {excludeTabImages.length === 0 ? (
                    <Empty />
                  ) : (
                    <div
                      style={{
                        display: 'flex',
                        marginTop: 10,
                        gap: 10,
                        overflowX: 'scroll',
                      }}
                    >
                      {excludeTabImages.map((image) => (
                        <div
                          style={{
                            position: 'relative',
                            display: 'inline-block',
                            border: '1px solid #e2e2e2',
                            borderRadius: 5,
                          }}
                        >
                          <Image
                            style={{
                              width: '23vh',
                              height: '23vh',
                              borderRadius: 6,
                            }}
                            src={image.product_image}
                            preview={false}
                            onClick={() => handleExcludeImageClick(image.id)}
                          />
                          <p
                            style={{
                              'max-width': '23vh',
                              'text-overflow': 'ellipsis',
                              'white-space': 'nowrap',
                              'text-align': 'center',
                              overflow: 'hidden',
                            }}
                          >
                            {image.product_name}
                          </p>
                          <Checkbox
                            checked={isCheckedExclude.includes(image.id)}
                            style={{
                              position: 'absolute',
                              top: 4,
                              right: 6,
                              zIndex: 1,
                            }}
                            onClick={() => handleExcludeCheckboxClick(image.id)}
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </TabPane>
              </Tabs>
            </div>

            {/* Detailed Targeted section with Search & suggestions */}
            <p style={{ color: '#0a0606', fontWeight: 'bold', marginTop: 10 }}>
              DETAILED TARGETED
            </p>
            <div style={{ display: 'flex' }}>
              <p
                style={{
                  color: '#0a0606',
                  letterspacing: 0.7,
                  opacity: 0.5,
                }}
              >
                Include people who match
              </p>
              <InfoCircleFilled
                style={{ marginLeft: 6, opacity: 0.5, marginTop: 3 }}
              />
            </div>
            <TreeSelect
              showSearch
              style={{ width: '100%', borderRadius: 5 }}
              dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
              placeholder={
                <span>
                  <SearchOutlined style={{ marginRight: 8 }} />
                  Add demographics, interests, or behaviors
                </span>
              }
              allowClear
              multiple
              size="small"
              showArrow={false}
              treeExpandAction="click"
              value={selectedValues}
              onSelect={handleSelect}
              onDeselect={handleSelect}
            >
              {renderTreeNodes(treeData)}
            </TreeSelect>
            {selectedValues.length > 0 && (
              <div
                style={{
                  padding: 10,
                  backgroundColor: '#efefef',
                  borderRadius: '0px  0px 5px 5px',
                }}
              >
                {selectedValues.map((value, index) => (
                  <div key={value}>
                    {nodePaths[index] && (
                      <p>
                        {nodePaths[index]
                          .slice(0, -1)
                          .map((node, i) => (
                            <span key={i} style={{ color: '#1890ff' }}>
                              {node}
                            </span>
                          ))
                          .reduce((prev, curr) => [prev, ' > ', curr])}
                      </p>
                    )}
                    <div
                      style={{
                        backgroundColor: '#fff',
                        padding: '5px 3px',
                        margin: '5px 0px',
                        borderRadius: 5,
                      }}
                    >
                      <p>{value}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        </div>
      )}
    </div>
  );
}

export default CreateNewAudienceTab;

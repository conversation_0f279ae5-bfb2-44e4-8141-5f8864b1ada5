/* eslint-disable react/jsx-no-bind */
/* eslint-disable no-shadow */
/* eslint-disable no-param-reassign */
/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
import React from 'react';
import { Col, Input, Row, Select, Upload, Radio } from 'antd';
import { useSelector } from 'react-redux';
import IntlMessages from '@chill/components/utility/intlMessages';
import Form from '@chill/components/uielements/form';
import { FormWrapper } from '@chill/assets/styles/drawerFormStyles';
import {
  AlignCenterOutlined,
  AlignLeftOutlined,
  AlignRightOutlined,
  PaperClipOutlined,
  PlusCircleOutlined,
} from '@ant-design/icons';
import theme from '@chill/config/theme/default';
import { SketchPicker } from 'react-color';
import { checkImage, getBase64 } from '@chill/lib/helpers/utility';
import Datepicker from '@chill/components/uielements/datePicker';
import moment from 'moment';
import Notification from '@chill/components/Notification';
import { find, isEmpty } from 'lodash-es';
import { ReactComponent as Top } from '../../assets/images/marketing_icon/Top.svg';
import { ReactComponent as Bottom } from '../../assets/images/marketing_icon/Bottom.svg';
import { ReactComponent as Center } from '../../assets/images/marketing_icon/center.svg';
import { ReactComponent as Full } from '../../assets/images/marketing_icon/Full.svg';
import { fontSize } from '../../config/staticData/list';
import PopOver from '../Widgets/Components/PopOver';
import RadioGroup from '../Widgets/Components/RadioGroup';

const RenderInAppPurchase = (prop) => {
  const {
    state,
    setState = () => {},
    propIntl,
    initVal,
    submitUser = () => {},
    segmentList,
    form,
  } = prop;

  const btnArr = state.btnCnt;
  const { intl } = propIntl;
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  const handleChange = (info) => {
    let fileList = [...info.fileList];
    console.log('fileList ==>', fileList, info);
    fileList = fileList.slice(-2);
    fileList = fileList.map((file) => {
      if (file.response) {
        file.url = file.response.url;
      }
      return file;
    });
    setState((p) => ({ ...p, file: fileList }));
  };
  const handleSizeChange = (value, type) => {
    console.log(`selected ${value}`);
    const { singleData } = state;
    const objIndex = btnArr.findIndex((obj) => obj.id === singleData.id);
    if (type === 'buttonSize') {
      btnArr[objIndex].button_text_size = value;
      setState((p) => ({ ...p, buttonFontSize: value }));
    } else {
      setState((p) => ({ ...p, fontSize: value }));
    }
  };

  const { Option } = Select;

  function onChangeSelect(value) {
    console.log(`selected ${value}`);
    setState((p) => ({ ...p, sendTo: value }));
    let count = 0;
    console.log('log ==>', value);
    if (value) {
      count = segmentList && find(segmentList, (o) => o.id === value);
    }
    setState((p) => ({
      ...p,
      userCount: count?.userCount ? count.userCount : 0,
    }));
  }

  function onBlur() {
    console.log('blur');
  }

  function onFocus() {
    console.log('focus');
  }

  function onSearch(val) {
    console.log('search:', val);
  }
  const beforeUpload = (file) => {
    setState((p) => ({ ...p, errorMsg: '' }));
    const res = checkImage(file);
    if (res.status) {
      // if (type === 'crop') {
      //   return true;
      // }
      setState((p) => ({ ...p, fileName: file?.name }));
      getBase64(file, (imageUrl) => {
        setState((p) => ({ ...p, selectfileUrl: imageUrl, loading: false }));
        console.log('imageUrl ==>', imageUrl);
      });
      setState((p) => ({ ...p, fileUrl: file, loading: false }));
    } else {
      Notification('error', res.message);
    }
    console.log('fileurl ==>', state.fileUrl, res, file);
    return false;
  };
  function onRadioChange(e) {
    if (e.target.value === 'full') {
      setState((p) => ({ ...p, attachmentLike: true }));
    } else {
      setState((p) => ({ ...p, attachmentLike: false }));
    }
    setState((p) => ({ ...p, messageType: e.target.value }));
  }
  function onAignRadioChange(e) {
    setState((p) => ({ ...p, textAlign: e.target.value }));
  }
  function onTextAignRadioChange(e, data) {
    const { value } = e.target;
    const objIndex = btnArr.findIndex((obj) => obj.id === data.id);
    btnArr[objIndex].button_text_align = value;
    setState((p) => ({ ...p, buttonTextAlign: value }));
  }

  function handleChangeComplete(color) {
    const { singleData } = state;
    const objIndex = btnArr.findIndex((obj) => obj.id === singleData.id);
    if (state.colorType === 'back') {
      btnArr[objIndex].background_color = color.hex;
    } else if (state.colorType === 'button') {
      btnArr[objIndex].button_text_color = color.hex;
    }

    if (state.colorType === 'text') {
      setState((p) => ({ ...p, textColor: color.hex, colorType: '' }));
      form.setFieldsValue({ text_color: color.hex });
    }
    setState((p) => ({ ...p, singleData: state.singleData }));
  }
  const colorPicker = () => {
    return (
      <SketchPicker
        color={state.textColor}
        onChangeComplete={(color) => {
          handleChangeComplete(color);
        }}
      />
    );
  };
  const props = {
    // action: 'https://www.mocky.io/v2/5cc8019d300000980a055e76',
    onChange: handleChange,
    multiple: false,
  };

  const radioContent = () => (
    <>
      <Radio.Button value="left">
        <AlignLeftOutlined />
      </Radio.Button>
      <Radio.Button value="center">
        <AlignCenterOutlined />
      </Radio.Button>
      <Radio.Button value="right">
        <AlignRightOutlined />
      </Radio.Button>
    </>
  );

  function onDateChange(e, type) {
    const date = moment(e).format('DD-MM-YYYY');
    if (type === 'last') {
      if (state?.startDate === '') {
        Notification('error', messageArray['err.startDate']);
      } else {
        setState((p) => ({ ...p, endDate: date }));
      }
    } else {
      setState((p) => ({ ...p, startDate: date }));
    }
  }

  function disabledDate(current) {
    // Can not select days before today and today
    return current && current > moment().endOf('day');
  }

  const radioMessageTypeContent = () => (
    <Row>
      <Col xs={6} xl={6} md={6} sm={6}>
        <Radio.Button value="top" className="radioButtonSelect">
          <div className="messageTypeWrapper">
            <Top />
          </div>
        </Radio.Button>
      </Col>
      <Col xs={6} xl={6} md={6} sm={6}>
        <Radio.Button value="center" checked className="radioButtonSelect">
          <div className="messageTypeWrapper">
            <Center />
          </div>
        </Radio.Button>
      </Col>
      <Col xs={6} xl={6} md={6} sm={6}>
        <Radio.Button value="bottom" className="radioButtonSelect">
          <div className="messageTypeWrapper">
            <Bottom />
          </div>
        </Radio.Button>
      </Col>
      <Col xs={6} xl={6} md={6} sm={6}>
        <Radio.Button
          value="full"
          style={{ display: 'inline-table' }}
          className="radioButtonSelect"
        >
          <div className="messageTypeWrapper">
            <Full />
          </div>
        </Radio.Button>
      </Col>
    </Row>
  );

  const btnObj = {
    id: btnArr?.length + 1,
    button_text: initVal?.button_text || state.btnText || 'Button1',
    button_text_color: initVal.button_text_color || '#000',
    button_text_size: initVal.button_text_size || '16',
    button_text_align: initVal.button_text_align || 'center',
    background_color: initVal.background_color || '#1172EC',
  };

  const { btnCnt } = state;
  return (
    <Col xl={24} xs={24} sm={24} md={24}>
      <FormWrapper>
        <Form
          requiredMark={false}
          form={form}
          onFinish={submitUser}
          layout="vertical"
          initialValues={{
            ...initVal,
            attachments: initVal?.post_file,
            text1:
              (state?.type === initVal?.campaign_type &&
                initVal?.text_info?.text_name) ||
              '',
            text_color:
              (state?.type === initVal?.campaign_type &&
                initVal?.text_info?.text_color) ||
              '',
            text_align:
              (state?.type === initVal?.campaign_type &&
                initVal?.text_info?.text_color) ||
              '',
            start_date:
              state?.type === initVal?.campaign_type &&
              moment(initVal?.start_date, 'YYYY-MM-DD')
                ? moment(initVal?.start_date, 'YYYY-MM-DD')
                : '',
            end_date:
              state?.type === initVal?.campaign_type &&
              moment(initVal?.end_date, 'YYYY-MM-DD')
                ? moment(initVal?.end_date, 'YYYY-MM-DD')
                : '',
          }}
        >
          <Row
            gutter={[12, 20]}
            style={{
              marginBottom: 20,
            }}
          >
            <Col xs={24}>
              <Form.Item
                label={
                  <p className="titleStyle">
                    <IntlMessages id="CAMPAIGNTITLE" />
                  </p>
                }
                name="post_title"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.campaignTitle" />,
                  },
                  {
                    whitespace: true,
                    message: <IntlMessages id="err.blankSpace" />,
                  },
                ]}
              >
                <Input
                  onChange={(val) => {
                    const { value } = val.target;
                    setState((p) => ({
                      ...p,
                      campaignTitle: value,
                    }));
                  }}
                />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={
                  <p className="titleStyle">
                    <IntlMessages id="CAMPAIGNNAME" />
                  </p>
                }
                name="campaign_name"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.campaignName" />,
                  },
                  {
                    whitespace: true,
                    message: <IntlMessages id="err.blankSpace" />,
                  },
                ]}
              >
                <Input
                  onChange={(val) => {
                    const { value } = val.target;
                    setState((p) => ({
                      ...p,
                      campaignName: value,
                    }));
                  }}
                />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={
                  <p className="titleStyle">
                    <IntlMessages id="CAMPAIGNTAGS" />
                  </p>
                }
                name="campaign_tags"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.campaignTags" />,
                  },
                ]}
              >
                <Select mode="tags" />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={
                  <p className="titleStyle">
                    <IntlMessages id="DESCRIPTION" />
                  </p>
                }
                name="description"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.description" />,
                  },
                  {
                    whitespace: true,
                    message: <IntlMessages id="err.blankSpace" />,
                  },
                ]}
              >
                <Input
                  onChange={(val) => {
                    const { value } = val.target;
                    setState((p) => ({
                      ...p,
                      campaignDesc: value,
                    }));
                  }}
                />
              </Form.Item>
            </Col>
            {/* <Row>
            <Col xl={24} md={24} sm={24} xs={24}>
              <Form.Item
                label={
                  <p className="titleStyle">
                    <IntlMessages id="marketing.sendTo" />
                  </p>
                }
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.customer" />,
                  },
                ]}
                name="segment_id"
              >
                <Select
                  showSearch
                  allowClear
                  placeholder={<IntlMessages id="marketing.customerSegment" />}
                  optionFilterProp="children"
                  onChange={onChangeSelect}
                  onFocus={onFocus}
                  onBlur={onBlur}
                  onSearch={onSearch}
                  filterOption={(input, option) =>
                    option.children
                      .toLowerCase()
                      .indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {segmentList &&
                    segmentList.map((data) => (
                      <Option value={data.id}>{data.segment_name}</Option>
                    ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <div className="iconStyle">
            <span style={{ cursor: 'pointer' }}>
              <div
                onClick={() => {
                  setState((p) => ({
                    ...p,
                    visible: true,
                    drawerType: 'add',
                  }));
                }}
                role="button"
                tabIndex={-1}
                onKeyPress={() => {
                  setState((p) => ({
                    ...p,
                    visible: true,
                    drawerType: 'add',
                  }));
                }}
                className="createSegmentP"
                style={{
                  color: theme.colors.primaryColor,
                  display: 'flex',
                }}
              >
                <p className="iconPadding" style={{ paddingRight: '10px' }}>
                  <PlusCircleOutlined />
                </p>
                <IntlMessages id="customer.addSegment" />
              </div>
            </span>
            {state.sendTo ? (
              <div
                className="createSegmentP"
                style={{
                  color: 'red',
                  display: 'flex',
                  marginLeft: 20,
                  fontSize: 12,
                }}
              >
                {`There are "${state?.userCount}" people that will be added to this segment`}
              </div>
            ) : null}
          </div> */}
            <Col xs={24} xl={24} md={24} sm={24}>
              <Form.Item
                label={
                  <p className="titleStyle">
                    <IntlMessages id="marketing.messageType" />
                  </p>
                }
                name="message_type"
              >
                <div className="radio-toolbar">
                  <RadioGroup
                    onChange={onRadioChange}
                    defaultValue={
                      (state?.type === initVal?.campaign_type &&
                        initVal?.message_position) ||
                      'center'
                    }
                    content={radioMessageTypeContent()}
                  />
                </div>
              </Form.Item>
              <Row
                gutter={[12, 20]}
                style={{
                  marginBottom: 20,
                }}
              >
                <Col xs={24}>
                  <Form.Item
                    label={
                      <p className="titleStyle">
                        <IntlMessages id="marketing.text1" />
                      </p>
                    }
                    name="text1"
                  >
                    <Input
                      placeholder="enter message"
                      onChange={(val) => {
                        const { value } = val.target;
                        setState((p) => ({
                          ...p,
                          postTitleInAppMsg: value,
                        }));
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item
                    label={
                      <p className="titleStyle">
                        <IntlMessages id="marketing.color" />
                      </p>
                    }
                    name="text_color"
                  >
                    <Input
                      addonAfter={
                        <PopOver
                          buttonBackColor={state.textColor}
                          colorPicker={colorPicker}
                          button
                          onClick={() => {
                            setState((p) => ({
                              ...p,
                              colorType: 'text',
                            }));
                          }}
                        />
                      }
                      placeholder="Select Color"
                      value={state.textColor}
                    />
                  </Form.Item>
                </Col>
                <Col xs={12} xl={12} md={12} sm={12}>
                  <Form.Item
                    label={
                      <p className="titleStyle">
                        <IntlMessages id="marketing.textSize" />
                      </p>
                    }
                    name="text_size"
                  >
                    <Select
                      defaultValue={
                        (state?.type === initVal?.campaign_type &&
                          initVal?.text_info?.text_size) ||
                        state.fontSize
                      }
                      style={{ width: 120 }}
                      allowClear
                      placeholder="Select Size"
                      onChange={handleSizeChange}
                    >
                      {fontSize.map((data) => (
                        <Option value={data.label}>{data.label}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={12} xl={12} md={12} sm={12}>
                  <Form.Item
                    label={
                      <p className="titleStyle">
                        <IntlMessages id="marketing.textAlign" />
                      </p>
                    }
                    name="text_align"
                  >
                    <RadioGroup
                      onChange={onAignRadioChange}
                      defaultValue={initVal?.text_info?.text_align || 'center'}
                      content={radioContent()}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item
                    label={
                      <p className="titleStyle">
                        <IntlMessages id="marketing.attachments" />
                      </p>
                    }
                    name="attachments"
                  >
                    <Upload
                      {...props}
                      fileList={
                        state.file ||
                        (state?.type === initVal?.campaign_type &&
                          initVal?.post_file)
                      }
                      beforeUpload={beforeUpload}
                      showUploadList={false}
                      style={{
                        color: theme.colors.primaryColor,
                        display: 'flex',
                      }}
                    >
                      <p
                        className="attachments"
                        style={{
                          cursor: 'pointer',
                          display: 'inline-block',
                        }}
                      >
                        <PaperClipOutlined />
                        {'  '}
                        <IntlMessages
                          id={
                            state.file ||
                            (state?.type === initVal?.campaign_type &&
                              initVal?.post_file)
                              ? state?.fileName || initVal?.file_name
                              : 'marketing.attachFiles'
                          }
                        />
                        <p style={{ fontSize: 12, display: 'inline-block' }}>
                          &nbsp;
                          {'  ('}
                          <IntlMessages id="err.max2MBFile" />)
                        </p>
                      </p>
                      <Row>
                        <div>
                          <p
                            style={{
                              fontSize: 12,
                              display: 'inline-block',
                              color: 'red',
                            }}
                          >
                            <IntlMessages id="attachmentLike" />
                            {' ('}
                            {state.attachmentLike ? '800 * 1200' : '452 * 280'}
                            {') '}
                            <IntlMessages id="attachmentDepend" />
                          </p>
                        </div>
                      </Row>
                    </Upload>
                  </Form.Item>
                </Col>
              </Row>
              {btnArr &&
                btnArr.map((data, index) => (
                  <div key={data.id} className="mainDiv">
                    <Row
                      gutter={[12, 20]}
                      style={{
                        marginBottom: 20,
                      }}
                    >
                      <Col xs={12} xl={12} md={12} sm={12}>
                        <text className="titleStyleMap">
                          <IntlMessages id="marketing.button" />
                        </text>
                        <Input
                          placeholder={intl.formatMessage({
                            id: 'marketing.enterButtonText',
                          })}
                          style={{ marginTop: '5px' }}
                          value={data.button_text}
                          onChange={(val) => {
                            const { value } = val.target;
                            btnArr.map((item) => {
                              if (data.id === item.id) {
                                const objIndex = btnArr.findIndex(
                                  (obj) => obj.id === item.id,
                                );
                                btnArr[objIndex].button_text = value;
                                setState((p) => ({
                                  ...p,
                                  btnText: value,
                                }));
                              }
                              return true;
                            });
                          }}
                        />
                      </Col>
                      <Col xs={12} xl={12} md={12} sm={12}>
                        <text className="titleStyleMap">
                          <IntlMessages id="marketing.backColor" />
                        </text>
                        <Input
                          style={{ marginTop: '5px' }}
                          addonAfter={
                            <PopOver
                              buttonBackColor={data.background_color}
                              colorPicker={colorPicker}
                              button
                              onClick={() => {
                                setState((p) => ({
                                  ...p,
                                  colorType: 'back',
                                  singleData: data,
                                }));
                              }}
                            />
                          }
                          placeholder="Select Color"
                          value={data.background_color}
                          onChange={(val) => {
                            const { value } = val.target;
                            setState((p) => ({
                              ...p,
                              backColor: value,
                            }));
                          }}
                        />
                      </Col>
                    </Row>
                    <Row
                      gutter={[12, 20]}
                      style={{ marginTop: '5px', marginBottom: '5px' }}
                    >
                      <Col xs={24} xl={24} md={24} sm={24}>
                        <text className="titleStyleMap">
                          <IntlMessages id="marketing.buttonTextColor" />
                        </text>
                        <Input
                          style={{ marginTop: '5px' }}
                          value={data.button_text_color}
                          addonAfter={
                            <PopOver
                              buttonBackColor={data.button_text_color}
                              colorPicker={colorPicker}
                              button
                              onClick={() => {
                                setState((p) => ({
                                  ...p,
                                  colorType: 'button',
                                  singleData: data,
                                }));
                              }}
                            />
                          }
                          placeholder="Select Color"
                          onChange={(val) => {
                            const { value } = val.target;
                            setState((p) => ({
                              ...p,
                              buttonTextColor: value,
                            }));
                          }}
                        />
                      </Col>
                    </Row>
                    <Row
                      gutter={[12, 20]}
                      style={{ marginTop: '5px', marginBottom: '5px' }}
                    >
                      <Col xs={10} xl={10} md={10} sm={10}>
                        <text className="titleStyleMap">
                          <IntlMessages id="marketing.ButtonTextSize" />
                        </text>
                        <Select
                          defaultValue={
                            (!isEmpty(initVal?.button_info) &&
                              initVal?.button_info[index]?.button_text_size) ||
                            state.fontSize
                          }
                          style={{ width: 120, marginTop: '5px' }}
                          allowClear
                          placeholder="Select Size"
                          onClick={() => {
                            setState((p) => ({
                              ...p,
                              singleData: data,
                            }));
                          }}
                          onChange={(val) => {
                            setTimeout(() => {
                              handleSizeChange(val, 'buttonSize');
                            }, 100);
                          }}
                        >
                          {fontSize.map((item) => (
                            <Option key={item.label} value={item.label}>
                              {item.label}
                            </Option>
                          ))}
                        </Select>
                      </Col>
                      <Col xs={14} xl={14} md={14} sm={14}>
                        <text className="titleStyleMap">
                          <IntlMessages id="marketing.buttonTextAlign" />
                        </text>
                        <RadioGroup
                          style={{ marginTop: '5px' }}
                          onChange={(e) => {
                            onTextAignRadioChange(e, data);
                          }}
                          defaultValue={
                            (!isEmpty(initVal?.button_info) &&
                              initVal?.button_info[index]?.button_text_align) ||
                            'center'
                          }
                          content={radioContent()}
                        />
                      </Col>
                    </Row>
                    <Row
                      gutter={[12, 20]}
                      style={{
                        marginBottom: 20,
                      }}
                    >
                      <Col xs={24} xl={24} md={24} sm={24}>
                        <text className="titleStyleMap">
                          <IntlMessages id="marketing.buttonurl" />
                        </text>
                        <Input
                          placeholder={intl.formatMessage({
                            id: 'marketing.enterbuttonurl',
                          })}
                          style={{ marginTop: '5px' }}
                          value={data.button_url}
                          onChange={(val) => {
                            const { value } = val.target;
                            btnArr.map((item) => {
                              if (data.id === item.id) {
                                const objIndex = btnArr.findIndex(
                                  (obj) => obj.id === item.id,
                                );
                                btnArr[objIndex].button_url = value;
                                setState((p) => ({
                                  ...p,
                                  btnText: value,
                                }));
                              }
                              return true;
                            });
                          }}
                        />
                      </Col>
                    </Row>
                  </div>
                ))}

              <Row
                gutter={[12, 20]}
                style={{
                  marginBottom: 20,
                }}
              >
                <Col xs={24} md={24} xl={24}>
                  <div className="iconStyle">
                    <span style={{ cursor: 'pointer' }}>
                      <div
                        onClick={() => {
                          // const data = state.btnText || 'Button';
                          btnCnt.push(btnObj);
                          setState((p) => ({
                            ...p,
                            btnCnt: state.btnCnt,
                          }));
                        }}
                        role="button"
                        tabIndex={-1}
                        onKeyPress={() => {
                          btnCnt.push(btnObj);
                          setState((p) => ({
                            ...p,
                            btnCnt: state.btnCnt,
                          }));
                        }}
                        className="createSegmentP"
                        style={{
                          color: theme.colors.primaryColor,
                          display: 'flex',
                        }}
                      >
                        <p
                          className="iconPadding"
                          style={{ paddingRight: '10px' }}
                        >
                          <PlusCircleOutlined />
                        </p>
                        <IntlMessages id="marketing.addButton" />
                      </div>
                    </span>
                  </div>
                </Col>
                <Col xs={24} xl={24} md={24} sm={24}>
                  <Form.Item
                    label={
                      <p className="titleStyle">
                        <IntlMessages id="marketing.triggers" />
                      </p>
                    }
                    name="triggers"
                  >
                    <Select
                      showSearch
                      placeholder={
                        <IntlMessages id="marketing.messageTriggers" />
                      }
                      optionFilterProp="children"
                      onChange={onChangeSelect}
                      onFocus={onFocus}
                      onBlur={onBlur}
                      onSearch={onSearch}
                      filterOption={(input, option) =>
                        option.children
                          .toLowerCase()
                          .indexOf(input.toLowerCase()) >= 0
                      }
                      allowClear
                    >
                      <Option value="inAppOpen">When Application open</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={12}>
                  <Form.Item
                    label={
                      <p className="titleStyle">
                        <IntlMessages id="antTable.title.startDate" />
                      </p>
                    }
                    name="start_date"
                  >
                    <Datepicker
                      style={{ minWidth: '100%' }}
                      format="DD-MM-YYYY"
                      placeholder="DD/MM/YYYY"
                      disabledDate={disabledDate}
                      onChange={(val) => {
                        onDateChange(val);
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col xs={12}>
                  <Form.Item
                    label={
                      <p className="titleStyle">
                        <IntlMessages id="antTable.title.endDate" />
                      </p>
                    }
                    name="end_date"
                  >
                    <Datepicker
                      style={{ minWidth: '100%' }}
                      format="DD-MM-YYYY"
                      disabledDate={(current) => {
                        if (state?.startDate) {
                          const date =
                            state?.drawerType === 'add'
                              ? moment(state?.startDate, 'DD-MM-YYYY')
                              : moment(state?.startDate);
                          return current <= date;
                        }
                        return current;
                      }}
                      onChange={(val) => {
                        onDateChange(val, 'last');
                      }}
                      placeholder="DD/MM/YYYY"
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Col>
          </Row>
        </Form>
      </FormWrapper>
    </Col>
  );
};
export default RenderInAppPurchase;

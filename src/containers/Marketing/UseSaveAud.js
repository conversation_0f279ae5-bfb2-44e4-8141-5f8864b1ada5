/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable prefer-destructuring */
import React, { useEffect, useState, Suspense } from 'react';
import { Input, Row, Col } from 'antd';
import { isObject } from 'lodash-es';

import getApiData from '@chill/lib/helpers/apiHelper';
import Loader from '@chill/components/utility/Loader';
import { SearchOutlined, DeleteOutlined } from '@ant-design/icons';

const UseSaveAud = ({ handleEditClick }) => {
  const [searchValue, setSearchValue] = useState('');
  const [savedData, setSavedData] = useState([]);

  const getAudienceData = async () => {
    try {
      // Make a GET request to fetch the audience data from the backend
      const response = await getApiData('audience', {}, 'GET');

      if (response.success && isObject(response.data)) {
        console.log(response.data);
        const sortedData = response.data.sort((a, b) => {
          // Assuming 'createdAt' is the property representing the creation date
          return new Date(b.createdAt) - new Date(a.createdAt);
        });

        setSavedData(sortedData);
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    // Fetch the saved audience data from the backend when the component mounts
    getAudienceData();
  }, []);

  const handleSearch = (event) => {
    setSearchValue(event.target.value);
  };

  const filteredAudienceData = savedData.filter(
    (data) =>
      data.name.toLowerCase().includes(searchValue.toLowerCase()) ||
      new RegExp(searchValue, 'i').test(data.title),
  );

  // Deleting the Saved Audience in Backend When Clicking the Delete ICon

  const DeleteAudience = async (id) => {
    try {
      const deleteResponse = await getApiData(
        `audience/delete-criteria/${id}`,
        {},
        'POST',
      );

      if (deleteResponse.success) {
        // Filter out the deleted audience from the savedData state
        setSavedData((prevData) => prevData.filter((data) => data.id !== id));
      } else {
        console.error('Audience deletion failed');
      }
    } catch (error) {
      console.error('Error deleting audience:', error);
    }
  };

  return (
    <div>
      <Suspense fallback={<Loader />}>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <div>
            <p style={{ color: '#0a0606', fontWeight: 'bold' }}>
              SAVED AUDIENCE
            </p>
          </div>
          {/* Create new dropdown */}
        </div>
        {/* SEARCH EXISTING AUDIENCE */}
        <Input
          placeholder="SEARCH EXISTING AUDIENCE"
          style={{ borderRadius: 2, marginTop: 5 }}
          onChange={handleSearch}
          value={searchValue}
          prefix={
            <SearchOutlined
              style={{
                fontSize: 12,
                border: 'none',
                margin: 0,
                paddingRight: 5,
              }}
            />
          }
        />
        <Row gutter={[16, 16]} style={{ marginTop: 10 }}>
          {filteredAudienceData.map((data) => (
            <Col span={12} key={data.id}>
              <div
                style={{
                  backgroundColor: '#fff',
                  padding: '10px 15px',
                  borderRadius: 10,
                  border: '0.5px solid #EDEDED',
                  cursor: 'pointer',
                  display: 'flex',
                  flexDirection: 'row',
                }}
              >
                <div
                  style={{
                    backgroundColor: '#fff',
                    // padding: '10px 15px',
                    cursor: 'pointer',
                  }}
                  onClick={() => handleEditClick(data.id)}
                >
                  <p style={{ fontWeight: 'bold', marginBottom: 5 }}>
                    {data.name}
                  </p>
                  <p>{data.title}</p>
                </div>
                <div
                  style={{
                    backgroundColor: '#fff',
                    // padding: '10px 15px',
                    cursor: 'pointer',
                  }}
                >
                  <DeleteOutlined
                    style={{
                      fontSize: '17px',
                    }}
                    onClick={() => DeleteAudience(data.id)}
                  />
                </div>
              </div>
            </Col>
          ))}
        </Row>
      </Suspense>
    </div>
  );
};

export default UseSaveAud;

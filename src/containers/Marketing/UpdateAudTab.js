/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable prefer-destructuring */
/* eslint-disable no-irregular-whitespace */
/* eslint-disable no-unused-vars */
import React, { useState, useEffect } from 'react';
import {
  Input,
  Slider,
  AutoComplete,
  Select,
  Radio,
  Checkbox,
  Tabs,
  Image,
  Empty,
} from 'antd';
import { isArray } from 'lodash-es';
import getApiData from '@chill/lib/helpers/apiHelper';

import {
  SearchOutlined,
  InfoCircleFilled,
  CaretDownOutlined,
} from '@ant-design/icons';
// import image1 from '@chill/assets/images/cute-baby-boy-scooter-autumnal-park.jpg';
// import image2 from '@chill/assets/images/full-shot-smiley-girl-with-tricycle.jpg';
// import image3 from '@chill/assets/images/funny-kid-rain-boots-playing-rain-park.jpg';
// import image4 from '@chill/assets/images/image4.jpg';
// import image5 from '@chill/assets/images/image5.jpg';

function onSearch() {
  // perform the search query
}

const { Option } = Select;
const { TabPane } = Tabs;

function UpdateAudTab({
  onSaveValueChange,
  savedCampaign,
  savedProduct,
  setSavedProduct,
  age1,
  setAge1,
  gender1,
  setGender1,
  language1,
  setLanguage1,
  childrens1,
  setChildren1,
  height1,
  setHeight1,
  airQuality1,
  setAirQuality1,
  weightUnit1,
  setWeightUnit1,
  temperatureUnit1,
  setTemperatureUnit1,
  weight1,
  setWeight1,
  temperature1,
  setTemperature1,
  userTitle1,
  setUserTitle1,
  criteriaTitle1,
  isCheckedInclude1,
  setIsCheckedInclude1,
  isCheckedExclude1,
  setIsCheckedExclude1,
  searchValueDetails,
  setSearchValueDetails,
  setUpdateCount,
  propCountry,
  setAccuracy1,
  setActualAccuracy1,
}) {
  const updatedCountry = propCountry;
  const [saveValue, setSaveValue] = useState('yes');
  const [images, setImages] = useState([]);
  const [languageOptions, setLanguageOptions] = useState([]);
  const [productsDetails, setProductsDetails] = useState([]);
  // const [campaign, setCampaign] = useState('awareness');
  // const [product, setProduct] = useState([]);

  // const handleRadioBtnChange = async (e) => {
  //   setCampaign(e.target.value);
  // };
  // Handling save text based on yes or no options in audience tab

  useEffect(() => {
    onSaveValueChange(saveValue);
  }, [saveValue, onSaveValueChange]);

  const handleRadioChange = (e) => {
    setSaveValue(e.target.value);
  };

  // Handling user title and criteria title

  const handleUserTitleChange = (e) => {
    const value = e.target.value;
    const regex = /^[^\d]*$/; // Regex to match any character except numbers

    if (regex.test(value)) {
      setUserTitle1(value);
    }
  };

  // Handling weight slider

  const handleWeightChange = (value) => {
    setWeight1(value);
  };

  const handleWeightUnitChange = (e) => {
    const checked = e.target.value === 'lbs';
    const newWeight = checked
      ? [weight1[0] * 2.2, weight1[1] * 2.2]
      : [weight1[0] / 2.2, weight1[1] / 2.2];
    setWeightUnit1(checked ? 'lbs' : 'kg');
    setWeight1(newWeight);
  };

  const weightRadioGroupProps = {
    value: weightUnit1,
    onChange: handleWeightUnitChange,
  };

  const onAfterWeightChange = async (value) => {
    console.log('onAfterChange value:', value);
    const updatedWeight = value;
    console.log('updatedWeight:', updatedWeight);

    setWeight1(updatedWeight, () => {
      console.log('weight after setHeight:', weight1);
    });

    setAccuracy1(false);

    const countDataa = {
      campaign_type: savedCampaign,
      age: {
        min: age1[0],
        max: age1[1],
        units: 'months',
      },
      aqi: {
        min: airQuality1[0],
        max: airQuality1[1],
        units: 'AQI',
      },
      gender: gender1,
      height: {
        min: height1[0],
        max: height1[1],
        units: 'cms',
      },
      languages: language1,
      locations: updatedCountry,
      no_of_children: childrens1,

      weight: {
        min: Math.round(updatedWeight[0] * 10) / 10,
        max: Math.round(updatedWeight[1] * 10) / 10,
        units: weightUnit1,
      },
      temperature: {
        min: Math.round(temperature1[0] * 10) / 10,
        max: Math.round(temperature1[1] * 10) / 10,
        units: temperatureUnit1,
      },
    };
    if (isCheckedInclude1.length > 0 || isCheckedExclude1.length > 0) {
      countDataa.productsOwned = {
        condition: isCheckedInclude1.length > 0 ? 'include' : 'exclude',
        ids:
          isCheckedInclude1.length > 0 ? isCheckedInclude1 : isCheckedExclude1,
      };
    }

    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('thi is weight api responce', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count; // Extract the count from the API response
        setUpdateCount(countData); // Set the count in the state
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  const weightSliderProps = {
    range: true,
    min: 0,
    max: weightUnit1 === 'kg' ? 50 : 100,
    step: 0.1,
    onChange: handleWeightChange,
    onAfterChange: onAfterWeightChange,
    value: weight1,
  };

  const weightUnitLabel = weightUnit1 === 'kg' ? '(kgs)' : '(lbs)';
  const weightUnitLabel1 = weightUnit1 === 'kg' ? 'kgs' : 'lbs';

  // Handling temperature slider

  const handleTemperatureChange = (value) => {
    setTemperature1(value);
  };

  const handleTemperatureUnitChange = (e) => {
    const checked = e.target.value === 'C';
    const newTemperature = checked
      ? [(temperature1[0] - 32) / 1.8, (temperature1[1] - 32) / 1.8]
      : [temperature1[0] * 1.8 + 32, temperature1[1] * 1.8 + 32];
    setTemperatureUnit1(checked ? 'C' : 'F');
    setTemperature1(newTemperature);
  };

  const temperatureRadioGroupProps = {
    value: temperatureUnit1,
    onChange: handleTemperatureUnitChange,
  };

  const onAfterTemperatureChange = async (value) => {
    console.log('onAfterChange value:', value);
    const updatedTemperature = value;
    console.log('updatedWeight:', updatedTemperature);

    setTemperature1(updatedTemperature, () => {
      console.log('temperature after setHeight:', temperature1);
    });

    setAccuracy1(false);

    const countDataa = {
      campaign_type: savedCampaign,
      age: {
        min: age1[0],
        max: age1[1],
        units: 'months',
      },
      aqi: {
        min: airQuality1[0],
        max: airQuality1[1],
        units: 'AQI',
      },
      gender: gender1,
      height: {
        min: height1[0],
        max: height1[1],
        units: 'cms',
      },
      languages: language1,
      locations: updatedCountry,
      no_of_children: childrens1,

      weight: {
        min: Math.round(weight1[0] * 10) / 10,
        max: Math.round(weight1[1] * 10) / 10,
        units: weightUnit1,
      },
      temperature: {
        min: Math.round(updatedTemperature[0] * 10) / 10,
        max: Math.round(updatedTemperature[1] * 10) / 10,
        units: temperatureUnit1,
      },
    };
    if (isCheckedInclude1.length > 0 || isCheckedExclude1.length > 0) {
      countDataa.productsOwned = {
        condition: isCheckedInclude1.length > 0 ? 'include' : 'exclude',
        ids:
          isCheckedInclude1.length > 0 ? isCheckedInclude1 : isCheckedExclude1,
      };
    }
    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('thi is weight api responce', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count; // Extract the count from the API response
        setUpdateCount(countData); // Set the count in the state
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  const temperatureSliderProps = {
    range: true,
    min: -40,
    max: temperatureUnit1 === 'F' ? 150 : 75,
    step: 0.1,
    onChange: handleTemperatureChange,
    onAfterChange: onAfterTemperatureChange,
    value: temperature1,
  };

  const temperatureUnitLabel = temperatureUnit1 === 'C' ? '(°C)' : '(F)';

  // Handling Age slider

  const onAfterChange1 = async (value) => {
    console.log('onAfterChange value:', value);
    const updatedAge = value;
    console.log('updatedAge:', updatedAge);

    setAge1(updatedAge, () => {
      console.log('Age after setHeight:', age1);
    });

    setAccuracy1(false);

    const countDataa = {
      campaign_type: savedCampaign,
      age: {
        min: updatedAge[0],
        max: updatedAge[1],
        units: 'months',
      },
      aqi: {
        min: airQuality1[0],
        max: airQuality1[1],
        units: 'AQI',
      },
      gender: gender1,
      height: {
        min: height1[0],
        max: height1[1],
        units: 'cms',
      },
      languages: language1,
      locations: updatedCountry,
      no_of_children: childrens1,
      // productsOwned: {
      //   include: isCheckedInclude1,
      //   exclude: isCheckedExclude1,
      // },
      weight: {
        min: Math.round(weight1[0] * 10) / 10,
        max: Math.round(weight1[1] * 10) / 10,
        units: weightUnit1,
      },
      temperature: {
        min: Math.round(temperature1[0] * 10) / 10,
        max: Math.round(temperature1[1] * 10) / 10,
        units: temperatureUnit1,
      },
    };
    if (isCheckedInclude1.length > 0 || isCheckedExclude1.length > 0) {
      countDataa.productsOwned = {
        condition: isCheckedInclude1.length > 0 ? 'include' : 'exclude',
        ids:
          isCheckedInclude1.length > 0 ? isCheckedInclude1 : isCheckedExclude1,
      };
    }
    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('thi is weight api responce', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count; // Extract the count from the API response
        setUpdateCount(countData); // Set the count in the state
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  const onChange1 = (value) => {
    setAge1(value);
  };

  // Handling Height slider

  const onChange2 = (value) => {
    console.log('onChange: ', value);
    setHeight1(value);
  };

  const onAfterChange2 = async (value) => {
    console.log('onAfterChange value:', value);
    const updatedHeight = value;
    console.log('updatedHeight:', updatedHeight);

    setHeight1(updatedHeight, () => {
      console.log('height after setHeight:', height1);
    });

    setAccuracy1(false);

    const countDataa = {
      campaign_type: savedCampaign,
      age: {
        min: age1[0],
        max: age1[1],
        units: 'months',
      },
      aqi: {
        min: airQuality1[0],
        max: airQuality1[1],
        units: 'AQI',
      },
      gender: gender1,
      height: {
        min: updatedHeight[0],
        max: updatedHeight[1],
        units: 'cms',
      },
      languages: language1,
      locations: updatedCountry,
      no_of_children: childrens1,

      weight: {
        min: Math.round(weight1[0] * 10) / 10,
        max: Math.round(weight1[1] * 10) / 10,
        units: weightUnit1,
      },
      temperature: {
        min: Math.round(temperature1[0] * 10) / 10,
        max: Math.round(temperature1[1] * 10) / 10,
        units: temperatureUnit1,
      },
    };
    if (isCheckedInclude1.length > 0 || isCheckedExclude1.length > 0) {
      countDataa.productsOwned = {
        condition: isCheckedInclude1.length > 0 ? 'include' : 'exclude',
        ids:
          isCheckedInclude1.length > 0 ? isCheckedInclude1 : isCheckedExclude1,
      };
    }

    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('thi is weight api responce', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count; // Extract the count from the API response
        setUpdateCount(countData); // Set the count in the state
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  // Handling Air quality slider

  const onChange5 = (value) => {
    console.log('onChange: ', value);
    setAirQuality1(value);
  };

  const onAfterChange5 = async (value) => {
    console.log('onAfterChange value:', value);
    const updatedAqi = value;
    console.log('updatedaqi:', updatedAqi);

    setAirQuality1(updatedAqi, () => {
      console.log('aqi after setaqi:', airQuality1);
    });

    setAccuracy1(false);

    const countDataa = {
      campaign_type: savedCampaign,
      age: {
        min: age1[0],
        max: age1[1],
        units: 'months',
      },
      aqi: {
        min: updatedAqi[0],
        max: updatedAqi[1],
        units: 'AQI',
      },
      gender: gender1,
      height: {
        min: height1[0],
        max: height1[1],
        units: 'cms',
      },
      languages: language1,
      locations: updatedCountry,
      no_of_children: childrens1,

      weight: {
        min: Math.round(weight1[0] * 10) / 10,
        max: Math.round(weight1[1] * 10) / 10,
        units: weightUnit1,
      },
      temperature: {
        min: Math.round(temperature1[0] * 10) / 10,
        max: Math.round(temperature1[1] * 10) / 10,
        units: temperatureUnit1,
      },
    };
    if (isCheckedInclude1.length > 0 || isCheckedExclude1.length > 0) {
      countDataa.productsOwned = {
        condition: isCheckedInclude1.length > 0 ? 'include' : 'exclude',
        ids:
          isCheckedInclude1.length > 0 ? isCheckedInclude1 : isCheckedExclude1,
      };
    }
    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('thi is weight api responce', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count; // Extract the count from the API response
        setUpdateCount(countData); // Set the count in the state
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  // Handling gender dropdown

  const handleGenderChange = async (option) => {
    console.log('onAfterChange value:', option);
    const updatedGender = option;
    console.log('updatedgender:', updatedGender);

    setGender1(updatedGender, () => {
      console.log('gender after setgender:', gender1);
    });

    setAccuracy1(false);

    const countDataa = {
      campaign_type: savedCampaign,
      age: {
        min: age1[0],
        max: age1[1],
        units: 'months',
      },
      aqi: {
        min: airQuality1[0],
        max: airQuality1[1],
        units: 'AQI',
      },
      gender: updatedGender,
      height: {
        min: height1[0],
        max: height1[1],
        units: 'cms',
      },
      languages: language1,
      locations: updatedCountry,
      no_of_children: childrens1,

      weight: {
        min: Math.round(weight1[0] * 10) / 10,
        max: Math.round(weight1[1] * 10) / 10,
        units: weightUnit1,
      },
      temperature: {
        min: Math.round(temperature1[0] * 10) / 10,
        max: Math.round(temperature1[1] * 10) / 10,
        units: temperatureUnit1,
      },
    };
    if (isCheckedInclude1.length > 0 || isCheckedExclude1.length > 0) {
      countDataa.productsOwned = {
        condition: isCheckedInclude1.length > 0 ? 'include' : 'exclude',
        ids:
          isCheckedInclude1.length > 0 ? isCheckedInclude1 : isCheckedExclude1,
      };
    }
    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('thi is weight api responce', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count; // Extract the count from the API response
        setUpdateCount(countData); // Set the count in the state
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  // Handling children dropdown

  const handleChildrenSelect = async (option) => {
    console.log('onAfterChange value:', option);
    const updatedChildren = option;
    console.log('updatedaqi:', updatedChildren);

    setChildren1(updatedChildren, () => {
      console.log('gender after setgender:', childrens1);
    });

    setAccuracy1(false);

    const countDataa = {
      campaign_type: savedCampaign,
      age: {
        min: age1[0],
        max: age1[1],
        units: 'months',
      },
      aqi: {
        min: airQuality1[0],
        max: airQuality1[1],
        units: 'AQI',
      },
      gender: gender1,
      height: {
        min: height1[0],
        max: height1[1],
        units: 'cms',
      },
      languages: language1,
      locations: updatedCountry,
      no_of_children: updatedChildren,

      weight: {
        min: Math.round(weight1[0] * 10) / 10,
        max: Math.round(weight1[1] * 10) / 10,
        units: weightUnit1,
      },
      temperature: {
        min: Math.round(temperature1[0] * 10) / 10,
        max: Math.round(temperature1[1] * 10) / 10,
        units: temperatureUnit1,
      },
    };
    if (isCheckedInclude1.length > 0 || isCheckedExclude1.length > 0) {
      countDataa.productsOwned = {
        condition: isCheckedInclude1.length > 0 ? 'include' : 'exclude',
        ids:
          isCheckedInclude1.length > 0 ? isCheckedInclude1 : isCheckedExclude1,
      };
    }
    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('thi is weight api responce', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count; // Extract the count from the API response
        setUpdateCount(countData); // Set the count in the state
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  const childOptions = [
    { label: '1', value: '1' },
    { label: '2', value: '2' },
    { label: '3', value: '3' },
    { label: '4', value: '4' },
    { label: '5', value: '5' },
    { label: '6', value: '6' },
  ];

  // Handling Language dropdown

  const handleLanguageSelect = async (value) => {
    console.log('onAfterChange value:', value);
    const updatedLanguage = value;
    console.log('updatelanguage:', updatedLanguage);

    setLanguage1(updatedLanguage, () => {
      console.log('gender after setgender:', language1);
    });

    setAccuracy1(false);

    const countDataa = {
      campaign_type: savedCampaign,
      age: {
        min: age1[0],
        max: age1[1],
        units: 'months',
      },
      aqi: {
        min: airQuality1[0],
        max: airQuality1[1],
        units: 'AQI',
      },
      gender: gender1,
      height: {
        min: height1[0],
        max: height1[1],
        units: 'cms',
      },
      languages: updatedLanguage,
      locations: updatedCountry,
      no_of_children: childrens1,

      weight: {
        min: Math.round(weight1[0] * 10) / 10,
        max: Math.round(weight1[1] * 10) / 10,
        units: weightUnit1,
      },
      temperature: {
        min: Math.round(temperature1[0] * 10) / 10,
        max: Math.round(temperature1[1] * 10) / 10,
        units: temperatureUnit1,
      },
    };
    if (isCheckedInclude1.length > 0 || isCheckedExclude1.length > 0) {
      countDataa.productsOwned = {
        condition: isCheckedInclude1.length > 0 ? 'include' : 'exclude',
        ids:
          isCheckedInclude1.length > 0 ? isCheckedInclude1 : isCheckedExclude1,
      };
    }

    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('thi is weight api responce', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count; // Extract the count from the API response
        setUpdateCount(countData); // Set the count in the state
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  const fetchLanguages = async () => {
    try {
      const response = await getApiData('getLanguageList', {}, 'POST');
      console.log(response.data);
      if (response.success) {
        console.log(response.data);
        setLanguageOptions(response.data);
      }
    } catch (error) {
      console.error('Error fetching images:', error);
    }
  };

  const fetchImages = async () => {
    try {
      const response = await getApiData('product/get-brand-products', 'GET');
      console.log(response.data);
      if (response.success && isArray(response.data)) {
        console.log(response.data);
        setImages(response.data);
        setProductsDetails(response.data);
      }
    } catch (error) {
      console.error('Error fetching images:', error);
    }
  };

  useEffect(() => {
    // Fetch images from the API
    fetchImages();
    fetchLanguages();
  }, []);

  const handleIncludeImageClick = async (id) => {
    const newIncludeIds = isCheckedInclude1.includes(id)
      ? isCheckedInclude1.filter((item) => item !== id)
      : [...isCheckedInclude1, id];

    const newExcludeIds = isCheckedExclude1.filter((item) => item !== id);

    setIsCheckedInclude1(newIncludeIds);
    setIsCheckedExclude1(newExcludeIds);

    setAccuracy1(false);

    const countDataa = {
      campaign_type: savedCampaign,
      age: {
        min: age1[0],
        max: age1[1],
        units: 'months',
      },
      aqi: {
        min: airQuality1[0],
        max: airQuality1[1],
        units: 'AQI',
      },
      gender: gender1,
      height: {
        min: height1[0],
        max: height1[1],
        units: 'cms',
      },
      languages: language1,
      locations: updatedCountry,
      no_of_children: childrens1,
      weight: {
        min: Math.round(weight1[0] * 10) / 10,
        max: Math.round(weight1[1] * 10) / 10,
        units: weightUnit1,
      },
      temperature: {
        min: Math.round(temperature1[0] * 10) / 10,
        max: Math.round(temperature1[1] * 10) / 10,
        units: temperatureUnit1,
      },
    };

    if (newIncludeIds.length > 0 || newExcludeIds.length > 0) {
      countDataa.productsOwned = {
        condition: newIncludeIds.length > 0 ? 'include' : 'exclude',
        ids: newIncludeIds.length > 0 ? newIncludeIds : newExcludeIds,
      };
    }

    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('this is weight api response', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count;
        setUpdateCount(countData);
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };
  const handleExcludeImageClick = async (id) => {
    const newExcludeIds = isCheckedExclude1.includes(id)
      ? isCheckedExclude1.filter((item) => item !== id)
      : [...isCheckedExclude1, id];

    const newIncludeIds = isCheckedInclude1.filter((item) => item !== id);

    setIsCheckedInclude1(newIncludeIds);
    setIsCheckedExclude1(newExcludeIds);
    setAccuracy1(false);
    const countDataa = {
      campaign_type: savedCampaign,
      age: {
        min: age1[0],
        max: age1[1],
        units: 'months',
      },
      aqi: {
        min: airQuality1[0],
        max: airQuality1[1],
        units: 'AQI',
      },
      gender: gender1,
      height: {
        min: height1[0],
        max: height1[1],
        units: 'cms',
      },
      languages: language1,
      locations: updatedCountry,
      no_of_children: childrens1,
      weight: {
        min: Math.round(weight1[0] * 10) / 10,
        max: Math.round(weight1[1] * 10) / 10,
        units: weightUnit1,
      },
      temperature: {
        min: Math.round(temperature1[0] * 10) / 10,
        max: Math.round(temperature1[1] * 10) / 10,
        units: temperatureUnit1,
      },
    };

    if (newIncludeIds.length > 0 || newExcludeIds.length > 0) {
      countDataa.productsOwned = {
        condition: newIncludeIds.length > 0 ? 'include' : 'exclude',
        ids: newIncludeIds.length > 0 ? newIncludeIds : newExcludeIds,
      };
    }

    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('this is weight api response', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count;
        setUpdateCount(countData);
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  const handleIncludeCheckboxClick = async (id) => {
    const newIncludeIds = isCheckedInclude1.includes(id)
      ? isCheckedInclude1.filter((item) => item !== id)
      : [...isCheckedInclude1, id];

    const newExcludeIds = isCheckedExclude1.filter((item) => item !== id);

    setIsCheckedInclude1(newIncludeIds);
    setIsCheckedExclude1(newExcludeIds);

    setAccuracy1(false);

    const countDataa = {
      campaign_type: savedCampaign,
      age: {
        min: age1[0],
        max: age1[1],
        units: 'months',
      },
      aqi: {
        min: airQuality1[0],
        max: airQuality1[1],
        units: 'AQI',
      },
      gender: gender1,
      height: {
        min: height1[0],
        max: height1[1],
        units: 'cms',
      },
      languages: language1,
      locations: updatedCountry,
      no_of_children: childrens1,
      weight: {
        min: Math.round(weight1[0] * 10) / 10,
        max: Math.round(weight1[1] * 10) / 10,
        units: weightUnit1,
      },
      temperature: {
        min: Math.round(temperature1[0] * 10) / 10,
        max: Math.round(temperature1[1] * 10) / 10,
        units: temperatureUnit1,
      },
    };

    if (newIncludeIds.length > 0 || newExcludeIds.length > 0) {
      countDataa.productsOwned = {
        condition: newIncludeIds.length > 0 ? 'include' : 'exclude',
        ids: newIncludeIds.length > 0 ? newIncludeIds : newExcludeIds,
      };
    }

    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('this is weight api response', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count;
        setUpdateCount(countData);
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  const handleExcludeCheckboxClick = async (id) => {
    const newExcludeIds = isCheckedExclude1.includes(id)
      ? isCheckedExclude1.filter((item) => item !== id)
      : [...isCheckedExclude1, id];

    const newIncludeIds = isCheckedInclude1.filter((item) => item !== id);

    setIsCheckedInclude1(newIncludeIds);
    setIsCheckedExclude1(newExcludeIds);
    setAccuracy1(false);
    const countDataa = {
      campaign_type: savedCampaign,
      age: {
        min: age1[0],
        max: age1[1],
        units: 'months',
      },
      aqi: {
        min: airQuality1[0],
        max: airQuality1[1],
        units: 'AQI',
      },
      gender: gender1,
      height: {
        min: height1[0],
        max: height1[1],
        units: 'cms',
      },
      languages: language1,
      locations: updatedCountry,
      no_of_children: childrens1,
      weight: {
        min: Math.round(weight1[0] * 10) / 10,
        max: Math.round(weight1[1] * 10) / 10,
        units: weightUnit1,
      },
      temperature: {
        min: Math.round(temperature1[0] * 10) / 10,
        max: Math.round(temperature1[1] * 10) / 10,
        units: temperatureUnit1,
      },
    };

    if (newIncludeIds.length > 0 || newExcludeIds.length > 0) {
      countDataa.productsOwned = {
        condition: newIncludeIds.length > 0 ? 'include' : 'exclude',
        ids: newIncludeIds.length > 0 ? newIncludeIds : newExcludeIds,
      };
    }

    try {
      const response = await getApiData(
        'audience/get-count',
        countDataa,
        'POST',
      );
      console.log('this is weight api response', response);
      console.log('this is count Data', countDataa);
      if (response.success) {
        const countData = response.count;
        setUpdateCount(countData);
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  const includeTabImages = images.filter(
    (image) => !isCheckedExclude1.includes(image.id),
  );

  const excludeTabImages = images.filter(
    (image) => !isCheckedInclude1.includes(image.id),
  );

  // Handling Detailed targeted search and options

  const handleSearchDetails = (value) => {
    onSearch(value);
  };

  const handleInputChange = (value) => {
    setSearchValueDetails(value);
  };

  const options = [
    { label: 'Demographics', value: 'demographics' },
    { label: 'Interests', value: 'interests' },
    { label: 'Behaviors', value: 'behaviors' },
  ];

  const handleProductChange = async (name) => {
    const prodItem = productsDetails.filter(
      (item) => item.product_name === name,
    )[0];
    setSavedProduct(prodItem);
    setHeight1([
      prodItem.height.min_recommended_range,
      prodItem.height.max_recommended_range,
    ]);
    setWeight1([
      prodItem.weight.min_recommended_range,
      prodItem.weight.max_recommended_range,
    ]);
    setAge1([
      prodItem.age.min_recommended_range,
      prodItem.age.max_recommended_range,
    ]);
    setTemperature1([
      prodItem.temperature.min_recommended_range,
      prodItem.temperature.max_recommended_range,
    ]);
    setAirQuality1([
      prodItem.aqi.min_recommended_range,
      prodItem.aqi.max_recommended_range,
    ]);
    setSavedProduct(prodItem);
    // setExpectedReach(expReach);
    setAccuracy1(true);

    const countData = {
      age: {
        accuracy_weightage: prodItem.age.accuracy_weightage,
        max_recommended_range: prodItem.age.max_recommended_range,
        min_recommended_range: prodItem.age.min_recommended_range,
        max_manufacturer_range: prodItem.age.max_manufacturer_range,
        min_manufacturer_range: prodItem.age.min_manufacturer_range,
      },
      height: {
        accuracy_weightage: prodItem.height.accuracy_weightage,
        max_recommended_range: prodItem.height.max_recommended_range,
        min_recommended_range: prodItem.height.min_recommended_range,
        max_manufacturer_range: prodItem.height.max_manufacturer_range,
        min_manufacturer_range: prodItem.height.min_manufacturer_range,
      },
      weight: {
        accuracy_weightage: prodItem.weight.accuracy_weightage,
        max_recommended_range: prodItem.weight.max_recommended_range,
        min_recommended_range: prodItem.weight.min_recommended_range,
        max_manufacturer_range: prodItem.weight.max_manufacturer_range,
        min_manufacturer_range: prodItem.weight.min_manufacturer_range,
      },
      temperature: {
        accuracy_weightage: prodItem.temperature.accuracy_weightage,
        max_recommended_range: prodItem.temperature.max_recommended_range,
        min_recommended_range: prodItem.temperature.min_recommended_range,
        max_manufacturer_range: prodItem.temperature.max_manufacturer_range,
        min_manufacturer_range: prodItem.temperature.min_manufacturer_range,
      },
      aqi: {
        accuracy_weightage: prodItem.aqi.accuracy_weightage,
        max_recommended_range: prodItem.aqi.max_recommended_range,
        min_recommended_range: prodItem.aqi.min_recommended_range,
        max_manufacturer_range: prodItem.aqi.max_manufacturer_range,
        min_manufacturer_range: prodItem.aqi.min_manufacturer_range,
      },
    };

    try {
      const response = await getApiData(
        'audience/get-sales-accuracy',
        countData,
        'POST',
      );
      console.log('this is product api responce', response);
      console.log('this is count data', countData);
      if (response.success) {
        const totalCount = response.count; // Extract the count from the API response
        setUpdateCount(totalCount); // Use the functional update approach here
        console.log('This is audience Count:', totalCount); // Log the updated audience count here
        const totalAccuracy = response.overall_accuracy;
        setActualAccuracy1(totalAccuracy);
      }
    } catch (countError) {
      console.log('Count error:', countError);
      // Handle the error from the get-count API
    }
  };

  const productOptions = productsDetails.map((item) => {
    return { value: item.product_name, label: item.product_name };
  });

  return (
    <div>
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          marginTop: 5,
        }}
      >
        <p style={{ color: '#0a0606', fontWeight: 'bold' }}>
          TITLE OF AUDIENCE CRITERIA
        </p>
      </div>
      <div style={{ marginTop: 5 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Input
            placeholder="User Title"
            type="text"
            size="small"
            value={userTitle1}
            onChange={handleUserTitleChange}
            style={{
              width: '15vw',
            }}
          />
          <div style={{ display: 'flex', flexDirection: 'row', width: '15vw' }}>
            <p
              style={{
                marginRight: 15,
                fontWeight: 'bold',
                marginTop: 3,
              }}
            >
              SAVE AUDIENCE:
            </p>
            <Radio.Group
              size="small"
              onChange={handleRadioChange}
              value={saveValue}
            >
              <Radio.Button
                value="yes"
                style={{
                  backgroundColor: saveValue === 'yes' ? '#2a77f4' : '',
                  color: saveValue === 'yes' ? 'white' : '',
                  borderRadius: '10px 0px 0px 10px',
                }}
              >
                {' '}
                Yes
              </Radio.Button>
              <Radio.Button
                value="no"
                style={{
                  backgroundColor: saveValue === 'no' ? '#2a77f4' : '',
                  color: saveValue === 'no' ? 'white' : '',
                  borderRadius: '0px 10px 10px 0px',
                }}
              >
                {' '}
                No
              </Radio.Button>
            </Radio.Group>
          </div>
        </div>
        {userTitle1 && (
          <Input
            style={{ marginTop: 10 }}
            bordered={false}
            size="small"
            value={userTitle1 ? criteriaTitle1 : ''}
            readOnly
          />
        )}
      </div>
      {/* <p style={{ marginTop: 10, color: '#0a0606', fontWeight: 'bold' }}>
        TYPE OF CAMPAIGN
      </p> */}
      {/* <div
        style={{
          marginTop: 10,
          display: 'flex',
          alignItems: 'center',
        }}
      >
        <Radio.Group onChange={handleRadioBtnChange} value={campaign}>
          <Radio value="awareness">Awareness campaign</Radio>
          <Radio value="sales">Sales campaign</Radio>
        </Radio.Group>
      </div> */}
      {savedCampaign === 'sales' && savedProduct ? (
        <>
          <Select
            placeholder="Select Any Product"
            optionFilterProp="children"
            size="lg"
            style={{
              width: '100%',
              marginTop: '20px',
            }}
            onChange={handleProductChange}
            value={savedProduct.product_name || undefined}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={productOptions}
          />
        </>
      ) : null}

      {/* Age slider */}
      <p style={{ color: '#0a0606', fontWeight: 'bold', marginTop: 10 }}>
        AGE
        <span style={{ color: '#0a0606', fontWeight: 'normal' }}>
          {' '}
          (months)
        </span>
      </p>
      <div style={{ display: 'flex' }}>
        <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
        <Slider
          range
          min={0}
          max={150}
          onChange={onChange1}
          onAfterChange={onAfterChange1}
          value={age1}
          trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
          railStyle={{ boxShadow: '0px 0px 6px #00000029', height: 5 }}
          style={{ width: '100%' }}
        />
        <span style={{ margin: '5px 0px 0px 5px' }}>150</span>
      </div>

      <span
        style={{
          color: '#040606',
          opacity: 0.5,
        }}
      >{`${age1[0]} - ${age1[1]} months`}</span>

      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginTop: 10,
        }}
      >
        {/* Gender dropdown */}
        <div>
          <p style={{ color: '#0a0606', fontWeight: 'bold', marginBottom: 5 }}>
            GENDER
          </p>
          <Select
            placeholder="All Genders"
            optionFilterProp="children"
            size="small"
            style={{
              minWidth: '15vw',
            }}
            onChange={handleGenderChange}
            value={gender1 || undefined}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={[
              {
                value: 'male',
                label: 'Male',
              },
              {
                value: 'female',
                label: 'Female',
              },
              {
                value: 'others',
                label: 'Others',
              },
            ]}
          />
        </div>
        {/* Languages dropdown */}
        <div>
          <p style={{ color: '#0a0606', fontWeight: 'bold', marginBottom: 5 }}>
            LANGUAGES
          </p>
          <Select
            size="small"
            mode="multiple"
            style={{
              minWidth: '15vw',
            }}
            placeholder="All Languages"
            onChange={handleLanguageSelect}
            maxTagCount="responsive"
            value={language1}
          >
            {languageOptions.map((option) => (
              <Option key={option.id} value={option.id}>
                {option.lang_name}
              </Option>
            ))}
          </Select>
        </div>
      </div>
      {/* Children dropdown */}
      <div>
        <p
          style={{
            color: '#0a0606',
            fontWeight: 'bold',
            marginBottom: 5,
            marginTop: 10,
          }}
        >
          CHILDREN
        </p>
        <Select
          size="small"
          mode="multiple"
          style={{
            minWidth: '15vw',
          }}
          placeholder="Select no of Children"
          onChange={handleChildrenSelect}
          value={childrens1}
        >
          {childOptions.map((option) => (
            <Option key={option.value} value={option.value}>
              {option.label}
            </Option>
          ))}
        </Select>
      </div>
      {/* Height Slider */}
      <p style={{ color: '#0a0606', fontWeight: 'bold', marginTop: 10 }}>
        HEIGHT
        <span style={{ color: '#0a0606', fontWeight: 'normal' }}> (cms)</span>
      </p>
      <div style={{ display: 'flex' }}>
        <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
        <Slider
          trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
          railStyle={{ boxShadow: '0px 0px 6px #00000029', height: 5 }}
          range
          step={0.1}
          min={0}
          max={150}
          value={height1}
          onChange={onChange2}
          onAfterChange={onAfterChange2}
          style={{ width: '100%' }}
        />
        <span style={{ margin: '5px 0px 0px 0px' }}>150</span>
      </div>
      <span
        style={{
          color: '#040606',
          opacity: 0.5,
        }}
      >{`${height1[0]} - ${height1[1]} cms`}</span>

      {/* Weight Slider */}
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          marginTop: 10,
        }}
      >
        <div style={{ display: 'flex' }}>
          <p style={{ color: '#0a0606', fontWeight: 'bold', marginRight: 5 }}>
            WEIGHT
          </p>
          <span>{weightUnitLabel}</span>
        </div>
        <div
          style={{
            marginRight: 13,
          }}
        >
          <Radio.Group {...weightRadioGroupProps} size="small">
            <Radio.Button
              value="kg"
              style={{
                backgroundColor: weightUnit1 === 'kg' ? '#2a77f4' : '',
                color: weightUnit1 === 'kg' ? 'white' : '',
                borderRadius: '10px 0px 0px 10px',
              }}
            >
              kgs
            </Radio.Button>
            <Radio.Button
              value="lbs"
              style={{
                backgroundColor: weightUnit1 === 'lbs' ? '#2a77f4' : '',
                color: weightUnit1 === 'lbs' ? 'white' : '',
                borderRadius: '0px 10px 10px 0px',
              }}
            >
              lbs
            </Radio.Button>
          </Radio.Group>
        </div>
      </div>
      <div style={{ display: 'flex' }}>
        <span style={{ margin: '5px 3px 0px 0px' }}>0</span>
        <Slider
          {...weightSliderProps}
          trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
          railStyle={{ boxShadow: '0px 0px 6px #00000029', height: 5 }}
          style={{ width: '100%' }}
        />
        <span style={{ margin: '5px 0px 0px 3px' }}>
          {weightUnit1 === 'kg' ? 50 : 100}
        </span>
      </div>
      <span style={{ color: '#040606', opacity: 0.5 }}>{`${
        Math.round(weight1[0] * 10) / 10
      } - ${Math.round(weight1[1] * 10) / 10} ${weightUnitLabel1}`}</span>

      {/* Temperature Slider */}
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          marginTop: 10,
        }}
      >
        <div style={{ display: 'flex' }}>
          <p style={{ color: '#0a0606', fontWeight: 'bold', marginRight: 5 }}>
            TEMPERATURE
          </p>
          <span>{temperatureUnitLabel}</span>
        </div>
        <div
          style={{
            marginRight: 13,
          }}
        >
          <Radio.Group {...temperatureRadioGroupProps} size="small">
            <Radio.Button
              value="F"
              style={{
                backgroundColor: temperatureUnit1 === 'F' ? '#2a77f4' : '',
                color: temperatureUnit1 === 'F' ? 'white' : '',
                width: '2.7vw',
                textAlign: 'center',
                borderRadius: '10px 0px 0px 10px',
              }}
            >
              F
            </Radio.Button>
            <Radio.Button
              value="C"
              style={{
                backgroundColor: temperatureUnit1 === 'C' ? '#2a77f4' : '',
                color: temperatureUnit1 === 'C' ? 'white' : '',
                width: '2.7vw',
                textAlign: 'center',
                borderRadius: '0px 10px 10px 0px',
              }}
            >
              °C
            </Radio.Button>
          </Radio.Group>
        </div>
      </div>
      <div style={{ display: 'flex' }}>
        <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
        <Slider
          {...temperatureSliderProps}
          trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
          railStyle={{ boxShadow: '0px 0px 6px #00000029', height: 5 }}
          style={{ width: '100%' }}
        />
        <span style={{ margin: '5px 0px 0px 0px' }}>
          {temperatureUnit1 === 'F' ? 150 : 75}
        </span>
      </div>
      <span style={{ color: '#040606', opacity: 0.5 }}>{`${
        Math.round(temperature1[0] * 10) / 10
      } - ${Math.round(temperature1[1] * 10) / 10} ${temperatureUnit1}`}</span>

      {/* Air Quality Slider */}
      <p style={{ color: '#0a0606', fontWeight: 'bold', marginTop: 10 }}>
        AIR QUALITY
        <span style={{ color: '#0a0606', fontWeight: 'normal' }}> (AQI)</span>
      </p>
      <div style={{ display: 'flex' }}>
        <span style={{ margin: '5px 5px 0px 0px' }}>0</span>
        <Slider
          trackStyle={{ backgroundColor: '#2A77F4', height: 5 }}
          railStyle={{ boxShadow: '0px 0px 6px #00000029', height: 5 }}
          min={0}
          max={1000}
          range
          onChange={onChange5}
          onAfterChange={onAfterChange5}
          value={airQuality1}
          style={{ width: '100%' }}
        />
        <span style={{ margin: '5px 0px 0px 5px' }}>1000</span>
      </div>
      <span
        style={{
          color: '#040606',
          opacity: 0.5,
        }}
      >{`${airQuality1[0]} - ${airQuality1[1]} AQI`}</span>

      {/* Products owned Section Images & Checkbox */}
      <div style={{ marginTop: 10, marginBottom: 10 }}>
        <p
          style={{
            color: '#0a0606',
            fontWeight: 'bold',
          }}
        >
          PRODUCTS OWNED
        </p>
        <Tabs defaultActiveKey="1">
          <TabPane tab="Include" key="1">
            {includeTabImages.length === 0 ? (
              <Empty />
            ) : (
              <div
                style={{
                  display: 'flex',
                  overflowX: 'scroll',
                  marginTop: 10,
                  gap: 10,
                }}
              >
                {includeTabImages.map((image) => (
                  <div
                    style={{
                      position: 'relative',
                      display: 'inline-block',
                      border: '1px solid #e2e2e2',
                      borderRadius: 5,
                    }}
                  >
                    <Image
                      style={{
                        width: '23vh',
                        height: '23vh',
                        borderRadius: 6,
                      }}
                      src={image.product_image}
                      preview={false}
                      onClick={() => handleIncludeImageClick(image.id)}
                    />
                    <Checkbox
                      checked={isCheckedInclude1.includes(image.id)}
                      style={{
                        position: 'absolute',
                        top: 4,
                        right: 6,
                        zIndex: 1,
                      }}
                      onClick={() => handleIncludeCheckboxClick(image.id)}
                    />
                  </div>
                ))}
              </div>
            )}
          </TabPane>
          <TabPane tab="Exclude" key="2">
            {excludeTabImages.length === 0 ? (
              <Empty />
            ) : (
              <div
                style={{
                  display: 'flex',

                  marginTop: 10,
                  gap: 10,
                  overflowX: 'scroll',
                }}
              >
                {excludeTabImages.map((image) => (
                  <div
                    style={{
                      position: 'relative',
                      display: 'inline-block',
                      border: '1px solid #e2e2e2',
                      borderRadius: 5,
                    }}
                  >
                    <Image
                      style={{
                        width: '23vh',
                        height: '23vh',
                        borderRadius: 6,
                      }}
                      src={image.product_image}
                      preview={false}
                      onClick={() => handleExcludeImageClick(image.id)}
                    />
                    <Checkbox
                      checked={isCheckedExclude1.includes(image.id)}
                      style={{
                        position: 'absolute',
                        top: 4,
                        right: 6,
                        zIndex: 1,
                      }}
                      onClick={() => handleExcludeCheckboxClick(image.id)}
                    />
                  </div>
                ))}
              </div>
            )}
          </TabPane>
        </Tabs>
      </div>

      {/* Detailed Targeted section with Search & suggestions */}
      <p style={{ color: '#0a0606', fontWeight: 'bold', marginTop: 10 }}>
        DETAILED TARGETED
      </p>
      <div style={{ display: 'flex' }}>
        <p
          style={{
            color: '#0a0606',
            letterspacing: 0.7,
            opacity: 0.5,
          }}
        >
          Include people who match
        </p>
        <InfoCircleFilled
          style={{ marginLeft: 6, opacity: 0.5, marginTop: 3 }}
        />
      </div>
      <AutoComplete
        options={options}
        onSelect={handleSearchDetails}
        onChange={handleInputChange}
        value={searchValueDetails}
        allowClear
        style={{ marginTop: 10, width: '100%' }}
      >
        <Input
          prefix={<SearchOutlined />}
          placeholder="ADD DEMOGRAPHICS, INTERESTS OR BEHAVIORS"
          size="small"
        />
      </AutoComplete>
    </div>
  );
}

export default UpdateAudTab;

/* eslint-disable no-unused-vars */
/* eslint-disable no-unused-expressions */
/* eslint-disable consistent-return */
/* eslint-disable  react/no-array-index-key */

import {
  Button,
  Col,
  Row,
  Spin,
  Card,
  Form,
  Input,
  Switch,
  Select,
  Image,
  Radio,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useHistory, useLocation } from 'react-router-dom';
import {
  LeftCircleOutlined,
  LoadingOutlined,
  CloseCircleOutlined,
  StarFilled,
  EllipsisOutlined,
} from '@ant-design/icons';
import IntlMessages from '@chill/components/utility/intlMessages';
import Notification from '@chill/components/Notification';
import theme from '@chill/config/theme/default';
import babyS from '@chill/assets/images/babyStrollerSee.png';
import closeBlue from '@chill/assets/images/cancel-circle-blue.png';
import getApiData from '@chill/lib/helpers/apiHelper';
import filterDrawerIcon from '@chill/assets/images/filter-drawer-icon.png';
import { isArray } from 'lodash';
import PageTitleHeader from '@chill/components/uielements/PageTitleHeader';
import CardDetailsWrapper from './CardDetailsWrapper.style';

export default function CardDetails() {
  const location = useLocation();
  const data = location.state;

  const history = useHistory();
  // const { setFromFenerateAI } = appActions;
  const [showPopup, setShowPopup] = useState(false);
  const [showPopupCircle, setShowPopupCircle] = useState(false);
  const handleGenerateCampaign = () => {
    setShowPopup(!showPopup);
  };
  const togglePopup = () => {
    setShowPopup(!showPopup);
  };

  const togglePopcircle = () => {
    setShowPopupCircle(!showPopupCircle);
  };
  const { Option } = Select;
  const [gender, setGender] = useState();
  const [language, setLanguage] = useState();
  const [languageOptions, setLanguageOptions] = useState([]);
  const [selectedGenders, setSelectedGenders] = useState([]);
  const [selectedLanguages, setSelectedLanguages] = useState([]);
  const [heading, setHeading] = useState();
  const [productsDetails, setProductsDetails] = useState([]);
  const [product, setProduct] = useState([]);
  const [productType, setProductType] = useState('awareness');
  const [userPrompt, setUserPrompt] = useState();
  const [showFilter, setShowFilter] = useState(true);

  let apiCardData = [];
  const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;
  const cardObj = {
    id: 1,
    title: 'Keeping baby cool',
    price: '$180',
    price1: '360',
    price2: '30',
    price3: '$60',
    description: 'Estimated Sold QTY :',
    description1: 'People in this audience:',
    description3: 'Value of the campaign:',
    imageSrc: babyS,
    // 'https://img.freepik.com/free-photo/cute-baby-boy-scooter-autumnal-park_1303-29193.jpg?w=900&t=st=1681296208~exp=1681296808~hmac=db2387c4656a1f6311d599ef3bfe7bcc87e651a820791a3584fcace0b25a9af0',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  };
  const [CardDetails1, setCardDetails1] = useState([]);
  const [loading, setLoading] = useState(false);
  const onFinish = async (values) => {
    setLoading(true);
    console.log('Form data:', values);

    const cardData = {
      product_id: data?.id,
      headline: heading,
      prompt: userPrompt,
      gender: selectedGenders,
      languages: selectedLanguages,
    };
    // console.log('this is data:', cardData);

    try {
      const response = await getApiData(
        'generative-ai/generate-multiple-images',
        cardData,
        'POST',
      );

      if (response.success) {
        // Notification('success', 'Generated ads');
        console.log('this is response:', response);
        console.log(response.data);
        setCardDetails1(
          response.data.map((card) => ({
            ...card,
            image: `data:image/png;base64,${card.image}`,
          })),
        );
        setLoading(false);
        apiCardData = response.data.map((card) => ({
          ...card,
          image: `data:image/png;base64,${card.image}`, // image property contains the base64-encoded data
        }));
        console.log('apiCardData:', apiCardData);
        // setShowPopupCircle(!showPopupCircle);
      }
    } catch (error) {
      Notification('error', 'Error generating the ad');
      // setShowPopupCircle(!showPopupCircle);
      setLoading(false);
      console.log(error);
    }
  };

  const handleNext = (values) => {
    if (CardDetails1.length === 0) {
      Notification('error', 'Generate image before Clicking on Next');
      return null;
    }
    togglePopup(values);
    handleGenerateCampaign();
    togglePopcircle();
    setTimeout(() => {
      history.push('/dashboard/dummyAd', { CardDetails1 });
    }, 5000);
  };

  const fetchLanguages = async () => {
    try {
      const response = await getApiData('getLanguageList', {}, 'POST');
      console.log(response.data);
      if (response.success) {
        console.log(response.data);
        setLanguageOptions(response.data);
      }
    } catch (error) {
      console.error('Error fetching images:', error);
    }
  };

  useEffect(() => {
    // Fetch images from the API
    fetchLanguages();
  }, []);

  const handleProductTypeChange = (e) => {
    setProductType(e.target.value);
  };

  const fetchImages = async () => {
    try {
      const response = await getApiData('product/get-brand-products', 'GET');
      console.log(response.data);
      if (response.success && isArray(response.data)) {
        console.log(response.data);
        setProductsDetails(response.data);
      }
    } catch (error) {
      console.error('Error fetching images:', error);
    }
  };

  useEffect(() => {
    fetchImages();
  }, []);

  const productOptions = productsDetails.map((item) => {
    return { value: item.product_name, label: item.product_name };
  });

  const handleProductChange = async (name) => {
    const prodItem = productsDetails.filter(
      (item) => item.product_name === name,
    )[0];
    // console.log('prod', prodItem);
    setProduct(prodItem);
  };

  const handleGenderChange = (selectedVlaues) => {
    setSelectedGenders(selectedVlaues);
  };
  const handleLanguageSelect = (selectedVlaues) => {
    setSelectedLanguages(selectedVlaues);
  };

  // useEffect(() => {
  //   if (showPopupCircle) {
  //     // setTimeout(() => {
  //     // setShowPopupCircle(!showPopupCircle);
  //     // history.push('/dashboard/dummyAd', apiCardData);
  //     // }, 5000);
  //   }
  // }, [showPopupCircle]);
  const updatedApiCardData = CardDetails1.map((card) => ({
    ...card, // Assuming there's only one object in cardData
  }));

  // Create a Set to store unique image URLs
  const uniqueImagesSet = new Set();

  const uniqueApiCardData = updatedApiCardData.filter((card) => {
    if (!uniqueImagesSet.has(card.image)) {
      uniqueImagesSet.add(card.image);
      return true;
    }
    return false;
  });

  console.log('uniqueApiCardData', uniqueApiCardData);

  return (
    <CardDetailsWrapper>
      <Row style={{ minHeight: '80vh' }}>
        <Col
          style={{
            padding: '20px 24px 20px 22px',
            overflowX: 'hidden',
            overflowY: 'hidden',
            // whitespace: 'nowrap',
          }}
          xs={24}
          sm={24}
          md={24}
          lg={24}
          xl={showFilter ? 17 : 24}
          xxl={showFilter ? 17 : 24}
        >
          <div
            style={{
              marginLeft: '20px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <PageTitleHeader title={<IntlMessages id="title.AIMarketing" />} />
            {!showFilter && (
              <Button
                type="text"
                style={{
                  padding: '0px',
                }}
                onClick={() => setShowFilter((prevState) => !prevState)}
              >
                <img
                  src={filterDrawerIcon}
                  alt="filterDrawerIcon"
                  // style={{ width: '24px', height: '24px' }}
                />
              </Button>
            )}
          </div>
          <div
            style={{
              padding: '24px 27px',
              overflowX: 'hidden',
              overflowY: 'hidden',
              marginTop: '38px',
              // whitespace: 'nowrap',
              borderRadius: '12px',
              background: '#FFFFFF',
            }}
          >
            <div>
              <span style={{ cursor: 'pointer' }}>
                <div
                  onClick={() => {
                    history.push('/dashboard/marketingnew1');
                  }}
                  role="button"
                  tabIndex={-1}
                  onKeyPress={() => {
                    history.push('/dashboard/marketingnew1');
                  }}
                  className="createSegmentP"
                  style={{
                    color: theme.colors.primaryColor,
                    display: 'flex',
                    justifyContent: 'flex-end',
                  }}
                >
                  <img
                    src={closeBlue}
                    alt="close"
                    style={{
                      width: '24px',
                      height: '24px',
                    }}
                  />
                </div>
              </span>
            </div>
            <div className="cardInnerDetails">
              <div className="cardDetails-content">
                <div>
                  <img
                    className="cardimage"
                    src={data?.image}
                    // "https://img.freepik.com/free-photo/father-teaching-his-little-son-ride-bicycle_1303-16323.jpg?size=626&ext=jpg&ga=GA1.1.907121617.1674105345&semt=ais"
                    alt="mychild"
                  />
                  <p
                    style={{
                      color: '#262E3D',
                      marginTop: '32px',
                      fontFamily: 'Inter',
                      fontSize: '14px',
                      fontWeight: 500,
                      lineHeight: '22px',
                      textAlign: 'left',
                    }}
                  >
                    {data?.name}
                  </p>
                </div>

                <div className="cardDetails">
                  <div className="cardDetails-space">
                    <p className="cardLabel">Recommended price</p>
                    <p className="cardLabelVal">${data?.price}</p>
                  </div>
                  <div className="cardDetails-space">
                    <p className="cardLabel">Audience</p>
                    <p className="cardLabelVal">{data?.audi}</p>
                  </div>
                  <div className="cardDetails-space">
                    <p className="cardLabel">Value of the campaign</p>
                    <p className="cardLabelVal">${data?.camp}</p>
                  </div>
                  <div className="cardDetails-space">
                    <p className="cardLabel">QTY of products to be sold</p>
                    <p className="cardLabelVal">{data?.qty}</p>
                  </div>
                  <div className="cardDetails-space">
                    <p className="cardLabel">Conversion rate</p>
                    <p className="cardLabelVal">18%</p>
                  </div>
                  <div className="cardDetails-space">
                    <p className="cardLabel">CTR</p>
                    <p className="cardLabelVal">---</p>
                  </div>
                </div>
              </div>
              <div className="buttonDiv">
                <Button type="ai" className="genreateBtn" onClick={togglePopup}>
                  <p className="btnText">Generate AI campaign creatives</p>
                </Button>
                {/* <Button
            disabled
            type="Schedule"
            style={{
              background: '#2A77F4 0% 0% no-repeat padding-box',
              borderRadius: '4px',
              textAlign: 'center',
              font: 'normal normal medium 14px/17px Rubik',
              border: 'none',
              color: '#2A77F4',
              width: '114px',
              height: '40px',
            }}
            // onClick={() => {
            //   window.open(
            //     'https://dev-dashboard.chillbaby.tech/design-studio ',
            //     '_blank',
            //     'noreferrer',
            //   );
            // }}
          >
            <p
              style={{
                color: '#fff',
                textAlign: 'center',
                font: 'normal normal medium 14px/17px Rubik',
              }}
            >
              Schedule
            </p>
          </Button> */}
                {/* <Button
            disabled
            type="Send"
            style={{
              background: '#2A77F4 0% 0% no-repeat padding-box',
              borderRadius: '4px',
              textAlign: 'center',
              font: 'normal normal medium 14px/17px Rubik',
              border: 'none',
              color: '#2A77F4',
              width: '100px',
              height: '40px',
              marginLeft: '20px',
            }}
            // onClick={() => {
            //   window.open(
            //     'https://dev-dashboard.chillbaby.tech/design-studio ',
            //     '_blank',
            //     'noreferrer',
            //   );
            // }}
          >
            <p
              style={{
                color: '#fff',
                textAlign: 'center',
                font: 'normal normal medium 14px/17px Rubik',
              }}
            >
              Send
            </p>
          </Button> */}
                <Button
                  // type="send"
                  style={{
                    // background: '#ffff',
                    borderRadius: '4px',
                    textAlign: 'center',
                    font: 'normal normal medium 14px/17px Rubik',
                    border: '1px solid #2A77F4',
                    marginRight: 20,
                    height: '40px',
                  }}
                  onClick={() => {
                    history.push({
                      pathname: '/design-studio',
                      state: {
                        audi: data?.audi,
                      }, // your data array of objects
                    });
                  }}
                >
                  <p
                    style={{
                      color: '#000',
                      textAlign: 'center',
                      font: 'normal normal medium 14px/17px Rubik',
                    }}
                  >
                    Create campaign
                  </p>
                </Button>
              </div>
            </div>
          </div>
        </Col>

        <Col
          style={{
            background: '#fff',
          }}
          xs={showFilter ? 24 : 0}
          sm={showFilter ? 24 : 0}
          md={showFilter ? 24 : 0}
          lg={showFilter ? 24 : 0}
          xl={showFilter ? 7 : 0}
          xxl={showFilter ? 7 : 0}
          className="cardDetails-right-col"
        >
          <div className="right-inner-content">
            <div className="title-div">
              <p className="chosenCat">Chosen Category</p>
              <Button
                type="text"
                style={{
                  padding: '0px',
                }}
                onClick={() => setShowFilter((prevState) => !prevState)}
              >
                <img
                  src={filterDrawerIcon}
                  alt="filterDrawerIcon"
                  // style={{ width: '24px', height: '24px' }}
                />
              </Button>
            </div>
            <div className="right-inner-points-div">
              <div className="detailsLabelVal">
                <p className="detailsLabel">Languages</p>
                <p className="detailsVal">English, Spanish, French</p>
              </div>
              <div className="detailsLabelVal">
                <p className="detailsLabel">Locations</p>
                <p className="detailsVal">USA, UK</p>
              </div>
              <div className="detailsLabelVal">
                <p className="detailsLabel">Child ages</p>
                <p className="detailsVal">2, 4,6</p>
              </div>
              <div className="detailsLabelVal">
                <p className="detailsLabel">Products owned</p>
                <p className="detailsVal">2, 4,6</p>
              </div>
            </div>
          </div>
        </Col>
        {showPopup && (
          <div
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              alignContent: 'center',
              zIndex: 90,
              overflow: 'auto',
            }}
          >
            <div
              style={{
                backgroundColor: 'white',
                padding: '50px 50px 10px 50px',
                borderRadius: '5px',
                boxShadow: '0 0 10px rgba(0, 0, 0, 0.3)',
                position: 'absolute',
                zIndex: 999,
                display: 'flex',
                // justifyContent: 'center',
                alignItems: 'center',
                // alignContent: 'center',
                flexDirection: 'column',
              }}
            >
              <Button
                type="ai"
                htmlType="submit"
                style={{
                  marginTop: -30,
                  border: 'none',
                  color: '#2A77F4',

                  alignSelf: 'flex-end',
                }}
                onClick={() => {
                  setShowPopup(false);
                }}
              >
                <CloseCircleOutlined
                  style={{
                    fontSize: '20px',
                  }}
                />
              </Button>
              <div
                style={{
                  backgroundColor: 'white',

                  borderRadius: '5px',

                  display: 'flex',
                  justifyContent: 'center',

                  alignContent: 'center',
                  flexDirection: 'row',
                }}
              >
                <Card
                  style={{
                    width: '14vw',
                    height: '35vh',
                    borderRadius: '12px',
                  }}
                  hoverable
                  cover={
                    <img
                      style={{
                        width: '14vw',
                        height: '25vh',
                        borderTopLeftRadius: '12px',
                        borderTopRightRadius: '12px',
                        objectFit: 'cover',
                      }}
                      alt="example"
                      src={data?.image}
                    />
                  }
                >
                  <div
                    style={{
                      marginTop: '-15px',
                      marginLeft: '-15px',
                      marginRight: '-15px',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between ',
                      }}
                    >
                      <h4
                        style={{
                          color: '#2A77F4',
                          fontSize: '14px',
                          fontFamily: 'rubik',
                          letterSpacing: '0px',
                        }}
                      >
                        {data?.name}
                      </h4>
                    </div>
                  </div>
                </Card>
                <div
                  style={{
                    flexDirection: 'column',
                    marginLeft: '10px',
                  }}
                >
                  <p
                    style={{
                      fontSize: '15px',
                      color: '#2A77F4',
                      marginBottom: 10,
                      fontWeight: 'bold',
                    }}
                  >
                    Create content :
                  </p>
                  <Form onFinish={onFinish} style={{ minWidth: '500px' }}>
                    {/* Text Input */}
                    <Form.Item
                      // label="Name"
                      style={{
                        marginBottom: 20,
                      }}
                      name="name"
                      rules={[
                        { required: true, message: 'Please enter title' },
                      ]}
                    >
                      <Input
                        placeholder="Headline text"
                        onChange={(e) => setHeading(e.target.value)}
                      />
                    </Form.Item>

                    {/* Text Area */}
                    <Form.Item
                      name="description"
                      style={{ marginBottom: 20 }}
                      rules={[
                        { required: true, message: 'Please enter Prompt' },
                      ]}
                    >
                      <Input.TextArea
                        placeholder="Prompt for the campaign..."
                        autoSize={{ minRows: 4, maxRows: 6 }}
                        onChange={(e) => setUserPrompt(e.target.value)}
                      />
                    </Form.Item>
                    <div
                      style={{
                        marginTop: 10,
                        display: 'flex',
                        alignItems: 'center',
                      }}
                    >
                      <Form.Item>
                        <Radio.Group
                          onChange={handleProductTypeChange}
                          value={productType}
                        >
                          <Radio value="awareness">Select Product</Radio>
                          <Radio value="sales">Add Product Link</Radio>
                        </Radio.Group>
                      </Form.Item>
                    </div>
                    <div>
                      {productType === 'awareness' ? (
                        <Form.Item
                          name="selectedProduct"
                          rules={[
                            {
                              required: true,
                              message: 'Please select a product',
                            },
                          ]}
                        >
                          <Select
                            placeholder="Select Any Product"
                            optionFilterProp="children"
                            size="lg"
                            style={{
                              width: '100%',
                            }}
                            onChange={handleProductChange}
                            value={product.product_name || undefined}
                            filterOption={(input, option) =>
                              (option?.label ?? '')
                                .toLowerCase()
                                .includes(input.toLowerCase())
                            }
                            options={productOptions}
                          />
                        </Form.Item>
                      ) : (
                        <Form.Item
                          name="productLink"
                          rules={[
                            {
                              required: true,
                              message: 'Please enter a product link',
                            },
                          ]}
                        >
                          <Input
                            addonBefore="https://"
                            allowClear
                            placeholder="Enter Product Link"
                          />
                        </Form.Item>
                      )}
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <Form.Item
                        name="genders"
                        rules={[
                          { required: true, message: 'Please Select Gender' },
                        ]}
                      >
                        <Select
                          placeholder="All Genders"
                          mode="multiple"
                          optionFilterProp="children"
                          size="medium"
                          style={{
                            minWidth: '15vw',
                          }}
                          onChange={handleGenderChange}
                          maxTagCount="responsive"
                          value={selectedGenders} // Use selectedGenders for multi-selection
                          filterOption={(input, option) =>
                            (option?.label ?? '')
                              .toLowerCase()
                              .includes(input.toLowerCase())
                          }
                          options={[
                            {
                              value: 'male',
                              label: 'Male',
                            },
                            {
                              value: 'female',
                              label: 'Female',
                            },
                          ]}
                        />
                      </Form.Item>
                      <Form.Item
                        name="languages"
                        rules={[
                          { required: true, message: 'Please Select Language' },
                        ]}
                      >
                        <Select
                          placeholder="All Languages"
                          size="medium"
                          mode="multiple"
                          style={{
                            minWidth: '15vw',
                          }}
                          onChange={handleLanguageSelect}
                          maxTagCount="responsive"
                          value={language}
                          filterOption={(inputValue, option) =>
                            option.children
                              .toLowerCase()
                              .indexOf(inputValue.toLowerCase()) >= 0
                          }
                        >
                          {languageOptions.map((option) => (
                            <Option key={option.id} value={option.lang_code}>
                              {option.lang_name}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </div>

                    <div
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'flex-end',
                        alignContent: 'flex-end',
                        alignItems: 'flex-end',
                        display: 'flex',
                        marginTop: '30px',
                      }}
                    >
                      <Form.Item>
                        <Button
                          // type="ai"
                          htmlType="submit"
                          style={{
                            // background:
                            //   'linear-gradient(138deg, rgb(20, 135, 255) 8%, rgb(41, 239, 196) 90%)',
                            borderRadius: '4px',
                            textAlign: 'center',
                            font: 'normal normal medium 14px/17px Rubik',
                            border: '1px solid #2A77F4',
                            // color: '#fff',
                            height: '40px',
                            alignSelf: 'flex-start',
                            marginRight: 20,
                          }}
                          // loading={loading}
                        >
                          {uniqueApiCardData.length > 0 ? 'Refresh' : 'Preview'}
                        </Button>
                      </Form.Item>
                      <Form.Item>
                        <Button
                          type="ai"
                          style={{
                            background:
                              'linear-gradient(138deg, rgb(20, 135, 255) 8%, rgb(41, 239, 196) 90%)',
                            borderRadius: '4px',
                            textAlign: 'center',
                            font: 'normal normal medium 14px/17px Rubik',
                            border: 'none',
                            color: '#2A77F4',
                            height: '40px',
                          }}
                          onClick={handleNext}
                        >
                          <p
                            style={{
                              color: '#fff',
                              textAlign: 'center',
                              font: 'normal normal medium 14px/17px Rubik',
                            }}
                          >
                            Generate AI WebPage
                          </p>
                        </Button>
                      </Form.Item>
                    </div>
                    {loading ? (
                      <center>
                        <Spin size="medium" />
                      </center>
                    ) : (
                      uniqueApiCardData &&
                      uniqueApiCardData.length > 0 && (
                        <Form.Item>
                          <div style={{ display: 'flex', flexWrap: 'wrap' }}>
                            {uniqueApiCardData.map((card, index) => (
                              <Card
                                style={{
                                  width: '10vw',
                                  height: '20vh',
                                  borderRadius: '12px',
                                  margin: '10px',
                                }}
                                key={index}
                                size="small"
                                cover={
                                  <img
                                    alt=""
                                    src={card.image}
                                    style={{
                                      width: '100%',
                                      height: '100%',
                                      borderRadius: '12px',
                                    }}
                                  />
                                }
                              />
                            ))}
                          </div>
                        </Form.Item>
                      )
                    )}
                  </Form>
                </div>
              </div>
            </div>

            {/* Style the black opacity background */}
            <div
              style={{
                content: '',
                position: 'fixed',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                backgroundColor: 'black',
                opacity: 0.7 /* Adjust the opacity level as needed */,
              }}
            />
          </div>
        )}
        {showPopupCircle && (
          <div
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              alignContent: 'center',
              zIndex: 9999,
            }}
          >
            <div
              style={{
                backgroundColor: 'white',
                padding: '50px',
                borderRadius: '5px',
                boxShadow: '0 0 10px rgba(0, 0, 0, 0.3)',
                position: 'absolute',
                zIndex: 999,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flexDirection: 'column',
              }}
            >
              <p
                style={{ fontSize: '20px', color: '#2A77F4', marginBottom: 50 }}
              >
                Our AI is working its magic…
              </p>
              <Spin indicator={antIcon} style={{ alignSelf: 'center' }} />
            </div>
            {/* Style the black opacity background */}
            <div
              style={{
                content: '',
                position: 'fixed',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                backgroundColor: 'black',
                opacity: 0.7 /* Adjust the opacity level as needed */,
              }}
            />
          </div>
        )}
      </Row>
    </CardDetailsWrapper>
  );
}

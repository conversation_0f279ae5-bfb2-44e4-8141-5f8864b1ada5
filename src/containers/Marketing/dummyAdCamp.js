/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable  react/jsx-no-bind */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
/* eslint-disable no-unused-expressions */
/* eslint-disable react/destructuring-assignment */

import React, { useEffect, useState } from 'react';
import {
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
} from 'recharts';

import {
  Col,
  Row,
  Card,
  Avatar,
  Dropdown,
  Space,
  Divider,
  Switch,
  Menu,
  Slider,
  Button,
  Progress,
  Table,
} from 'antd';
import { Link, useHistory, useLocation } from 'react-router-dom';
import {
  StarFilled,
  EllipsisOutlined,
  SettingFilled,
  CopyOutlined,
  LeftCircleOutlined,
  RocketOutlined,
} from '@ant-design/icons';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { useSelector } from 'react-redux';
import ContentLoader from 'react-content-loader';
import theme from '@chill/config/theme/default';
import IntlMessages from '@chill/components/utility/intlMessages';
import themes from '@chill/config/theme/theme.config';
import babyS from '@chill/assets/images/babyStrollerSee.png';

// Array of card data
const cardData = [
  {
    id: 1,
    title: 'Keeping baby cool',
    price: 'Safty,Comfort',
    price1: '360',
    price2: '3980',
    price3: '$60',
    description: 'Estimated views :',
    description1: 'Estimated shares :',
    description3: 'Values of the campaign :',
    description4: 'Focus :',
    imageSrc: babyS,
    //   'https://img.freepik.com/free-photo/cute-baby-boy-scooter-autumnal-park_1303-29193.jpg?w=900&t=st=1681296208~exp=1681296808~hmac=db2387c4656a1f6311d599ef3bfe7bcc87e651a820791a3584fcace0b25a9af0',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
  {
    id: 2,
    title: 'Keeping baby cool',
    price: 'Tech',
    price1: '360',
    price2: '4998',
    price3: '$60',
    description: 'Estimated views :',
    description1: 'Estimated shares :',
    description3: 'Values of the campaign :',
    description4: 'Focus :',
    imageSrc: babyS,
    //   'https://img.freepik.com/free-photo/small-girl-skate_1303-9646.jpg?size=626&ext=jpg&ga=GA1.1.1186299328.1674125467&semt=ais',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
  {
    id: 3,
    title: 'Keeping baby cool',

    price: 'Growing up',
    price1: '360',
    price2: '5090',
    price3: '$60',
    description: 'Estimated views :',
    description1: 'Estimated shares :',
    description3: 'Values of the campaign :',
    description4: 'Focus :',
    imageSrc: babyS,
    //   'https://img.freepik.com/free-photo/funny-kid-rain-boots-playing-rain-park_1157-37683.jpg?size=626&ext=jpg&ga=GA1.2.1186299328.1674125467&semt=ais',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
  {
    id: 4,
    title: 'Keeping baby cool',
    price: 'Safty,Comfort',
    price1: '360',
    price2: '4675',
    price3: '$60',
    description: 'Estimated views :',
    description1: 'Estimated shares :',
    description3: 'Values of the campaign :',
    description4: 'Focus :',
    imageSrc: babyS,
    //   'https://img.freepik.com/premium-photo/happy-child-with-robot-funny-kid-playing-home-success-creative-innovation-technology-concept_411285-9771.jpg?size=626&ext=jpg&ga=GA1.1.1186299328.1674125467&semt=ais',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
  {
    id: 5,
    title: 'Keeping baby cool',
    price: 'Tech',
    price1: '360',
    price2: '3989',
    price3: '$60',
    description: 'Estimated views :',
    description1: 'Estimated shares :',
    description3: 'Values of the campaign :',
    description4: 'Focus :',
    imageSrc: babyS,
    //   'https://img.freepik.com/free-photo/daughter-helping-packing-boxes_23-2148569347.jpg?size=626&ext=jpg&ga=GA1.1.1186299328.1674125467&semt=ais',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
  {
    id: 6,
    title: 'Keeping baby cool',
    price: 'Growing up',
    price1: '360',
    price2: '3546',
    price3: '$60',
    description: 'Estimated views :',
    description1: 'Estimated shares :',
    description3: 'Values of the campaign :',
    description4: 'Focus :',
    imageSrc: babyS,
    //   'https://img.freepik.com/premium-photo/happy-little-girl-protective-hat-riding-her-bike-outdoors-sunny-day-near-forest_146671-41396.jpg?size=626&ext=jpg&ga=GA1.1.1186299328.1674125467&semt=ais',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
  {
    id: 7,
    title: 'Keeping baby cool',
    price: 'Safty,Comfort',
    price1: '360',
    price2: '3234',
    price3: '$60',
    description: 'Estimated views :',
    description1: 'Estimated shares :',
    description3: 'Values of the campaign :',
    description4: 'Focus :',
    imageSrc: babyS,
    //   'https://img.freepik.com/free-photo/young-boy-having-fun-playground_23-2149490452.jpg?size=626&ext=jpg&ga=GA1.1.1186299328.1674125467&semt=robertav1_2_sidr',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
];

export default function dummyAdCamp() {
  const history = useHistory();
  const location = useLocation();
  const data = location.state;

  console.log('Data:', data);

  // store the state of selected icon
  const [selectedIcon, setSelectedIcon] = useState('AppstoreFilled');

  // const [selectedDropdownItem, setSelectedDropdownItem] = useState(items[0]);

  // store the state of sliderRange
  const [sliderRange, setSliderRange] = useState([6, 9]);
  const onAfterChange1 = (value) => {
    console.log('onAfterChange: ', value);
    setSliderRange(value);
  };

  const onChange1 = (value) => {
    console.log('onChange: ', value);
    setSliderRange(value);
  };
  // store the state of selected options and keys
  const { SubMenu } = Menu;
  const [openKeys, setOpenKeys] = useState();
  const [selectedOptions, setSelectedOptions] = useState({});

  const [imageLoader, setImageLoader] = useState(false);
  const [title1Loader, setTitle1loader] = useState(false);

  const MyImageLoader = (props) => (
    <ContentLoader
      speed={2}
      width={props.width || '14vw'}
      height={props.height || 117}
      viewBox={`0 0 ${props.width || '14vw'} ${props.height || 117}`}
      backgroundColor="#f3f3f3"
      foregroundColor="#ecebeb"
      {...props}
    >
      {/* Placeholder for the image */}
      <rect x="0" y="0" rx="12" ry="12" width="100%" height="100%" />
    </ContentLoader>
  );

  const TextLoader = (props) => (
    <ContentLoader
      speed={2}
      width={props.width || 200}
      height={props.height || 14}
      viewBox={`0 0 ${props.width || 200} ${props.height || 14}`}
      backgroundColor="#f3f3f3"
      foregroundColor="#ecebeb"
      {...props}
    >
      {/* Placeholder for the first h4 */}
      <rect x="0" y="0" rx="3" ry="3" width="70%" height="100%" />

      {/* Placeholder for the second h4 */}
      <rect x="0" y="20" rx="3" ry="3" width="50%" height="100%" />
    </ContentLoader>
  );

  const onOpenChange = (keys) => {
    setOpenKeys(keys);
  };
  // function for selecting menu items
  const handleOptionSelect = (event, key) => {
    setSelectedOptions({ ...selectedOptions, [key]: event.target.checked });
  };

  const handleApplyClick = () => {
    localStorage.setItem('selectedOptions', JSON.stringify(selectedOptions));
  };
  // function for checkbox clicked
  const onChange = (checked) => {
    console.log(`switch to ${checked}`);
  };

  // const [cards, setCards] = useState(cardData);

  // function handleDragEnd(result) {
  //   if (!result.destination) return;
  //   const item = Array.from(cards);
  //   const [reorderedItem] = item.splice(result.source.index, 1);
  //   item.splice(result.destination.index, 0, reorderedItem);
  //   setCards(item);
  // }
  useEffect(() => {
    console.log('set loaderssss true======');
    setImageLoader(true);
    setTitle1loader(true);
    setTimeout(() => {
      setImageLoader(false);
      setTitle1loader(false);
      console.log('set loaderssss false======');
      localStorage.setItem('fromAI', false);
      // Once the operation is done, set loading to false
    }, 5000);
  }, []);
  // to store the state of carddata
  const [cards, setCards] = useState(cardData);
  // functions for handling drag and drop
  function handleDragEnd(result) {
    if (!result.destination) return;

    const { source, destination } = result;

    if (source.index === destination.index) return;

    const newCards = [...cards];
    const [draggedCard] = newCards.splice(source.index, 1);

    newCards.splice(destination.index, 0, draggedCard);

    setCards(newCards);
  }

  // const handleDropdownClick = (item) => {
  //   setSelectedDropdownItem(item);
  // };

  // // Sort cards based on the selected value
  // const sortedCards = cards.slice().sort((a, b) => {
  //   const selectedValue = selectedDropdownItem.value;

  //   if (a[selectedValue] < b[selectedValue]) {
  //     return -1;
  //   }
  //   if (a[selectedValue] > b[selectedValue]) {
  //     return 1;
  //   }
  //   return 0;
  // });

  return (
    <div style={{ display: 'flex' }}>
      <div style={{ width: '75%' }}>
        <div style={{ background: '#fff' }}>
          <div
            style={{
              paddingTop: '10px',
              paddingLeft: '20px',
              paddingBottom: '30px',
            }}
          >
            <span style={{ cursor: 'pointer' }}>
              <div
                onClick={() => {
                  history.push('/dashboard/FeedCampaigns');
                }}
                role="button"
                tabIndex={-1}
                onKeyPress={() => {
                  history.push('/dashboard/FeedCampaigns');
                }}
                className="createSegmentP"
                style={{
                  color: theme.colors.primaryColor,
                  display: 'flex',
                }}
              >
                <p className="iconPadding" style={{ paddingRight: '10px' }}>
                  <LeftCircleOutlined />
                </p>
                <IntlMessages id="common.goBack" />
              </div>
            </span>
          </div>

          <div>
            <p
              style={{
                fontSize: '20px',
                fontWeight: 'bold',
                paddingLeft: '20px',
              }}
            >
              <h4>My Feed Campaigns:</h4>
              <span style={{ color: theme.colors.primaryColor }}>
                We have created
              </span>
              <span
                style={{
                  background:
                    'linear-gradient(138deg, rgb(20, 135, 255) 8%, rgb(41, 239, 196) 90%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}
              >
                {' '}
                64 Ad creatives
              </span>
              <span style={{ color: theme.colors.primaryColor }}> In</span>
              <span
                style={{
                  background:
                    'linear-gradient(138deg, rgb(20, 135, 255) 8%, rgb(41, 239, 196) 90%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}
              >
                {' '}
                6 different languages
              </span>{' '}
              <span style={{ color: theme.colors.primaryColor }}> With</span>
              <span
                style={{
                  background:
                    'linear-gradient(138deg, rgb(20, 135, 255) 8%, rgb(41, 239, 196) 90%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}
              >
                {' '}
                4 different messages
              </span>
            </p>
          </div>

          {/* main div for the stat cards */}
          {/* <div
            style={{
              display: 'flex',
              justifyContent: 'space-evenly',
              flexDirection: 'row',
              marginTop: '10px',
            }}
          >
            <div
              style={{
                backgroundColor: '#F0F0F0',
                height: '130px',
                width: '230px',
                borderRadius: '15px',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <h6 style={{ textAlign: 'center' }}>Total Products</h6>
              <p
                style={{
                  textAlign: 'center',
                  fontFamily: 'rubik',
                  fontSize: '25px',
                }}
              >
                20
              </p>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <RiseOutlined
                  style={{
                    color: '#4CB083',
                    fontWeight: 'bold',
                    fontSize: '20px',
                  }}
                />
                <h4
                  style={{
                    marginLeft: '5px',
                    color: '#4CB083',
                    fontFamily: 'rubik',
                  }}
                >
                  12% MoM
                </h4>
              </div>
            </div>

            <div
              style={{
                backgroundColor: '#F0F0F0',
                height: '130px',
                width: '230px',
                borderRadius: '15px',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <h6 style={{ textAlign: 'center' }}>Total Campaigns</h6>
              <p
                style={{
                  textAlign: 'center',
                  fontFamily: 'rubik',
                  fontSize: '25px',
                }}
              >
                100
              </p>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <RiseOutlined
                  style={{
                    color: '#4CB083',
                    fontWeight: 'bold',
                    fontSize: '20px',
                  }}
                />
                <h4
                  style={{
                    marginLeft: '5px',
                    color: '#4CB083',
                    fontFamily: 'rubik',
                  }}
                >
                  7% MoM
                </h4>
              </div>
            </div>
            <div
              style={{
                backgroundColor: '#F0F0F0',
                height: '130px',
                width: '230px',
                borderRadius: '15px',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <h6 style={{ textAlign: 'center' }}>Productivity Rate</h6>
              <p
                style={{
                  textAlign: 'center',
                  fontFamily: 'rubik',
                  fontSize: '25px',
                }}
              >
                20%
              </p>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <FallOutlined
                  style={{
                    color: '#EA0606',
                    fontWeight: 'bold',
                    fontSize: '20px',
                  }}
                />
                <h4
                  style={{
                    marginLeft: '5px',
                    color: '#EA0606',
                    fontFamily: 'rubik',
                  }}
                >
                  4% MoM
                </h4>
              </div>
            </div>
          </div> */}
          {/* end of cards */}
          {/* chat cards */}
          {/* <div style={{ background: '#fff', margin: '10px' }}>
            <Card
              style={{ width: '750px', height: '350px', background: '#fff' }}
            >
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  color: '#fff',
                }}
              >
                <p style={{ fontSize: '16px', color: '#000000' }}>
                  Recent Campaign Results
                </p>
                <Button
                  style={{
                    marginLeft: '50px',
                    width: '100px',
                    background: '#000',
                    borderRadius: '15px 15px 15px 15px',
                    color: '#fff',
                  }}
                >
                  Show all <RightOutlined />
                </Button>
              </div>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginTop: '20px',
                }}
              >
                <p style={{ color: '#A9ABBC' }}>Type</p>
                <p style={{ color: '#A9ABBC' }}>Date</p>
                <p style={{ color: '#A9ABBC' }}>Status</p>
                <p style={{ color: '#A9ABBC' }}>Productivity rate</p>
              </div>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginTop: '10px',
                  background: ' 	#F8F8F8',
                  padding: '10px 10px 10px 10px',
                  borderRadius: '15px 15px 15px 15px',
                }}
              >
                <p>internal payment</p>
                <div style={{ display: 'flex' }}>
                  <p style={{ color: '#A9ABBC' }}>20-12-2021 </p>
                  <p style={{ marginLeft: '10px' }}>12:30</p>
                </div>

                <Progress
                  percent={25}
                  status="active"
                  style={{ width: '30%' }}
                />

                <div style={{ display: 'flex' }}>
                  <p>456/987</p>
                
                </div>
              </div>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginTop: '10px',
                  background: ' 	#F8F8F8',
                  padding: '10px 10px 10px 10px',
                  borderRadius: '15px 15px 15px 15px',
                }}
              >
                <p>External payment</p>
                <div style={{ display: 'flex', marginLeft: '4px' }}>
                  <p style={{ marginLeft: '-15px', color: '#A9ABBC' }}>
                    18-12-2021
                  </p>
                  <p style={{ marginLeft: '10px' }}>14:10</p>
                </div>
                <Progress
                  percent={50}
                  status="active"
                  style={{ width: '30%' }}
                />
                <div style={{ display: 'flex' }}>
                  <p>566/990</p>
                 
                </div>
              </div>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginTop: '10px',
                  background: ' 	#F8F8F8',
                  padding: '10px 10px 10px 10px',
                  borderRadius: '15px 15px 15px 15px',
                }}
              >
                <p>internal payment</p>
                <div style={{ display: 'flex', marginLeft: '-10px' }}>
                  <p style={{ marginLeft: '10px', color: '#A9ABBC' }}>
                    16-11-2021{' '}
                  </p>
                  <p style={{ marginLeft: '10px' }}>23:10</p>
                </div>
                <Progress
                  percent={75}
                  status="active"
                  strokeColor="#52c41a" // Green color
                  style={{ width: '30%' }}
                />
                <div style={{ display: 'flex' }}>
                  <p>1019/200</p>
                 
                </div>
              </div>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginTop: '10px',
                  background: ' 	#F8F8F8',
                  padding: '10px 10px 10px 10px',
                  borderRadius: '15px 15px 15px 15px',
                }}
              >
                <p>internal payment</p>
                <div style={{ display: 'flex', marginLeft: '-15px' }}>
                  <p style={{ color: '#A9ABBC' }}>18-12-2021 </p>
                  <p style={{ marginLeft: '10px' }}>14:10</p>
                </div>
                <Progress
                  percent={100}
                  status="active"
                  strokeColor="#52c41a" // Green color
                  style={{ width: '30%' }}
                />
                <div style={{ display: 'flex' }}>
                  <p>019/2000</p>
                 
                </div>
              </div>
            </Card>
          </div> */}

          {/* <div>
            <Card
              style={{
                width: '720px',
                height: '320px',
                background: ' 	#F8F8F8',
                borderRadius: '20px',
                marginLeft: '20px',
                marginTop: '20px',
              }}
            >
              <p style={{ color: '#000000', fontSize: '16px' }}>Incomes</p>
              <p style={{ color: '#39A496', fontSize: '20px' }}>+$23,140</p>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}
              >
                <div>
                  <Card
                    style={{
                      width: '350px',
                      height: '210px',
                      borderRadius: '8px',
                    }}
                  >
                    <BarChart
                      width={350}
                      height={200}
                      data={barData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
                      style={{ marginTop: '-10px', marginLeft: '-20px' }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <Tooltip />
                      {/* <Legend /> */}
          {/* <Bar
                        dataKey="value"
                        shape={<CustomBar />}
                        barSize={30}
                        radius={[10, 10, 0, 0]}
                      />
                    </BarChart>
                  </Card>
                </div>
                <div>
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      gap: '15px',
                    }}
                  >
                    <Card
                      style={{
                        // width: '150px',
                        height: '100px',
                        borderRadius: '8px 8px 8px 8px',
                      }}
                    >
                      <p style={{ textAlign: 'center' }}>187k</p>
                      <Divider style={{ marginTop: '5px' }} />
                      <p style={{ marginTop: '-10px', textAlign: 'center' }}>
                        Total Balance
                      </p>
                    </Card>
                    <Card
                      style={{
                        // width: '150px',
                        height: '100px',
                        borderRadius: '8px 8px 8px 8px',
                      }}
                    >
                      <p style={{ textAlign: 'center' }}>83</p>
                      <Divider style={{ marginTop: '5px' }} />
                      <p style={{ marginTop: '-10px', textAlign: 'center' }}>
                        Transacations
                      </p>
                    </Card>
                  </div>
                  <Card
                    style={{
                      // width: '150px',
                      height: '100px',
                      borderRadius: '8px',
                      marginTop: '10px',
                      padding: '10px',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <div>
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <div
                            style={{
                              height: '15px',
                              width: '30px',
                              backgroundColor: '#82C0F0',
                              borderRadius: '10px',
                              marginTop: '3px',
                              marginRight: '4px',
                            }}
                          />
                          <p>Internal</p>
                        </div>
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <div
                            style={{
                              height: '15px',
                              width: '30px',
                              backgroundColor: '#E96929',
                              borderRadius: '10px',
                              marginTop: '3px',
                              marginRight: '4px',
                            }}
                          />
                          <p>External</p>
                        </div>
                      </div>
                      <PieChart
                        width={80}
                        height={80}
                        style={{ marginTop: '-25px' }}
                      >
                        <Pie
                          data={data}
                          dataKey="value"
                          cx="50%"
                          cy="50%"
                          innerRadius={0}
                          outerRadius={40}
                          startAngle={0}
                          endAngle={360}
                          paddingAngle={0}
                          animate
                        >
                          {data.map((entry) => (
                            <Cell
                              key={`cell-${entry.name}`}
                              fill={COLORS[data.indexOf(entry) % COLORS.length]}
                            />
                          ))}
                        </Pie>
                      </PieChart>
                    </div>
                  </Card>
                </div>
              </div>
            </Card>
          </div> */}
          {/* <div style={{ background: '#fff' }}>
            <Card
              style={{ width: '750px', height: '390px', background: '#fff' }}
            >
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}
              >
                <p style={{ fontSize: '16px', color: '#000000' }}>
                  Recent Transactions
                </p>
                <Button
                  style={{
                    marginLeft: '50px',
                    width: '100px',
                    background: '#000',
                    borderRadius: '15px 15px 15px 15px',
                    color: '#fff',
                  }}
                >
                  Show all <RightOutlined />
                </Button>
              </div>
              {/* <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginTop: '20px',
                }}
              >
                <p style={{ color: '#A9ABBC' }}>Type</p>
                <p style={{ color: '#A9ABBC' }}>Date</p>
                <p style={{ color: '#A9ABBC' }}>Status</p>
                <p style={{ color: '#A9ABBC' }}>Amount</p>
              </div>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginTop: '10px',
                  background: '#e7e7e7',
                  padding: '10px 10px 10px 10px',
                  borderRadius: '15px 15px 15px 15px',
                }}
              >
                <p>internal payment</p>
                <div style={{ display: 'flex' }}>
                  <p style={{ color: '#A9ABBC' }}>20-12-2021 </p>
                  <p style={{ marginLeft: '10px' }}>12:30</p>
                </div>
                <div style={{ display: 'flex' }}>
                  <p style={{ marginRight: '30px', color: '#FFA07A' }}>
                    <ClockCircleOutlined
                      style={{ color: '#FFA07A', marginLeft: '-5px' }}
                    />
                  </p>
                  <p style={{ marginLeft: '-20px', color: '#FFA07A' }}>
                    Waiting
                  </p>
                </div>
                <div style={{ display: 'flex' }}>
                  <p>1,500</p>
                  <p style={{ marginLeft: '10px' }}>USD</p>
                </div>
              </div>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginTop: '10px',
                  background: '#e7e7e7',
                  padding: '10px 10px 10px 10px',
                  borderRadius: '15px 15px 15px 15px',
                }}
              >
                <p>External payment</p>
                <div style={{ display: 'flex' }}>
                  <p style={{ marginLeft: '-15px', color: '#A9ABBC' }}>
                    18-12-2021{' '}
                  </p>
                  <p style={{ marginLeft: '10px' }}>14:10</p>
                </div>
                <div style={{ display: 'flex' }}>
                  <p style={{ marginRight: '30px' }}>
                    <CheckCircleOutlined
                      style={{ color: '#39A496', marginLeft: '-22px' }}
                    />
                  </p>
                  <p style={{ marginLeft: '-20px', color: '#39A496' }}>Paid</p>
                </div>
                <div style={{ display: 'flex' }}>
                  <p>4,000</p>
                  <p style={{ marginLeft: '10px' }}>USD</p>
                </div>
              </div>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginTop: '10px',
                  background: '#e7e7e7',
                  padding: '10px 10px 10px 10px',
                  borderRadius: '15px 15px 15px 15px',
                }}
              >
                <p>internal payment</p>
                <div style={{ display: 'flex' }}>
                  <p style={{ marginLeft: '10px', color: '#A9ABBC' }}>
                    16-11-2021{' '}
                  </p>
                  <p style={{ marginLeft: '10px' }}>23:10</p>
                </div>
                <div style={{ display: 'flex' }}>
                  <p style={{ marginRight: '30px' }}>
                    <CloseCircleOutlined
                      style={{ color: '#F70C1B', marginLeft: '13px' }}
                    />
                  </p>
                  <p style={{ marginLeft: '-20px', color: '#F70C1B' }}>
                    Canceled
                  </p>
                </div>
                <div style={{ display: 'flex' }}>
                  <p>1,000</p>
                  <p style={{ marginLeft: '10px' }}>USD</p>
                </div>
              </div>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginTop: '10px',
                  background: '#e7e7e7',
                  padding: '10px 10px 10px 10px',
                  borderRadius: '15px 15px 15px 15px',
                }}
              >
                <p>internal payment</p>
                <div style={{ display: 'flex' }}>
                  <p style={{ marginLeft: '-20px', color: '#A9ABBC' }}>
                    18-12-2021{' '}
                  </p>
                  <p style={{ marginLeft: '10px' }}>14:10</p>
                </div>
                <div style={{ display: 'flex' }}>
                  <p style={{ marginRight: '30px' }}>
                    <CheckCircleOutlined
                      style={{ color: '#39A496', marginLeft: '-40px' }}
                    />
                  </p>
                  <p style={{ marginLeft: '-40px', color: '#39A496' }}>Paid</p>
                </div>
                <div style={{ display: 'flex' }}>
                  <p>500</p>
                  <p style={{ marginLeft: '10px' }}>USD</p>
                </div>
              </div> 

              <Table
                columns={columns}
                dataSource={tableData}
                style={{ marginTop: '10px' }}
              />
            </Card>
          </div> */}

          <div>
            <div
              style={{
                height: '100%',
                width: '100%',
                paddingTop: '29.86px',
                paddingLeft: '10px',
                paddingRight: '10px',
                paddingBottom: '10px',

                overflowX: 'hidden',
                overflowY: 'hidden',
                whitespace: 'nowrap',
                display: selectedIcon === 'AppstoreFilled' ? 'block' : 'none',
              }}
            >
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="cards">
                  {(provided) => (
                    <div ref={provided.innerRef} {...provided.droppableProps}>
                      <Row gutter={[8, 16]}>
                        {cards.map((card, index) => (
                          <Draggable
                            key={card.id}
                            draggableId={card.id.toString()}
                            index={index}
                          >
                            {(dragProvided) => (
                              <Col
                                span={12}
                                md={6}
                                ref={dragProvided.innerRef}
                                {...dragProvided.draggableProps}
                                {...dragProvided.dragHandleProps}
                              >
                                {/* <Link to="/dashboard/card"> */}
                                <Card
                                  style={{
                                    width: '14vw',
                                    // height: '44vh',
                                    borderRadius: '12px',
                                  }}
                                  hoverable
                                  cover={
                                    imageLoader ? (
                                      <MyImageLoader />
                                    ) : (
                                      <img
                                        style={{
                                          width: '14vw',
                                          height: '117px',
                                          borderTopLeftRadius: '12px',
                                          borderTopRightRadius: '12px',
                                          objectFit: 'cover',
                                          background:
                                            'linear-gradient(138deg, rgb(20, 135, 255) 8%, rgb(41, 239, 196) 90%)',
                                        }}
                                        alt="example"
                                        src={data?.image}
                                      />
                                    )
                                  }
                                >
                                  <div
                                    style={{
                                      marginTop: '-15px',
                                      marginLeft: '-15px',
                                      marginRight: '-15px',
                                    }}
                                  >
                                    <div
                                      style={{
                                        display: 'flex',
                                        // justifyContent: 'space-between',
                                      }}
                                    >
                                      {title1Loader ? (
                                        <TextLoader />
                                      ) : (
                                        <h4
                                          style={{
                                            color: '#2A77F4',
                                            fontSize: '14px',
                                            fontFamily: 'rubik',
                                            letterSpacing: '0px',
                                            marginRight: '5px',
                                          }}
                                        >
                                          {data?.name}
                                        </h4>
                                      )}
                                    </div>
                                    <div
                                      style={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        color: '#A9ABBC',
                                        fontSize: '12px',
                                        fontFamily: 'Rubik',
                                        marginTop: '8px',
                                      }}
                                    >
                                      {title1Loader ? (
                                        <TextLoader />
                                      ) : (
                                        <>
                                          {card.description}
                                          <h4>{card.price1}</h4>
                                        </>
                                      )}
                                    </div>
                                    <div
                                      style={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        color: '#A9ABBC',
                                        fontSize: '12px',
                                        fontFamily: 'Rubik',
                                        marginTop: '8px',
                                      }}
                                    >
                                      {title1Loader ? (
                                        <TextLoader />
                                      ) : (
                                        <>
                                          {card.description1}
                                          <h4>{card.price2}</h4>
                                        </>
                                      )}
                                    </div>
                                    <div
                                      style={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        color: '#A9ABBC',
                                        fontSize: '12px',
                                        fontFamily: 'Rubik',
                                        marginTop: '8px',
                                      }}
                                    >
                                      {title1Loader ? (
                                        <TextLoader />
                                      ) : (
                                        <>
                                          {card.description3}
                                          <h4>{card.price3}</h4>
                                        </>
                                      )}
                                    </div>

                                    <div
                                      style={{
                                        display: 'flex',
                                        justifyContent: 'flex-end',
                                      }}
                                    >
                                      <div
                                        style={{
                                          fontSize: '16px',
                                          color: '#FFC107',
                                          marginLeft: '105px',
                                          marginTop: '5px',
                                        }}
                                      >
                                        {card.icon1}
                                      </div>
                                      <div
                                        style={{
                                          fontSize: '20px',
                                          color: '#A9ABBC',
                                          marginLeft: '5px',
                                          marginTop: '5px',
                                        }}
                                      >
                                        {card.icon2}
                                      </div>
                                    </div>
                                  </div>
                                </Card>
                                {/* </Link> */}
                              </Col>
                            )}
                          </Draggable>
                        ))}

                        <Col span={12} md={6}>
                          <Card
                            hoverable
                            style={{
                              display: 'flex',
                              width: '14vw',
                              height: '32vh',
                              borderRadius: '10px',
                              alignContent: 'center',
                              alignItems: 'center',
                              justifyContent: 'center',
                              background:
                                'linear-gradient(138deg, rgb(20, 135, 255) 8%, rgb(41, 239, 196) 90%)',
                            }}
                            bodyStyle={{
                              display: 'flex',
                              padding: 0,
                              flexDirection: 'column',
                            }}
                          >
                            <RocketOutlined
                              style={{
                                fontSize: '40px',
                                color: 'white',

                                justifyContent: 'center',
                                alignItems: 'center',
                              }}
                            />
                            <h5
                              style={{
                                color: 'white',
                                fontFamily: 'Rubik',
                                fontSize: '17px',
                                textAlign: 'center',
                              }}
                            >
                              Send All
                            </h5>
                          </Card>
                        </Col>
                      </Row>

                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            </div>

            {/* <div
              style={{
                height: '100%',
                width: '58.5vw',
                paddingLeft: '23px',
                paddingRight: '23px',

                overflowY: 'overflow',
                whitespace: 'nowrap',
                marginTop: '20px',
                paddingBottom: '10px',
                display: selectedIcon === 'MenuOutlined' ? 'block' : 'none',
              }}
            >
              {cardData.map((card) => (
                <Link to="/dashboard/card">
                  <Card
                    hoverable
                    style={{
                      width: '56vw',
                      height: '14vh',
                      marginLeft: '-10px',
                      borderRadius: '12px',
                      marginTop: '20px',
                    }}
                  >
                    <div style={{ display: 'flex', flexDirection: 'row' }}>
                      <img
                        src={card.imageSrc}
                        alt="mydata"
                        style={{
                          width: '11.8vw',
                          height: '14vh',
                          margin: '-25px',
                          borderTopLeftRadius: '12px',
                          borderBottomLeftRadius: '12px',
                        }}
                      />
                      <div style={{ marginLeft: '30px', marginTop: '-40px' }}>
                        <h3
                          style={{
                            color: '#2A77F4',
                            fontSize: '14px',
                            fontFamily: 'Rubik',
                            letterSpacing: '0px',
                            marginTop: '24px',
                            marginLeft: '10px',
                          }}
                        >
                          {card.title}
                        </h3>
                        <h5
                          style={{
                            color: '#0A0606',
                            fontSize: '12px',
                            fontFamily: 'Rubik',
                            marginLeft: '10px',
                          }}
                        >
                          {card.price}
                        </h5>
                        <p
                          style={{
                            color: '#A9ABBC',
                            fontFamily: 'Rubik',
                            fontSize: '12px',
                            marginLeft: '10px',
                            marginRight: '-30px',
                          }}
                        >
                          {card.description}{' '}
                          <span style={{ color: '#0A0606' }}>
                            {card.price1}
                          </span>
                        </p>
                        <div>
                          <p
                            style={{
                              color: '#A9ABBC',
                              fontFamily: 'Rubik',
                              fontSize: '12px',
                              marginTop: '-40px',
                              marginRight: '-30px',

                              marginLeft: '200px',
                            }}
                          >
                            {card.description1}
                            <span style={{ color: '#0A0606' }}>
                              {card.price2}
                            </span>
                          </p>
                          <p
                            style={{
                              color: '#A9ABBC',
                              fontFamily: 'Rubik',
                              fontSize: '12px',
                              marginTop: '5px',
                              marginRight: '-30px',

                              marginLeft: '200px',
                            }}
                          >
                            {card.description3}
                            <span style={{ color: '#0A0606' }}>
                              {card.price3}
                            </span>
                          </p>
                        </div>
                      </div>
                      <div style={{ marginTop: '-15px', marginRight: '-15px' }}>
                        <EllipsisOutlined
                          style={{
                            color: '#A9ABBC',
                            fontSize: '30px',
                            marginTop: '-10px',
                            marginRight: '-10px',
                            marginLeft: '200px',
                          }}
                        />
                        <div style={{ display: 'flex' }}>
                          <StarFilled
                            style={{
                              color: '#FFC107',
                              marginLeft: '100px',
                              marginTop: '20px',
                              fontSize: '20px',
                            }}
                          />
                          <StarFilled
                            style={{
                              color: '#FFC107',
                              marginLeft: '15px',
                              marginTop: '20px',
                              fontSize: '20px',
                            }}
                          />
                          <StarFilled
                            style={{
                              color: '#FFC107',
                              marginLeft: '15px',
                              marginTop: '20px',
                              fontSize: '20px',
                            }}
                          />
                          <StarFilled
                            style={{
                              color: '#FFC107',
                              marginLeft: '15px',
                              marginTop: '20px',
                              fontSize: '20px',
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  </Card>
                </Link>
              ))}
            </div> */}
          </div>
          {/* <center>
            <h1 style={{ padding: '20px' }}>
              *There is no Products in this Campaign
            </h1>
          </center> */}
        </div>
      </div>
      <div style={{ width: '25%' }}>
        <div
          style={{
            width: '100%',
            marginLeft: '15px',
            background: '#fff',
            height: '100%',
            marginRight: '10px',
            padding: '20px',
          }}
        >
          <div>
            <Divider />
            <h1
              style={{
                marginTop: '-10px',
                fontFamily: 'Rubik',
                fontSize: '14px',
                color: '0A0606',
              }}
            >
              Filter
            </h1>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <h1
                style={{
                  marginTop: '10px',

                  fontSize: '12px',
                }}
              >
                Email Notification
              </h1>
              <Switch
                defaultChecked
                onChange={onChange}
                style={{
                  marginTop: '10px',
                  marginRight: '5px',
                  width: '3vw',
                  height: '3.5vh',
                }}
              />
            </div>
            <Divider />

            <Menu
              mode="inline"
              openKeys={openKeys}
              onOpenChange={onOpenChange}
              style={{
                width: '100%',
              }}
            >
              <SubMenu
                style={{
                  marginTop: '-20px',
                  fontSize: '13px',
                  width: '100%',
                }}
                key="sub1"
                title="Audience size"
              >
                <Menu.Item
                  key="1"
                  style={{
                    color: '#A9ABBC',
                    marginLeft: '-20px',
                  }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '0.412')}
                    style={{ marginRight: '10px' }}
                  />
                  2(8)
                </Menu.Item>

                <Menu.Item
                  key="2"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '0.413')}
                    style={{ marginRight: '10px' }}
                  />
                  4(125)
                </Menu.Item>
                <Menu.Item
                  key="3"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '0.414')}
                    style={{ marginRight: '10px' }}
                  />
                  6(529)
                </Menu.Item>
              </SubMenu>

              <Divider style={{ marginTop: '8px' }} />

              <SubMenu
                style={{
                  marginTop: '-20px',
                  fontSize: '13px',
                }}
                key="sub2"
                title="Conversion rate"
              >
                <Menu.Item
                  key="5"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '5%')}
                    style={{ marginRight: '10px' }}
                  />
                  5%
                </Menu.Item>
                <Menu.Item
                  key="6"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '6%')}
                    style={{ marginRight: '10px' }}
                  />
                  6%
                </Menu.Item>
              </SubMenu>
              <Divider style={{ marginTop: '8px' }} />

              <SubMenu
                style={{
                  marginTop: '-20px',
                  fontSize: '13px',
                }}
                key="sub3"
                title="QTY"
              >
                <Menu.Item
                  key="7"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '81')}
                    style={{ marginRight: '10px' }}
                  />
                  81
                </Menu.Item>
                <Menu.Item
                  key="8"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '85')}
                    style={{ marginRight: '10px' }}
                  />
                  85
                </Menu.Item>
              </SubMenu>
              <Divider style={{ marginTop: '8px' }} />

              <SubMenu
                style={{
                  marginTop: '-20px',
                  fontSize: '13px',
                }}
                key="sub4"
                title="Language"
              >
                <Menu.Item
                  key="9"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, 'english')}
                    style={{ marginRight: '10px' }}
                  />
                  English
                </Menu.Item>
                <Menu.Item
                  key="10"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, 'Spanish')}
                    style={{ marginRight: '10px' }}
                  />
                  Spanish
                </Menu.Item>
              </SubMenu>
              <Divider style={{ marginTop: '8px' }} />
              <SubMenu
                style={{
                  marginTop: '-20px',
                  fontSize: '13px',
                }}
                key="sub5"
                title="Location"
              >
                <Menu.Item
                  key="11"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, 'USA')}
                    style={{ marginRight: '10px' }}
                  />
                  USA
                </Menu.Item>
                <Menu.Item
                  key="12"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, 'UK')}
                    style={{ marginRight: '10px' }}
                  />
                  UK
                </Menu.Item>
              </SubMenu>
              <Divider style={{ marginTop: '8px' }} />

              <SubMenu
                style={{
                  marginTop: '-20px',
                  fontSize: '13px',
                }}
                key="sub9"
                title="Products owned"
              >
                <Menu.Item
                  key="13"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '4,2,6')}
                    style={{ marginRight: '10px' }}
                  />
                  4,2,6
                </Menu.Item>
                <Menu.Item
                  key="14"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '5,2,6')}
                    style={{ marginRight: '10px' }}
                  />
                  5,2,6
                </Menu.Item>
              </SubMenu>
              <Divider style={{ marginTop: '8px' }} />

              <SubMenu
                style={{
                  marginTop: '-20px',
                  fontSize: '13px',
                }}
                key="sub6"
                title="Child age range"
              >
                <Slider
                  style={{ marginTop: '-8px' }}
                  trackStyle={{ backgroundColor: '#2A77F4' }}
                  railStyle={{ boxShadow: '0px 0px 6px #00000029' }}
                  range
                  defaultValue={[6, 9]}
                  onChange={onChange1}
                  onAfterChange={onAfterChange1}
                />
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'left',
                    color: '#040606',
                    opacity: 0.5,
                  }}
                >
                  <span>{sliderRange[0]}</span>
                  <span>-</span>
                  <span>{sliderRange[1]}+</span>
                </div>
              </SubMenu>
              <Divider style={{ marginTop: '8px' }} />
            </Menu>
            <div
              style={{
                flex: 1,
                width: '100%',
                hegith: '100%',
              }}
            >
              <center>
                <Button
                  style={{
                    background: 'blue',
                    borderRadius: '4px',
                    color: '#fff',
                    paddingLeft: '70px',
                    paddingRight: '70px',
                  }}
                  onClick={handleApplyClick}
                >
                  Apply
                </Button>
              </center>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

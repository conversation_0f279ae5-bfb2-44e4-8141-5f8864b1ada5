import feed from '@chill/assets/images/feed.png';
import cellphone from '@chill/assets/images/cellphone.png';
import message from '@chill/assets/images/message.png';

import React from 'react';
import { Image } from 'antd';
import { useHistory } from 'react-router-dom';

function MarketingCreateNew() {
  const history = useHistory();

  // handling icon clicks

  const handleIconClick = (audienceType) => {
    console.log('Icon Name: ', audienceType);
    localStorage.setItem('audienceType', audienceType);
    history.push('/dashboard/marketing/audience');
  };

  return (
    <div
      style={{
        flex: 1,

        width: '100%',
        boxShadow: '2px 1px 10px 1px #ccc',
        border: '0.5px solid #ededed',
        borderRadius: 20,
        opacity: 1,
        background:
          'transparent linear-gradient(100deg, #5087FF 0%, #29EFC4 100%) 0% 0% no-repeat padding-box',
      }}
    >
      <p
        style={{
          textAlign: 'center',
          paddingTop: 20,
          color: '#ffffff',
          fontSize: 14,
        }}
      >
        WHAT DO YOU WANT TO CREATE TODAY
      </p>
      <div
        style={{
          flex: 1,

          padding: '40px 60px 20px 60px',
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-around',
          paddingTop: 41,
          margin: '0 auto',
        }}
      >
        <div>
          <Image
            src={feed}
            preview={false}
            onClick={() => handleIconClick('feed_post')}
            style={{ paddingLeft: 3, cursor: 'pointer' }}
          />
          <p style={{ fontSize: 14, color: '#ffffff', paddingTop: 10 }}>
            Feed Post
          </p>
        </div>
        <div>
          <Image
            src={cellphone}
            preview={false}
            onClick={() => handleIconClick('in_app_message')}
            style={{ paddingLeft: 30, cursor: 'pointer' }}
          />
          <p style={{ fontSize: 14, color: '#ffffff', paddingTop: 10 }}>
            In-App Message
          </p>
        </div>
        <div>
          <Image
            src={message}
            preview={false}
            onClick={() => handleIconClick('push_message')}
            style={{ paddingLeft: 12, cursor: 'pointer' }}
          />
          <p style={{ fontSize: 14, color: '#ffffff', paddingTop: 10 }}>
            Push Message
          </p>
        </div>
      </div>
    </div>
  );
}

export default MarketingCreateNew;

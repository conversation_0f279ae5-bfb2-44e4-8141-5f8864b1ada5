/* eslint-disable no-shadow */
/* eslint-disable no-use-before-define */
/* eslint-disable no-use-before-define */
/* eslint-disable no-unused-vars */
/* eslint-disable react/no-array-index-key */
/* eslint-disable react/jsx-boolean-value */
/* eslint-disable no-param-reassign */
/* eslint-disable  react/jsx-fragments */
/* eslint-disable func-names */
/* eslint-disable react/self-closing-comp */
/* eslint-disable no-new */
/* eslint-disable import/no-cycle */

import React, { useRef, useEffect, useState } from 'react';
import { Stage, Layer, Rect, Transformer, Image } from 'react-konva';
import { useDispatch, useSelector } from 'react-redux';
import Konva from 'konva';
import KonvaText, {
  KonvaLayer,
  KonvaLabel,
  KonvaTag,
  KonvaTransformer,
  KonvaUploadImage,
  KonvaButtontext,
  KonvaShape,
} from './KonvaText';

export default class KonvaBoard2 {
  static obj = null;

  static objRef = null;

  static transformer = null;

  static undoStack = [];

  static redoStack = [];

  static selectionRectangle = null;

  static clear() {
    KonvaBoard2.obj.destroyChildren();
    KonvaBoard2.obj.draw();
  }

  static toDataURL() {
    const dataURL = KonvaBoard2.obj.toDataURL({ pixelRatio: 3 });
    return dataURL;
  }

  constructor(objRef) {
    KonvaBoard2.objRef = objRef;
    KonvaBoard2.obj = objRef.current;
    KonvaBoard2.setupTransformer();
    KonvaBoard2.setupListners();
  }

  static setupTransformer() {}

  static setupListners() {}

  static getClassObject(name, attrs) {
    let obj = null;

    switch (name) {
      case 'Layer':
        obj = new KonvaLayer(attrs);
        break;
      case 'Text':
        obj = new KonvaText(attrs);
        break;
      case 'Label':
        obj = new KonvaLabel(attrs);
        break;
      case 'Buttontext':
        obj = new KonvaButtontext(attrs);
        break;
      case 'Tag':
        obj = new KonvaTag(attrs);
        break;
      case 'Image':
        obj = new KonvaUploadImage(attrs);
        break;
      case 'Shape':
        obj = new KonvaShape(attrs);
        break;
      case 'Transformer':
        obj = new KonvaTransformer(attrs);
        break;
      default:
        break;
    }
    return obj;
  }

  static downloadURI(uri, name) {
    const link = document.createElement('a');
    link.download = name;
    link.href = uri;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  static toJSON() {
    const img = new window.Image();
    const dataURL = KonvaBoard2.obj.toDataURL({ pixelRatio: 3 });
    img.src = dataURL;
    // img can be use to display on templates now
    KonvaBoard2.obj.attrs.stageImg = img.src;
    const json = KonvaBoard2.obj.toJSON();
    // console.log(json);
    return json;
  }

  static fromJSON(json) {
    let layers = [];
    console.log('json in konvaboard2 ', json);
    console.log(json);
    console.log(json.children);

    if (
      !Object.prototype.hasOwnProperty.call(json, 'className') ||
      json.className !== 'Stage'
    ) {
      return;
    }

    KonvaBoard2.obj.destroyChildren();
    KonvaLayer.count = 0;
    KonvaBoard2.setupTransformer();

    if (!Object.prototype.hasOwnProperty.call(json, 'attrs')) {
      KonvaBoard2.obj.setAttrs(json.attrs);
    }

    if (
      !Object.prototype.hasOwnProperty.call(json, 'children') ||
      json.children.length === 0
    ) {
      return;
    }

    json.children.forEach((layer) => {
      layers.push(layer.attrs?.id);
    });

    json.children.forEach((child) => {
      if (
        !Object.prototype.hasOwnProperty.call(child, 'className') ||
        !Object.prototype.hasOwnProperty.call(child, 'attrs') ||
        !Object.prototype.hasOwnProperty.call(child, 'children') ||
        child.children.length === 0
      ) {
        return;
      }

      if (
        child.className === 'Layer' &&
        child.attrs?.id === 'transformer_layer'
      ) {
        return;
      }

      const parent = KonvaBoard2.getClassObject(child.className, child.attrs);
      child.children.forEach((grandC) => {
        if (
          !Object.prototype.hasOwnProperty.call(grandC, 'className') ||
          !Object.prototype.hasOwnProperty.call(grandC, 'attrs')
        ) {
          return;
        }

        const grandCC = KonvaBoard2.getClassObject(
          grandC.className,
          grandC.attrs,
        );

        if (parent && parent.obj && grandCC && grandCC.obj) {
          parent.obj.add(grandCC.obj); // add to layer
        }
        if (grandCC.type !== 'Label') return;
        grandC.children.forEach((ggrandC) => {
          if (
            !Object.prototype.hasOwnProperty.call(ggrandC, 'className') ||
            !Object.prototype.hasOwnProperty.call(ggrandC, 'attrs')
          ) {
            return;
          }
          const ggrandCC = KonvaBoard2.getClassObject(
            ggrandC.className,
            ggrandC.attrs,
          );
          console.log('may be btn');
          console.log(grandCC);
          console.log(ggrandC);
          console.log(ggrandCC);

          if (grandCC && grandCC.obj && ggrandCC && ggrandCC.obj) {
            grandCC.obj.add(ggrandCC.obj); // add to btn
          }
        });
      });

      if (parent && parent.obj) {
        KonvaBoard2.obj.add(parent.obj); // Add layer to Stage
        // KonvaBoard2.obj.batchDraw();
      }
    });

    const jsonEnd = JSON.parse(KonvaBoard2.toJSON());
    layers = [];
    jsonEnd.children?.forEach((child) => {
      layers.push(child.attrs?.id);
    });

    // ...
  }
}

export const KonvaCanvas = ({ images, width, height, zoomLevel }) => {
  // const scaledWidth = (width * zoomLevel) / 100;
  // const scaledHeight = (height * zoomLevel) / 100;
  const objRef = React.useRef();
  if (!width) width = 300;
  if (!height) height = 300;

  useEffect(() => {
    new KonvaBoard2(objRef);
  }, [objRef]);

  const layerRef = useRef(null);
  const layer = useSelector((state) => state.layer);
  const [loadedImages, setLoadedImages] = useState([]);
  const [selectedImageIndex, setSelectedImageIndex] = useState(-1);
  const [selectedImagePosition, setSelectedImagePosition] = useState({
    x: 0,
    y: 0,
  });
  const [isTransforming, setIsTransforming] = useState(false);
  const transformerRef = useRef();
  const dispatch = useDispatch();
  if (!layer) layerRef.current = KonvaBoard2.getClassObject(`Layer`, {});

  useEffect(() => {
    // Access the stage reference and update the width and height properties
    const stage = objRef.current;
    stage.width(width);
    stage.height(height);
    // Call batchDraw to redraw the stage with the updated size
    stage.batchDraw();
  }, [width, height]);

  useEffect(() => {
    const loadImages = async () => {
      try {
        const loadedImages = await Promise.all(
          images.map((image) => loadImage(image)),
        );
        setLoadedImages(loadedImages);
      } catch (error) {
        console.error('Error loading images:', error);
      }
    };

    loadImages();
  }, [images]);

  useEffect(() => {
    const stage = objRef.current;
    const json = stage.toJSON();
    console.log('stageRef JSON format:', json);
  }, []);

  function loadImage(src) {
    return new Promise((resolve, reject) => {
      const image = new window.Image();
      image.onload = () => resolve(image);
      image.onerror = (error) => reject(error);
      image.src = src;
    });
  }

  const handleStageClick = (e) => {
    if (!e.target.hasName('transformer')) {
      setSelectedImageIndex(-1);
      setIsTransforming(false);
    }
  };

  return (
    <div>
      <Stage
        style={{
          background: '#fff',
          border: '1px solid #0000001a',
          boxShadow: '0.5px 0.5px 200px 0.5px gray',
          transform: `scale(${zoomLevel / 100})`, // Apply the zoom level as transform scale
        }}
        width={width}
        height={height}
        ref={objRef}
        onMouseDown={handleStageClick}
      ></Stage>
    </div>
  );
};

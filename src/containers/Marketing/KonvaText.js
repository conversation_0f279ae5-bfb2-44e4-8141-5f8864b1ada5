/* eslint-disable no-plusplus */
/* eslint-disable no-prototype-builtins */
/* eslint-disable  no-param-reassign */
/* eslint-disable max-classes-per-file */
/* eslint-disable import/no-cycle */
import Konva from 'konva';
import availableShapes from './Shapes';
import KonvaBoard2 from './KonvaBoard2';

let trRef = null;

let selectedObj = null;

export default class KonvaText {
  static count = 0;

  static textCount = 0;

  static headingCount = 0;

  static editText = null;

  static onTextClickCallback = null;

  obj = null;

  type = 'Text';

  constructor(attrs) {
    this.obj = new Konva.Text(attrs);
    const name = `${attrs.isHeading ? 'Heading' : 'Paragraph'}-${
      attrs.isHeading ? KonvaText.headingCount++ : KonvaText.textCount++
    }`;
    if (!Object.hasOwn(attrs, 'name')) attrs.name = name;
    const id = `${this.type}-${KonvaText.count++}`;
    if (!Object.hasOwn(attrs, 'name')) attrs.id = id;
    this.obj.setAttrs(attrs);
    this.setupTransformer();
  }

  toJson() {
    return JSON.stringify(this.obj, null, 2);
  }

  static fromJson(json) {
    const text = new KonvaText();
    text.obj = JSON.parse(json);
    return text;
  }

  setupTransformer() {
    this.obj.addEventListener('dblclick', () => {
      // Create textarea/input at position of text node
      const textarea = document.createElement('textarea');
      document.body.appendChild(textarea);

      const textPosition = this.obj.absolutePosition();
      const areaPosition = {
        x: this.obj.parent.parent.container().offsetLeft + textPosition.x + 10,
        y: this.obj.parent.parent.container().offsetTop + textPosition.y,
      };

      console.log(`Position:`);
      console.log(this.obj);
      console.log(areaPosition);
      // Set initial value and style
      textarea.value = this.obj.text();
      textarea.style.fontSize = this.obj.fontSize();
      textarea.style.position = `absolute`;
      textarea.style.top = `${areaPosition.y}px`;
      textarea.style.left = `${areaPosition.x}px`;
      // textarea.style.width = `${this.obj.width() - this.obj.padding() * 2}px`;
      // textarea.style.height = `${
      //   this.obj.height() - this.obj.padding() * 2 + 5
      // }px`;
      textarea.style.width = '300px';
      textarea.style.maxHeight = `${KonvaBoard2.obj.height}px`;
      textarea.style.minHeight = `${this.obj.height()}px`;
      textarea.style.textAlign = 'center';
      textarea.style.border = 'none';
      textarea.style.outline = 'none';
      textarea.style.resize = 'none';
      textarea.style.zIndex = 9999;
      textarea.style.overflow = 'hidden';
      textarea.style.padding = '1px';
      textarea.focus();
      textarea.addEventListener('blur', () => {
        console.log('clicked on stage');
        // this.obj.text(textarea.value);
        // KonvaText.trRef = null;
        // this.obj.parent.batchDraw();
        // if (KonvaText.trRef) document.body.removeChild(KonvaText.trRef);
        // if (textarea) document.body.removeChild(textarea);
        // Check if textarea is empty
        if (textarea.value.trim() === '') {
          // Remove the current element or object
          this.obj.parent.remove(); // You might need to adjust this based on your actual object hierarchy
        } else {
          this.obj.text(textarea.value);
          this.obj.parent.batchDraw();
        }

        // Cleanup
        if (KonvaText.trRef) {
          document.body.removeChild(KonvaText.trRef);
          KonvaText.trRef = null;
        }
        if (textarea) {
          document.body.removeChild(textarea);
        }
      });
    });
    this.obj.addEventListener('click', () => {
      console.log(`text clicked`);
      if (selectedObj !== this.obj) {
        if (trRef) {
          trRef.nodes([]);
          this.obj.off('transform');
        }
      }
      selectedObj = this.obj;
      KonvaText.editText = this.obj;
      if (KonvaText.onTextClickCallback) {
        KonvaText.onTextClickCallback();
      }
      const minWidth = 20;
      if (this.obj.parent.children.includes(trRef)) return;
      trRef = new Konva.Transformer({
        nodes: [this.obj],
        padding: 5,
        keepRatio: true,
        anchorSize: 7,
        enabledAnchors: ['middle-left', 'middle-right'],
        boundBoxFunc: (oldBox, newBox) => {
          if (newBox.width < minWidth) {
            return oldBox;
          }
          return newBox;
        },
      });
      trRef.attrs.type = 'Transformer';
      this.obj.parent.add(trRef);
      console.log(this.obj.parent);
      this.obj.on('transform', () => {
        const newWidth = Math.max(
          this.obj.width() * this.obj.scaleX(),
          minWidth,
        );
        const maxWidth = 350;
        const finalWidth = Math.min(newWidth, maxWidth);
        console.log('new W new H', finalWidth);
        this.obj.setAttr('width', finalWidth);
        this.obj.setAttr('scaleX', 1);
        this.obj.setAttr('scaleY', 1);
        this.obj.cache();
        trRef.forceUpdate();
        this.obj.parent.batchDraw();
      });
    });
  }
}
export class KonvaParagraph {
  static count = 0;

  obj = null;

  type = 'para';

  constructor(attrs) {
    console.log('New Text Obj');
    this.obj = new Konva.Text(attrs);
    this.obj.setAttrs(attrs);
    this.setupTransformer();
  }

  toJson() {
    return JSON.stringify(this.obj, null, 2);
  }

  static fromJson(json) {
    const text = new KonvaParagraph();
    text.obj = JSON.parse(json);
    return text;
  }

  setupTransformer() {
    this.obj.addEventListener('dblclick', () => {
      // Create textarea/input at position of text node
      const textarea = document.createElement('textarea');
      document.body.appendChild(textarea);
      const areaPosition = {
        x: 50,
        y: 50,
      };

      console.log(`Position:`);
      console.log(this.obj);
      console.log(areaPosition);
      // Set initial value and style
      textarea.value = this.obj.text();
      textarea.style.fontSize = this.obj.fontSize();
      textarea.style.position = `absolute`;
      textarea.style.top = `${areaPosition.y}px`;
      textarea.style.left = `${areaPosition.x}px`;
      textarea.style.width = `${this.obj.width() - this.obj.padding() * 2}px`;
      textarea.style.height = `${
        this.obj.height() - this.obj.padding() * 2 + 5
      }px`;
      textarea.style.textAlign = 'center';
      textarea.style.border = 'none';
      textarea.style.outline = 'none';
      textarea.style.resize = 'none';
      textarea.style.zIndex = 9999;
      textarea.style.overflow = 'hidden';
      textarea.style.padding = '1px';
      textarea.focus();
      textarea.addEventListener('blur', () => {
        // Update text of text node and remove textarea/input
        this.obj.text(textarea.value);
        if (textarea) document.body.removeChild(textarea);
      });
    });
    this.obj.addEventListener('click', () => {
      console.log(`text clicked`);
    });
  }
}

export class KonvaLabel {
  static count = 0;

  obj = null;

  type = 'Label';

  url = null;

  add(element) {
    this.obj.add(element);
  }

  constructor(attrs) {
    console.log(attrs);
    this.url = attrs.url;
    this.obj = new Konva.Label({
      x: 50,
      y: 50,
      // opacity: 0.75,
      draggable: true,
      borderRadious: 10,
    });
    const name = `${this.type}-${KonvaLabel.count++}`;
    if (!Object.hasOwn(attrs, 'name')) attrs.name = name;
    this.obj.setAttrs(attrs);
    this.setupEventListener();
  }

  setupEventListener() {
    this.obj.on('click', () => {
      console.log('button clicked');
      console.log(this.url);
      if (this.url) {
        window.open(this.url, '_blank');
      }
    });
  }
}

export class KonvaButtontext {
  static count = 0;

  static editButtonText = null;

  obj = null;

  type = 'text';

  constructor(attrs) {
    this.obj = new Konva.Text(attrs);
    this.obj.setAttrs(attrs);
    KonvaButtontext.editButtonText = this.obj;
  }
}
export class KonvaTag {
  static count = 0;

  static editButton = null;

  obj = null;

  type = 'Tag';

  constructor(attrs) {
    this.obj = new Konva.Tag(attrs);
    this.obj.setAttrs(attrs);
    KonvaTag.editButton = this.obj;
    console.log('tag', this.obj);
    console.log('KonvaTag.editButton', KonvaTag.editButton);
  }
}

export class KonvaUploadImage {
  static count = 0;

  static uploadImageCount = 0;

  static aiImageCount = 0;

  static editImage = null;

  static images = [];

  static onImageClickCallback = null;

  obj = null;

  type = 'Image';

  constructor(attrs) {
    attrs.filters = [
      Konva.Filters.Brighten,
      Konva.Filters.HSL,
      Konva.Filters.Blur,
      Konva.Filters.Contrast,
    ];
    this.obj = new Konva.Image(attrs);
    if (!attrs.Image?.src) {
      const img = new window.Image();
      img.src = attrs.imageSource;
      img.onload = () => {
        this.obj.cache();
      };
      attrs.Image = img;
    } else {
      this.obj.cache();
    }
    const name = `${attrs.isAiImage ? 'AiImage' : 'Image'}-${
      attrs.isAiImage
        ? KonvaUploadImage.aiImageCount++
        : KonvaUploadImage.uploadImageCount++
    }`;
    if (!Object.hasOwn(attrs, 'name')) attrs.name = name;
    const id = `${this.type}-${KonvaUploadImage.count++}`;
    if (!Object.hasOwn(attrs, 'name')) attrs.id = id;
    this.obj.setAttrs(attrs);
    this.setupTransformer();
  }

  setupTransformer() {
    this.obj.addEventListener('click', () => {
      console.log(`image clicked`);
      if (selectedObj !== this.obj) {
        if (trRef) {
          trRef.nodes([]);
          this.obj.off('transform');
        }
      }
      selectedObj = this.obj;
      KonvaUploadImage.editImage = this.obj;
      if (KonvaUploadImage.onImageClickCallback) {
        KonvaUploadImage.onImageClickCallback();
      }
      const minWidth = 20;
      const minHeight = 20;
      if (this.obj.parent.children.includes(trRef)) return;
      console.log('parent', this.obj.parent);
      trRef = new Konva.Transformer({
        nodes: [this.obj],
        padding: 5,
        keepRatio: false,
        anchorSize: 7,
        enabledAnchors: [
          'top-left',
          'top-center',
          'top-right',
          'middle-left',
          'middle-right',
          'bottom-left',
          'bottom-center',
          'bottom-right',
        ],
        boundBoxFunc: (oldBox, newBox) => {
          if (newBox.height < minHeight || newBox.width < minWidth) {
            return oldBox;
          }
          return newBox;
        },
      });
      trRef.attrs.type = 'Transformer';
      this.obj.parent.add(trRef);
      this.obj.on('transformend', () => {
        console.log('transformer');
        const newWidth = Math.max(
          this.obj.width() * this.obj.scaleX(),
          minWidth,
        );
        const newHeight = Math.max(
          this.obj.height() * this.obj.scaleY(),
          minHeight,
        );
        console.log('new W new H', newWidth, newHeight);
        this.obj.setAttr('width', newWidth);
        this.obj.setAttr('height', newHeight);
        this.obj.setAttr('scaleX', 1);
        this.obj.setAttr('scaleY', 1);
        this.obj.cache();
        trRef.forceUpdate();
        this.obj.parent.batchDraw();
      });
    });
  }
}
export class KonvaShape {
  static count = 0;

  static editShape = null;

  static onShapeClickCallback = null;

  url = null;

  obj = null;

  type = 'Shape';

  constructor(attrs) {
    this.url = attrs.url || null;
    const selectedShape = availableShapes.find(
      (shape) => shape.id === attrs.shapeId,
    );
    this.obj = new Konva.Shape({ ...selectedShape.component.props, ...attrs });
    const name = `${this.type}-${KonvaShape.count++}`;
    if (!Object.hasOwn(attrs, 'name')) attrs.name = name;
    this.obj.setAttrs(attrs);

    KonvaShape.editShape = this.obj;
    this.setupTransformer();
  }

  static removeTransformer() {
    if (trRef) {
      trRef.nodes([]);
      KonvaUploadImage.editImage?.off('transform');
      KonvaText.editText?.off('transform');
      KonvaButtontext.editButtonText?.off('transform');
      KonvaTag.editButton?.off('transform');
      KonvaShape.editShape?.off('transform');
      KonvaTag.editButton?.off('transform');
    }
  }

  setupTransformer() {
    this.obj.addEventListener('click', () => {
      console.log('clicked');
      console.log('this.obj', this.obj);
      console.log('Object dimensions:', this.obj.width(), this.obj.height());
      if (this.url) {
        window.open(this.url, '_blank');
      }
      KonvaShape.editShape = this.obj;
      if (selectedObj !== this.obj) {
        if (trRef) {
          trRef.nodes([]);
          this.obj.off('transform');
        }
      }
      selectedObj = this.obj;
      // if (KonvaShape.onShapeClickCallback) {
      //   KonvaShape.onShapeClickCallback();
      // }
      const minWidth = 20;
      const minHeight = 20;
      if (this.obj.parent.children.includes(trRef)) return;
      trRef = new Konva.Transformer({
        nodes: [this.obj],
        padding: 5,
        keepRatio: false,
        anchorSize: 7,
        enabledAnchors: [
          'top-left',
          'top-center',
          'top-right',
          'middle-left',
          'middle-right',
          'bottom-left',
          'bottom-center',
          'bottom-right',
        ],
        boundBoxFunc: (oldBox, newBox) => {
          if (newBox.height < minHeight || newBox.width < minWidth) {
            return oldBox;
          }
          return newBox;
        },
      });
      trRef.attrs.type = 'Transformer';
      this.obj.parent.add(trRef);
      this.obj.on('transform', () => {
        console.log('this  object', this.obj);
        console.log('width', this.obj.width());
        console.log('height', this.obj.height());
        const newWidth = Math.max(
          this.obj.width() * this.obj.scaleX(),
          minWidth,
        );
        const newHeight = Math.max(
          this.obj.height() * this.obj.scaleY(),
          minHeight,
        );
        if (newWidth > 0 && newHeight > 0) {
          // this.obj.setAttr('width', newWidth);
          // this.obj.setAttr('height', newHeight);
          this.obj.setAttr('scaleX', newWidth / this.obj.width());
          this.obj.setAttr('scaleY', newHeight / this.obj.height());
          // this.obj.cache();
        }
        trRef.forceUpdate();
        this.obj.parent.batchDraw();
      });
    });
  }
}

export class KonvaLayer {
  static count = 0;

  obj = null;

  type = 'Layer';

  constructor(attrs) {
    this.obj = new Konva.Layer(attrs);
    const id = `${this.type}-${KonvaLayer.count++}`;

    // Set default attrs if not there
    if (!Object.hasOwn(attrs, 'id')) attrs.id = id;
    if (!Object.hasOwn(attrs, 'type')) attrs.type = `${this.type}`;
    if (!Object.hasOwn(attrs, 'name')) attrs.name = attrs.id;
    this.obj.setAttrs(attrs);
  }
}

export class KonvaTransformer {
  static count = 0;

  obj = null;

  type = 'Transformer';

  constructor(attrs) {
    this.obj = new Konva.Transformer(attrs);
    this.obj.setAttrs(attrs);
  }
}

/* eslint-disable react/react-in-jsx-scope */
/* eslint-disable no-plusplus */
import React from 'react';
import { Shape } from 'react-konva';

const availableShapes = [
  {
    id: '1',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const numPoints = 5;
          const width = 100; // Adjust the width here
          const height = 100; // Adjust the height here
          const innerRadius = width * 0.3;
          const outerRadius = width * 0.5;
          const x = width / 2;
          const y = height / 2;

          context.beginPath();
          for (let i = 0; i < numPoints * 2; i++) {
            const radius = i % 2 === 0 ? outerRadius : innerRadius;
            const angle = (i * Math.PI) / numPoints;
            const pointX = x + radius * Math.sin(angle);
            const pointY = y - radius * Math.cos(angle);
            if (i === 0) {
              context.moveTo(pointX, pointY);
            } else {
              context.lineTo(pointX, pointY);
            }
          }
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="purple"
      />
    ),
    width: 100, // Assign the width here
    height: 100, // Assign the height here
    name: 'Star',
    category: '',
  },
  {
    id: '2',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const x = 50;
          const y = 20;
          const scale = 0.9; // Adjust the scale value for a smaller heart

          context.beginPath();
          context.moveTo(x, y + 30 * scale);
          context.bezierCurveTo(
            x + 40 * scale,
            y - 20 * scale,
            x + 90 * scale,
            y + 45 * scale,
            x,
            y + 90 * scale,
          );
          context.bezierCurveTo(
            x - 90 * scale,
            y + 45 * scale,
            x - 40 * scale,
            y - 20 * scale,
            x,
            y + 30 * scale,
          );
          context.closePath();
          // Fill the heart with the specified color
          context.fillStrokeShape(shape);
        }}
        fill="red"
      />
    ),
    name: 'Heart',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '3',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          // Draw the star shape using context methods (custom sceneFunc)
          const numPoints = 7;
          const innerRadius = 30;
          const outerRadius = 50;
          const x = 50;
          const y = 50;

          context.beginPath();
          for (let i = 0; i < numPoints * 2; i++) {
            const radius = i % 2 === 0 ? outerRadius : innerRadius;
            const angle = (i * Math.PI) / numPoints;
            const pointX = x + radius * Math.sin(angle);
            const pointY = y - radius * Math.cos(angle);
            if (i === 0) {
              context.moveTo(pointX, pointY);
            } else {
              context.lineTo(pointX, pointY);
            }
          }
          context.closePath();
          // Fill the star with the specified color
          context.fillStrokeShape(shape);
        }}
        fill="cyan"
      />
    ),
    name: 'Star2',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '4',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 75;
          const height = 75;
          const x = 10;
          const y = 10;

          context.beginPath();
          context.moveTo(x, y);
          context.lineTo(x + width, y);
          context.lineTo(x + width, y + height);
          context.lineTo(x, y + height);
          context.closePath();
          // Fill the rectangle with the specified color
          context.fillStrokeShape(shape);
        }}
        fill="gray"
      />
    ),
    name: 'Rectangle',
    category: 'basicShapes',
    height: 100,
    width: 100,
  },
  {
    id: '5',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const radius = 40;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.arc(x, y, radius, 0, 2 * Math.PI);
          context.closePath();
          // Fill the circle with the specified color
          context.fillStrokeShape(shape);
        }}
        fill="gray"
      />
    ),
    name: 'Circle',
    category: 'basicShapes',
    height: 100,
    width: 100,
  },
  {
    id: '6',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const radiusX = 50;
          const radiusY = 25;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.ellipse(x, y, radiusX, radiusY, 0, 0, 2 * Math.PI);
          context.closePath();
          // Fill the ellipse with the specified color
          context.fillStrokeShape(shape);
        }}
        fill="orange"
      />
    ),
    name: 'Ellipse',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '7',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const innerRadius = 20;
          const outerRadius = 40;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.arc(x, y, outerRadius, 0, 2 * Math.PI);
          context.arc(x, y, innerRadius, 0, 2 * Math.PI, true);
          context.closePath();
          // Fill the ring with the specified color
          context.fillStrokeShape(shape);
        }}
        fill="yellow"
      />
    ),
    name: 'Ring',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '8',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const startAngle = 0;
          const endAngle = Math.PI; // Angle of 180 degrees (in radians)
          const innerRadius = 20;
          const outerRadius = 50;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.arc(x, y, outerRadius, startAngle, endAngle);
          context.arc(x, y, innerRadius, endAngle, startAngle, true); // Inner arc (reverse direction)
          context.closePath();
          // Fill the arc with the specified color
          context.fillStrokeShape(shape);
        }}
        fill="pink"
      />
    ),
    name: 'Arc',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '9',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const sides = 6;
          const radius = 50;
          const x = 50;
          const y = 50;

          context.beginPath();
          for (let i = 0; i < sides; i++) {
            const angle = (i * (2 * Math.PI)) / sides;
            const xPoint = x + radius * Math.cos(angle);
            const yPoint = y + radius * Math.sin(angle);
            if (i === 0) {
              context.moveTo(xPoint, yPoint);
            } else {
              context.lineTo(xPoint, yPoint);
            }
          }
          context.closePath();
          // Fill the hexagon with the specified color
          context.fillStrokeShape(shape);
        }}
        fill="teal"
      />
    ),
    name: 'Hexagon',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '10',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const sides = 3;
          const radius = 50;
          const x = 50;
          const y = 50;

          context.beginPath();
          for (let i = 0; i < sides; i++) {
            const angle = (i * 2 * Math.PI) / sides - Math.PI / 2;
            const xPoint = x + radius * Math.cos(angle);
            const yPoint = y + radius * Math.sin(angle);
            if (i === 0) {
              context.moveTo(xPoint, yPoint);
            } else {
              context.lineTo(xPoint, yPoint);
            }
          }
          context.closePath();
          // Fill the triangle with the specified color
          context.fillStrokeShape(shape);
        }}
        fill="gray"
      />
    ),
    name: 'Triangle',
    category: 'basicShapes',
    height: 100,
    width: 100,
  },
  {
    id: '11',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const sides = 8;
          const radius = 50;
          const x = 50;
          const y = 50;

          context.beginPath();
          for (let i = 0; i < sides; i++) {
            const angle = (i * (2 * Math.PI)) / sides;
            const xPoint = x + radius * Math.cos(angle);
            const yPoint = y + radius * Math.sin(angle);
            if (i === 0) {
              context.moveTo(xPoint, yPoint);
            } else {
              context.lineTo(xPoint, yPoint);
            }
          }
          context.closePath();
          // Fill the octagon with the specified color
          context.fillStrokeShape(shape);
        }}
        fill="lime"
      />
    ),
    name: 'Octagon',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '12',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 100; // Desired width
          const height = 100; // Desired height
          context.beginPath();
          context.moveTo(width * 0.3, height * 0.1); // Top-left point
          context.lineTo(width * 0.1, height * 0.3); // Left point
          context.lineTo(width * 0.3, height * 0.5); // Middle-left point
          context.lineTo(width * 0.1, height * 0.7); // Bottom-left point
          context.lineTo(width * 0.3, height * 0.9); // Bottom-middle point
          context.lineTo(width * 0.5, height * 0.7); // Bottom-right point
          context.lineTo(width * 0.7, height * 0.9); // Middle-right point
          context.lineTo(width * 0.9, height * 0.7); // Right point
          context.lineTo(width * 0.7, height * 0.5); // Middle-right point
          context.lineTo(width * 0.9, height * 0.3); // Top-right point
          context.lineTo(width * 0.7, height * 0.1); // Top-middle point
          context.lineTo(width * 0.5, height * 0.3); // Top-right point
          context.closePath();
          // (!) Konva specific method, it is very important
          context.fillStrokeShape(shape);
        }}
        fill="#00D2FF"
      />
    ),
    name: 'arrow',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '13',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 75; // Desired width
          const height = 38; // Desired height
          context.beginPath();
          context.arc(width * 0.3, height * 0.3, width * 0.2, 0, 2 * Math.PI); // Left circle
          context.arc(width * 0.5, height * 0.4, width * 0.3, 0, 2 * Math.PI); // Middle circle
          context.arc(width * 0.7, height * 0.3, width * 0.2, 0, 2 * Math.PI); // Right circle
          context.closePath();
          // (!) Konva specific method, it is very important
          context.fillStrokeShape(shape);
        }}
        fill="#00D2FF"
        rotation={45}
      />
    ),
    name: 'cloud',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '14',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          context.beginPath();
          context.moveTo(50, 0); // Top point
          context.lineTo(100, 50); // Right point
          context.lineTo(50, 100); // Bottom point
          context.lineTo(0, 50); // Left point
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="gray"
      />
    ),
    name: 'diamond',
    category: 'basicShapes',
    height: 100,
    width: 100,
  },
  {
    id: '15',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 75; // Desired width
          const height = 75; // Desired height

          const centerX = width / 2; // X-coordinate of the center
          const centerY = height / 2; // Y-coordinate of the center

          context.beginPath();
          context.moveTo(centerX, centerY - height / 2); // Top point
          context.lineTo(centerX + width / 2, centerY); // Right point
          context.lineTo(centerX, centerY + height / 2); // Bottom point
          context.lineTo(centerX - width / 2, centerY); // Left point
          context.closePath();
          // (!) Konva specific method, it is very important
          context.fillStrokeShape(shape);
        }}
        fill="gray"
      />
    ),
    name: 'rightangle triangle',
    category: 'basicShapes',
    height: 100,
    width: 100,
  },
  {
    id: '16',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const sides = 5;
          const radius = 50;
          const x = 50;
          const y = 50;

          context.beginPath();
          for (let i = 0; i < sides; i++) {
            const angle = (i * (2 * Math.PI)) / sides;
            const xPoint = x + radius * Math.cos(angle);
            const yPoint = y + radius * Math.sin(angle);
            if (i === 0) {
              context.moveTo(xPoint, yPoint);
            } else {
              context.lineTo(xPoint, yPoint);
            }
          }
          context.closePath();
          // Fill the pentagon with the specified color
          context.fillStrokeShape(shape);
        }}
        fill="magenta"
      />
    ),
    name: 'Pentagon',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '17',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const sides = 6;
          const radius = 50;
          const x = 50;
          const y = 50;

          context.beginPath();
          for (let i = 0; i < sides; i++) {
            const angle = (i * (2 * Math.PI)) / sides - Math.PI / 6;
            const xPoint = x + radius * Math.cos(angle);
            const yPoint = y + radius * Math.sin(angle);
            if (i === 0) {
              context.moveTo(xPoint, yPoint);
            } else {
              context.lineTo(xPoint, yPoint);
            }
          }
          context.closePath();
          // Fill the hexagon with the specified color
          context.fillStrokeShape(shape);
        }}
        fill="darkorange"
      />
    ),
    name: 'Hexagon2',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '18',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          // Draw the star shape using context methods (custom sceneFunc)
          const numPoints = 6;
          const innerRadius = 30;
          const outerRadius = 50;
          const x = 50;
          const y = 50;

          context.beginPath();
          for (let i = 0; i < numPoints * 2; i++) {
            const radius = i % 2 === 0 ? outerRadius : innerRadius;
            const angle = (i * Math.PI) / numPoints;
            const pointX = x + radius * Math.sin(angle);
            const pointY = y - radius * Math.cos(angle);
            if (i === 0) {
              context.moveTo(pointX, pointY);
            } else {
              context.lineTo(pointX, pointY);
            }
          }
          context.closePath();
          // Fill the star with the specified color
          context.fillStrokeShape(shape);
        }}
        fill="pink"
      />
    ),
    name: 'Star1',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '19',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const numPoints = 10;
          const innerRadius = 20;
          const outerRadius = 50;
          const x = 50;
          const y = 50;

          context.beginPath();
          for (let i = 0; i < numPoints * 2; i++) {
            const radius = i % 2 === 0 ? outerRadius : innerRadius;
            const angle = (i * Math.PI) / numPoints;
            const pointX = x + radius * Math.sin(angle);
            const pointY = y - radius * Math.cos(angle);
            if (i === 0) {
              context.moveTo(pointX, pointY);
            } else {
              context.lineTo(pointX, pointY);
            }
          }
          context.closePath();
          // Fill the starburst with the specified color
          context.fillStrokeShape(shape);
        }}
        fill="gold"
      />
    ),
    name: 'Starburst',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '20',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80; // Desired width
          const height = 80; // Desired height

          context.beginPath();
          context.arc(width * 0.3, height * 0.5, width * 0.4, 0, Math.PI * 2); // Outer circle
          context.arc(width * 0.6, height * 0.5, width * 0.4, 0, Math.PI * 2); // Inner circle
          context.closePath();
          // Fill the moon with the specified color
          context.fillStrokeShape(shape);
        }}
        fill="lightgray"
      />
    ),
    name: 'Moon',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '21',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 50;
          const height = 80;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x - width / 2, y - height / 2); // Top point
          context.lineTo(x + width / 2, y - height / 2); // Right point
          context.lineTo(x + width / 2, y + height / 2); // Bottom point
          context.lineTo(x - width / 2, y + height / 2); // Left point
          context.closePath();
          context.moveTo(x, y - height / 2); // Move to the top center
          context.lineTo(x, y + height / 2); // Line through the center
          context.moveTo(x - width / 2, y); // Move to the left center
          context.lineTo(x + width / 2, y); // Line through the center
          context.fillStrokeShape(shape);
        }}
        fill="lightblue"
      />
    ),
    name: 'Cross',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '22',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const x = 50;
          const y = 50;
          const scale = 0.6;

          context.beginPath();
          context.moveTo(x, y + 30 * scale);
          context.bezierCurveTo(
            x + 40 * scale,
            y - 40 * scale,
            x + 90 * scale,
            y + 30 * scale,
            x,
            y + 90 * scale,
          );
          context.bezierCurveTo(
            x - 90 * scale,
            y + 30 * scale,
            x - 40 * scale,
            y - 40 * scale,
            x,
            y + 30 * scale,
          );
          context.closePath();
          context.moveTo(x, y - 20 * scale);
          context.lineTo(x - 20 * scale, y - 50 * scale);
          context.lineTo(x + 20 * scale, y - 50 * scale);
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="red"
      />
    ),
    name: 'Spade',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '23',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const x = 50;
          const y = 50;
          const scale = 0.5;

          context.beginPath();
          context.moveTo(x, y + 30 * scale);
          context.bezierCurveTo(
            x + 40 * scale,
            y - 40 * scale,
            x + 90 * scale,
            y + 30 * scale,
            x,
            y + 90 * scale,
          );
          context.bezierCurveTo(
            x - 90 * scale,
            y + 30 * scale,
            x - 40 * scale,
            y - 40 * scale,
            x,
            y + 30 * scale,
          );
          context.closePath();
          context.moveTo(x - 20 * scale, y - 20 * scale);
          context.lineTo(x - 20 * scale, y - 50 * scale);
          context.lineTo(x + 20 * scale, y - 50 * scale);
          context.lineTo(x + 20 * scale, y - 20 * scale);
          context.closePath();
          context.arc(x, y - 75 * scale, 20 * scale, 0, Math.PI * 2);
          context.fillStrokeShape(shape);
        }}
        fill="blue"
      />
    ),
    name: 'Club',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '24',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          context.beginPath();
          context.moveTo(50, 0); // Top point
          context.lineTo(75, 50); // Right point
          context.lineTo(50, 100); // Bottom point
          context.lineTo(25, 50); // Left point
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="purple"
      />
    ),
    name: 'Diamond2',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '25',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 100; // Desired width
          const height = 100; // Desired height
          context.beginPath();
          context.moveTo(width * 0.2, height * 0.1); // Top-left point
          context.lineTo(width * 0.1, height * 0.5); // Left point
          context.lineTo(width * 0.2, height * 0.9); // Bottom-left point
          context.lineTo(width * 0.8, height * 0.9); // Bottom-right point
          context.lineTo(width * 0.9, height * 0.5); // Right point
          context.lineTo(width * 0.8, height * 0.1); // Top-right point
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="#FF5733"
      />
    ),
    name: 'Arrow2',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '26',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const x = 50;
          const y = 50;
          const radius = 40;

          context.beginPath();
          context.arc(x, y, radius, 0, Math.PI * 2); // Outer circle (sun)
          context.closePath();

          context.moveTo(x + radius * 0.8, y); // Move to the top point of the sun
          context.lineTo(x + radius * 0.4, y - radius * 0.4); // Ray 1
          context.lineTo(x + radius * 0.6, y - radius * 0.6); // Ray 2
          context.lineTo(x + radius * 0.2, y - radius * 0.8); // Ray 3
          context.lineTo(x, y - radius); // Ray 4 (top ray)
          context.lineTo(x - radius * 0.2, y - radius * 0.8); // Ray 5
          context.lineTo(x - radius * 0.6, y - radius * 0.6); // Ray 6
          context.lineTo(x - radius * 0.4, y - radius * 0.4); // Ray 7
          context.lineTo(x - radius * 0.8, y); // Ray 8
          context.closePath();

          context.fillStrokeShape(shape);
        }}
        fill="yellow"
      />
    ),
    name: 'Sun',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '27',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const x = 50;
          const y = 50;
          const size = 50;

          context.beginPath();
          context.moveTo(x, y - size); // Top point

          for (let i = 1; i <= 6; i++) {
            const angle = (i * Math.PI) / 3;
            const xPoint = x + size * Math.sin(angle);
            const yPoint = y - size * Math.cos(angle);
            context.lineTo(xPoint, yPoint);
          }

          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="blue"
      />
    ),
    name: 'Star of David',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '28',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80; // Desired width
          const height = 40; // Desired height
          context.beginPath();
          context.arc(width * 0.3, height * 0.3, width * 0.2, 0, 2 * Math.PI); // Left circle
          context.arc(width * 0.6, height * 0.4, width * 0.3, 0, 2 * Math.PI); // Middle circle
          context.arc(width * 0.9, height * 0.3, width * 0.2, 0, 2 * Math.PI); // Right circle
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="#D3D3D3"
      />
    ),
    name: 'Cloud2',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '29',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const x = 50;
          const y = 50;
          const width = 60;
          const height = 60;

          context.beginPath();
          context.moveTo(x - width / 2, y + height / 2); // Bottom left corner
          context.lineTo(x + width / 2, y + height / 2); // Bottom right corner
          context.lineTo(x + width / 2, y - height / 2); // Top right corner
          context.lineTo(x, y - height); // Roof peak
          context.lineTo(x - width / 2, y - height / 2); // Top left corner
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="tan"
      />
    ),
    name: 'House',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '30',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const x = 50;
          const y = 50;
          const width = 80;
          const height = 30;

          context.beginPath();
          context.moveTo(x - width / 2, y + height / 2); // Bottom left corner
          context.lineTo(x + width / 2, y + height / 2); // Bottom right corner
          context.lineTo(x + width / 4, y - height / 2); // Top right corner
          context.lineTo(x - width / 4, y - height / 2); // Top left corner
          context.closePath();
          context.moveTo(x, y - height / 2); // Mast
          context.lineTo(x, y - height); // Top of the mast
          context.fillStrokeShape(shape);
        }}
        fill="saddlebrown"
      />
    ),
    name: 'Boat',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '31',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const x = 50;
          const y = 20;
          const stickHeight = 40;
          const canopyRadius = 40;

          context.beginPath();
          context.moveTo(x, y); // Top point
          context.lineTo(x, y + stickHeight); // Stick
          context.arc(
            x,
            y + stickHeight + canopyRadius,
            canopyRadius,
            Math.PI,
            0,
          ); // Canopy
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="red"
      />
    ),
    name: 'Umbrella',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '32',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const x = 50;
          const y = 50;
          const radius = 40;

          context.beginPath();
          context.moveTo(x + radius, y); // First point

          for (let i = 1; i <= 5; i++) {
            const angle = (i * 2 * Math.PI) / 5;
            const xPoint = x + radius * Math.cos(angle);
            const yPoint = y + radius * Math.sin(angle);
            context.lineTo(xPoint, yPoint);

            const innerAngle = angle + Math.PI / 5;
            const innerX = x + (radius / 2) * Math.cos(innerAngle);
            const innerY = y + (radius / 2) * Math.sin(innerAngle);
            context.lineTo(innerX, innerY);
          }

          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="gold"
      />
    ),
    name: 'Star2',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '33',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const x = 50;
          const y = 50;
          const outerRadius = 40;
          const innerRadius = 30;
          const teeth = 12;
          //   const toothWidth = 10;
          const angleStep = (2 * Math.PI) / teeth;

          context.beginPath();

          for (let i = 0; i < teeth; i++) {
            const angle = i * angleStep;
            const xOuter = x + outerRadius * Math.cos(angle);
            const yOuter = y + outerRadius * Math.sin(angle);
            const xInner = x + innerRadius * Math.cos(angle + angleStep / 2);
            const yInner = y + innerRadius * Math.sin(angle + angleStep / 2);

            if (i === 0) {
              context.moveTo(xOuter, yOuter);
            } else {
              context.lineTo(xOuter, yOuter);
            }

            context.lineTo(xInner, yInner);
          }

          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="purple"
      />
    ),
    name: 'Gear',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '34',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const x = 50;
          const y = 50;
          const petalRadius = 30;
          const centerRadius = 15;
          const petals = 6;
          const angleStep = (2 * Math.PI) / petals;

          context.beginPath();

          for (let i = 0; i < petals; i++) {
            const angle = i * angleStep;
            const xPetal = x + petalRadius * Math.cos(angle);
            const yPetal = y + petalRadius * Math.sin(angle);
            const xCenter = x + centerRadius * Math.cos(angle + angleStep / 2);
            const yCenter = y + centerRadius * Math.sin(angle + angleStep / 2);

            context.arc(xPetal, yPetal, petalRadius, angle, angle + angleStep);
            context.lineTo(xCenter, yCenter);
          }

          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="pink"
      />
    ),
    name: 'Flower',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '35',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const x = 50;
          const y = 50;
          const peakHeight = 30;
          const baseWidth = 80;

          context.beginPath();
          context.moveTo(x - baseWidth / 2, y + peakHeight); // Left base corner
          context.lineTo(x + baseWidth / 2, y + peakHeight); // Right base corner
          context.lineTo(x, y - peakHeight); // Mountain peak
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkgreen"
      />
    ),
    name: 'Mountains',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '36',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const x = 50;
          const y = 50;
          const size = 60;

          context.beginPath();
          context.rect(x - size / 2, y - size / 2, size, size);
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="gray"
      />
    ),
    name: 'Square',
    category: 'basicShapes',
    height: 100,
    width: 100,
  },
  {
    id: '37',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 40;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.rect(x - width / 2, y - height / 2, width, height);
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="gray"
      />
    ),
    name: 'Rectangle2',
    category: 'basicShapes',
    height: 100,
    width: 100,
  },
  {
    id: '38',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 60;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x - width / 2, y + height / 2); // Bottom left corner
          context.lineTo(x + width / 2, y + height / 2); // Bottom right corner
          context.lineTo(x - width / 2, y - height / 2); // Top left corner
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="coral"
      />
    ),
    name: 'Triangle2',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '39',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const radius = 50;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.arc(x, y, radius, 0, Math.PI * 2);
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="gray"
      />
    ),
    name: 'Circle2',
    category: 'basicShapes',
    height: 100,
    width: 100,
  },
  {
    id: '40',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const radius1 = 30;
          const radius2 = 50;
          const radius3 = 70;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.arc(x, y, radius1, 0, Math.PI * 2);
          context.arc(x, y, radius2, 0, Math.PI * 2);
          context.arc(x, y, radius3, 0, Math.PI * 2);
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="gray"
      />
    ),
    name: 'Concentric Circles',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '41',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const radius = 40;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.arc(x, y, radius, 0, Math.PI * 2);
          context.moveTo(x + radius, y);
          context.lineTo(x, y - radius);
          context.lineTo(x - radius, y);
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkorange"
      />
    ),
    name: 'Triangle in Circle',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '42',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const radius = 40;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.arc(x, y, radius, 0, Math.PI * 2);
          context.moveTo(x - radius, y);
          context.lineTo(x + radius, y);
          context.moveTo(x, y - radius);
          context.lineTo(x, y + radius);
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkblue"
      />
    ),
    name: 'Cross in Circle',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '43',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const radius = 40;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.arc(x, y, radius, 0, Math.PI * 2);
          context.moveTo(x, y - radius);
          context.lineTo(x + radius, y);
          context.lineTo(x, y + radius);
          context.lineTo(x - radius, y);
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="indigo"
      />
    ),
    name: 'Diamond in Circle',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '44',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const radius = 50;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.arc(x, y, radius, 0, Math.PI, true);
          context.lineTo(x - radius, y);
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="violet"
      />
    ),
    name: 'Semi-Circle',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '45',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const radius = 50;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.arc(x, y, radius, 0, Math.PI / 2, true);
          context.lineTo(x, y);
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="salmon"
      />
    ),
    name: 'Quarter-Circle',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '46',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 60;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y + height / 2); // Bottom point
          context.lineTo(x + width / 2, y - height / 2); // Top-right point
          context.lineTo(x - width / 2, y - height / 2); // Top-left point
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkslategray"
      />
    ),
    name: 'Inverted Triangle',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '47',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const x = 50;
          const y = 50;
          const width = 60;
          const height = 60;

          context.beginPath();
          context.moveTo(x, y - height / 2); // Top point
          context.lineTo(x + width / 2, y); // Right point
          context.lineTo(x, y + height / 2); // Bottom point
          context.lineTo(x - width / 2, y); // Left point
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="olive"
      />
    ),
    name: 'Diamond',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '48',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 60;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.rect(x - width / 2, y - height / 2, width, height);
          context.moveTo(x - width / 2, y - height / 2); // Top-left corner
          context.lineTo(x + width / 2, y + height / 2); // Bottom-right corner
          context.moveTo(x + width / 2, y - height / 2); // Top-right corner
          context.lineTo(x - width / 2, y + height / 2); // Bottom-left corner
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="chocolate"
      />
    ),
    name: 'Rectangle with Diagonal Cross',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '49',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 60;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2); // Top point
          context.lineTo(x + width / 2, y + height / 2); // Bottom right corner
          context.lineTo(x - width / 2, y + height / 2); // Bottom left corner
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkslateblue"
      />
    ),
    name: 'Pyramid',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '50',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 60;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2); // Top point
          context.lineTo(x + width / 2, y); // Right point
          context.lineTo(x, y + height / 2); // Bottom point
          context.lineTo(x - width / 2, y); // Left point
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkmagenta"
      />
    ),
    name: 'Arrowhead',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '51',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 60;
          const cornerRadius = 10;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x + width / 2 - cornerRadius, y - height / 2); // Top-right of the rectangle
          context.arc(
            x + width / 2 - cornerRadius,
            y - height / 2 + cornerRadius,
            cornerRadius,
            0,
            Math.PI / 2,
          ); // Top-right corner
          context.lineTo(x - width / 2 + cornerRadius, y + height / 2); // Bottom-left of the rectangle
          context.arc(
            x - width / 2 + cornerRadius,
            y + height / 2 - cornerRadius,
            cornerRadius,
            Math.PI / 2,
            Math.PI,
          ); // Bottom-left corner
          context.lineTo(x - width / 2, y - height / 2 + cornerRadius); // Top-left of the rectangle
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="blueviolet"
      />
    ),
    name: 'Rounded Rectangle',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '52',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const sideLength = 80;
          const height = (Math.sqrt(3) / 2) * sideLength;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2); // Top point
          context.lineTo(x + sideLength / 2, y + height / 2); // Bottom right corner
          context.lineTo(x - sideLength / 2, y + height / 2); // Bottom left corner
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkorchid"
      />
    ),
    name: 'Equilateral Triangle',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '53',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 60;
          const cornerRadius = 20;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2); // Top point
          context.arcTo(
            x + width / 2,
            y - height / 2,
            x + width / 2,
            y + height / 2,
            cornerRadius,
          ); // Top-right corner
          context.arcTo(
            x + width / 2,
            y + height / 2,
            x - width / 2,
            y + height / 2,
            cornerRadius,
          ); // Bottom-right corner
          context.arcTo(
            x - width / 2,
            y + height / 2,
            x,
            y - height / 2,
            cornerRadius,
          ); // Bottom-left corner
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="mediumpurple"
      />
    ),
    name: 'Curved Triangle',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '54',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 60;
          const cornerRadius = 20;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x + width / 2 - cornerRadius, y - height / 2); // Top-right of the rectangle
          context.arcTo(
            x + width / 2,
            y - height / 2,
            x + width / 2,
            y + height / 2,
            cornerRadius,
          ); // Top-right corner
          context.arcTo(
            x + width / 2,
            y + height / 2,
            x - width / 2,
            y + height / 2,
            cornerRadius,
          ); // Bottom-right corner
          context.arcTo(
            x - width / 2,
            y + height / 2,
            x - width / 2,
            y - height / 2,
            cornerRadius,
          ); // Bottom-left corner
          context.arcTo(
            x - width / 2,
            y - height / 2,
            x + width / 2 - cornerRadius,
            y - height / 2,
            cornerRadius,
          ); // Top-left corner
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="gray"
      />
    ),
    name: 'Curved Rectangle',
    category: 'basicShapes',
    height: 100,
    width: 100,
  },
  {
    id: '55',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const size = 80;
          const cornerRadius = 20;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x + size / 2 - cornerRadius, y - size / 2); // Top-right of the square
          context.arcTo(
            x + size / 2,
            y - size / 2,
            x + size / 2,
            y + size / 2,
            cornerRadius,
          ); // Top-right corner
          context.arcTo(
            x + size / 2,
            y + size / 2,
            x - size / 2,
            y + size / 2,
            cornerRadius,
          ); // Bottom-right corner
          context.arcTo(
            x - size / 2,
            y + size / 2,
            x - size / 2,
            y - size / 2,
            cornerRadius,
          ); // Bottom-left corner
          context.arcTo(
            x - size / 2,
            y - size / 2,
            x + size / 2 - cornerRadius,
            y - size / 2,
            cornerRadius,
          ); // Top-left corner
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="gray"
      />
    ),
    name: 'Curved Square',
    category: 'basicShapes',
    height: 100,
    width: 100,
  },
  {
    id: '56',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const size = 80;
          const cornerRadius = 20;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - size / 2); // Top point
          context.arcTo(x + size / 2, y, x, y + size / 2, cornerRadius); // Bottom-right corner
          context.arcTo(x, y + size / 2, x - size / 2, y, cornerRadius); // Bottom-left corner
          context.arcTo(x - size / 2, y, x, y - size / 2, cornerRadius); // Top-left corner
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="purple"
      />
    ),
    name: 'Curved Diamond',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '57',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 60;
          const cornerRadius = 20;
          const borderWidth = 4;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2 + borderWidth); // Top point
          context.arcTo(
            x + width / 2 - borderWidth,
            y - height / 2 + borderWidth,
            x + width / 2 - borderWidth,
            y + height / 2 - borderWidth,
            cornerRadius,
          ); // Top-right corner
          context.arcTo(
            x + width / 2 - borderWidth,
            y + height / 2 - borderWidth,
            x - width / 2 + borderWidth,
            y + height / 2 - borderWidth,
            cornerRadius,
          ); // Bottom-right corner
          context.arcTo(
            x - width / 2 + borderWidth,
            y + height / 2 - borderWidth,
            x,
            y - height / 2 + borderWidth,
            cornerRadius,
          ); // Bottom-left corner
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="cadetblue"
      />
    ),
    name: 'Curved Triangle with Border',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '58',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 60;
          const cornerRadius = 20;
          const shadowOffsetX = 5;
          const shadowOffsetY = 5;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.shadowOffsetX = shadowOffsetX;
          context.shadowOffsetY = shadowOffsetY;
          context.moveTo(x + width / 2 - cornerRadius, y - height / 2); // Top-right of the rectangle
          context.arcTo(
            x + width / 2,
            y - height / 2,
            x + width / 2,
            y + height / 2,
            cornerRadius,
          ); // Top-right corner
          context.arcTo(
            x + width / 2,
            y + height / 2,
            x - width / 2,
            y + height / 2,
            cornerRadius,
          ); // Bottom-right corner
          context.arcTo(
            x - width / 2,
            y + height / 2,
            x - width / 2,
            y - height / 2,
            cornerRadius,
          ); // Bottom-left corner
          context.arcTo(
            x - width / 2,
            y - height / 2,
            x + width / 2 - cornerRadius,
            y - height / 2,
            cornerRadius,
          ); // Top-left corner
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="gray"
      />
    ),
    name: 'Curved Rectangle with Shadow',
    category: 'basicShapes',
    height: 100,
    width: 100,
  },
  {
    id: '59',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const size = 80;
          const cornerRadius = 20;
          const borderWidth = 4;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(
            x + size / 2 - cornerRadius,
            y - size / 2 + borderWidth,
          ); // Top-right of the square
          context.arcTo(
            x + size / 2 - borderWidth,
            y - size / 2 + borderWidth,
            x + size / 2 - borderWidth,
            y + size / 2 - borderWidth,
            cornerRadius,
          ); // Top-right corner
          context.arcTo(
            x + size / 2 - borderWidth,
            y + size / 2 - borderWidth,
            x - size / 2 + borderWidth,
            y + size / 2 - borderWidth,
            cornerRadius,
          ); // Bottom-right corner
          context.arcTo(
            x - size / 2 + borderWidth,
            y + size / 2 - borderWidth,
            x - size / 2 + borderWidth,
            y - size / 2 + borderWidth,
            cornerRadius,
          ); // Bottom-left corner
          context.arcTo(
            x - size / 2 + borderWidth,
            y - size / 2 + borderWidth,
            x + size / 2 - cornerRadius,
            y - size / 2 + borderWidth,
            cornerRadius,
          ); // Top-left corner
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="gray"
      />
    ),
    name: 'Curved Square with Border',
    category: 'basicShapes',
    height: 100,
    width: 100,
  },
  {
    id: '60',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const size = 80;
          const cornerRadius = 20;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - size / 2); // Top point
          context.arcTo(
            x + size / 2,
            y - size / 4,
            x + size / 4,
            y + size / 2,
            cornerRadius,
          ); // Top-right corner
          context.arcTo(
            x + size / 4,
            y + size / 2,
            x - size / 4,
            y + size / 2,
            cornerRadius,
          ); // Bottom-right corner
          context.arcTo(
            x - size / 4,
            y + size / 2,
            x - size / 2,
            y - size / 4,
            cornerRadius,
          ); // Bottom-left corner
          context.arcTo(
            x - size / 2,
            y - size / 4,
            x,
            y - size / 2,
            cornerRadius,
          ); // Top-left corner
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkslategrey"
      />
    ),
    name: 'Curved Pentagon',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '61',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 100;
          const curveAmount = 20;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2);
          context.quadraticCurveTo(
            x + curveAmount,
            y - height / 2,
            x + width / 2,
            y,
          );
          context.quadraticCurveTo(
            x + curveAmount,
            y + height / 2,
            x,
            y + height / 2,
          );
          context.quadraticCurveTo(
            x - curveAmount,
            y + height / 2,
            x - width / 2,
            y,
          );
          context.quadraticCurveTo(
            x - curveAmount,
            y - height / 2,
            x,
            y - height / 2,
          );
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="deeppink"
      />
    ),
    name: 'Flower Petal 1',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '62',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 100;
          const curveAmount = 20;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2);
          context.quadraticCurveTo(
            x + width / 2,
            y - curveAmount,
            x + width / 2,
            y,
          );
          context.quadraticCurveTo(
            x + width / 2,
            y + curveAmount,
            x,
            y + height / 2,
          );
          context.quadraticCurveTo(
            x - width / 2,
            y + curveAmount,
            x - width / 2,
            y,
          );
          context.quadraticCurveTo(
            x - width / 2,
            y - curveAmount,
            x,
            y - height / 2,
          );
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="mediumorchid"
      />
    ),
    name: 'Flower Petal 2',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '63',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 100;
          const curveAmount = 30;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2);
          context.quadraticCurveTo(
            x + width / 2,
            y - curveAmount,
            x + width / 2,
            y,
          );
          context.quadraticCurveTo(
            x + width / 2,
            y + curveAmount,
            x,
            y + height / 2,
          );
          context.quadraticCurveTo(
            x - width / 2,
            y + curveAmount,
            x - width / 2,
            y,
          );
          context.quadraticCurveTo(
            x - width / 2,
            y - curveAmount,
            x,
            y - height / 2,
          );
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkorange"
      />
    ),
    name: 'Flower Petal 3',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '64',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 100;
          const curveAmount = 30;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2);
          context.quadraticCurveTo(
            x + curveAmount,
            y - curveAmount,
            x + width / 2,
            y,
          );
          context.quadraticCurveTo(
            x + curveAmount,
            y + curveAmount,
            x,
            y + height / 2,
          );
          context.quadraticCurveTo(
            x - curveAmount,
            y + curveAmount,
            x - width / 2,
            y,
          );
          context.quadraticCurveTo(
            x - curveAmount,
            y - curveAmount,
            x,
            y - height / 2,
          );
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="forestgreen"
      />
    ),
    name: 'Flower Petal 4',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '65',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 100;
          const curveAmount = 40;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2);
          context.quadraticCurveTo(
            x + width / 2,
            y - curveAmount,
            x + width / 2,
            y,
          );
          context.quadraticCurveTo(
            x + width / 2,
            y + curveAmount,
            x,
            y + height / 2,
          );
          context.quadraticCurveTo(
            x - width / 2,
            y + curveAmount,
            x - width / 2,
            y,
          );
          context.quadraticCurveTo(
            x - width / 2,
            y - curveAmount,
            x,
            y - height / 2,
          );
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkturquoise"
      />
    ),
    name: 'Flower Petal 5',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '66',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 100;
          const curveAmount = 40;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2);
          context.quadraticCurveTo(
            x + curveAmount,
            y - curveAmount,
            x + width / 2,
            y,
          );
          context.quadraticCurveTo(
            x + curveAmount,
            y + curveAmount,
            x,
            y + height / 2,
          );
          context.quadraticCurveTo(
            x - curveAmount,
            y + curveAmount,
            x - width / 2,
            y,
          );
          context.quadraticCurveTo(
            x - curveAmount,
            y - curveAmount,
            x,
            y - height / 2,
          );
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkslateblue"
      />
    ),
    name: 'Flower Petal 6',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '67',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 100;
          const curveAmount = 50;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2);
          context.quadraticCurveTo(
            x + width / 2,
            y - curveAmount,
            x + width / 2,
            y,
          );
          context.quadraticCurveTo(
            x + width / 2,
            y + curveAmount,
            x,
            y + height / 2,
          );
          context.quadraticCurveTo(
            x - width / 2,
            y + curveAmount,
            x - width / 2,
            y,
          );
          context.quadraticCurveTo(
            x - width / 2,
            y - curveAmount,
            x,
            y - height / 2,
          );
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkmagenta"
      />
    ),
    name: 'Flower Petal 7',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '68',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 100;
          const curveAmount = 50;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2);
          context.quadraticCurveTo(
            x + curveAmount,
            y - curveAmount,
            x + width / 2,
            y,
          );
          context.quadraticCurveTo(
            x + curveAmount,
            y + curveAmount,
            x,
            y + height / 2,
          );
          context.quadraticCurveTo(
            x - curveAmount,
            y + curveAmount,
            x - width / 2,
            y,
          );
          context.quadraticCurveTo(
            x - curveAmount,
            y - curveAmount,
            x,
            y - height / 2,
          );
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkcyan"
      />
    ),
    name: 'Flower Petal 8',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '69',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 100;
          const curveAmount = 60;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2);
          context.quadraticCurveTo(
            x + width / 2,
            y - curveAmount,
            x + width / 2,
            y,
          );
          context.quadraticCurveTo(
            x + width / 2,
            y + curveAmount,
            x,
            y + height / 2,
          );
          context.quadraticCurveTo(
            x - width / 2,
            y + curveAmount,
            x - width / 2,
            y,
          );
          context.quadraticCurveTo(
            x - width / 2,
            y - curveAmount,
            x,
            y - height / 2,
          );
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkviolet"
      />
    ),
    name: 'Flower Petal 9',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '70',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 80;
          const height = 100;
          const curveAmount = 60;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2);
          context.quadraticCurveTo(
            x + width / 2,
            y - curveAmount,
            x + width / 2,
            y,
          );
          context.quadraticCurveTo(
            x + width / 2,
            y + curveAmount,
            x,
            y + height / 2,
          );
          context.quadraticCurveTo(
            x - width / 2,
            y + curveAmount,
            x - width / 2,
            y,
          );
          context.quadraticCurveTo(
            x - width / 2,
            y - curveAmount,
            x,
            y - height / 2,
          );
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkolivegreen"
      />
    ),
    name: 'Flower Petal 10',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '71',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const sideLength = 100;
          const height = (Math.sqrt(3) * sideLength) / 2;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2);
          context.lineTo(x + sideLength / 2, y + height / 2);
          context.lineTo(x - sideLength / 2, y + height / 2);
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="coral"
      />
    ),
    name: 'Concave Triangle',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '72',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const sideLength = 100;
          const height = (Math.sqrt(3) * sideLength) / 2;
          const curveAmount = 20;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x - sideLength / 2, y + height / 2);
          context.quadraticCurveTo(
            x,
            y - height / 2 - curveAmount,
            x + sideLength / 2,
            y + height / 2,
          );
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkslategray"
      />
    ),
    name: 'Curved Triangle',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '73',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 100;
          const height = 100;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2);
          context.lineTo(x + width / 2, y + height / 2);
          context.lineTo(x - width / 2, y + height / 2);
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkolivegreen"
      />
    ),
    name: 'Pyramid',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '74',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 100;
          const height = 100;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2);
          context.lineTo(x + width / 2, y);
          context.lineTo(x, y + height / 2);
          context.lineTo(x - width / 2, y);
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="indianred"
      />
    ),
    name: 'Diamond',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '75',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const radius = 50;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.arc(x, y, radius, 0, 2 * Math.PI);
          context.arc(x - radius / 2, y, radius, 0, 2 * Math.PI, true);
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="chocolate"
      />
    ),
    name: 'Crescent Moon',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '76',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const sideLength = 100;
          // const cornerRadius = 20;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(
            x + sideLength * Math.cos(0),
            y + sideLength * Math.sin(0),
          );
          for (let i = 1; i <= 5; i++) {
            const angle = (i * 2 * Math.PI) / 5;
            context.lineTo(
              x + sideLength * Math.cos(angle),
              y + sideLength * Math.sin(angle),
            );
          }
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkgoldenrod"
      />
    ),
    name: 'Pentagon with Rounded Corners',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '77',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const sideLength = 100;
          const height = (Math.sqrt(3) * sideLength) / 2;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2);
          context.lineTo(x + sideLength / 2, y + height / 2);
          context.lineTo(x - sideLength / 2, y + height / 2);
          context.closePath();

          context.moveTo(x, y + height / 4);
          context.lineTo(x + sideLength / 4, y + (3 * height) / 4);
          context.lineTo(x - sideLength / 4, y + (3 * height) / 4);
          context.closePath();

          context.fillStrokeShape(shape);
        }}
        fill="darkcyan"
      />
    ),
    name: 'Double Triangle',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '78',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const sideLength = 50; // Decreased side length
          const cornerRadius = 10; // Decreased corner radius
          const x = 50; // Decreased x coordinate
          const y = 50; // Decreased y coordinate

          context.beginPath();
          context.moveTo(
            x + sideLength * Math.cos(0),
            y + sideLength * Math.sin(0),
          );
          for (let i = 1; i <= 5; i++) {
            const angle = (i * 2 * Math.PI) / 5;
            const nextX = x + sideLength * Math.cos(angle);
            const nextY = y + sideLength * Math.sin(angle);
            const controlX = (x + nextX) / 2 + cornerRadius * Math.sin(angle);
            const controlY = (y + nextY) / 2 - cornerRadius * Math.cos(angle);
            context.quadraticCurveTo(controlX, controlY, nextX, nextY);
          }
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkorchid"
      />
    ),
    name: 'Smaller Pentagon with Curved Sides',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '79',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const sideLength = 100;
          const height = (Math.sqrt(3) * sideLength) / 2;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2);
          context.lineTo(x + sideLength / 2, y + height / 2);
          context.lineTo(x - sideLength / 2, y + height / 2);
          context.closePath();

          context.moveTo(x, y - height / 2);
          context.lineTo(x, y + height / 2);

          context.moveTo(x + sideLength / 2, y + height / 2);
          context.lineTo(x + sideLength / 2, y - height / 2);

          context.moveTo(x - sideLength / 2, y + height / 2);
          context.lineTo(x - sideLength / 2, y - height / 2);

          context.fillStrokeShape(shape);
        }}
        fill="darkolivegreen"
      />
    ),
    name: 'Triangular Prism',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '80',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const centerX = 50;
          const centerY = 50;
          const numLoops = 5;
          const numSegments = 300;
          const innerRadius = 10;
          const outerRadius = 40;

          context.beginPath();
          for (let i = 0; i < numSegments; i++) {
            const angle = (numLoops * Math.PI * i) / numSegments;
            const radius =
              innerRadius + (outerRadius - innerRadius) * (i / numSegments);
            const x = centerX + radius * Math.cos(angle);
            const y = centerY + radius * Math.sin(angle);
            if (i === 0) {
              context.moveTo(x, y);
            } else {
              context.lineTo(x, y);
            }
          }
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkorange"
      />
    ),
    name: 'Spiral',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '81',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 100;
          const height = 100;
          const curveAmount = 20;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x - width / 2, y);
          context.quadraticCurveTo(x, y - curveAmount, x + width / 2, y);
          context.quadraticCurveTo(x, y + curveAmount, x - width / 2, y);
          context.moveTo(x, y - height / 2);
          context.quadraticCurveTo(x - curveAmount, y, x, y + height / 2);
          context.quadraticCurveTo(x + curveAmount, y, x, y - height / 2);
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkseagreen"
      />
    ),
    name: 'Curved Cross',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '82',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const width = 100;
          const height = 100;
          const radius = 20;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x, y - height / 2);
          context.lineTo(x + width / 2, y - height / 2);
          context.arcTo(x + width / 2, y, x, y, radius);
          context.arcTo(
            x - width / 2,
            y,
            x - width / 2,
            y - height / 2,
            radius,
          );
          context.lineTo(x, y - height / 2);
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkslateblue"
      />
    ),
    name: 'Jigsaw Puzzle Piece',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '83',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const radius = 40;
          const numPoints = 10;
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x + radius, y);
          for (let i = 1; i < numPoints * 2; i++) {
            const angle = (i * Math.PI) / numPoints;
            const radiusOffset = i % 2 === 0 ? radius : radius / 2;
            const pointX = x + radiusOffset * Math.cos(angle);
            const pointY = y + radiusOffset * Math.sin(angle);
            context.lineTo(pointX, pointY);
          }
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkslategray"
      />
    ),
    name: 'Abstract Blob',
    category: '',
    height: 100,
    width: 100,
  },
  {
    id: '84',
    component: (
      <Shape
        sceneFunc={(context, shape) => {
          const x = 50;
          const y = 50;

          context.beginPath();
          context.moveTo(x + 10, y - 15);
          context.lineTo(x + 25, y - 35);
          context.lineTo(x + 40, y - 15);
          context.lineTo(x + 30, y + 10);
          context.lineTo(x + 10, y + 10);
          context.closePath();
          context.fillStrokeShape(shape);
        }}
        fill="darkmagenta"
      />
    ),
    name: 'Irregular Pentagon',
    category: '',
    height: 100,
    width: 100,
  },
];

export default availableShapes;

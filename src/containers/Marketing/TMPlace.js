/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable  react/jsx-no-bind */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
/* eslint-disable no-unused-expressions */
/* eslint-disable react/destructuring-assignment */

import React, { useEffect, useState } from 'react';
import {
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
} from 'recharts';
import useWindowSize from '@chill/shared/common/library/hooks/useWindowSize';
import './TMPlace.css';

import {
  Col,
  Row,
  Card,
  Avatar,
  Dropdown,
  Space,
  Divider,
  Switch,
  Menu,
  Slider,
  Button,
  Progress,
  Table,
  Form,
  Input,
  Spin,
  Select,
} from 'antd';
import { Link, useHistory } from 'react-router-dom';
import {
  StarFilled,
  EllipsisOutlined,
  MenuOutlined,
  AppstoreFilled,
  SettingFilled,
  DownOutlined,
  CopyOutlined,
  RiseOutlined,
  FallOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  RightOutlined,
  LoadingOutlined,
  FilterOutlined,
} from '@ant-design/icons';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { useSelector } from 'react-redux';
import ContentLoader from 'react-content-loader';
import babyS from '@chill/assets/images/babyStrollerSee.png';
import expandIcon from '@chill/assets/images/up-arrow.png';
import downArrow from '@chill/assets/images/down-arrow.png';
import TP1 from '@chill/assets/images/TP1.jpg';
import TP2 from '@chill/assets/images/TP2.jpg';
import TP3 from '@chill/assets/images/TP3.png';
import TP4 from '@chill/assets/images/TP4.jpg';
import TP5 from '@chill/assets/images/TP5.jpg';
import getApiData from '@chill/lib/helpers/apiHelper';

const cardObjD = {
  id: 1,
  title: 'Keeping baby cool',
  price: '$180',
  price1: '360',
  price2: '30',
  price3: '$60',
  description: 'Estimated Sold QTY :',
  description1: 'People in this audience:',
  description3: 'Value of the campaign:',
  imageSrc: babyS,
  // 'https://img.freepik.com/free-photo/cute-baby-boy-scooter-autumnal-park_1303-29193.jpg?w=900&t=st=1681296208~exp=1681296808~hmac=db2387c4656a1f6311d599ef3bfe7bcc87e651a820791a3584fcace0b25a9af0',
  icon1: <StarFilled />,
  icon2: <EllipsisOutlined />,
};
// Array of card data
const cardData = [
  {
    id: 1,
    title: 'BabyShusher',
    price: '$180',
    price1: '360',
    price2: '30',
    price3: '$60',
    description: 'Estimated Sold QTY :',
    description1: 'People in this audience:',
    description3: 'Value of the campaign:',
    imageSrc: TP1,
    // 'https://img.freepik.com/free-photo/cute-baby-boy-scooter-autumnal-park_1303-29193.jpg?w=900&t=st=1681296208~exp=1681296808~hmac=db2387c4656a1f6311d599ef3bfe7bcc87e651a820791a3584fcace0b25a9af0',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
  {
    id: 2,
    title: 'Rockit®',
    price: '$270',
    price1: '360',
    price2: '30',
    price3: '$60',
    description: 'Estimated Sold QTY :',
    description1: 'People in this audience:',
    description3: 'Value of the campaign:',
    imageSrc: TP2,
    // 'https://img.freepik.com/free-photo/small-girl-skate_1303-9646.jpg?size=626&ext=jpg&ga=GA1.1.1186299328.1674125467&semt=ais',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
  {
    id: 3,
    title: 'Ewan the sheep',
    price: '$90',
    price1: '360',
    price2: '30',
    price3: '$60',
    description: 'Estimated Sold QTY :',
    description1: 'People in this audience:',
    description3: 'Value of the campaign:',
    imageSrc: TP3,
    // 'https://img.freepik.com/free-photo/funny-kid-rain-boots-playing-rain-park_1157-37683.jpg?size=626&ext=jpg&ga=GA1.2.1186299328.1674125467&semt=ais',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
  {
    id: 4,
    title: 'GroEgg',
    price: '$261',
    price1: '360',
    price2: '30',
    price3: '$60',
    description: 'Estimated Sold QTY :',
    description1: 'People in this audience:',
    description3: 'Value of the campaign:',
    imageSrc: TP4,
    // 'https://img.freepik.com/premium-photo/happy-child-with-robot-funny-kid-playing-home-success-creative-innovation-technology-concept_411285-9771.jpg?size=626&ext=jpg&ga=GA1.1.1186299328.1674125467&semt=ais',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
  {
    id: 5,
    title: 'Build®Fort',
    price: '$180',
    price1: '360',
    price2: '30',
    price3: '$60',
    description: 'Estimated Sold QTY :',
    description1: 'People in this audience:',
    description3: 'Value of the campaign:',
    imageSrc: TP5,
    // 'https://img.freepik.com/free-photo/daughter-helping-packing-boxes_23-2148569347.jpg?size=626&ext=jpg&ga=GA1.1.1186299328.1674125467&semt=ais',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
  {
    id: 6,
    title: 'Riding a bike',
    price: '$180',
    price1: '360',
    price2: '30',
    price3: '$60',
    description: 'Estimated Sold QTY :',
    description1: 'People in this audience:',
    description3: 'Value of the campaign:',
    imageSrc:
      'https://img.freepik.com/premium-photo/happy-little-girl-protective-hat-riding-her-bike-outdoors-sunny-day-near-forest_146671-41396.jpg?size=626&ext=jpg&ga=GA1.1.1186299328.1674125467&semt=ais',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
  {
    id: 7,
    title: 'Trike®',
    price: '$180',
    price1: '360',
    price2: '30',
    price3: '$60',
    description: 'Estimated Sold QTY :',
    description1: 'People in this audience:',
    description3: 'Value of the campaign:',
    imageSrc:
      'https://img.freepik.com/free-photo/young-boy-having-fun-playground_23-2149490452.jpg?size=626&ext=jpg&ga=GA1.1.1186299328.1674125467&semt=robertav1_2_sidr',
    icon1: <StarFilled />,
    icon2: <EllipsisOutlined />,
  },
];

// Responsive helper functions
const getResponsiveCardWidth = (windowWidth) => {
  if (windowWidth < 768) return '100%'; // Mobile: full width
  if (windowWidth < 1024) return '48%'; // Tablet: 2 columns
  return '23%'; // Desktop: 4 columns
};

const getResponsiveCardHeight = (windowWidth) => {
  if (windowWidth < 768) return 'auto'; // Mobile: auto height
  if (windowWidth < 1024) return '280px'; // Tablet: fixed height
  return '320px'; // Desktop: fixed height
};

const getResponsiveImageHeight = (windowWidth) => {
  if (windowWidth < 768) return '200px'; // Mobile: larger image
  if (windowWidth < 1024) return '140px'; // Tablet: medium image
  return '117px'; // Desktop: original size
};

const getResponsivePopupWidth = (windowWidth) => {
  if (windowWidth < 768) return '90%'; // Mobile: almost full width
  if (windowWidth < 1024) return '70%'; // Tablet: 70% width
  return 'auto'; // Desktop: auto width
};

const getResponsivePopupPadding = (windowWidth) => {
  if (windowWidth < 768) return '20px'; // Mobile: smaller padding
  if (windowWidth < 1024) return '30px'; // Tablet: medium padding
  return '50px'; // Desktop: original padding
};

const getResponsiveFilterWidth = (windowWidth, showFilter) => {
  if (windowWidth < 768) return showFilter ? '100%' : '0%'; // Mobile: full width or hidden
  return showFilter ? '25%' : '0%'; // Tablet/Desktop: 25% or hidden
};

const getResponsiveMainWidth = (windowWidth, showFilter) => {
  if (windowWidth < 768) return showFilter ? '0%' : '100%'; // Mobile: hidden or full width
  return showFilter ? '75%' : '100%'; // Tablet/Desktop: 75% or full width
};

export default function TMPlace() {
  // Initialize window size hook for real-time responsive behavior
  const windowSize = useWindowSize({ throttleMs: 100 });

  // Array of menus for Dropdowm
  const [showPopup, setShowPopup] = useState(false);
  const [showPopupCircle, setShowPopupCircle] = useState(false);
  const [cardObj, setCardObj] = useState(cardObjD);
  const togglePopup = () => {
    setShowPopupCircle(!showPopupCircle);
  };
  const history = useHistory();
  const items = [
    {
      label: 'Value',
      key: '0',
      value: 'value',
    },
    {
      label: 'Audience size',
      key: '1',
      value: 'audienceSize',
    },
    {
      label: 'Conversion rate',
      key: '3',
      value: 'conversionRate',
    },
    {
      label: 'Quantity',
      key: '4',
      value: 'quantity',
    },
  ];
  // store the state of selected icon
  const [selectedIcon, setSelectedIcon] = useState('AppstoreFilled');
  const [form] = Form.useForm();
  // const [selectedDropdownItem, setSelectedDropdownItem] = useState(items[0]);

  // store the state of sliderRange
  const [sliderRange, setSliderRange] = useState([6, 9]);
  const onAfterChange1 = (value) => {
    console.log('onAfterChange: ', value);
    setSliderRange(value);
  };

  const onChange1 = (value) => {
    console.log('onChange: ', value);
    setSliderRange(value);
  };
  // store the state of selected options and keys
  const { SubMenu } = Menu;
  const [openKeys, setOpenKeys] = useState();
  const [selectedOptions, setSelectedOptions] = useState({});

  const [imageLoader, setImageLoader] = useState(false);
  const [title1Loader, setTitle1loader] = useState(false);
  const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;
  const handleGenerateCampaign = () => {
    setShowPopup(!showPopup);
    history.push('/dashboard/dummyAd');
  };
  const { Option } = Select;
  const [gender, setGender] = useState();
  const [language, setLanguage] = useState();
  const [languageOptions, setLanguageOptions] = useState([]);
  const [selectedGenders, setSelectedGenders] = useState([]);
  const [showFilter, setShowFilter] = useState(false);
  const [emailChecked, setEmailChecked] = useState(false);

  const fetchLanguages = async () => {
    try {
      const response = await getApiData('getLanguageList', {}, 'POST');
      console.log(response.data);
      if (response.success) {
        console.log(response.data);
        setLanguageOptions(response.data);
      }
    } catch (error) {
      console.error('Error fetching images:', error);
    }
  };

  const handleGenderChange = (selectedVlaues) => {
    setSelectedGenders(selectedVlaues);
  };
  const handleLanguageSelect = () => {};

  useEffect(() => {
    // Fetch images from the API
    fetchLanguages();
  }, []);
  useEffect(() => {
    if (showPopupCircle) {
      setTimeout(() => {
        setShowPopupCircle(!showPopupCircle);
        history.push('/dashboard/dummyAdCamp');
      }, 5000);
    }
  }, [showPopupCircle]);
  const onOpenChange = (keys) => {
    setOpenKeys(keys);
  };
  // function for selecting menu items
  const handleOptionSelect = (event, key) => {
    setSelectedOptions({ ...selectedOptions, [key]: event.target.checked });
  };

  const handleApplyClick = () => {
    localStorage.setItem('selectedOptions', JSON.stringify(selectedOptions));
  };
  // function for checkbox clicked
  const onChange = (checked) => {
    setEmailChecked(checked);
    console.log(`switch to ${checked}`);
  };
  const fromAI = localStorage.getItem('fromAI');

  // to store the state of carddata
  const [cards, setCards] = useState(cardData);
  // functions for handling drag and drop
  function handleDragEnd(result) {
    if (!result.destination) return;

    const { source, destination } = result;

    if (source.index === destination.index) return;

    const newCards = [...cards];
    const [draggedCard] = newCards.splice(source.index, 1);

    newCards.splice(destination.index, 0, draggedCard);

    setCards(newCards);
  }

  const onFinish = (values) => {
    console.log('Form data:', values);
    setShowPopup(false);
    togglePopup();
  };

  return (
    <div
      className="responsiveGrid"
      style={{
        display: 'flex',
        flexDirection: windowSize.width < 768 && showFilter ? 'column' : 'row',
      }}
    >
      <div
        style={{
          width: getResponsiveMainWidth(windowSize.width, showFilter),
          display: windowSize.width < 768 && showFilter ? 'none' : 'block',
        }}
      >
        <div style={{ background: '#fff' }} className="widgetSpacing">
          <div
            className="headerControls"
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              padding: windowSize.width < 768 ? '16px' : '20px',
              paddingTop: windowSize.width < 768 ? '20px' : '28px',
            }}
          >
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                width: '100%',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  gap: windowSize.width < 768 ? '16px' : '25px',
                }}
              >
                <AppstoreFilled
                  style={{
                    width: windowSize.width < 768 ? '28px' : '24px',
                    height: windowSize.width < 768 ? '28px' : '24px',
                    fontSize: windowSize.width < 768 ? '28px' : '25px',
                    color:
                      selectedIcon === 'AppstoreFilled' ? '#2A77F4' : '#A9ABBC',
                    cursor: 'pointer',
                    minWidth: '44px', // Touch-friendly minimum
                    minHeight: '44px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                  onClick={() => setSelectedIcon('AppstoreFilled')}
                />
                <MenuOutlined
                  style={{
                    width: windowSize.width < 768 ? '28px' : '24px',
                    height: windowSize.width < 768 ? '28px' : '24px',
                    fontSize: windowSize.width < 768 ? '28px' : '23px',
                    color:
                      selectedIcon === 'MenuOutlined' ? '#2A77F4' : '#A9ABBC',
                    cursor: 'pointer',
                    minWidth: '44px', // Touch-friendly minimum
                    minHeight: '44px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                  onClick={() => setSelectedIcon('MenuOutlined')}
                />
              </div>
              <div>
                <FilterOutlined
                  style={{
                    width: windowSize.width < 768 ? '28px' : '24px',
                    height: windowSize.width < 768 ? '28px' : '24px',
                    fontSize: windowSize.width < 768 ? '28px' : '25px',
                    cursor: 'pointer',
                    color: showFilter ? '#2A77F4' : '#A9ABBC',
                    minWidth: '44px', // Touch-friendly minimum
                    minHeight: '44px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                  onClick={() => setShowFilter((prevState) => !prevState)}
                />
              </div>
            </div>

            {/* <div>
              <p
                style={{
                  // marginLeft: '550px',
                  marginTop: '20px',
                  fontFamily: 'Rubik',
                  fontSize: '14px',
                  color: '#A9ABBC',
                  marginBottom: '-5px',
                }}
              >
                sort
              </p>
              <Dropdown
                menu={{
                  items,
                  // onClick: handleDropdownClick, // Pass the click handler to the menu // Pass the click handler to the menu
                }}
                trigger={['click']}
              >
                <Space
                  style={{
                    // marginLeft: '620px',
                    color: '#0A0606',
                    fontSize: '14px',
                    fontFamily: 'Rubik',
                    marginTop: '10px',
                  }}
                >
                  By Activity
                  <DownOutlined />
                </Space>
              </Dropdown>
            </div> */}
          </div>

          <div>
            <div
              className="chartContainer"
              style={{
                height: '100%',
                width: '100%',
                padding:
                  windowSize.width < 768 ? '16px' : '20px 10px 10px 10px',
                overflowX: 'hidden',
                overflowY: 'hidden',
                whitespace: 'nowrap',
                display: selectedIcon === 'AppstoreFilled' ? 'block' : 'none',
              }}
            >
              <h4
                style={{
                  marginBottom: windowSize.width < 768 ? '16px' : '10px',
                  fontSize: windowSize.width < 768 ? '18px' : '16px',
                  padding: windowSize.width < 768 ? '0 8px' : '0',
                }}
              >
                Third party marketplace campaigns :{' '}
              </h4>
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="cards">
                  {(provided) => (
                    <div ref={provided.innerRef} {...provided.droppableProps}>
                      <Row
                        gutter={windowSize.width < 768 ? [16, 16] : [8, 16]}
                        style={{
                          display: 'grid',
                          gridTemplateColumns:
                            windowSize.width < 768
                              ? '1fr' // Mobile: 1 column
                              : windowSize.width < 1024
                              ? 'repeat(2, 1fr)' // Tablet: 2 columns
                              : 'repeat(4, 1fr)', // Desktop: 4 columns
                          gap: windowSize.width < 768 ? '16px' : '12px',
                          margin: 0,
                        }}
                      >
                        {cards.map((card, index) => (
                          <Draggable
                            key={card.id}
                            draggableId={card.id.toString()}
                            index={index}
                          >
                            {(dragProvided) => (
                              <div
                                ref={dragProvided.innerRef}
                                {...dragProvided.draggableProps}
                                {...dragProvided.dragHandleProps}
                                style={{
                                  width: '100%',
                                  ...dragProvided.draggableProps.style,
                                }}
                              >
                                {/* <Link to="/dashboard/card"> */}
                                <Card
                                  style={{
                                    width: '100%',
                                    height: getResponsiveCardHeight(
                                      windowSize.width,
                                    ),
                                    borderRadius: '12px',
                                    minHeight:
                                      windowSize.width < 768 ? '280px' : 'auto',
                                  }}
                                  hoverable
                                  cover={
                                    <img
                                      style={{
                                        width: '100%',
                                        height: getResponsiveImageHeight(
                                          windowSize.width,
                                        ),
                                        borderTopLeftRadius: '12px',
                                        borderTopRightRadius: '12px',
                                        objectFit: 'cover',
                                      }}
                                      alt="example"
                                      src={card.imageSrc}
                                    />
                                  }
                                  onClick={() => {
                                    setShowPopup(true);
                                    setCardObj(card);
                                  }}
                                >
                                  <div
                                    style={{
                                      marginTop: '-15px',
                                      marginLeft: '-15px',
                                      marginRight: '-15px',
                                    }}
                                  >
                                    <div
                                      style={{
                                        display: 'flex',
                                        justifyContent: 'space-between ',
                                      }}
                                    >
                                      <h4
                                        style={{
                                          color: '#2A77F4',
                                          fontSize: '14px',
                                          fontFamily: 'rubik',
                                          letterSpacing: '0px',
                                        }}
                                      >
                                        {card.title}
                                      </h4>
                                      <h4
                                        style={{
                                          fontSize: '14px',
                                          fontFamily: 'Rubik',
                                        }}
                                      >
                                        {card.price}
                                      </h4>
                                    </div>
                                    <div
                                      style={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        color: '#A9ABBC',
                                        fontSize: '12px',
                                        fontFamily: 'Rubik',
                                        marginTop: '8px',
                                      }}
                                    >
                                      {card.description}
                                      <h4>
                                        {index === 0
                                          ? 489
                                          : index === 1
                                          ? 387
                                          : index === 2
                                          ? 566
                                          : index === 3
                                          ? 401
                                          : index === 4
                                          ? 600
                                          : index === 5
                                          ? 589
                                          : 489}
                                      </h4>
                                    </div>
                                    <div
                                      style={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        color: '#A9ABBC',
                                        fontSize: '12px',
                                        fontFamily: 'Rubik',
                                        marginTop: '8px',
                                      }}
                                    >
                                      {card.description1}
                                      <h4>
                                        {index === 0
                                          ? 2050
                                          : index === 1
                                          ? 4300
                                          : index === 2
                                          ? 3020
                                          : index === 3
                                          ? 7000
                                          : index === 4
                                          ? 5490
                                          : index === 5
                                          ? 6700
                                          : 7000}
                                      </h4>
                                    </div>

                                    <div
                                      style={{
                                        display: 'flex',
                                        justifyContent: 'flex-end',
                                      }}
                                    >
                                      <div
                                        style={{
                                          fontSize: '16px',
                                          color: '#FFC107',
                                          marginLeft: '105px',
                                          marginTop: '5px',
                                        }}
                                      >
                                        {card.icon1}
                                      </div>
                                      <div
                                        style={{
                                          fontSize: '20px',
                                          color: '#A9ABBC',
                                          marginLeft: '5px',
                                          marginTop: '5px',
                                        }}
                                      >
                                        {card.icon2}
                                      </div>
                                    </div>
                                  </div>
                                </Card>
                                {/* </Link> */}
                              </div>
                            )}
                          </Draggable>
                        ))}

                        {/* <Col span={12} md={6}>
                          <Card
                            hoverable
                            style={{
                              width: '14vw',
                              // height: '48vh',
                              borderRadius: '12px',
                              border: '6px solid #7070701A',
                            }}
                          >
                            <CopyOutlined
                              style={{
                                flex: 1,
                                fontSize: '40px',
                                color: '#A9ABBC',
                                marginTop: '60px',
                                marginLeft: '40px',
                                justifyContent: 'center',
                                alignItems: 'center',
                              }}
                            />
                            <h5
                              style={{
                                color: 'A9ABBC',
                                fontFamily: 'Rubik',
                                fontSize: '12px',
                                textAlign: 'center',
                              }}
                            >
                              Make a copy
                            </h5>
                            <div style={{ height: 80 }} />
                          </Card>
                        </Col> */}
                      </Row>

                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            </div>
          </div>
        </div>
      </div>
      <div
        style={{
          width: getResponsiveFilterWidth(windowSize.width, showFilter),
          display: showFilter ? 'block' : 'none',
          position: windowSize.width < 768 ? 'fixed' : 'relative',
          top: windowSize.width < 768 ? '0' : 'auto',
          left: windowSize.width < 768 ? '0' : 'auto',
          height: windowSize.width < 768 ? '100vh' : 'auto',
          zIndex: windowSize.width < 768 ? '1000' : 'auto',
          backgroundColor:
            windowSize.width < 768 ? 'rgba(0,0,0,0.5)' : 'transparent',
        }}
      >
        <div
          className="widgetSpacing"
          style={{
            width: windowSize.width < 768 ? '80%' : '100%',
            marginLeft: windowSize.width < 768 ? '0' : '15px',
            background: '#fff',
            height: windowSize.width < 768 ? '100vh' : '100%',
            padding: windowSize.width < 768 ? '16px' : '20px',
            maxHeight: windowSize.width < 768 ? '100vh' : '80vh',
            overflowX: 'hidden',
            overflowY: 'scroll',
            position: windowSize.width < 768 ? 'relative' : 'static',
          }}
        >
          {/* <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div>
              <h6
                style={{
                  marginTop: '20px',

                  fontFamily: 'Rubik',
                  color: '#A9ABBC',
                }}
              >
                Hello.
              </h6>
              <h4
                style={{
                  marginTop: '4px',

                  fontFamily: 'Rubik',
                  fontSize: '14px',
                  color: '0A0606',
                }}
              >
                Alexander
              </h4>
            </div>
            <div style={{ display: 'flex', marginTop: '20px' }}>
              <Avatar
                src={
                  <img
                    src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTWzw8_87ASKSkDuQx7gbTSbjJtcxUQ7aXD9Q&usqp=CAU"
                    alt="User"
                  />
                }
              />
              <SettingFilled
                style={{
                  color: '#2A77F4',
                  marginLeft: '5px',
                  marginTop: '7px',
                  marginRight: '5px',
                }}
              />
            </div>
          </div> */}

          <div>
            {/* Mobile close button */}
            {windowSize.width < 768 && (
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                  marginBottom: '16px',
                }}
              >
                <Button
                  type="text"
                  onClick={() => setShowFilter(false)}
                  style={{
                    minWidth: '44px',
                    minHeight: '44px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <CloseCircleOutlined style={{ fontSize: '24px' }} />
                </Button>
              </div>
            )}

            <Divider />
            {/* <h1
              style={{
                marginTop: '-10px',
                fontFamily: 'Rubik',
                fontSize: '14px',
                color: '0A0606',
              }}
            >
              Filter
            </h1> */}
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                marginTop: '10px',
                padding: windowSize.width < 768 ? '0px 8px' : '0px 22px',
                alignItems: 'center',
              }}
            >
              <h1
                style={{
                  fontWeight: 700,
                  fontSize: windowSize.width < 768 ? '16px' : '14px',
                  margin: 0,
                }}
              >
                Email Notification
              </h1>
              <Switch
                checked={emailChecked}
                onChange={onChange}
                style={{
                  backgroundColor: emailChecked ? '#04B8FF' : '#718096',
                  minWidth: '44px', // Touch-friendly
                  minHeight: '24px',
                }}
              />
            </div>
            <Divider />
            <Menu
              mode="inline"
              openKeys={openKeys}
              onOpenChange={onOpenChange}
              style={{
                width: '100%',
              }}
            >
              <SubMenu
                className="custom-submenu"
                key="sub1"
                title={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    Audience size
                  </div>
                }
                expandIcon={({ isOpen }) =>
                  isOpen ? (
                    <img
                      src={downArrow}
                      alt="collapse arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  ) : (
                    <img
                      src={expandIcon}
                      alt="expand arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  )
                }
              >
                <Menu.Item
                  key="1"
                  style={{
                    color: '#A9ABBC',
                    marginLeft: '-20px',
                  }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '0.412')}
                    style={{ marginRight: '10px' }}
                  />
                  2(8)
                </Menu.Item>

                <Menu.Item
                  key="2"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '0.413')}
                    style={{ marginRight: '10px' }}
                  />
                  4(125)
                </Menu.Item>
                <Menu.Item
                  key="3"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '0.414')}
                    style={{ marginRight: '10px' }}
                  />
                  6(529)
                </Menu.Item>
              </SubMenu>

              <Divider style={{ marginTop: '8px' }} />

              <SubMenu
                className="custom-submenu"
                key="sub2"
                title="Conversion rate"
                expandIcon={({ isOpen }) =>
                  isOpen ? (
                    <img
                      src={downArrow}
                      alt="collapse arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  ) : (
                    <img
                      src={expandIcon}
                      alt="expand arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  )
                }
              >
                <Menu.Item
                  key="5"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '5%')}
                    style={{ marginRight: '10px' }}
                  />
                  5%
                </Menu.Item>
                <Menu.Item
                  key="6"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '6%')}
                    style={{ marginRight: '10px' }}
                  />
                  6%
                </Menu.Item>
              </SubMenu>
              <Divider style={{ marginTop: '8px' }} />

              <SubMenu
                className="custom-submenu"
                key="sub3"
                title="QTY"
                expandIcon={({ isOpen }) =>
                  isOpen ? (
                    <img
                      src={downArrow}
                      alt="collapse arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  ) : (
                    <img
                      src={expandIcon}
                      alt="expand arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  )
                }
              >
                <Menu.Item
                  key="7"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '81')}
                    style={{ marginRight: '10px' }}
                  />
                  81
                </Menu.Item>
                <Menu.Item
                  key="8"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '85')}
                    style={{ marginRight: '10px' }}
                  />
                  85
                </Menu.Item>
              </SubMenu>
              <Divider style={{ marginTop: '8px' }} />

              <SubMenu
                className="custom-submenu"
                key="sub4"
                title="Language"
                expandIcon={({ isOpen }) =>
                  isOpen ? (
                    <img
                      src={downArrow}
                      alt="collapse arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  ) : (
                    <img
                      src={expandIcon}
                      alt="expand arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  )
                }
              >
                <Menu.Item
                  key="9"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, 'english')}
                    style={{ marginRight: '10px' }}
                  />
                  English
                </Menu.Item>
                <Menu.Item
                  key="10"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, 'Spanish')}
                    style={{ marginRight: '10px' }}
                  />
                  Spanish
                </Menu.Item>
              </SubMenu>
              <Divider style={{ marginTop: '8px' }} />
              <SubMenu
                className="custom-submenu"
                key="sub5"
                title="Location"
                expandIcon={({ isOpen }) =>
                  isOpen ? (
                    <img
                      src={downArrow}
                      alt="collapse arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  ) : (
                    <img
                      src={expandIcon}
                      alt="expand arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  )
                }
              >
                <Menu.Item
                  key="11"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, 'USA')}
                    style={{ marginRight: '10px' }}
                  />
                  USA
                </Menu.Item>
                <Menu.Item
                  key="12"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, 'UK')}
                    style={{ marginRight: '10px' }}
                  />
                  UK
                </Menu.Item>
              </SubMenu>
              <Divider style={{ marginTop: '8px' }} />

              <SubMenu
                className="custom-submenu"
                key="sub9"
                title="Products owned"
                expandIcon={({ isOpen }) =>
                  isOpen ? (
                    <img
                      src={downArrow}
                      alt="collapse arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  ) : (
                    <img
                      src={expandIcon}
                      alt="expand arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  )
                }
              >
                <Menu.Item
                  key="13"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '4,2,6')}
                    style={{ marginRight: '10px' }}
                  />
                  4,2,6
                </Menu.Item>
                <Menu.Item
                  key="14"
                  style={{ marginLeft: '-20px', color: '#A9ABBC' }}
                >
                  <input
                    type="checkbox"
                    onChange={(e) => handleOptionSelect(e, '5,2,6')}
                    style={{ marginRight: '10px' }}
                  />
                  5,2,6
                </Menu.Item>
              </SubMenu>
              <Divider style={{ marginTop: '8px' }} />

              <SubMenu
                className="custom-submenu"
                key="sub6"
                title="Child age range"
                expandIcon={({ isOpen }) =>
                  isOpen ? (
                    <img
                      src={downArrow}
                      alt="collapse arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  ) : (
                    <img
                      src={expandIcon}
                      alt="expand arrow"
                      // style={{ width: '16px', height: '16px' }}
                    />
                  )
                }
              >
                <Slider
                  style={{ marginTop: '-8px' }}
                  trackStyle={{ backgroundColor: '#2A77F4' }}
                  railStyle={{ boxShadow: '0px 0px 6px #00000029' }}
                  range
                  defaultValue={[6, 9]}
                  onChange={onChange1}
                  onAfterChange={onAfterChange1}
                />
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'left',
                    color: '#040606',
                    opacity: 0.5,
                  }}
                >
                  <span>{sliderRange[0]}</span>
                  <span>-</span>
                  <span>{sliderRange[1]}+</span>
                </div>
              </SubMenu>
              <Divider style={{ marginTop: '8px' }} />
            </Menu>
            <div
              style={{
                flex: 1,
                width: '100%',
                height: '100%',
                marginTop: '20px',
              }}
            >
              <center>
                <Button
                  style={{
                    background: '#09295D',
                    borderRadius: '4px',
                    color: '#fff',
                    paddingLeft: windowSize.width < 768 ? '40px' : '70px',
                    paddingRight: windowSize.width < 768 ? '40px' : '70px',
                    minHeight: '44px', // Touch-friendly minimum
                    fontSize: windowSize.width < 768 ? '16px' : '14px',
                    fontWeight: '500',
                  }}
                  onClick={handleApplyClick}
                >
                  Apply
                </Button>
              </center>
            </div>
          </div>
        </div>
        {showPopup && (
          <div
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              alignContent: 'center',
              zIndex: 9999,
              padding: windowSize.width < 768 ? '16px' : '0',
            }}
          >
            <div
              style={{
                backgroundColor: 'white',
                padding:
                  windowSize.width < 768
                    ? '20px 20px 10px 20px'
                    : '50px 50px 10px 50px',
                borderRadius: '5px',
                boxShadow: '0 0 10px rgba(0, 0, 0, 0.3)',
                position: 'absolute',
                zIndex: 999,
                display: 'flex',
                alignItems: 'center',
                flexDirection: 'column',
                width: getResponsivePopupWidth(windowSize.width),
                maxWidth: windowSize.width < 768 ? '100%' : 'none',
                maxHeight: windowSize.width < 768 ? '90vh' : 'none',
                overflowY: windowSize.width < 768 ? 'auto' : 'visible',
              }}
            >
              <Button
                type="text"
                style={{
                  marginTop: windowSize.width < 768 ? -10 : -30,
                  border: 'none',
                  color: '#2A77F4',
                  alignSelf: 'flex-end',
                  minWidth: '44px', // Touch-friendly minimum
                  minHeight: '44px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
                onClick={() => {
                  setShowPopup(false);
                }}
              >
                <CloseCircleOutlined
                  style={{
                    fontSize: windowSize.width < 768 ? '24px' : '20px',
                  }}
                />
              </Button>
              <div
                style={{
                  backgroundColor: 'white',

                  borderRadius: '5px',

                  display: 'flex',
                  justifyContent: 'center',

                  alignContent: 'center',
                  flexDirection: 'row',
                }}
              >
                <Card
                  style={{
                    width: '14vw',
                    height: '35vh',
                    borderRadius: '12px',
                  }}
                  hoverable
                  cover={
                    <img
                      style={{
                        width: '14vw',
                        height: '25vh',
                        borderTopLeftRadius: '12px',
                        borderTopRightRadius: '12px',
                        objectFit: 'cover',
                      }}
                      alt="example"
                      src={cardObj.imageSrc}
                    />
                  }
                >
                  <div
                    style={{
                      marginTop: '-15px',
                      marginLeft: '-15px',
                      marginRight: '-15px',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between ',
                      }}
                    >
                      <h4
                        style={{
                          textAlign: 'center',
                          color: '#2A77F4',
                          fontSize: '14px',
                          fontFamily: 'rubik',
                          letterSpacing: '0px',
                        }}
                      >
                        {cardObj.title}
                      </h4>
                      {/* <h4
                        style={{
                          fontSize: '14px',
                          fontFamily: 'Rubik',
                        }}
                      >
                        {cardObj.price}
                      </h4> */}
                    </div>
                    {/* <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        color: '#A9ABBC',
                        fontSize: '12px',
                        fontFamily: 'Rubik',
                        marginTop: '8px',
                      }}
                    >
                      {cardObj.description}
                      <h4>{cardObj.price1}</h4>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        color: '#A9ABBC',
                        fontSize: '12px',
                        fontFamily: 'Rubik',
                        marginTop: '8px',
                      }}
                    >
                      {cardObj.description1}
                      <h4>{cardObj.price2}</h4>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        color: '#A9ABBC',
                        fontSize: '12px',
                        fontFamily: 'Rubik',
                        marginTop: '8px',
                      }}
                    >
                      {cardObj.description1}
                      <h4>{cardObj.price3}</h4>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'flex-end',
                      }}
                    >
                      <div
                        style={{
                          fontSize: '16px',
                          color: '#FFC107',
                          marginLeft: '105px',
                          marginTop: '5px',
                        }}
                      >
                        {cardObj.icon1}
                      </div>
                      <div
                        style={{
                          fontSize: '20px',
                          color: '#A9ABBC',
                          marginLeft: '5px',
                          marginTop: '5px',
                        }}
                      >
                        {cardObj.icon2}
                      </div>
                    </div> */}
                  </div>
                </Card>
                <div
                  style={{
                    flexDirection: 'column',
                    marginLeft: windowSize.width < 768 ? '0' : '10px',
                    marginTop: windowSize.width < 768 ? '16px' : '0',
                    width: windowSize.width < 768 ? '100%' : 'auto',
                  }}
                >
                  <p
                    style={{
                      fontSize: windowSize.width < 768 ? '16px' : '15px',
                      color: '#2A77F4',
                      marginBottom: windowSize.width < 768 ? '16px' : '10px',
                      fontWeight: 'bold',
                    }}
                  >
                    Create content :
                  </p>
                  <Form
                    onFinish={onFinish}
                    style={{
                      minWidth: windowSize.width < 768 ? '100%' : '500px',
                      width: windowSize.width < 768 ? '100%' : 'auto',
                    }}
                  >
                    {/* Text Input */}
                    <Form.Item
                      // label="Name"
                      style={{
                        marginBottom: windowSize.width < 768 ? '16px' : '20px',
                      }}
                      name="name"
                      rules={[
                        { required: true, message: 'Please enter title' },
                      ]}
                    >
                      <Input
                        placeholder="Headline text"
                        style={{
                          minHeight: '44px', // Touch-friendly minimum
                          fontSize: windowSize.width < 768 ? '16px' : '14px',
                        }}
                      />
                    </Form.Item>

                    {/* Text Area */}
                    <Form.Item
                      name="description"
                      style={{
                        marginBottom: windowSize.width < 768 ? '16px' : '20px',
                      }}
                    >
                      <Input.TextArea
                        placeholder="Prompt for the campaign..."
                        autoSize={{
                          minRows: windowSize.width < 768 ? 3 : 4,
                          maxRows: windowSize.width < 768 ? 5 : 6,
                        }}
                        style={{
                          fontSize: windowSize.width < 768 ? '16px' : '14px',
                        }}
                      />
                    </Form.Item>

                    {/* Switch */}
                    <Form.Item
                      style={{
                        marginBottom: windowSize.width < 768 ? '12px' : '7px',
                        fontSize: windowSize.width < 768 ? '16px' : '14px',
                      }}
                      label="Include product promotion"
                      name="status"
                      valuePropName="checked"
                    >
                      <Switch
                        style={{
                          minWidth: '44px', // Touch-friendly minimum
                        }}
                      />
                    </Form.Item>
                    <div
                      style={{
                        display: 'flex',
                        flexDirection:
                          windowSize.width < 768 ? 'column' : 'row',
                        justifyContent: 'space-between',
                        gap: windowSize.width < 768 ? '12px' : '0',
                      }}
                    >
                      <Form.Item
                        style={{
                          flex: 1,
                          marginBottom: windowSize.width < 768 ? '0' : 'auto',
                        }}
                      >
                        <Select
                          size={windowSize.width < 768 ? 'large' : 'small'}
                          mode="multiple"
                          optionFilterProp="children"
                          style={{
                            minWidth: windowSize.width < 768 ? '100%' : '15vw',
                            width: windowSize.width < 768 ? '100%' : 'auto',
                            minHeight: windowSize.width < 768 ? '44px' : 'auto',
                          }}
                          placeholder="All Genders"
                          onChange={handleGenderChange}
                          maxTagCount="responsive"
                          value={selectedGenders}
                          filterOption={(input, option) =>
                            (option?.label ?? '')
                              .toLowerCase()
                              .includes(input.toLowerCase())
                          }
                          options={[
                            {
                              value: 'male',
                              label: 'Male',
                            },
                            {
                              value: 'female',
                              label: 'Female',
                            },
                          ]}
                        />
                      </Form.Item>
                      <Form.Item
                        style={{
                          flex: 1,
                          marginBottom: windowSize.width < 768 ? '0' : 'auto',
                        }}
                      >
                        <Select
                          size={windowSize.width < 768 ? 'large' : 'small'}
                          mode="multiple"
                          style={{
                            minWidth: windowSize.width < 768 ? '100%' : '15vw',
                            width: windowSize.width < 768 ? '100%' : 'auto',
                            minHeight: windowSize.width < 768 ? '44px' : 'auto',
                          }}
                          placeholder="All Languages"
                          onChange={handleLanguageSelect}
                          maxTagCount="responsive"
                          value={language}
                        >
                          {languageOptions.map((option) => (
                            <Option key={option.id} value={option.id}>
                              {option.lang_name}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </div>
                    <Form.Item
                      style={{
                        display: 'flex',
                        justifyContent:
                          windowSize.width < 768 ? 'center' : 'flex-end',
                        marginTop: windowSize.width < 768 ? '20px' : '0',
                      }}
                    >
                      <Button
                        type="ai"
                        htmlType="submit"
                        style={{
                          background:
                            'linear-gradient(138deg, rgb(20, 135, 255) 8%, rgb(41, 239, 196) 90%)',
                          borderRadius: '4px',
                          textAlign: 'center',
                          font:
                            windowSize.width < 768
                              ? 'normal normal medium 16px/20px Rubik'
                              : 'normal normal medium 14px/17px Rubik',
                          border: 'none',
                          color: '#2A77F4',
                          height: windowSize.width < 768 ? '48px' : '40px', // Touch-friendly height
                          minHeight: '44px', // Touch-friendly minimum
                          width: windowSize.width < 768 ? '100%' : 'auto',
                          padding:
                            windowSize.width < 768 ? '12px 20px' : '8px 16px',
                        }}
                        // onClick={() => {
                        //   setShowPopup(false);
                        //   togglePopup();
                        // }}
                      >
                        <p
                          style={{
                            color: '#fff',
                            textAlign: 'center',
                            font:
                              windowSize.width < 768
                                ? 'normal normal medium 16px/20px Rubik'
                                : 'normal normal medium 14px/17px Rubik',
                            margin: 0,
                          }}
                        >
                          Generate AI campaign creatives
                        </p>
                      </Button>
                    </Form.Item>
                  </Form>
                </div>
              </div>

              {/* <Button
                type="ai"
                htmlType="submit"
                style={{
                  background:
                    'linear-gradient(138deg, rgb(20, 135, 255) 8%, rgb(41, 239, 196) 90%)',
                  borderRadius: '4px',
                  textAlign: 'center',
                  font: 'normal normal medium 14px/17px Rubik',
                  border: 'none',
                  color: '#2A77F4',
                  height: '40px',
                  alignSelf: 'flex-end',
                  marginTop: '50px',
                }}
                onClick={() => {
                  setShowPopup(false);
                  togglePopup();
                }}
              >
                <p
                  style={{
                    color: '#fff',
                    textAlign: 'center',
                    font: 'normal normal medium 14px/17px Rubik',
                  }}
                >
                  Generate AI campaign creatives
                </p>
              </Button> */}
            </div>

            {/* Style the black opacity background */}
            <div
              style={{
                content: '',
                position: 'fixed',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                backgroundColor: 'black',
                opacity: 0.7 /* Adjust the opacity level as needed */,
              }}
            />
          </div>
        )}
        {showPopupCircle && (
          <div
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              alignContent: 'center',
              zIndex: 9999,
              padding: windowSize.width < 768 ? '16px' : '0',
            }}
          >
            <div
              style={{
                backgroundColor: 'white',
                padding: windowSize.width < 768 ? '30px' : '50px',
                borderRadius: '5px',
                boxShadow: '0 0 10px rgba(0, 0, 0, 0.3)',
                position: 'absolute',
                zIndex: 999,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flexDirection: 'column',
                maxWidth: windowSize.width < 768 ? '90%' : 'none',
                textAlign: 'center',
              }}
            >
              <p
                style={{
                  fontSize: windowSize.width < 768 ? '18px' : '20px',
                  color: '#2A77F4',
                  margin: 0,
                  marginBottom: windowSize.width < 768 ? '30px' : '50px',
                }}
              >
                Our AI is working its magic…
              </p>
              <Spin
                indicator={antIcon}
                style={{
                  alignSelf: 'center',
                  fontSize: windowSize.width < 768 ? '32px' : '24px',
                }}
              />
            </div>
            {/* Style the black opacity background */}
            <div
              style={{
                content: '',
                position: 'fixed',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                backgroundColor: 'black',
                opacity: 0.7 /* Adjust the opacity level as needed */,
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
}

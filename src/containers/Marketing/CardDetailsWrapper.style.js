import styled from 'styled-components';

const CardDetailsWrapper = styled.div(() => ({
  '& .cardInnerDetails': {
    padding: '27px 45px',
    '& .cardDetails-content': {
      display: 'flex',
      alignItems: 'flex-start',
      gap: '51px',
      '@media (max-width: 1440px)': {
        gap: '20px',
      },
      '@media (max-width: 992px)': {
        flexWrap: 'wrap',
      },
      '& .cardDetails': {
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        gap: '29px',
        '& .cardDetails-space': {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          '& .cardLabel': {
            fontFamily: 'Inter',
            fontSize: '14px',
            fontWeight: 500,
            lineHeight: '22px',
            textAlign: 'left',
            textUnderlinePosition: 'from-font',
            textDecorationSkipInk: 'none',
            color: '#262E3D',
          },
          '& .cardLabelVal': {
            fontFamily: 'Inter',
            fontSize: '13px',
            fontWeight: 700,
            lineHeight: '24px',
            letterSpacing: '0.10000000149011612px',
            textAlign: 'left',
            textUnderlinePosition: 'from-font',
            textDecorationSkipInk: 'none',
            color: '#252B42',
          },
        },
      },
    },
    '& .buttonDiv': {
      flexDirection: 'row',
      justifyContent: 'flex-start',
      alignContent: 'flex-start',
      alignItems: 'flex-start',
      display: 'flex',
      gap: '17px',
      flexWrap: 'wrap',
      marginTop: '59px',
      '& .genreateBtn': {
        background: 'linear-gradient(90deg, #06A9EA 0%, #02D9C0 100%)',
        borderRadius: '8px',
        height: '40px',
        padding: '11px 10px 12px 16px',
        '& .btnText': {
          fontFamily: 'Inter',
          fontSize: '14px',
          fontWeight: 600,
          lineHeight: '16.94px',
          textAlign: 'left',
          textUnderlinePosition: 'from-font',
          textDecorationSkipInk: 'none',
          color: '#FFFFFF',
        },
      },
    },
    '& .cardimage': {
      width: '281px',
      height: '281px',
      borderRadius: '12px',
      objectFit: 'cover',
      //   maxWidth: '500px',

      backgroundColor: 'GrayText',
      padding: '1px',
    },
  },
  '& .cardDetails-right-col': {
    padding: '25px 17px 25px 15px',
    '& .right-inner-content': {
      width: '100%',
      '& .title-div': {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        '& .chosenCat': {
          fontFamily: 'Inter',
          fontSize: '16px',
          fontWeight: 700,
          lineHeight: '24px',
          letterSpacing: '0.10000000149011612px',
          textAlign: 'left',
          textUnderlinePosition: 'from-font',
          textDecorationSkipInk: 'none',
          color: '#00B2F8',
        },
      },
      '& .right-inner-points-div': {
        padding: '56px 16px',
        display: 'flex',
        flexDirection: 'column',
        gap: '56px',
        '& .detailsLabelVal': {
          display: 'flex',
          flexDirection: 'column',
          gap: '11px',
          justifyContent: 'flex-start',
          '& .detailsLabel': {
            fontFamily: 'Inter',
            fontSize: 14,
            fontWeight: 700,
            lineHeight: '24px',
            letterSpacing: '0.10000000149011612px',
            textAlign: 'left',
            textUnderlinePosition: 'from-font',
            textDecorationSkipInk: 'none',
            color: '#252B42',
          },
          '& .detailsVal': {
            fontFamily: 'Inter',
            fontSize: 14,
            fontWeight: 500,
            lineHeight: '24px',
            letterSpacing: '0.10000000149011612px',
            textAlign: 'left',
            textUnderlinePosition: 'from-font',
            textDecorationSkipInk: 'none',
            color: '#718096',
          },
        },
      },
    },
  },
}));

export default CardDetailsWrapper;

/* eslint-disable no-unused-vars */
/* eslint-disable no-shadow */
/* eslint-disable react/no-unescaped-entities */
/* eslint-disable no-dupe-keys */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable  no-use-before-define */
/* eslint-disable no-plusplus */
/* eslint-disable react/jsx-boolean-value */
/* eslint-disable object-shorthand */
/* eslint-disable import/order */
/* eslint-disable react/self-closing-comp */
/* eslint-disable no-restricted-syntax */
/* eslint-disable no-alert */
/* eslint-disable no-param-reassign */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-debugger */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import { Provider, useDispatch, useSelector } from 'react-redux';
import Konva from 'konva';
import { SketchPicker } from 'react-color';
import moment from 'moment';
import imageCompression from 'browser-image-compression';
import {
  Image,
  Drawer,
  Button,
  Row,
  Col,
  Input,
  Modal,
  List,
  Slider,
  InputNumber,
  Select,
  Divider,
  Popover,
  Switch,
  DatePicker,
  TimePicker,
  Popconfirm,
  Tabs,
  Radio,
  Spin,
  Skeleton,
  Alert,
  Form,
} from 'antd';
import IntlMessages from '@chill/components/utility/intlMessages';
import { useHistory, useLocation } from 'react-router';
import { findIndex, isArray, isEmpty, isObject } from 'lodash';
import Notification from '@chill/components/Notification';
import useWindowSize from '@chill/lib/hooks/useWindowSize';
import PlaySVG from '@chill/assets/images/play.svg';
import SettingSVG from '@chill/assets/images/setting.svg';
import SendSVG from '@chill/assets/images/send.svg';
import DownloadSVG from '@chill/assets/images/download.svg';
import EditSVG from '@chill/assets/images/edit.svg';
import NewProjectSVG from '@chill/assets/images/new-project.svg';
import TemplateSVG from '@chill/assets/images/template.svg';
import TextSVG from '@chill/assets/images/text.svg';
import ElementsSVG from '@chill/assets/images/elements.svg';
import UploadSVG from '@chill/assets/images/cloud-upload.svg';
import CameraSVG from '@chill/assets/images/camera.svg';
import VideoSVG from '@chill/assets/images/video.svg';
import GifSVG from '@chill/assets/images/gif.svg';
import UndoSVG from '@chill/assets/images/turn-back.svg';
import RedoSVG from '@chill/assets/images/turn-front.svg';
import ColPalSVG from '@chill/assets/images/color-palette.svg';
import LayerSVG from '@chill/assets/images/layers.svg';
import left from '@chill/assets/images/Group 9243.svg';
import middle from '@chill/assets/images/Group 9242.svg';
import right from '@chill/assets/images/Group 9241.svg';
import Topalign from '@chill/assets/images/topAlign.svg';
import Vertical from '@chill/assets/images/verticalAlign.svg';
import bottom from '@chill/assets/images/Group 9063.svg';
import Grid from '@chill/assets/images/Repeat Grid 3.svg';
import Paintbucket from '@chill/assets/images/paint-bucket.svg';
import Bgremoval from '@chill/assets/images/Mask Group 44.svg';
import TextT from '@chill/assets/images/text_icons/T.svg';
import leftAlign from '@chill/assets/images/text_icons/left-align.svg';
import CenterAlign from '@chill/assets/images/left-align-1.svg';
import fontSizeI from '@chill/assets/images/text_icons/Group 9055.svg';
import Aplus from '@chill/assets/images/text_icons/Aplus.svg';
import Border from '@chill/assets/images/text_icons/Group 9060.svg';
import colorPickerSvg from '@chill/assets/images/text_icons/Rectangle 1427.svg';
import centerAlign from '@chill/assets/images/text_icons/centerAlign.svg';
import rightAlign from '@chill/assets/images/text_icons/left-align-2.svg';
import linesVertical from '@chill/assets/images/text_icons/left-align-3.svg';
import fI from '@chill/assets/images/text_icons/fi.svg';
import TT from '@chill/assets/images/text_icons/Group 9078.svg';
import A from '@chill/assets/images/text_icons/Group 9077.svg';
import textTt from '@chill/assets/images/text_icons/Group 9071.svg';
import av from '@chill/assets/images/text_icons/Group 9075.svg';
import RotateLeft from '@chill/assets/images/rotate left.svg';
import PathLeft from '@chill/assets/images/path left .svg';
import G1875 from '@chill/assets/images/g1875.svg';
import BusinessCard from '@chill/assets/images/businesscard.svg';
import Layer2123 from '@chill/assets/images/layer 2123.svg';
import Group9122 from '@chill/assets/images/group 9122.svg';
import FontCard from '@chill/assets/images/textfont1.jpg';
import FontCard2 from '@chill/assets/images/textfont2.jpg';
import SamplTextImg from '@chill/assets/images/samplefont.jpg';
import SampleTextImg1 from '@chill/assets/images/samplefont1.jpg';
import SampleTextImg2 from '@chill/assets/images/samplefont4.jpg';
import SampleTextImg3 from '@chill/assets/images/samplefont5.jpg';
import Magic from '@chill/assets/images/magicwand.png';
import LightBulb from '@chill/assets/images/lightbulb.png';
import Mob1 from '@chill/assets/images/Mob1.png';
import Mob2 from '@chill/assets/images/Mob3.jpg';
import TextlayerSVG from '@chill/assets/images/textlayer.svg';
import LinkIcon from '@chill/assets/images/link.svg';
import OrnimentsShapes from '@chill/assets/images/star.svg';
import IllustrationShapes from '@chill/assets/images/rose.svg';
import AbstractShapes from '@chill/assets/images/abstract-shape.svg';
import DashboardBackground from '@chill/assets/images/dashboard_background.jpeg.jpg';
import ChillbabyLogo from '@chill/assets/images/cleanAirLogoWhite.svg';
import BabyBoy from '@chill/assets/images/image4.jpg';
import ArcSlider from '@chill/assets/images/arcSliderImg.png';
import ArrowW from '@chill/assets/images/arrowW.png';
import Bluetooth from '@chill/assets/images/blt.png';
import Battery from '@chill/assets/images/battery_full.png';
import Home from '@chill/assets/images/HomeB.png';
import Dashboard from '@chill/assets/images/DshboardW.png';
import Graph from '@chill/assets/images/layerW.png';
import Setting from '@chill/assets/images/SettingsW.png';

import getApiData from '@chill/lib/helpers/apiHelper';

import {
  ArrowLeftOutlined,
  FormOutlined,
  MinusOutlined,
  PlusOutlined,
  RightOutlined,
  LeftOutlined,
  CloudUploadOutlined,
  CloseOutlined,
  CaretDownOutlined,
  FolderOutlined,
  EyeOutlined,
  LockOutlined,
  SearchOutlined,
  FacebookFilled,
  InstagramFilled,
  TwitterCircleFilled,
  RobotFilled,
  LinkOutlined,
  FontSizeOutlined,
  AlignLeftOutlined,
  BellOutlined,
  CloudOutlined,
  PictureTwoTone,
  ControlOutlined,
  SwapOutlined,
  UserOutlined,
  HomeOutlined,
} from '@ant-design/icons';

import {
  Stage,
  Layer,
  Rect,
  Circle,
  Line,
  Ellipse,
  Star,
  Ring,
  Arc,
  RegularPolygon,
  Shape,
} from 'react-konva';
import availableShapes from './Shapes';
import logoPng from '@chill/assets/images/logo_b.png';
import { Link } from 'react-router-dom';
import store from './store';
import {
  addText,
  addParagraph,
  addParagraph1,
  addParagraph2,
  addParagraph3,
  addParagraph4,
  addTextImg,
  addTextSampleImg,
  addButton,
  addUploadImage,
  addGenerateImage,
  addShape,
} from './reducer/layerSlice';

import TopbarUser from '../Topbar/TopbarUser';
import MarketingWrapper from './Marketing.Styles';
import ArtBoardSizePop from './ArtBoardSizePop';
import KonvaBoard2, { KonvaCanvas } from './KonvaBoard2';
import KonvaText, {
  KonvaUploadImage,
  KonvaTag,
  KonvaShape,
  KonvaButtontext,
} from './KonvaText';
import LayerContent from './layerContent';
// import { set } from 'lodash';

const { Option } = Select;

// New project drawer content

const brandNames = [
  'bugaboo',
  'cbt-aqm',
  'cbtsmartpad',
  'cbtsmartcar',
  'cbthc',
];

const NewProject = ({ Name, setName, setEditableText, aud_data }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [savedProjectData, setSavedProjectData] = useState(null);
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [projectDataLocal, setProjectDataLocal] = useState(null);
  const [ImageData, setImageData] = useState(null);
  const [selectedCardDescription, setSelectedCardDescription] = useState('');
  const [productsDetails, setProductsDetails] = useState([]);
  const [product, setProduct] = useState([]);
  const [productType, setProductType] = useState('awareness');

  const audienceType = localStorage.getItem('audienceType');

  const fetchImages = async () => {
    try {
      const response = await getApiData('product/get-brand-products', 'GET');
      console.log(response.data);
      if (response.success && isArray(response.data)) {
        console.log(response.data);
        setProductsDetails(response.data);
      }
    } catch (error) {
      console.error('Error fetching images:', error);
    }
  };

  useEffect(() => {
    fetchImages();
  }, []);

  const handleButtonClick = () => {
    setConfirmVisible(true);
  };

  const handleCreate = () => {
    // Store the selected card description in local storage
    localStorage.setItem('selectedCardDescription', selectedCardDescription);

    // Close the modal
    setModalVisible(false);
  };

  const handleCardSelect = useCallback((description) => {
    setSelectedCardDescription(description);
  }, []);

  const handleConfirm = () => {
    setConfirmVisible(false);
    setModalVisible(true);
    const projectJson = KonvaBoard2.toJSON();
    const ConvertJson = JSON.parse(projectJson);
    setSavedProjectData(projectJson);
    console.log('This is project data:', projectJson);

    const projectData = {
      project_Name: Name, // Use the updated project name
      projectDataJson: ConvertJson,
    };
    console.log('This is local storage data:', projectData);
    // Store projectData in local storage
    localStorage.setItem('projectData', JSON.stringify(projectData));
    setProjectDataLocal(projectData);

    // Clear the contents of KonvaBoard2
    KonvaBoard2.clear();
    // Clear the input field value
    setName('New Project');
    setEditableText('New Project');
    // handlegetlocalstoragedata();
  };

  useEffect(() => {
    const storedProjectData = localStorage.getItem('projectData');
    if (storedProjectData) {
      const projectData = JSON.parse(storedProjectData);
      setProjectDataLocal(projectData);
      console.log('this is getting data', projectData);
      const Imagetting = projectData?.projectDataJson?.attrs?.stageImg;
      setImageData(Imagetting);
      console.log('Imagetting', Imagetting);
    }
  }, []);

  // const handlegetlocalstoragedata = () => {
  //   const storedProjectData = localStorage.getItem('projectData');
  //   if (storedProjectData) {
  //     const projectData = JSON.parse(storedProjectData);
  //     setProjectDataLocal(projectData);
  //     console.log('this is getting data', projectData);
  //     const Imagetting = projectData?.projectDataJson?.attrs?.stageImg;
  //     setImageData(Imagetting);
  //     console.log('Imagetting', Imagetting);
  //   }
  // };

  const handleCancel = () => {
    setConfirmVisible(false);
    setModalVisible(true);
    handleClear();
    // Clear the input field value
    setName('New Project');
    setEditableText('New Project');
  };

  const handleClear = () => {
    KonvaBoard2.clear();
  };

  const handleProjectClick = () => {
    // const StoredName = Name;
    setName(projectDataLocal.project_Name);
    setEditableText(projectDataLocal.project_Name);
    // Import the JSON data from the respective div
    const importedData = projectDataLocal && projectDataLocal.projectDataJson;
    if (importedData) {
      console.log('Imported data:', importedData);
      // Perform the desired action with the imported data
    }
    const jsonnp = importedData;
    KonvaBoard2.fromJSON(jsonnp);
  };

  const shouldShowProjectData =
    projectDataLocal && projectDataLocal.projectDataJson;

  const productOptions = productsDetails.map((item) => {
    return { value: item.product_name, label: item.product_name };
  });

  const handleProductChange = async (name) => {
    const prodItem = productsDetails.filter(
      (item) => item.product_name === name,
    )[0];
    // console.log('prod', prodItem);
    setProduct(prodItem);
  };

  const handleProductTypeChange = (e) => {
    setProductType(e.target.value);
  };

  return (
    <div>
      <Popconfirm
        visible={confirmVisible}
        title="Do you want to save your current project?"
        onConfirm={handleConfirm}
        onCancel={handleCancel}
        okText="Yes"
        cancelText="No"
      >
        <Button
          type="primary"
          onClick={handleButtonClick}
          icon={<FormOutlined />}
          size="small"
          style={{
            width: '100%',
            height: '30px',
            borderRadius: 10,
            // background: 'linear-gradient(137deg, #5087FF 0%, #29EFC4 100%)',
            background: 'rgb(0, 157, 254);',
          }}
        >
          New Project
        </Button>
      </Popconfirm>
      {audienceType === 'in_app_message' ? (
        <Modal
          centered
          open={modalVisible}
          onCancel={() => setModalVisible(false)}
          footer={[
            <Button key="create" type="primary" onClick={handleCreate}>
              Create
            </Button>,
          ]}
          width="60%"
        >
          <ArtBoardSizePop onCardSelect={handleCardSelect} />
        </Modal>
      ) : null}
      {shouldShowProjectData && (
        <Row
          gutter={[16, 16]}
          style={{
            display: 'flex',
            alignItems: 'center',
            marginTop: 10,
          }}
        >
          <Col xs={24} md={12} lg={10}>
            {projectDataLocal && (
              <div
                onClick={handleProjectClick}
                style={{
                  width: '20vh',
                  height: '20vh',
                  cursor: 'pointer',
                  // backgroundColor: '#ccc',
                  // border: '1px solid #0000001a',
                  backgroundImage: `url(${ImageData})`,
                  backgroundSize: 'cover',
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: 'center',
                }}
              />
            )}
            {projectDataLocal && <p>{projectDataLocal.project_Name}</p>}
          </Col>
        </Row>
      )}
      <div
        style={{
          marginTop: 10,
          display: 'flex',
          alignItems: 'center',
        }}
      >
        <Form.Item>
          <Radio.Group onChange={handleProductTypeChange} value={productType}>
            <Radio value="awareness">Select Product</Radio>
            <Radio value="sales">Add Product Link</Radio>
          </Radio.Group>
        </Form.Item>
      </div>
      <div>
        {productType === 'awareness' ? (
          <Form.Item
            name="selectedProduct"
            rules={[
              {
                required: true,
                message: 'Please select a product',
              },
            ]}
          >
            <Select
              placeholder="Select Any Product"
              optionFilterProp="children"
              size="lg"
              style={{
                width: '100%',
              }}
              onChange={handleProductChange}
              value={product.product_name || undefined}
              filterOption={(input, option) =>
                (option?.label ?? '')
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              options={productOptions}
            />
          </Form.Item>
        ) : (
          <Form.Item
            name="productLink"
            rules={[
              {
                required: true,
                message: 'Please enter a product link',
              },
            ]}
          >
            <Input
              addonBefore="https://"
              allowClear
              placeholder="Enter Product Link"
            />
          </Form.Item>
        )}
      </div>
      {aud_data && (
        <div>
          <p style={{ marginBottom: 5 }}>Campaign Title: {aud_data.name}</p>
          {/* <p>Criteria Title: {aud_data.c_name}</p> */}
          <p>Audience Count: {aud_data.aud_count}</p>
        </div>
      )}
    </div>
  );
};
// Elements drawer content

const { Search } = Input;
const Shapes = () => {
  const [showTextFields, setShowTextFields] = useState(true);
  const [showshapes, setShowShapes] = useState(false);
  const [textValue, setTextValue] = useState('');
  const [urlValue, setUrlValue] = useState('');
  const [selectedButton, setSelectedButton] = useState(null);
  const [visible, setVisible] = useState(false);
  const [selectedColor, setSelectedColor] = useState('#318CE7');
  const [showAllShapes, setShowAllShapes] = useState(false);
  const [showbasic, setShowbasic] = useState(false);
  const [visibleShapes, setVisibleShapes] = useState(2);
  const [visiblebasic, setvisibleBasic] = useState(2);
  const [selectedShapeId, setSelectedShapeId] = useState(null);
  const [titleInput, setTitleInput] = useState('');
  const [linkInput, setLinkInput] = useState('');
  const [selectedTextColor, setSelectedTextColor] = useState('#0A0606');
  const [visible2, setVisible2] = useState(false);
  const [shapeColor, setShapecolor] = useState('blue');

  const basicShapes = availableShapes.filter(
    (shape) => shape.category === 'basicShapes',
  );

  const circleStyle = {
    height: 30,
    width: 30,
    border: 'solid 2px #e5e5e5',
    borderRadius: 3,
    backgroundColor: selectedColor,
    cursor: 'pointer',
    padding: '5px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  };

  const circleStyle1 = {
    height: 30,
    width: 30,
    border: 'solid 2px #e5e5e5',
    borderRadius: 3,
    backgroundColor: selectedTextColor,
    cursor: 'pointer',
    padding: '5px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  };

  const ShapecircleStyle = {
    height: 40,
    width: 40,
    borderRadius: '50%', // Make it a circle
    backgroundColor: shapeColor,
    cursor: 'pointer',
    padding: '5px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: '10px',
  };

  const handleTextColorPopover = (visible2) => {
    setVisible2(visible2);
  };

  const handleTextColorChange = (color) => {
    if (KonvaButtontext.editButtonText) {
      const prev = KonvaButtontext.editButtonText.fill();
      setSelectedTextColor(color.hex);
      console.log(selectedTextColor);
      console.log(KonvaButtontext.editButtonText);

      KonvaButtontext.editButtonText.setAttr('fill', color.hex);

      KonvaBoard2.undoStack.push({
        element: KonvaButtontext.editButtonText,
        attr: 'fill',
        attrValue: color.hex,
        attrprev: prev,
      });

      console.log(KonvaBoard2.undoStack);
    }
  };

  const handleColorChange = (color) => {
    if (KonvaTag.editButton) {
      const prev = KonvaTag.editButton.fill();
      setSelectedColor(color.hex);
      console.log(selectedColor);
      console.log(KonvaTag.editButton);

      KonvaTag.editButton.setAttr('fill', color.hex);

      KonvaBoard2.undoStack.push({
        element: KonvaTag.editButton,
        attr: 'fill',
        attrValue: color.hex,
        attrprev: prev,
      });

      console.log(KonvaBoard2.undoStack);
    }
  };

  const handleShapeColorChange = (color) => {
    if (KonvaShape.editShape) {
      const prev = KonvaShape.editShape.fill();
      setShapecolor(color.hex);
      console.log(KonvaShape.editShape);

      KonvaShape.editShape.setAttr('fill', color.hex);

      KonvaBoard2.undoStack.push({
        element: KonvaShape.editShape,
        attr: 'fill',
        attrValue: color.hex,
        attrprev: prev,
      });

      KonvaShape.editShape.cache();

      console.log(KonvaBoard2.undoStack);
    }
  };

  const toggleShowAll = () => {
    setShowAllShapes(!showAllShapes);
    setVisibleShapes(showAllShapes ? 2 : availableShapes.length);
  };

  const toggleShowAll1 = () => {
    setShowbasic(!showbasic);
    setvisibleBasic(showbasic ? 2 : basicShapes.length);
  };

  const handleButtonClick = () => {
    setShowTextFields(!showTextFields);
    setShowShapes(false);
  };

  const handleShapesButtonClick = () => {
    setShowShapes(!showshapes);
    setShowTextFields(false);
  };

  const handleTextChange = (e) => {
    setTextValue(e.target.value);
  };

  const handleUrlChange = (e) => {
    setUrlValue(e.target.value);
  };

  const handleLinkChange = (e) => {
    setLinkInput(e.target.value);
  };

  const handleHideButtonClick = () => {
    setShowTextFields(false);
    setTextValue('');
    setUrlValue('');
  };

  const handleButtonSelect = () => {
    store.dispatch(addButton({ text: textValue, url: urlValue }));
    setSelectedButton({
      text: textValue,
      url: urlValue,
    });
  };

  const handlePopoverVisibleChange = (visible) => {
    setVisible(visible);
  };

  const handleShapeClick = (shape) => {
    setSelectedShapeId(shape.id);
    setSelectedButton((prevButton) => ({
      ...prevButton,
      shape: shape.component,
    }));
    if (KonvaTag.editButton) {
      KonvaTag.editButton.setAttr('shape', shape.component);
      KonvaTag.editButton.getLayer().batchDraw();
    }
    store.dispatch(
      addShape({
        shapeId: shape.id,
        url: linkInput,
        height: shape.height,
        width: shape.width,
      }),
    );
    console.log('KonvaShape.link', KonvaShape.link);
    console.log('KonvaShape.link', linkInput);
  };
  return (
    <div>
      <div
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          display: 'flex',
        }}
      >
        <Radio.Group
          defaultValue="a"
          buttonStyle="solid"
          style={{
            margin: 10,
            borderRadius: 10,
            // height: '20vh',
            width: '42vw',
            display: 'flex', // Use flex display
            gap: 3, // Set the gap between radio buttons
            justifyContent: 'center', // Center-align the buttons horizontally
          }}
        >
          <Radio.Button
            value="a"
            style={{
              backgroundColor: '#f7f7f7',
              borderRadius: '10px 0px 0px 10px',
              height: '7vh',
              width: '25%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onClick={handleButtonClick}
          >
            <Image
              src={LinkIcon}
              preview={false}
              value="a"
              style={{
                height: '20px',
                cursor: 'pointer',
                color: '#ffffff',
              }}
            />
          </Radio.Button>
          <Radio.Button
            value="b"
            style={{
              backgroundColor: '#f7f7f7',
              width: '25%',
              height: '7vh',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onClick={handleShapesButtonClick}
          >
            <Image
              src={OrnimentsShapes}
              preview={false}
              style={{
                height: '20px',
                color: '#ffffff',
                cursor: 'pointer',
              }}
            />
          </Radio.Button>
          <Radio.Button
            value="c"
            disabled
            style={{
              backgroundColor: '#A9ABBC',
              width: '25%',
              height: '7vh',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Image
              src={IllustrationShapes}
              preview={false}
              style={{
                height: '20px',
                color: '#ffffff',
              }}
            />
          </Radio.Button>
          <Radio.Button
            value="d"
            disabled
            style={{
              backgroundColor: '#A9ABBC',
              borderRadius: '0px 10px 10px 0px',
              height: '7vh',
              width: '25%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Image
              src={AbstractShapes}
              preview={false}
              style={{
                height: '20px',
                color: '#ffffff',
              }}
            />
          </Radio.Button>
        </Radio.Group>
      </div>
      <div
        style={{
          display: 'flex',
          fontSize: 'x-small',
          gap: 15,
          alignItems: 'center',
          justifyContent: 'space-evenly',
          marginTop: -5,
          color: '#040606',
          paddingLeft: '15px',
          paddingRight: '15px',
        }}
      >
        <p style={{ textAlign: 'center' }}>Button</p>
        <p style={{ textAlign: 'center' }}>Shapes</p>
        <p style={{ textAlign: 'center' }}>Illustration</p>
        <p style={{ textAlign: 'center' }}>Abstract</p>
      </div>
      <div>
        {showTextFields && (
          <div>
            <div
              style={{
                backgroundColor: '#0000000d',
                marginTop: '10px',
                padding: '10px',
                borderRadius: '10px',
              }}
            >
              {/* <CloseOutlined
                onClick={handleHideButtonClick}
                style={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                }}
              /> */}
              <Input
                style={{
                  marginTop: '10px',
                  cornRadius: 20,
                  width: '100%',
                }}
                size="middle"
                type="text"
                placeholder="Enter Text"
                value={textValue}
                addonBefore={<FontSizeOutlined />}
                onChange={handleTextChange}
              />
              <Input
                style={{
                  marginTop: '10px',
                  borderRadius: 20,
                  width: '100%',
                }}
                size="middle"
                addonBefore={<LinkOutlined />}
                type="text"
                placeholder="Enter URL"
                value={urlValue}
                onChange={handleUrlChange}
              />
              <div
                style={{
                  marginTop: 10,
                  display: 'flex',
                  justifyContent: 'space-around',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'column',
                  }}
                >
                  <h5 style={{ fontSize: 12, color: '#0a0606' }}>
                    BUTTON COLOR
                  </h5>
                  <Popover
                    content={
                      <SketchPicker
                        color={selectedColor}
                        onChange={handleColorChange}
                      />
                    }
                    trigger="click"
                    visible={visible}
                    onVisibleChange={handlePopoverVisibleChange}
                  >
                    <div style={circleStyle} />
                  </Popover>
                </div>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'column',
                  }}
                >
                  <h5 style={{ fontSize: 12, color: '#0a0606' }}>TEXT COLOR</h5>
                  <Popover
                    content={
                      <SketchPicker
                        color={selectedTextColor}
                        onChange={handleTextColorChange}
                      />
                    }
                    trigger="click"
                    visible={visible2}
                    onVisibleChange={handleTextColorPopover}
                  >
                    <div style={circleStyle1} />
                  </Popover>
                </div>
              </div>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-around',
                }}
              >
                <Button
                  style={{
                    marginTop: '10px',

                    borderRadius: 10,
                    color: '#fff',
                    background:
                      'linear-gradient(137deg, #5087FF 0%, #29EFC4 100%)',
                  }}
                  onClick={handleButtonSelect}
                >
                  ADD BUTTON
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
      <div>
        {showshapes && (
          <div
            style={{
              backgroundColor: '#0000000d',
              marginTop: '10px',
              padding: '5px',
              width: '18vw',
              marginLeft: 0,

              borderRadius: '10px',
            }}
          >
            <h5>Enter Url and select the Shape</h5>
            <Input
              style={{
                marginTop: '10px',
                borderRadius: 10,
                width: '100%',
              }}
              type="text"
              placeholder="Enter the shape URL"
              value={linkInput}
              onChange={handleLinkChange}
            />
            <div
              style={{
                backgroundColor: '#0000000d',
                marginTop: '10px',
                padding: '10px',
                borderRadius: '10px',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  marginTop: '10px',
                  justifyContent: 'space-between',
                }}
              >
                <div>
                  <span>All({availableShapes.length})</span>
                </div>
                <div>
                  <Button
                    type="text"
                    onClick={toggleShowAll}
                    style={{ color: 'blue', background: '0000' }}
                  >
                    {showAllShapes ? 'Show Less' : 'Show All'}
                  </Button>
                </div>
              </div>
              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: '1fr 1fr',
                  gap: '10px',
                }}
              >
                {availableShapes.slice(0, visibleShapes).map((shape) => (
                  <div
                    key={shape.id}
                    onClick={() => handleShapeClick(shape)}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: '10px',
                      cursor: 'pointer',
                    }}
                  >
                    <Stage width={100} height={100}>
                      <Layer>{shape.component}</Layer>
                    </Stage>
                  </div>
                ))}
              </div>
            </div>
            <div
              style={{
                backgroundColor: '#0000000d',
                marginTop: '10px',
                padding: '10px',
                borderRadius: '10px',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  marginTop: '10px',
                  justifyContent: 'space-between',
                }}
              >
                <div>
                  <span>Basic Shapes({basicShapes.length})</span>
                </div>
                <div>
                  <Button
                    type="text"
                    onClick={toggleShowAll1}
                    style={{ color: 'blue', background: '0000' }}
                  >
                    {showbasic ? 'Show Less' : 'Show All'}
                  </Button>
                </div>
              </div>
              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: '1fr 1fr',
                  gap: '10px',
                }}
              >
                {basicShapes.slice(0, visiblebasic).map((shape) => (
                  <div
                    key={shape.id}
                    onClick={() => handleShapeClick(shape)}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: '10px',
                      cursor: 'pointer',
                    }}
                  >
                    <Stage width={100} height={100}>
                      <Layer>{shape.component}</Layer>
                    </Stage>
                  </div>
                ))}
              </div>
            </div>
            <div
              style={{ marginTop: '10px', width: '100%', marginLeft: '10px' }}
            >
              <h5>Select Shape Color</h5>
              <Popover
                content={
                  <SketchPicker
                    color={shapeColor}
                    onChange={handleShapeColorChange}
                  />
                }
                trigger="click"
                visible={visible}
                onVisibleChange={handlePopoverVisibleChange}
              >
                <div style={ShapecircleStyle} />
              </Popover>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
// Upload image drawer content

const UploadI = () => {
  const [uploadedImages, setUploadedImages] = useState([]);
  const dispatch = useDispatch();

  const clearLocalStorage = () => {
    localStorage.removeItem('uploadedImages');
  };

  const handleImageUpload = async (event) => {
    const file = event.target.files[0];

    try {
      const compressedImage = await imageCompression(file, {
        quality: 0.5,
        useWebWorker: true,
      });

      const reader = new FileReader();
      reader.onload = (e) => {
        const imageSrc = e.target.result;

        const storedImages =
          JSON.parse(localStorage.getItem('uploadedImages')) || [];
        storedImages.push(imageSrc);
        localStorage.setItem('uploadedImages', JSON.stringify(storedImages));
        setUploadedImages(storedImages);
        store.dispatch(addUploadImage({ imageSrc }));
      };

      reader.readAsDataURL(compressedImage);
    } catch (error) {
      console.error('Image compression error:', error);
    }
  };

  useEffect(() => {
    const storedImages =
      JSON.parse(localStorage.getItem('uploadedImages')) || [];
    setUploadedImages(storedImages);

    // Attach the clearLocalStorage function to the beforeunload event
    window.addEventListener('beforeunload', clearLocalStorage);

    // Remove the event listener when the component unmounts
    return () => {
      window.removeEventListener('beforeunload', clearLocalStorage);
    };
  }, []);

  return (
    <div>
      <Button
        type="primary"
        icon={<CloudUploadOutlined />}
        block
        onClick={() => {
          document.getElementById('uploadInput').click();
        }}
        style={{
          width: '100%',
          borderRadius: 10,
          // background: 'linear-gradient(137deg, #5087FF 0%, #29EFC4 100%)',
          background: 'rgb(0, 157, 254);',
        }}
      >
        Upload Media (<span style={{ fontSize: 'x-small' }}>PNG, JPG, SVG</span>
        )
      </Button>
      <input
        id="uploadInput"
        type="file"
        accept=".svg, .jpg, .jpeg, .png"
        style={{ display: 'none' }}
        onChange={handleImageUpload}
      />
      <div
        style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '2px' }}
      >
        {uploadedImages.map((imageSrc, index) => (
          <img
            key={imageSrc}
            src={imageSrc}
            alt={`Uploaded ${index}`}
            height={100}
            width={100}
            style={{ margin: 10 }}
          />
        ))}
      </div>
    </div>
  );
};
// Upload image right side drawer content
const initialImageState = {
  // contrast: 0,
  // opacity: 100,
  // brightness: 0,
  // blur: 0,
  // hsl: { saturation: 0, hue: 0, luminance: 0 },
  coordinates: {},
};

const RightSideDrawer = () => {
  const [blendMode, setBlendMode] = useState('normal');
  const [state, setState] = useState(initialImageState);
  const [imageOpacity, setImageOpacity] = useState(100);
  const [brightness, setBrightness] = useState(0);
  const [contrast, setContrast] = useState(0);
  const [blur, setBlur] = useState(0);
  const [saturation, setSaturation] = useState(0);
  const [hue, setHue] = useState(0);
  const [luminance, setLuminance] = useState(0);
  const coordinates = useRef(null);

  const stateHandler = (id, value) => {
    const objId = id.toLowerCase();
    console.log('state handle ', id);
    switch (id) {
      case 'OPACITY':
        setState(() => ({ [objId]: value / 100 }));
        setImageOpacity(value);
        break;
      case 'BRIGHTNESS':
        setState(() => ({ [objId]: value / 100 }));
        setBrightness(value);
        break;
      case 'SATURATION':
        setState(() => ({ [objId]: value / 50 }));
        setSaturation(value);
        break;
      case 'HUE':
        setState(() => ({ [objId]: value }));
        setHue(value);
        break;
      case 'CONTRAST':
        setState(() => ({ [objId]: value }));
        setContrast(value);
        break;
      case 'BLUR':
        setState(() => ({ [objId]: value / 2 }));
        setBlur(value);
        break;
      case 'LUMINANCE':
        setState(() => ({ [objId]: value }));
        setLuminance(value);
        break;
      default:
        setState(() => ({ [objId]: value }));
        break;
    }
  };

  const handleBlendModeChange = (value) => {
    setBlendMode(value);
  };

  const resetHandler = () => {
    const defaultValues = {
      opacity: 100,
      brightness: 0,
      contrast: 0,
      blur: 0,
      saturation: 0,
      hue: 0,
    };

    // Reset the state variables to default values
    setState(defaultValues);
    setImageOpacity(defaultValues.opacity);
    setBlur(defaultValues.blur);
    setBrightness(defaultValues.brightness);
    setContrast(defaultValues.contrast);
    setHue(defaultValues.hue);
    setSaturation(defaultValues.saturation);

    // Apply the default values to the image filters
    if (KonvaUploadImage.editImage !== null) {
      updateFilters(
        KonvaUploadImage.editImage,
        defaultValues.opacity,
        'OPACITY',
      );
      updateFilters(
        KonvaUploadImage.editImage,
        defaultValues.brightness,
        'BRIGHTNESS',
      );
      updateFilters(KonvaUploadImage.editImage, defaultValues.blur, 'BLUR');
      updateFilters(
        KonvaUploadImage.editImage,
        defaultValues.saturation,
        'SATURATION',
      );
      updateFilters(KonvaUploadImage.editImage, defaultValues.hue, 'HUE');
      updateFilters(
        KonvaUploadImage.editImage,
        defaultValues.contrast,
        'CONTRAST',
      );
    }
  };

  const updateFilters = (img, value, id) => {
    const key = id.toLowerCase();
    let prev = img.getAttr(key);
    if (key.includes('align_')) {
      prev = { x: img.getAttr('x'), y: img.getAttr('y') };
    }
    // img.cache();
    img.filters([
      Konva.Filters.Brighten,
      Konva.Filters.HSL,
      Konva.Filters.Blur,
      Konva.Filters.Contrast,
    ]);
    switch (id) {
      case 'OPACITY':
        img.setAttr(`opacity`, value);
        break;
      case 'BRIGHTNESS':
        img.setAttr(`${key}`, value);
        break;
      case 'SATURATION':
        img.setAttr(`${key}`, value);
        break;
      case 'Hue':
        img.setAttr(`${key}`, value);
        break;
      case 'BLUR':
        img.setAttr(`blurRadius`, value);
        break;
      case 'ALIGN_LEFT':
      case 'ALIGN_RIGHT':
      case 'ALIGN_HORIZONTAL':
      case 'ALIGN_VERTICAL':
      case 'ALIGN_TOP':
      case 'ALIGN_BOTTOM':
        img.setAttr(`x`, value.x);
        img.setAttr(`y`, value.y);
        break;

      default:
        img.setAttr(`${key}`, value);
        break;
    }
    KonvaBoard2.undoStack.push({
      element: img,
      attr: key,
      attrValue: value,
      attrprev: prev,
    });
    img.cache();
    console.log('undostack :', KonvaBoard2.undoStack);
  };

  const alignmentHandler = (position) => {
    const absolutePosition = KonvaUploadImage.editImage.absolutePosition();
    const artBoardSize = {
      height: KonvaBoard2.obj.height(),
      width: KonvaBoard2.obj.width(),
    };

    switch (position) {
      case 'ALIGN_LEFT':
        updateFilters(
          KonvaUploadImage.editImage,
          { x: 0, y: absolutePosition.y },
          position,
        );
        break;
      case 'ALIGN_RIGHT':
        updateFilters(
          KonvaUploadImage.editImage,
          {
            x: artBoardSize.width - KonvaUploadImage.editImage.width(),
            y: absolutePosition.y,
          },
          position,
        );
        break;
      case 'ALIGN_CENTER':
        updateFilters(
          KonvaUploadImage.editImage,
          {
            x: (artBoardSize.width - KonvaUploadImage.editImage.width()) / 2,
            y: artBoardSize.height / 2,
          },
          position,
        );
        break;
      case 'ALIGN_HORIZONTAL':
        updateFilters(
          KonvaUploadImage.editImage,
          {
            x: absolutePosition.x,
            y: (artBoardSize.height - KonvaUploadImage.editImage.height()) / 2,
          },
          position,
        );
        break;
      case 'ALIGN_VERTICAL':
        updateFilters(
          KonvaUploadImage.editImage,
          {
            x: (artBoardSize.width - KonvaUploadImage.editImage.width()) / 2,
            y: absolutePosition.y,
          },
          position,
        );
        break;
      case 'ALIGN_TOP':
        updateFilters(
          KonvaUploadImage.editImage,
          { x: absolutePosition.x, y: 0 },
          position,
        );
        break;
      case 'ALIGN_BOTTOM':
        updateFilters(
          KonvaUploadImage.editImage,
          {
            x: absolutePosition.x,
            y: artBoardSize.height - KonvaUploadImage.editImage.height(),
          },
          position,
        );
        break;
      default: {
        const centerCoordinates = { x: 0, y: 0 };
        setState((prev) => ({ ...prev, coordinates: centerCoordinates }));
        break;
      }
    }
  };

  console.log('Stateeee', state);
  useEffect(() => {
    console.log(
      'Stateeee KonvaUploadImage',
      KonvaUploadImage.obj,
      KonvaUploadImage.editImage,
      KonvaBoard2.obj,
    );
    if (KonvaUploadImage.editImage !== null) {
      if (state.opacity || state.opacity === 0)
        updateFilters(KonvaUploadImage.editImage, state.opacity, 'OPACITY');
      if (state.brightness || state.brightness === 0)
        updateFilters(
          KonvaUploadImage.editImage,
          state.brightness,
          'BRIGHTNESS',
        );
      if (state.blur || state.blur === 0)
        updateFilters(KonvaUploadImage.editImage, state.blur, 'BLUR');
      if (state.saturation || state.saturation === 0)
        updateFilters(
          KonvaUploadImage.editImage,
          state.saturation,
          'SATURATION',
        );
      if (state.hue || state.hue === 0)
        updateFilters(KonvaUploadImage.editImage, state.hue, 'HUE');
      if (state.contrast || state.contrast === 0)
        updateFilters(KonvaUploadImage.editImage, state.contrast, 'CONTRAST');
    }
  }, [state]);

  return (
    <div
      style={{
        backgroundColor: '#f7f7f7',
        margin: '0px 0px 5px 5px',
        display: 'flex',
        flexDirection: 'column',
        padding: '5px',
      }}
    >
      <p
        style={{
          color: '#0A0606',
          fontWeight: 'bold',
          fontSize: 12,
        }}
      >
        IMAGE SETTINGS
      </p>
      <div>
        <p style={{ color: '#0a0606', fontSize: 12, fontWeight: 'normal' }}>
          Opacity
        </p>
        <div style={{ display: 'flex' }}>
          <Slider
            trackStyle={{ backgroundColor: '#2A77F4', height: 4 }}
            railStyle={{ height: 3, backgroundColor: '#fff', borderRadius: 4 }}
            min={0}
            max={100}
            step={1}
            value={imageOpacity}
            onChange={(value) => stateHandler('OPACITY', value)}
            style={{ width: '80%' }}
          />
          <InputNumber
            min={0}
            max={100}
            step={1}
            value={imageOpacity}
            onChange={(value) => stateHandler('OPACITY', value)}
            formatter={(value) => `${value}%`}
            parser={(value) => value.replace('%', '')}
            style={{
              marginLeft: 5,
              border: '1px solid #7070701A',
              borderRadius: 4,
              color: '#0a0606',
            }}
          />
        </div>
        <div
          style={{
            display: 'flex',
            marginTop: 10,
            justifyContent: 'space-evenly',
          }}
        >
          <div
            style={{
              display: 'flex',
              gap: 2,
              border: '1px solid #7070701A',
              borderRadius: 4,
            }}
          >
            <Image
              width="4.5vh"
              height="4.5vh"
              preview={false}
              src={left}
              style={{ backgroundColor: '#fff', padding: 4, cursor: 'pointer' }}
              onClick={() => alignmentHandler('ALIGN_LEFT')}
            />
            <Image
              width="4.5vh"
              height="4.5vh"
              preview={false}
              src={middle}
              onClick={() => alignmentHandler('ALIGN_VERTICAL')}
              style={{ backgroundColor: '#fff', padding: 4, cursor: 'pointer' }}
            />
            <Image
              width="4.5vh"
              height="4.5vh"
              preview={false}
              src={right}
              onClick={() => alignmentHandler('ALIGN_RIGHT')}
              style={{ backgroundColor: '#fff', padding: 4, cursor: 'pointer' }}
            />
          </div>
          <div
            style={{
              display: 'flex',
              gap: 2,

              border: '1px solid #7070701A',
              borderRadius: 4,
            }}
          >
            <Image
              width="4.5vh"
              height="4.5vh"
              preview={false}
              src={Topalign}
              onClick={() => alignmentHandler('ALIGN_TOP')}
              style={{ backgroundColor: '#fff', padding: 5, cursor: 'pointer' }}
            />
            <Image
              width="4.5vh"
              height="4.5vh"
              preview={false}
              src={Vertical}
              onClick={() => alignmentHandler('ALIGN_HORIZONTAL')}
              style={{ backgroundColor: '#fff', padding: 5, cursor: 'pointer' }}
            />
            <Image
              width="4.5vh"
              height="4.5vh"
              preview={false}
              src={bottom}
              onClick={() => alignmentHandler('ALIGN_BOTTOM')}
              style={{ backgroundColor: '#fff', padding: 5, cursor: 'pointer' }}
            />
          </div>
          {/* <div
            style={{
              marginLeft: 5,
              border: '1px solid #7070701A',
              borderRadius: 4,
            }}
          >
            <Image
              width="4.5vh"
              height="4.5vh"
              preview={false}
              src={Grid}
              style={{ backgroundColor: '#fff', padding: 6 }}
            />
          </div> */}
        </div>
        {/* <div style={{ marginTop: 10 }}>
          <p style={{ color: '#0A0606', fontSize: 12, fontWeight: 'normal' }}>
            Blending Mode
          </p>
          <Select
            value={blendMode}
            onChange={handleBlendModeChange}
            size="small"
            style={{
              width: '100%',
              border: '1px solid #7070701A',
              borderRadius: 4,
            }}
          >
            <Option value="normal">Normal</Option>
            <Option value="dissolve">Dissolve</Option>
            <Option value="behind">Behind</Option>
            <Option value="clear">Clear</Option>
          </Select>
        </div>
        <div
          style={{
            display: 'flex',
            backgroundColor: '#fff',
            border: '1px solid #7070701A',
            borderRadius: 4,
            marginTop: 8,
          }}
        >
          <Image width="7vh" height="7vh" src={Bgremoval} preview={false} />
          <div>
            <p
              style={{
                color: '#0A0606',
                fontWeight: 'bold',
                padding: '5px 0px 0px 5px',
                fontSize: 12,
              }}
            >
              AI Background Remover
            </p>

            <p style={{ color: '#0A0606', fontSize: 10, paddingLeft: 5 }}>
              Click to remove image background
            </p>
          </div>
        </div> */}
      </div>
      <Divider style={{ margin: '10px 0px 10px 0px' }} />
      <p
        style={{
          color: '#0A0606',
          fontWeight: 'bold',
          fontSize: 12,
        }}
      >
        ADJUSTMENTS
      </p>
      <p style={{ color: '#0a0606', fontSize: 12, fontWeight: 'normal' }}>
        Brightness
      </p>
      <div style={{ display: 'flex' }}>
        <Slider
          trackStyle={{ backgroundColor: '#2A77F4', height: 4 }}
          railStyle={{ height: 3, backgroundColor: '#fff', borderRadius: 4 }}
          min={0}
          max={100}
          value={brightness}
          onChange={(value) => stateHandler('BRIGHTNESS', value)}
          style={{ width: '80%' }}
        />
        <InputNumber
          min={0}
          max={100}
          value={brightness}
          onChange={(value) => stateHandler('BRIGHTNESS', value)}
          formatter={(value1) => `${value1}%`}
          parser={(value1) => value1.replace('%', '')}
          style={{
            marginLeft: 5,
            border: '1px solid #7070701A',
            borderRadius: 4,
            color: '#0a0606',
          }}
        />
      </div>
      <p style={{ color: '#0a0606', fontSize: 12, fontWeight: 'normal' }}>
        Saturation
      </p>
      <div style={{ display: 'flex' }}>
        <Slider
          trackStyle={{ backgroundColor: '#2A77F4', height: 4 }}
          railStyle={{ height: 3, backgroundColor: '#fff', borderRadius: 4 }}
          min={0}
          max={100}
          value={saturation}
          onChange={(value) => stateHandler('SATURATION', value)}
          style={{ width: '80%' }}
        />
        <InputNumber
          min={0}
          max={100}
          value={saturation}
          onChange={(value) => stateHandler('SATURATION', value)}
          formatter={(value1) => `${value1}%`}
          parser={(value1) => value1.replace('%', '')}
          style={{
            marginLeft: 5,
            border: '1px solid #7070701A',
            borderRadius: 4,
            color: '#0a0606',
          }}
        />
      </div>
      <p style={{ color: '#0a0606', fontSize: 12, fontWeight: 'normal' }}>
        Hue
      </p>
      <div style={{ display: 'flex' }}>
        <Slider
          trackStyle={{ backgroundColor: '#2A77F4', height: 4 }}
          railStyle={{ height: 3, backgroundColor: '#fff', borderRadius: 4 }}
          min={0}
          max={100}
          value={hue}
          onChange={(value) => stateHandler('HUE', value)}
          style={{ width: '80%' }}
        />
        <InputNumber
          min={0}
          max={100}
          value={hue}
          onChange={(value) => stateHandler('HUE', value)}
          formatter={(value1) => `${value1}%`}
          parser={(value1) => value1.replace('%', '')}
          style={{
            marginLeft: 5,
            border: '1px solid #7070701A',
            borderRadius: 4,
            color: '#0a0606',
          }}
        />
      </div>

      <p style={{ color: '#0a0606', fontSize: 12, fontWeight: 'normal' }}>
        Contrast
      </p>
      <div style={{ display: 'flex' }}>
        <Slider
          trackStyle={{ backgroundColor: '#2A77F4', height: 4 }}
          railStyle={{ height: 3, backgroundColor: '#fff', borderRadius: 4 }}
          min={0}
          max={100}
          value={contrast}
          onChange={(value) => stateHandler('CONTRAST', value)}
          style={{ width: '80%' }}
        />
        <InputNumber
          min={0}
          max={100}
          value={contrast}
          onChange={(value) => stateHandler('CONTRAST', value)}
          formatter={(value1) => `${value1}%`}
          parser={(value1) => value1.replace('%', '')}
          style={{
            marginLeft: 5,
            border: '1px solid #7070701A',
            borderRadius: 4,
            color: '#0a0606',
          }}
        />
      </div>
      <p style={{ color: '#0a0606', fontSize: 12, fontWeight: 'normal' }}>
        Blur
      </p>
      <div style={{ display: 'flex' }}>
        <Slider
          trackStyle={{ backgroundColor: '#2A77F4', height: 4 }}
          railStyle={{ height: 3, backgroundColor: '#fff', borderRadius: 4 }}
          min={0}
          max={100}
          value={blur}
          onChange={(value) => stateHandler('BLUR', value)}
          style={{ width: '80%' }}
        />
        <InputNumber
          min={0}
          max={100}
          value={blur}
          onChange={(value) => stateHandler('BLUR', value)}
          formatter={(value1) => `${value1}%`}
          parser={(value1) => value1.replace('%', '')}
          style={{
            marginLeft: 5,
            border: '1px solid #7070701A',
            borderRadius: 4,
            color: '#0a0606',
          }}
        />
      </div>
      <Button
        size="small"
        style={{
          background: '#FFFFFF 0% 0% no-repeat padding-box',
          border: '1px solid #2A77F4',
          borderRadius: 4,
          color: '#2A77F4',
          fontSize: 14,
          margin: 10,
        }}
        onClick={resetHandler}
      >
        Reset Settings
      </Button>
    </div>
  );
};
const initialTextState = {};
const RightSideTextDrawer = () => {
  const [visible, setVisible] = useState(false);
  const [visible1, setvisible1] = useState(false);
  const [state, setState] = useState(initialTextState);
  const [textColor, setTextColor] = useState('black');
  const [textOpacity, setTextOpacity] = useState(1);
  const [strokeColor, setStrokeColor] = useState('black');
  const [strokeWidth, setStrokeWidth] = useState(0);
  const [fontFamily, setFontFamily] = useState('Arial');
  const [fontStyle, setFontStyle] = useState('normal');
  const [fontSize, setFontSize] = useState(24);
  const [letterSpacing, setLetterSpacing] = useState(0);

  const stateHandler = (id, value) => {
    const objId = id.toLowerCase();
    console.log('state handle ', id);
    switch (id) {
      case 'OPACITY':
        setState(() => ({ [objId]: value }));
        setTextOpacity(value);
        break;
      case 'FILL':
        setState(() => ({ [objId]: value }));
        setTextColor(value);
        break;
      case 'STROKE':
        setState(() => ({ [objId]: value }));
        setStrokeColor(value);
        break;
      case 'STROKEWIDTH':
        setState(() => ({ [objId]: value / 100 }));
        setStrokeWidth(value);
        break;
      case 'FONTFAMILY':
        setState(() => ({ [objId]: value }));
        setFontFamily(value);
        break;
      case 'FONT_STYLE':
        setState(() => ({ [objId]: value }));
        setFontStyle(value);
        break;
      case 'FONT_SIZE':
        setState(() => ({ [objId]: value }));
        setFontSize();
        break;
      case 'LETTER_SPACING':
        setState(() => ({ [objId]: value }));
        setLetterSpacing(value);
        break;
      default:
        setState(() => ({}));
        break;
    }
    // KonvaBoard2.undoStack.push({ id, value });
  };

  const updateAlignments = (text, value, id) => {
    let key = id.toLowerCase();
    let prev = text.getAttr(key);
    if (
      key === 'left' ||
      key === 'right' ||
      key === 'center' ||
      key === 'justify'
    ) {
      value = key;
      key = 'align';
      prev = text.getAttr(key);
    }
    if (key.includes('align_')) {
      prev = { x: text.getAttr('x'), y: text.getAttr('y') };
    } else if (key === 'strokewidth') {
      key = 'strokeWidth';
      prev = text.getAttr(key);
    } else if (key === 'fontfamily') {
      key = 'fontFamily';
      prev = text.getAttr(key);
    } else if (key === 'font_style') {
      key = 'fontStyle';
      prev = text.getAttr(key);
    } else if (key === 'font_size') {
      key = 'fontSize';
      prev = text.getAttr(key);
    } else if (key === 'letter_spacing') {
      key = 'letterSpacing';
      prev = text.getAttr(key);
    }
    console.log('applying attrs id:', id);
    console.log('applying attrs value:', value);
    switch (id) {
      case 'FILL':
        text.setAttr(`fill`, value);
        break;
      case 'OPACITY':
        text.setAttr(`opacity`, value);
        break;
      case 'STROKE':
        text.setAttr(`stroke`, value);
        break;
      case 'STROKEWIDTH':
        text.setAttr(`strokeWidth`, value);
        break;
      case 'FONTFAMILY':
        text.setAttr(`fontFamily`, value);
        break;
      case 'FONT_STYLE':
        text.fontStyle(value);
        text.draw();
        break;
      case 'FONT_SIZE':
        text.setAttr(`fontSize`, value);
        break;
      case 'LETTER_SPACING':
        text.setAttr(`letterSpacing`, value);
        break;
      case 'ALIGN_LEFT':
      case 'ALIGN_RIGHT':
      case 'ALIGN_HORIZONTAL':
      case 'ALIGN_VERTICAL':
      case 'ALIGN_TOP':
      case 'ALIGN_BOTTOM':
        text.setAttr(`x`, value.x);
        text.setAttr(`y`, value.y);
        break;
      case 'LEFT':
        text.setAttr(`align`, 'left');
        break;
      case 'CENTER':
        text.setAttr(`align`, 'center');
        break;
      case 'RIGHT':
        text.setAttr(`align`, `right`);
        break;
      case 'JUSTIFY':
        text.setAttr(`align`, 'justify');
        break;
      default:
        text.setAttr(`${key}`, value);
        break;
    }
    KonvaBoard2.undoStack.push({
      element: text,
      attr: key,
      attrValue: value,
      attrprev: prev,
    });
    text.cache();
    console.log(KonvaBoard2.undoStack);
  };
  const alignmentHandler = (position) => {
    if (KonvaText.editText) {
      const absolutePosition = KonvaText.editText.absolutePosition();
      const artBoardSize = {
        height: KonvaBoard2.obj.height(),
        width: KonvaBoard2.obj.width(),
      };

      switch (position) {
        case 'ALIGN_LEFT':
          updateAlignments(
            KonvaText.editText,
            {
              x: 8,
              y: Math.max(0, Math.min(artBoardSize.height, absolutePosition.y)),
            },
            position,
          );
          break;
        case 'ALIGN_RIGHT':
          updateAlignments(
            KonvaText.editText,
            {
              x: Math.max(
                0,
                Math.min(artBoardSize.width - KonvaText.editText.width() - 8),
              ),
              y:
                Math.max(0, Math.min(artBoardSize.height, absolutePosition.y)) -
                0,
            },
            position,
          );
          break;
        case 'ALIGN_HORIZONTAL':
          updateAlignments(
            KonvaText.editText,
            {
              // x: Math.max(0, Math.min(artBoardSize.width, absolutePosition.x)),
              // y: artBoardSize.height / 2,
              x: absolutePosition.x,
              y: (artBoardSize.height - KonvaText.editText.height()) / 2,
            },
            position,
          );
          break;
        case 'ALIGN_VERTICAL':
          updateAlignments(
            KonvaText.editText,
            {
              // x: artBoardSize.width / 4,
              // y: Math.max(0, Math.min(artBoardSize.height, absolutePosition.y)),
              x: (artBoardSize.width - KonvaText.editText.width()) / 2,
              y: absolutePosition.y,
            },
            position,
          );
          break;
        case 'ALIGN_TOP':
          updateAlignments(
            KonvaText.editText,
            {
              x: Math.max(
                0,
                Math.min(
                  artBoardSize.width - KonvaText.editText.width(),
                  absolutePosition.x,
                ),
              ),
              y: 8,
            },
            position,
          );
          break;
        case 'ALIGN_BOTTOM':
          updateAlignments(
            KonvaText.editText,
            {
              x: Math.max(0, Math.min(artBoardSize.width, absolutePosition.x)),
              y: Math.max(
                0,
                Math.min(artBoardSize.height - KonvaText.editText.height() - 8),
              ),
            },
            position,
          );
          break;
        default: {
          const centerCoordinates = { x: 0, y: 0 };
          setState((prev) => ({ ...prev, coordinates: centerCoordinates }));
          break;
        }
      }
    }
  };

  useEffect(() => {
    console.log(
      'Stateeee KonvaText',
      state,
      KonvaText.editText,
      KonvaBoard2.obj,
    );
    if (KonvaText.editText !== null) {
      if (state.fill) updateAlignments(KonvaText.editText, state.fill, 'FILL');
      if (state.stroke)
        updateAlignments(KonvaText.editText, state.stroke, 'STROKE');
      if (state.opacity)
        updateAlignments(KonvaText.editText, state.opacity, 'OPACITY');
      if (state.strokewidth)
        updateAlignments(KonvaText.editText, state.strokewidth, 'STROKEWIDTH');
      if (state.fontfamily)
        updateAlignments(KonvaText.editText, state.fontfamily, 'FONTFAMILY');
      if (state.font_style)
        updateAlignments(KonvaText.editText, state.font_style, 'FONT_STYLE');
      if (state.font_size)
        updateAlignments(KonvaText.editText, state.font_size, 'FONT_SIZE');

      if (state.letter_spacing)
        updateAlignments(
          KonvaText.editText,
          state.letter_spacing,
          'LETTER_SPACING',
        );
    }
  }, [state]);
  console.log('venkat:', state);

  const handlePopoverVisibleChange = (visible) => {
    setVisible(visible);
  };
  const handlePopoverVisibleChange1 = (visible1) => {
    setvisible1(visible1);
  };

  const circleStyle = {
    height: 25,
    width: 25,
    borderRadius: '50%',
    backgroundColor: textColor,
    cursor: 'pointer',
    padding: '0px',
  };

  const circleStyle1 = {
    height: 20,
    width: 20,
    borderRadius: '50%',
    backgroundColor: strokeColor,
    cursor: 'pointer',
    padding: '10px',
  };

  const { Option } = Select;

  const FontSelect = () => {
    const [searchValue, setSearchValue] = useState('');
    const handleSearchChange = (value) => {
      setSearchValue(value);
    };

    const filteredOptions = options.filter((option) =>
      option.toLowerCase().includes(searchValue.toLowerCase()),
    );

    const displayedOptions = options;

    return (
      <Select
        value={fontFamily}
        onChange={(value) => {
          stateHandler('FONTFAMILY', value);
        }}
        showSearch
        dropdownStyle={{ height: '130px' }}
        onSearch={handleSearchChange}
        style={{ width: '70%', marginTop: '10px' }}
      >
        {displayedOptions.map((option) => (
          <Option key={option} value={option}>
            {option}
          </Option>
        ))}
      </Select>
    );
  };

  // Define the options array outside the component
  const options = [
    'Arial',
    'Georgia',
    'Times New Roman',
    'Garamond',
    'Courier New',
    'Brush Script MT',
    'Copperplate',
    'cursive',
    'system-ui',
    'Palatino',
    'Impact',
    'Gill Sans',
    'Lucida Console',
    'Monaco',
    'Comic Sans MS',
    'Didot',
    'Papyrus',
    'Trattatello',
  ];

  return (
    <div style={{ padding: 2 }}>
      <div style={{ backgroundColor: '#E8E8E8', paddingBottom: 5 }}>
        {/* this div is for both text color and opacity */}
        <div
          style={{
            display: 'flex',
            padding: '5px',
            justifyContent: 'space-between',
            alignContent: 'center',
          }}
        >
          {/* this div is for text color */}
          <div
            style={{
              fontWeight: 600,
              fontSize: '12px',
              marginLeft: '8px',
              color: '#0A0606',
            }}
          >
            TEXT COLOR
            <br />
            <Popover
              content={
                <SketchPicker
                  color={textColor}
                  onChange={(color) => stateHandler('FILL', color.hex)}
                />
              }
              trigger="click"
              visible={visible}
              onVisibleChange={handlePopoverVisibleChange}
            >
              <div style={circleStyle} />
            </Popover>
          </div>

          {/* this div is for opacity */}
          <div
            style={{
              display: 'flex',
              padding: '4px',
              textAlign: 'center',
              marginTop: '14px',
            }}
          >
            <p
              style={{
                fontSize: 'small',
                opacity: 5,
                textAlign: 'center',
                paddingRight: '3px',
              }}
            >
              Opacity
            </p>
            <InputNumber
              min={0}
              max={100}
              step={1}
              value={textOpacity * 100} // Convert to percentage
              formatter={(value) => `${value}%`}
              parser={(value) => value.replace('%', '')}
              onChange={(value) => stateHandler('OPACITY', value / 100)} // Convert back to decimal
              style={{
                marginLeft: 5,
                height: 25,
                width: 70,
                border: '1px solid #7070701A',
                borderRadius: 4,
                color: '#0A0606',
                backgroundColor: '#fff',
                opacity: 1,
                marginTop: -4,
                fontSize: 12,
                padding: '0px 2px 4px 2px',
              }}
            />
          </div>
        </div>
        {/* this div is for border weight and slider InputNumber  */}
        <div style={{ display: 'flex', marginLeft: 15, marginTop: 15 }}>
          <div>
            <Popover
              content={
                <SketchPicker
                  color={strokeColor}
                  onChange={(color) => stateHandler('STROKE', color.hex)}
                />
              }
              trigger="click"
              visible={visible1}
              onVisibleChange={handlePopoverVisibleChange1}
            >
              <div style={circleStyle1} />
            </Popover>
          </div>

          <div style={{ marginLeft: '15px' }}>
            <p style={{ fontSize: 10, paddingBottom: -20, color: '#0A0606' }}>
              Border Weight
            </p>
            <Slider
              min={0}
              max={100}
              value={strokeWidth}
              style={{ width: '150%', margin: '4px 2px 4px 4px' }}
              onChange={(value) => stateHandler('STROKEWIDTH', value)}
              trackStyle={{ backgroundColor: '#2A77F4', height: 2 }}
              railStyle={{
                boxShadow: '0px 0px 6px #00000029',
                height: 2,
              }}
            />
          </div>
          <div
            style={{
              display: 'flex-end',
              marginLeft: '50px',
            }}
          >
            <InputNumber
              min={0}
              max={100}
              value={strokeWidth}
              onChange={(value) => stateHandler('STROKEWIDTH', value)}
              formatter={(value) => `${value}%`}
              parser={(value) => value.replace(`${value} %`, '')}
              style={{
                height: 25,
                width: 67,
                border: '1px solid #7070701A',
                borderRadius: 4,
                color: '#0A0606',
                backgroundColor: '#fff',
                opacity: 1,
                marginTop: 4,
                fontSize: 12,
                marginRight: 6,
                padding: '0px 0px 4px 2px',
              }}
            />
          </div>
        </div>
        {/* this div is for alignment */}
        <div
          style={{
            display: 'flex',
            marginLeft: 20,
            gap: 2,
            marginTop: 10,
          }}
        >
          <div
            style={{
              display: 'flex',
              gap: 1,
              border: '1px solid #7070701A',
              borderRadius: 4,
            }}
          >
            <Image
              width={26}
              height={26}
              src={left}
              preview={false}
              onClick={() => alignmentHandler('ALIGN_LEFT')}
              style={{
                padding: 4,
                backgroundColor: '#fff',
                borderRadius: '5px 0px 0px 5px',
                cursor: 'pointer',
                color: '#0A0606',
              }}
            />
            <Image
              width={26}
              height={26}
              preview={false}
              src={middle}
              onClick={() => alignmentHandler('ALIGN_VERTICAL')}
              style={{
                backgroundColor: '#fff',
                padding: 4,
                cursor: 'pointer',
                color: '#0A0606',
              }}
            />
            <Image
              width={26}
              height={26}
              src={right}
              preview={false}
              onClick={() => alignmentHandler('ALIGN_RIGHT')}
              style={{
                backgroundColor: '#fff',
                padding: 4,
                borderRadius: '0px 5px 5px 0px',
                backgroundColor: '#fff',
                cursor: 'pointer',
                color: '#0A0606',
              }}
            />
          </div>
          <div
            style={{
              display: 'flex',
              gap: 1,
              border: '1px solid #7070701A',
              cursor: 'pointer',

              marginLeft: 2,
            }}
          >
            <Image
              width={26}
              height={26}
              src={Topalign}
              preview={false}
              onClick={() => alignmentHandler('ALIGN_TOP')}
              style={{
                backgroundColor: '#fff',
                padding: 5,
                borderRadius: '5px 0px 0px 5px',
                backgroundColor: '#fff',
                color: '#0A0606',
              }}
            />
            <Image
              width={26}
              height={26}
              src={Vertical}
              preview={false}
              onClick={() => alignmentHandler('ALIGN_HORIZONTAL')}
              style={{
                backgroundColor: '#fff',
                padding: 5,
                color: '#0A0606',
              }}
            />
            <Image
              width={26}
              height={26}
              src={bottom}
              preview={false}
              onClick={() => alignmentHandler('ALIGN_BOTTOM')}
              style={{
                backgroundColor: '#fff',
                padding: 5,
                borderRadius: '0px 5px 5px 0px',
                color: '#0A0606',
              }}
            />
          </div>
          {/* <div
            style={{
              marginLeft: 2,
              border: '1px solid #7070701A',
              borderRadius: 4,
            }}
          >
            <Image
              width={26}
              height={26}
              src={Grid}
              preview={false}
              style={{
                backgroundColor: '#fff',
                padding: 6,
                borderRadius: 5,
                cursor: 'pointer',
                color: '#0A0606',
              }}
            />
          </div> */}
        </div>
        <div style={{ margin: '15px 15px 15px 15px' }}>
          <Divider style={{ width: '100% ' }} />
        </div>
        <div
          style={{
            fontWeight: '600',
            textAlign: 'start',
            marginLeft: 15,
            marginTop: -15,
            color: '#0A0606',
          }}
        >
          TEXT SETTINGS
        </div>
        <div style={{ marginLeft: 10 }}>
          <div>
            <FontSelect />
            <Select
              value={fontStyle}
              onChange={(value) => stateHandler('FONT_STYLE', value)}
              defaultValue="normal"
              style={{
                width: '40%',
                fontSize: 'x-small',
                margin: '10px 0px 5px 4px',
                border: '1px solid #7070701A',
                borderRadius: '4px',
              }}
            >
              <Option value="normal">normal</Option>
              <Option value="bold">bold</Option>
              <Option value="bolder">bolder</Option>
              <Option value="lighter">lighter</Option>
              <Option value="1000">x-large</Option>
            </Select>
          </div>
          <div
            style={{
              display: 'flex',
              margin: 5,
              gap: 15,
            }}
          >
            <div
              style={{
                display: 'flex',
                gap: 1,
                border: '1px solid #7070701A',
                borderRadius: 4,
              }}
            >
              <Image
                width={30}
                height={23}
                src={textTt}
                preview={false}
                onClick={() => {}}
                style={{
                  backgroundColor: '#fff',
                  padding: 4,
                  borderRadius: '5px 0px 0px 5px',
                  cursor: 'pointer',
                }}
              />
              <InputNumber
                min={0}
                max={100}
                value={fontSize}
                onChange={(value) => stateHandler('FONT_SIZE', value)}
                defaultValue={24}
                style={{
                  backgroundColor: '#fff',
                  padding: 0,
                  width: 55,
                  height: 25,
                  left: 0,
                  top: 0,
                }}
              />
            </div>
            <div
              style={{
                display: 'flex',
                gap: 1,
                // border: '1px solid #7070701A',
              }}
            >
              <Image
                width={30}
                height={23}
                src={av}
                preview={false}
                onClick={() => {}}
                style={{
                  backgroundColor: '#fff',
                  padding: 5,
                  borderRadius: '5px 0px 0px 5px',
                  cursor: 'pointer',
                }}
              />
              <InputNumber
                value={letterSpacing}
                min={0}
                max={100}
                defaultValue={0}
                onChange={(value) => {
                  stateHandler('LETTER_SPACING', value);
                }}
                style={{
                  backgroundColor: '#fff',
                  padding: 0,
                  borderRadius: '0px 5px 5px 0px',
                  width: 55,
                  height: 25,
                }}
              />
            </div>
          </div>
          <div
            style={{
              display: 'flex',
              gap: 1,
              marginLeft: '5px',
              // border: '1px solid #7070701A',
            }}
          >
            {/* <Image
              width={30}
              height={23}
              src={Grid}
              preview={false}
              onClick={() => {}}
              style={{
                backgroundColor: '#fff',
                padding: 4,
                borderRadius: '5px 0px 0px 5px',
                cursor: 'pointer',
              }}
            />
            <InputNumber
              onChange={() => {}}
              style={{
                backgroundColor: '#fff',
                padding: 4,
                borderRadius: '0px 5px 5px 0px',
                width: 55,
                height: 25,
              }}
            /> */}
          </div>
          <div
            style={{
              display: 'flex',
              margin: 5,
              gap: 1,
              marginLeft: 6,
            }}
          >
            <div
              style={{
                display: 'flex',
                gap: 1,
                border: '1px solid #7070701A',
                borderRadius: 4,
                cursor: 'pointer',
              }}
            >
              <Image
                width={27}
                height={27}
                src={leftAlign}
                preview={false}
                onClick={() => {
                  stateHandler('LEFT', 'left');
                  updateAlignments(KonvaText.editText, state.align, 'LEFT');
                }}
                style={{
                  backgroundColor: '#fff',
                  padding: 4,
                  borderRadius: '5px 0px 0px 5px',
                }}
              />
              <Image
                width={27}
                height={27}
                src={CenterAlign}
                preview={false}
                onClick={() => {
                  stateHandler('CENTER', 'center');
                  updateAlignments(KonvaText.editText, state.align, 'CENTER');
                }}
                style={{ backgroundColor: '#fff', padding: 4 }}
              />
              <Image
                width={27}
                height={27}
                src={rightAlign}
                preview={false}
                onClick={() => {
                  stateHandler('RIGHT', 'right');
                  updateAlignments(KonvaText.editText, state.align, 'RIGHT');
                }}
                style={{
                  backgroundColor: '#fff',
                  padding: 4,
                }}
              />
              <Image
                width={27}
                height={27}
                src={linesVertical}
                preview={false}
                onClick={() => {
                  stateHandler('JUSTIFY', 'justify');
                  updateAlignments(KonvaText.editText, state.align, 'JUSTIFY');
                }}
                style={{
                  backgroundColor: '#fff',
                  padding: 5,
                }}
              />
              <Image
                width={27}
                height={27}
                preview={false}
                onClick={() => {}}
                src={TT}
                style={{ backgroundColor: '#fff', padding: 5 }}
              />
              <Image
                width={27}
                height={27}
                src={fI}
                preview={false}
                onClick={() => {}}
                style={{
                  backgroundColor: '#fff',
                  padding: 5,
                  borderRadius: '0px 5px 5px 0px',
                }}
              />
            </div>
            <div
              style={{
                marginLeft: 4,
                border: '1px solid #7070701A',
                borderRadius: 4,
              }}
            >
              <Image
                width={27}
                height={27}
                preview={false}
                onClick={() => {}}
                src={A}
                style={{
                  backgroundColor: '#fff',
                  padding: 6,
                  borderRadius: 5,
                  cursor: 'pointer',
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const TemplateContent = () => {
  const [savedData, setSavedData] = useState([]);
  // const [imageDataUrls, setImageDataUrls] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [importJson, setImportJson] = useState('');
  const [showAll, setShowAll] = useState(false);
  const numTemplatesToShow = showAll ? savedData.length : 4;

  useEffect(() => {
    setLoading(true);
    templateData();
  }, []);

  const templateData = async () => {
    try {
      const response = await getApiData('ds-templates/getAll', {}, 'GET');
      if (response.success && Array.isArray(response.data)) {
        await processResponse(response.data);
        console.log('response.data', response.data);
      }
      setLoading(false);
    } catch (error) {
      console.error(error);
      // Handle error state here (e.g., display an error message to the user)
      setLoading(true);
    }
  };

  const processResponse = async (data) => {
    try {
      const fetchDataPromises = data.map(async (item) => {
        const response = await fetch(item.stage_json);

        if (response.ok) {
          const json = await response.json();
          item.url = json.attrs?.stageImg;
        } else {
          console.error('Failed to fetch JSON data from URL:', response.status);
          item.url = null;
        }
      });

      // Wait for all promises to complete
      await Promise.all(fetchDataPromises);

      // Update the state with the modified data
      setSavedData(data);
      console.log('Data with URLs:', data);
    } catch (error) {
      console.error('Error fetching JSON data:', error);
    }
  };

  useEffect(() => {
    templateData();
  }, []);

  const handleImport = (savedTemp) => {
    console.log('Import', savedTemp);
    KonvaBoard2.fromJSON(savedTemp);
  };

  const handleItemClick = async (item) => {
    try {
      // Fetch the JSON content from the URL
      const response = await fetch(item.stage_json);

      if (response.ok) {
        const json = await response.json();
        // Call the handleImport function with the fetched JSON
        console.log(json, 'this is stage_json');
        const mydata = json.stageImg;
        setImportJson(mydata);
        handleImport(json);
      } else {
        console.error('Failed to fetch JSON data from URL:', response.status);
      }
    } catch (error) {
      console.error('Error fetching JSON data:', error);
    }
  };

  return (
    <div>
      <Input
        style={{ borderRadius: 2, marginTop: 5 }}
        prefix={
          <SearchOutlined
            style={{
              fontSize: 12,
              border: 'none',
              margin: 0,
              paddingRight: 5,
            }}
          />
        }
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
      />
      <div style={{ backgroundColor: '#0000000D', borderRadius: 10 }}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            marginTop: 10,
          }}
        >
          <h1
            style={{
              fontSize: 11,
              color: '#0A0606',
              padding: 7,
              fontWeight: 'bold',
            }}
          >
            SAVED TEMPLATES
          </h1>
          {savedData.length > 4 && (
            <Button
              onClick={() => setShowAll(!showAll)}
              style={{ border: 'none' }}
              type="text"
            >
              {showAll ? 'Show Less' : 'Show All'}
            </Button>
          )}
        </div>
        {loading ? (
          <Skeleton active round={true} style={{ marginTop: '20px' }} />
        ) : (
          <div>
            <List
              style={{ marginTop: '20px' }}
              dataSource={savedData
                .filter((item) =>
                  item.name.toLowerCase().includes(searchQuery.toLowerCase()),
                )
                .slice(0, numTemplatesToShow)} // Display only a subset of templates
              grid={{ gutter: 16, column: 2 }}
              renderItem={(item, index) => (
                <List.Item>
                  <div
                    className="savedTemplatesContainer"
                    onClick={() => handleItemClick(item)}
                    style={{ cursor: 'pointer' }}
                  >
                    <div
                      className="savedTemplates"
                      style={{
                        backgroundImage: `url(${item.url})`,
                        backgroundSize: 'cover',
                        width: 100,
                        height: 100,
                        border: '1px solid gray',
                      }}
                    />
                    <div>{item.name}</div>
                  </div>
                </List.Item>
              )}
            />
          </div>
        )}
      </div>
    </div>
  );
};

// Text drawer content

const Text = (setSelectedRightsideTextDrawer) => {
  const heading = 'Heading';
  const paragraph = 'paragraph';

  const [isExpanded, setIsExpanded] = useState(false);

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
  };

  // const [textValue, setTextValue] = useState('');
  const handleAddHeading = () => {
    store.dispatch(addText(heading, setSelectedRightsideTextDrawer));
  };
  const handleAddparagraph = () => {
    store.dispatch(addParagraph({ paragraph }));
  };
  const handleAddparagraph1 = () => {
    store.dispatch(addParagraph1({ paragraph }));
  };
  const handleAddparagraph2 = () => {
    store.dispatch(addParagraph2({ paragraph }));
  };
  const handleAddparagraph3 = () => {
    store.dispatch(addParagraph3({ paragraph }));
  };
  const handleAddparagraph4 = () => {
    store.dispatch(addParagraph4({ paragraph }));
  };
  const handleAddTxt = () => {
    store.dispatch(addTextImg());
  };
  const handleAddTxt1 = () => {
    store.dispatch(addTextSampleImg());
  };

  return (
    <div>
      <div>
        <Button
          type="primary"
          onClick={handleAddHeading}
          icon={
            <Image
              src={TextT}
              preview={false}
              onClick={() => {}}
              style={{
                cursor: 'pointer',
                paddingRight: '10px',
                height: '13px',
              }}
            />
          }
          // size="small"
          style={{
            width: '100%',
            height: '35px',
            borderRadius: 10,
            // background: 'linear-gradient(137deg, #5087FF 0%, #29EFC4 100%)',
            background: 'rgb(0, 157, 254);',
          }}
        >
          Add Heading
        </Button>
      </div>
      <div>
        <Button
          type="primary"
          onClick={handleAddparagraph}
          icon={<AlignLeftOutlined />}
          // size="small"
          style={{
            marginTop: '10px',
            width: '100%',
            height: '35px',
            borderRadius: 10,
            // background: 'linear-gradient(137deg, #5087FF 0%, #29EFC4 100%)',
            background: 'rgb(0, 157, 254);',
          }}
        >
          Add Paragraph
        </Button>
      </div>
      <div
        style={{
          marginTop: '20px',
          width: '100%',
          backgroundColor: '#0000000D',
          padding: '10px',
          borderRadius: '10px',
        }}
      >
        <div
          style={{
            display: 'flex',
            width: '100%',
            justifyContent: 'space-between',
          }}
        >
          <h5
            style={{
              textAlign: 'left',
              color: '#0A0606',
            }}
          >
            Vintage
          </h5>
          {/* <Button type="link" style={{ marginTop: '-10px' }}>
            Show All
          </Button> */}
        </div>
        <div
          style={{
            display: 'flex',
            marginTop: '10px',
            justifyContent: 'space-between',
            // padding: '10px',
          }}
        >
          <Image
            src={FontCard}
            style={{ cursor: 'pointer', width: '8vw', height: '16vh' }}
            preview={false}
            onClick={handleAddTxt}
          />
          <Image
            src={FontCard2}
            style={{ cursor: 'pointer', width: '8vw', height: '16vh' }}
            preview={false}
            onClick={handleAddTxt1}
          />
        </div>
      </div>
      <div
        style={{
          width: '100%',
          backgroundColor: '#0000000D',
          borderRadius: '10px',
          paddingBottom: '10px',
        }}
      >
        <div
          style={{
            width: '100%',
            marginTop: '20px',
            display: 'flex',
            justifyContent: 'space-between',
            padding: '10px',
          }}
        >
          <h5 style={{ textAlign: 'left', color: ' #0A0606' }}>
            Paragraphs (4)
          </h5>
          <Button
            type="text"
            style={{ marginTop: '-10px' }}
            onClick={handleToggle}
          >
            {isExpanded ? 'Show Less' : 'Show All'}
          </Button>
        </div>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            padding: '10px',
          }}
        >
          <Image
            src={SamplTextImg}
            style={{ cursor: 'pointer', width: '8vw', height: '16vh' }}
            preview={false}
            onClick={handleAddparagraph1}
          />
          <Image
            src={SampleTextImg1}
            style={{ cursor: 'pointer', width: '8vw', height: '16vh' }}
            preview={false}
            onClick={handleAddparagraph2}
          />
        </div>
        {isExpanded && (
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              marginTop: '10px',
              padding: '10px',
            }}
          >
            <Image
              src={SampleTextImg3}
              style={{ cursor: 'pointer', width: '8vw', height: '16vh' }}
              preview={false}
              onClick={handleAddparagraph3}
            />
            <Image
              src={SampleTextImg2}
              style={{ cursor: 'pointer', width: '8vw', height: '16vh' }}
              preview={false}
              onClick={handleAddparagraph4}
            />
          </div>
        )}
      </div>
    </div>
  );
};

const images = [
  {
    id: 'new-project',
    src: NewProjectSVG,
    title: 'My Project',
    content: <NewProject />,
  },
  {
    id: 'template',
    src: TemplateSVG,
    title: 'Templates',
    content: <TemplateContent />,
  },
  {
    id: 'text',
    src: TextSVG,
    title: 'Add Headline',
    content: <Text />,
    rightSideTextDrawer: {
      title: 'Text Settings',
      content: <RightSideTextDrawer />,
    },
  },
  {
    id: 'elements',
    src: ElementsSVG,
    title: 'Add Elements',
    content: <Shapes />,
  },
  {
    id: 'upload',
    src: UploadSVG,
    title: 'Upload',
    content: <UploadI />,
    rightSideDrawer: {
      title: 'Object Settings',
      content: <RightSideDrawer />,
    },
  },
  // {
  //   id: 'camera',
  //   src: CameraSVG,
  //   title: 'Camera',
  //   content: 'camera',
  // },
  // {
  //   id: 'video',
  //   src: VideoSVG,
  //   title: 'video',
  //   content: 'video',
  // },
  // {
  //   id: 'gif',
  //   src: GifSVG,
  //   title: 'gif',
  //   content: 'gif',
  // },
];

function DesignStudio() {
  const location = useLocation();
  const data = location.state;
  const aud_data = location.aud_info;
  const AiData = location.data;
  console.log('AiData', AiData);
  const [modalOpen, setModalOpen] = useState(true);
  const [selectedImage, setSelectedImage] = useState('new-project');
  const [selectedImageDrawer, setSelectedImageDrawer] = useState(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedRightSideDrawer, setSelectedRightSideDrawer] = useState(false);
  const [editableText, setEditableText] = useState('New Project');
  const [Name, setName] = useState('New Project');
  const [selectedRightsideTextDrawer, setSelectedRightsideTextDrawer] =
    useState(false);
  const [layervisible, setlayerVisible] = useState(false);
  const [selectedImageSrc, setSelectedImageSrc] = useState(null);
  const [whiteboardImages, setWhiteboardImages] = useState([]);
  const [isClicked, setIsClicked] = useState(false);
  const [isClicked1, setIsClicked1] = useState(false);
  const [isClicked2, setIsClicked2] = useState(false);
  const [isClicked3, setIsClicked3] = useState(false);
  const [isClicked4, setIsClicked4] = useState(false);
  const [isClicked5, setIsClicked5] = useState(false);
  const [isClicked6, setIsClicked6] = useState(false);
  const [isClicked7, setIsClicked7] = useState(false);
  const [width, setWidth] = useState(380);
  const [height, setHeight] = useState(300);
  const [sendvisible, setsendVisible] = useState(false);
  const [settingvisible, setsettingVisible] = useState(false);
  const [playvisible, setplayVisible] = useState(false);
  const [generateVisible, setGenerateVisible] = useState(false);

  const [downloadvisible1, setdownloadVisible1] = useState(false);
  const [layerisHeading, setlayerIsHeading] = useState(false);
  const [selectedOption, setSelectedOption] = useState('');
  const [selectedUser, setSelectedUser] = useState('');
  const [selectedDate, setSelectedDate] = useState(null); // Use null as initial state for the date
  const [selectedTime, setSelectedTime] = useState(null); // Use null as initial state for the time
  const [savedTemp, setSavedTemp] = useState('');

  const [isInputEnabled, setIsInputEnabled] = useState(false);
  const [isInputBordered, setIsInputBordered] = useState(false);
  const [newWidth, setNewWidth] = useState(width);
  const [newHeight, setNewHeight] = useState(height);
  const [selectedCardDescription, setSelectedCardDescription] = useState('');

  const audienceType = localStorage.getItem('audienceType');

  useEffect(() => {
    console.log('@@@@@@@@@', data?.audi);
  }, []);

  const imageSrc = AiData?.img;
  const campaign_name = AiData?.campaign_name;

  useEffect(() => {
    if (AiData) {
      setEditableText(campaign_name);
      setName(campaign_name);
      console.log('imageSrc', imageSrc);
      store.dispatch(addUploadImage({ imageSrc }));
    }
  }, [AiData]);

  const handleCreate = () => {
    // Store the selected card description in local storage
    localStorage.setItem('selectedCardDescription', selectedCardDescription);

    // Close the modal
    setModalOpen(false);
  };

  const handleCardSelect = useCallback((description) => {
    setSelectedCardDescription(description);
  }, []);

  const handleEditImageClick = () => {
    setIsInputEnabled((prevState) => !prevState);
    setIsInputBordered((prevState) => !prevState);
  };

  const [zoomLevel, setZoomLevel] = useState(100);
  // settings Open
  const [preview, setPreview] = useState('');
  const [previewurl, setPreviewurl] = useState();

  const userData = useSelector((states) => states.Auth.userData);
  const uData = isObject(userData) ? { ...userData } : {};

  const playhandleopenChange = () => {
    KonvaShape.removeTransformer();
    setplayVisible(!playvisible);
    const kp1 = KonvaBoard2.toJSON();
    setPreview(kp1);
    const url = () => {
      try {
        const prev = JSON.parse(kp1);
        console.log('this is my prev', prev);
        console.log('this is my prev', prev.attrs?.stageImg);
        return prev.attrs?.stageImg;
      } catch (e) {
        console.error(e);
      }
      return null;
    };
    setPreviewurl(url);
  };
  console.log('this is image display', previewurl);
  const artbordSizeSelected = localStorage.getItem('selectedCardDescription');
  console.log('this is selected option', artbordSizeSelected);

  const customheight = parseInt(localStorage.getItem('customHeight'), 10);
  console.log('this is customheight:', customheight);

  const customwidth = parseInt(localStorage.getItem('customWidth'), 10);
  console.log('this is customwidth:', customwidth);

  if (customheight > 380 && customwidth > 180) {
    localStorage.setItem('customHeight', 100);
    localStorage.setItem('customWidth', 100);

    console.log('Updated custom height and width');
  }

  const getBackgroundSize = () => {
    switch (artbordSizeSelected) {
      case 'Top alignment':
        return '180px 140px'; // Adjust the size as per your requirements
      case 'Bottom alignment':
        return '180px 140px'; // Adjust the size as per your requirements
      case 'Centre':
        return '180px 140px'; // Adjust the size as per your requirements
      case 'Standard':
        return '180px 300px'; // Adjust the size as per your requirements
      case 'Full screen':
        return '100% 100%';
      case 'Custom':
        return `${customwidth}% ${customheight}%`; // Replace with your custom size // Replace with your custom size
      case 'Poster Big':
        return '180px 300px'; // Adjust the size as per your requirements
      case 'Instagram Post':
        return '180px 180px'; // Adjust the size as per your requirements
      case 'Facebook Post':
        return '180px 250px'; // Adjust the size as per your requirements
      default:
        return '180px 380px'; // Default size
    }
  };

  const getBackgroundPosition = () => {
    switch (artbordSizeSelected) {
      case 'Top alignment':
        return 'top center';
      case 'Bottom alignment':
        return 'bottom center';
      case 'Centre':
        return 'center center';
      case 'Standard':
        return 'center center';
      case 'Full screen':
        return 'center center';
      case 'Custom':
        return 'center center';
      case 'Poster Big':
        return 'center center';
      case 'Instagram Post':
        return 'center center';
      case 'Facebook Post':
        return 'center center';
      default:
        return 'center center';
    }
  };

  let playpopoverContent = null;

  if (audienceType === 'in_app_message') {
    playpopoverContent = (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          width: '220px', // Set the width of the simulator
          height: '420px', // Set the height of the simulator
          backgroundColor: '#fff',
          borderRadius: '10px',
          overflow: 'hidden',
          boxShadow: '0px 0px 20px rgba(0, 0, 0, 0.5)',
          position: 'relative',
        }}
      >
        {/* Outline */}
        <div
          style={{
            position: 'absolute',
            top: '0',
            left: '0',
            right: '0',
            bottom: '0',
            border: '10px solid #444',
            borderRadius: '10px',
          }}
        ></div>

        {/* Top speaker */}
        <div
          style={{
            position: 'absolute',
            top: '20px',
            left: 'calc(50% - 60px)',
            width: '80px',
            height: '10px',
            backgroundColor: '#444',
            borderRadius: '5px',
          }}
        ></div>

        {/* Front-facing camera */}
        <div
          style={{
            position: 'absolute',
            top: '18px',
            right: 'calc(50% - 60px)',
            width: '15px',
            height: '15px',
            backgroundColor: '#444',
            borderRadius: '50%',
          }}
        ></div>

        {/* Screen */}
        <div
          style={{
            position: 'absolute',
            top: '40px',
            left: '10px',
            right: '10px',
            bottom: '10px',
            borderRadius: '8px',
            overflow: 'hidden',
            backgroundImage: `url(${previewurl})`,
            backgroundSize: getBackgroundSize(),
            backgroundRepeat: 'no-repeat',
            backgroundPosition: getBackgroundPosition(),
          }}
        >
          {/* Add your content here */}
          {/* <h1
            style={{
              textAlign: 'center',
              marginTop: '40%',
              fontSize: '24px',
              color: '#fff',
            }}
          >
            Google Pixel 5a Simulator
          </h1>
          <p
            style={{
              textAlign: 'center',
              fontSize: '16px',
              color: '#ccc',
            }}
          >
            This is a simulator for Google Pixel 5a created using React.
          </p> */}
        </div>
      </div>
    );
  } else if (audienceType === 'feed_post') {
    if (
      brandNames.includes(uData.brand_name) &&
      uData.brand_name === 'cbt-aqm'
    ) {
      playpopoverContent = (
        <div
          style={{
            // display: 'flex',

            width: '220px', // Set the width of the simulator
            height: '420px', // Set the height of the simulator
            backgroundColor: '#F9F9F9',
            borderRadius: '10px',
            overflow: 'hidden',
            boxShadow: '0px 0px 20px rgba(0, 0, 0, 0.5)',
            position: 'relative',
            backgroundImage: `url(${DashboardBackground})`,
            backgroundSize: 'cover',
            backgroundRepeat: 'no-repeat',
          }}
        >
          {/* Outline */}
          <div
            style={{
              position: 'absolute',
              top: '0',
              left: '0',
              right: '0',
              bottom: '0',
              border: '10px solid #444',
              borderRadius: '10px',
            }}
          ></div>

          {/* Top speaker */}
          {/* <div
          style={{
            position: 'absolute',
            top: '20px',
            left: 'calc(50% - 60px)',
            width: '80px',
            height: '10px',
            backgroundColor: '#444',
            borderRadius: '5px',
          }}
        ></div> */}

          {/* Front-facing camera */}
          {/* <div
          style={{
            position: 'absolute',
            top: '18px',
            right: 'calc(50% - 60px)',
            width: '15px',
            height: '15px',
            backgroundColor: '#444',
            borderRadius: '50%',
          }}
        ></div> */}

          {/* Screen */}
          <div>
            <div>
              <div
                style={{
                  position: 'absolute',
                  top: '20px',
                  left: 'calc(50% - 60px)',
                  width: '80px',
                  height: '10px',
                  backgroundColor: '#444',
                  borderRadius: '5px',
                }}
              ></div>
              <div
                style={{
                  position: 'absolute',
                  top: '18px',
                  right: 'calc(50% - 60px)',
                  width: '15px',
                  height: '15px',
                  backgroundColor: '#444',
                  borderRadius: '50%',
                }}
              ></div>
            </div>
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                padding: 15,
                marginTop: 4,
              }}
            >
              <Image
                src={ChillbabyLogo}
                width={30}
                height={30}
                preview={false}
              />

              <BellOutlined style={{ color: '#fff' }} />
            </div>
            <div
              style={{
                display: 'flex',
                // justifyContent: 'space-between',
                marginTop: -20,
              }}
            >
              <Image
                src={BabyBoy}
                preview={false}
                width={110}
                height={90}
                style={{ marginTop: 20, borderRadius: 10, marginLeft: 20 }}
              />
              <div
                style={{
                  backgroundColor: '#fff',
                  width: 80,
                  marginTop: 20,
                  marginLeft: 40,
                  borderRadius: '10px 0px 0px 10px',
                  padding: 3,
                }}
              >
                <div
                  style={{ display: 'flex', justifyContent: 'space-evenly' }}
                >
                  <CloudOutlined style={{ color: '#000', marginLeft: -6 }} />
                  <div style={{ marginLeft: -6 }}>
                    <p style={{ color: '#0a0606', fontSize: 7 }}>Outside</p>
                    <p style={{ color: '#276B20', fontSize: 7, marginTop: -3 }}>
                      GOOD
                    </p>
                  </div>

                  <Image
                    src={ArcSlider}
                    preview={false}
                    width={15}
                    height={15}
                    style={{ marginLeft: -6, marginTop: -5 }}
                  />
                  <p
                    style={{
                      fontSize: 7,
                      color: '#276B20',
                      fontWeight: 'bold',
                      marginLeft: -29,
                      marginTop: 4,
                    }}
                  >
                    12
                  </p>
                </div>
                <div
                  style={{
                    backgroundColor: '#746E6E',
                    width: 80,
                    marginLeft: -3,
                    padding: 5,
                    borderRadius: '5px 0px 0px 5px',
                    display: 'flex',
                  }}
                >
                  <div>
                    <p style={{ fontSize: 5 }}>01:25pm</p>
                    <p style={{ fontSize: 5 }}>Aug 27,2023</p>
                    <p style={{ fontSize: 5 }}>LB Nagar, Hyderabad</p>
                  </div>
                  <div
                    style={{
                      backgroundColor: '#4463FE',
                      borderRadius: '10px 0px 0px 10px',
                      width: 15,
                      height: 18,
                      marginTop: -5,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Image
                      src={ArrowW}
                      width={13}
                      height={13}
                      preview={false}
                      style={{ marginTop: -10 }}
                    />
                  </div>
                </div>
                <div
                  style={{ display: 'flex', justifyContent: 'space-evenly' }}
                >
                  <div>
                    <Image
                      src={Bluetooth}
                      width={10}
                      height={10}
                      preview={false}
                    />
                  </div>
                  <div>
                    <Image
                      src={Battery}
                      width={10}
                      height={10}
                      preview={false}
                    />
                    <p
                      style={{
                        color: '#0A0606',
                        fontSize: 5,
                        marginTop: -5,
                      }}
                    >
                      95%
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div
              style={{
                width: 185,
                height: 40,
                backgroundColor: '#fff',
                margin: 17.5,
                padding: 10,
                borderRadius: 5,
                display: 'flex',
                justifyContent: 'space-evenly',
              }}
            >
              <div>
                <p style={{ fontSize: 8, color: '#0A0606' }}>
                  Air Quality in Stroller
                </p>
                <p
                  style={{ fontSize: 8, color: '#276B20', fontWeight: 'bold' }}
                >
                  GOOD
                </p>
              </div>
              <div style={{}}>
                <Image src={ArcSlider} width={20} height={20} preview={false} />
                <p
                  style={{
                    fontSize: 8,
                    color: '#276B20',
                    fontWeight: 'bold',
                    marginTop: -15,
                    marginLeft: 5,
                  }}
                >
                  12
                </p>
              </div>
              <p
                style={{
                  fontSize: 8,
                  color: '#276B20',
                  fontWeight: 'bold',
                  marginTop: -6,
                  marginRight: -15,
                }}
              >
                View
              </p>
            </div>
            <div
              style={{
                backgroundColor: '#fff',
                padding: 10,
                margin: '-10px 20px 60px 20px',
                borderRadius: 10,
                boxShadow: '0px 0px 20px rgba(0, 0, 0, 0.5)',
              }}
            >
              <div style={{ display: 'flex' }}>
                <p style={{ fontSize: 8, fontWeight: 'bold' }}>My Chillbaby</p>
              </div>
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'center',
                    width: 150,
                    height: 120,
                    position: 'relative',
                    backgroundImage: `url(${previewurl})`,
                    backgroundSize: 'cover',
                    backgroundRepeat: 'no-repeat',
                    padding: 10,
                  }}
                >
                  <div />
                  {/* <Image src={previewurl} width={500} height={125} /> */}
                </div>
              </div>
            </div>
            <div
              style={{
                backgroundColor: '#9E999B',
                width: 180,
                height: 35,
                margin: '-50px 20px 20px 20px',
                borderRadius: 20,
                padding: 5,
                display: 'flex',
                justifyContent: 'space-evenly',
              }}
            >
              <div
                style={{
                  backgroundColor: '#DBD9DA',
                  borderRadius: 50,
                  padding: 5,
                }}
              >
                <Image
                  src={Home}
                  preview={false}
                  width={15}
                  height={15}
                  style={{ marginTop: -5 }}
                />
              </div>
              <Image src={Dashboard} width={15} height={15} preview={false} />
              <Image src={Graph} width={15} height={15} preview={false} />
              <Image src={Setting} width={15} height={15} preview={false} />
            </div>
          </div>
        </div>
      );
    } else if (
      brandNames.includes(uData.brand_name) &&
      uData.brand_name === 'cbtsmartpad'
    ) {
      playpopoverContent = (
        <div
          style={{
            // display: 'flex',
            width: '220px', // Set the width of the simulator
            height: '420px', // Set the height of the simulator
            backgroundColor: '#fff',
            borderRadius: '10px',
            overflow: 'hidden',
            boxShadow: '0px 0px 20px rgba(0, 0, 0, 0.5)',
            position: 'relative',
            // backgroundImage: `url(${DashboardBackground})`,
            // backgroundSize: 'cover',
            // backgroundRepeat: 'no-repeat',
          }}
        >
          {/* Outline */}
          <div
            style={{
              position: 'absolute',
              top: '0',
              left: '0',
              right: '0',
              bottom: '0',
              border: '10px solid #444',
              borderRadius: '10px',
            }}
          ></div>

          {/* Screen */}
          <div>
            <div>
              {' '}
              <div
                style={{
                  position: 'absolute',
                  top: '20px',
                  left: 'calc(50% - 60px)',
                  width: '80px',
                  height: '10px',
                  backgroundColor: '#444',
                  borderRadius: '5px',
                }}
              ></div>
              <div
                style={{
                  position: 'absolute',
                  top: '18px',
                  right: 'calc(50% - 60px)',
                  width: '15px',
                  height: '15px',
                  backgroundColor: '#444',
                  borderRadius: '50%',
                }}
              ></div>
            </div>
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                padding: 15,
                marginTop: 10,
              }}
            >
              <div
                style={{
                  border: '1px solid #444',
                  borderRadius: '20px',
                  padding: 3,
                  width: 25,
                  height: 25,
                  marginLeft: 5,
                  transform: 'rotate(90deg)',
                  display: 'inline-block',
                }}
              >
                <ControlOutlined style={{ fontSize: 15, paddingLeft: 1 }} />
              </div>
              <div
                style={{
                  border: '1px solid #444',
                  borderRadius: '20px',
                  padding: 3,
                  width: 23,
                  height: 23,
                  display: 'flex',
                  justifyContent: 'center',
                  marginRight: 5,
                }}
              >
                <BellOutlined
                  style={{
                    fontSize: 15,
                    color: '#000',
                  }}
                />
                <div
                  style={{
                    width: '5px',
                    height: '5px',
                    backgroundColor: '#ff0000',
                    borderRadius: '50%',
                    marginLeft: -5,
                    marginTop: -4,
                  }}
                />
              </div>
            </div>
          </div>
          <div
            style={{
              border: '1px solid #444',
              borderRadius: '10px',
              height: 90,
              width: 90,
              padding: 10,
              margin: 20,
            }}
          >
            <PictureTwoTone
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                fontSize: 25,
              }}
            />
            <div style={{ display: 'flex', paddingTop: 5 }}>
              <PlusOutlined />
              <p style={{ fontSize: 11, color: '#7BC9ED', paddingLeft: 4 }}>
                ADD NEW
              </p>
            </div>
            <p
              style={{
                fontSize: 7,
                marginTop: 5,
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              Add SMART Device
            </p>
          </div>
          <div
            style={{
              backgroundColor: '#E5E5E5',
              width: '100%',
              height: '100%',
              padding: 10,
              margin: 10,
            }}
          >
            <p style={{ fontSize: 12, fontWeight: 'bold' }}>WHATS'S NEW</p>
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                width: 150,
                height: 120,
                marginLeft: 10,
                position: 'relative',
                backgroundImage: `url(${previewurl})`,
                backgroundSize: 'cover',
                backgroundRepeat: 'no-repeat',
                padding: 10,
              }}
            >
              <div />
              {/* <Image src={previewurl} width={500} height={125} /> */}
            </div>
            <div
              style={{
                backgroundColor: '#fff',
                width: 180,
                height: 50,
                margin: '0px 20px 20px 0px',
                borderRadius: 10,
                padding: 5,
                display: 'flex',
                justifyContent: 'space-evenly',
                alignItems: 'center',
              }}
            >
              <div style={{ alignItems: 'center', justifyContent: 'center' }}>
                <SwapOutlined
                  style={{ fontSize: 14, transform: 'rotate(90deg)' }}
                />
                <p style={{ fontSize: 6 }}>DEVICES</p>
              </div>

              <div
                style={{
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: -8,
                }}
              >
                <UserOutlined style={{ fontSize: 14 }} />
                <p style={{ fontSize: 6, marginLeft: -4 }}>DASHBOARD</p>
              </div>
              <div
                style={{
                  backgroundColor: '#7BC9ED',
                  height: 50,
                  width: 55,
                  marginRight: -30,
                  borderRadius: 10,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <div style={{ marginLeft: 20, marginTop: 5 }}>
                  <HomeOutlined style={{ fontSize: 14 }} />
                  <p style={{ fontSize: 6 }}>HOME</p>
                  <div
                    style={{
                      width: '5px',
                      height: '5px',
                      backgroundColor: '#fff',
                      borderRadius: '50%',
                      marginLeft: 5,
                      marginTop: 5,
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (
      brandNames.includes(uData.brand_name) &&
      uData.brand_name === 'cbtsmartcar'
    ) {
      playpopoverContent = (
        <div
          style={{
            // display: 'flex',
            width: '220px', // Set the width of the simulator
            height: '420px', // Set the height of the simulator
            backgroundColor: '#34BEEF',
            borderRadius: '10px',
            overflow: 'hidden',
            boxShadow: '0px 0px 20px rgba(0, 0, 0, 0.5)',
            position: 'relative',
            // backgroundImage: `url(${DashboardBackground})`,
            // backgroundSize: 'cover',
            // backgroundRepeat: 'no-repeat',
          }}
        >
          {/* Outline */}
          <div
            style={{
              position: 'absolute',
              top: '0',
              left: '0',
              right: '0',
              bottom: '0',
              border: '10px solid #444',
              borderRadius: '10px',
            }}
          ></div>

          {/* Screen */}
          <div>
            <div>
              {' '}
              <div
                style={{
                  position: 'absolute',
                  top: '15px',
                  left: 'calc(50% - 60px)',
                  width: '80px',
                  height: '10px',
                  backgroundColor: '#444',
                  borderRadius: '5px',
                }}
              ></div>
              <div
                style={{
                  position: 'absolute',
                  top: '15px',
                  right: 'calc(50% - 60px)',
                  width: '15px',
                  height: '15px',
                  backgroundColor: '#444',
                  borderRadius: '50%',
                }}
              ></div>
            </div>
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                padding: 15,
                marginTop: 15,
              }}
            >
              <div
                style={{
                  border: '1px solid #fff',
                  borderRadius: '20px',
                  padding: 3,
                  width: 27,
                  height: 27,
                  marginLeft: 5,
                  transform: 'rotate(90deg)',
                  display: 'inline-block',
                }}
              >
                <ControlOutlined
                  style={{ fontSize: 15, color: '#fff', marginLeft: 2 }}
                />
              </div>
              <div>
                <Image
                  src={ChillbabyLogo}
                  width={30}
                  height={30}
                  preview={false}
                />
              </div>
              <div
                style={{
                  border: '1px solid #fff',
                  borderRadius: '20px',
                  padding: 3,
                  width: 25,
                  height: 25,
                  display: 'flex',
                  justifyContent: 'center',
                  marginRight: 5,
                }}
              >
                <BellOutlined
                  style={{
                    fontSize: 15,
                    color: '#fff',
                  }}
                />
                <div
                  style={{
                    width: '5px',
                    height: '5px',
                    backgroundColor: '#ff0000',
                    borderRadius: '50%',
                    marginLeft: -5,
                    marginTop: -4,
                  }}
                />
              </div>
            </div>
          </div>
          <div style={{ margin: '-14px 20px 10px 20px' }}>
            <p style={{ fontSize: 8, color: '#fff' }}>Good Afternoon!</p>
            <p style={{ fontSize: 12, color: '#fff' }}>John Doe</p>
            <p style={{ fontSize: 8, color: '#fff' }}>MY DEVICES</p>
          </div>
          <div
            style={{
              border: '1px dashed #fff',
              borderRadius: '10px',
              height: 70,
              width: 70,
              padding: 10,
              margin: '-10px 20px 15px 20px',
            }}
          >
            <PlusOutlined
              style={{
                fontSize: 14,
                fontWeight: 'bold',
                color: '#fff',
                justifyContent: 'center',
                alignItems: 'center',
                marginLeft: 16,
                marginTop: 16,
              }}
            />
          </div>
          <div
            style={{
              backgroundColor: '#039BE6',
              width: '100%',
              height: '100%',
              padding: 10,
              margin: 10,
              borderRadius: '30px 0px 0px 0px',
            }}
          >
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                width: 150,
                height: 120,
                marginLeft: 10,
                position: 'relative',
                backgroundImage: `url(${previewurl})`,
                backgroundSize: 'cover',
                backgroundRepeat: 'no-repeat',
                padding: 10,
              }}
            >
              <div />
              {/* <Image src={previewurl} width={500} height={125} /> */}
            </div>
            <div
              style={{
                backgroundColor: '#fff',
                width: 180,
                height: 50,
                margin: '20px 20px 20px 0px',
                borderRadius: '0px 20px 0px 20px',
                padding: 5,
                display: 'flex',
                justifyContent: 'space-evenly',
                alignItems: 'center',
              }}
            >
              <div style={{ alignItems: 'center', justifyContent: 'center' }}>
                <SwapOutlined style={{ fontSize: 14 }} />
                <p style={{ fontSize: 6 }}>DEVICES</p>
              </div>

              <div
                style={{
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: -8,
                }}
              >
                <UserOutlined style={{ fontSize: 14 }} />
                <p style={{ fontSize: 6, marginLeft: -4 }}>DASHBOARD</p>
              </div>
              <div
                style={{
                  backgroundColor: '#7BC9ED',
                  height: 50,
                  width: 55,
                  marginRight: -30,
                  borderRadius: '0px 20px 0px 20px',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <div style={{ marginLeft: 20, marginTop: 5 }}>
                  <HomeOutlined style={{ fontSize: 14 }} />
                  <p style={{ fontSize: 6 }}>HOME</p>
                  <div
                    style={{
                      width: '5px',
                      height: '5px',
                      backgroundColor: '#fff',
                      borderRadius: '50%',
                      marginLeft: 5,
                      marginTop: 5,
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else if (
      brandNames.includes(uData.brand_name) &&
      uData.brand_name === 'cbthc'
    ) {
      playpopoverContent = (
        <div
          style={{
            // display: 'flex',

            width: '220px', // Set the width of the simulator
            height: '420px', // Set the height of the simulator
            backgroundColor: '#F9F9F9',
            borderRadius: '10px',
            overflow: 'hidden',
            boxShadow: '0px 0px 20px rgba(0, 0, 0, 0.5)',
            position: 'relative',
            backgroundImage: `url(${DashboardBackground})`,
            backgroundSize: 'cover',
            backgroundRepeat: 'no-repeat',
          }}
        >
          {/* Outline */}
          <div
            style={{
              position: 'absolute',
              top: '0',
              left: '0',
              right: '0',
              bottom: '0',
              border: '10px solid #444',
              borderRadius: '10px',
            }}
          ></div>

          {/* Top speaker */}
          {/* <div
          style={{
            position: 'absolute',
            top: '20px',
            left: 'calc(50% - 60px)',
            width: '80px',
            height: '10px',
            backgroundColor: '#444',
            borderRadius: '5px',
          }}
        ></div> */}

          {/* Front-facing camera */}
          {/* <div
          style={{
            position: 'absolute',
            top: '18px',
            right: 'calc(50% - 60px)',
            width: '15px',
            height: '15px',
            backgroundColor: '#444',
            borderRadius: '50%',
          }}
        ></div> */}

          {/* Screen */}
          <div>
            <div>
              <div
                style={{
                  position: 'absolute',
                  top: '20px',
                  left: 'calc(50% - 60px)',
                  width: '80px',
                  height: '10px',
                  backgroundColor: '#444',
                  borderRadius: '5px',
                }}
              ></div>
              <div
                style={{
                  position: 'absolute',
                  top: '18px',
                  right: 'calc(50% - 60px)',
                  width: '15px',
                  height: '15px',
                  backgroundColor: '#444',
                  borderRadius: '50%',
                }}
              ></div>
            </div>
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                padding: 15,
                marginTop: 4,
              }}
            >
              <div>
                <Image
                  src={ChillbabyLogo}
                  width={30}
                  height={30}
                  preview={false}
                />
              </div>
              <BellOutlined style={{ color: '#fff' }} />
            </div>
            <div
              style={{
                display: 'flex',
                // justifyContent: 'space-between',
                marginTop: -20,
              }}
            >
              <Image
                src={BabyBoy}
                preview={false}
                width={110}
                height={90}
                style={{ marginTop: 20, borderRadius: 10, marginLeft: 20 }}
              />
              <div
                style={{
                  backgroundColor: '#F4F4F4',
                  width: 80,
                  marginTop: 20,
                  marginLeft: 40,
                  borderRadius: '10px 0px 0px 10px',
                  padding: 2,
                }}
              >
                <div
                  style={{ display: 'flex', justifyContent: 'space-evenly' }}
                >
                  <CloudOutlined style={{ color: '#000', marginLeft: -5 }} />
                  <div style={{ marginLeft: -5 }}>
                    <p style={{ color: '#0a0606', fontSize: 7 }}>Outside</p>
                    <p
                      style={{
                        color: '#276B20',
                        fontSize: 7,
                        marginTop: -3,
                        fontWeight: 'bold',
                      }}
                    >
                      Temp.
                    </p>
                  </div>
                  <p
                    style={{
                      fontSize: 7,
                      color: '#0A0606',
                      fontWeight: 'bold',
                      marginLeft: -5,
                    }}
                  >
                    12°c
                  </p>
                </div>
                <div
                  style={{
                    backgroundColor: '#746E6E',
                    width: 80,
                    marginLeft: -3,
                    padding: 5,
                    borderRadius: '5px 0px 0px 5px',
                    display: 'flex',
                  }}
                >
                  <div>
                    <p style={{ fontSize: 5 }}>01:25pm</p>
                    <p style={{ fontSize: 5 }}>Aug 27,2023</p>
                    <p style={{ fontSize: 5 }}>LB Nagar, Hyderabad</p>
                  </div>
                  <div
                    style={{
                      backgroundColor: '#4463FE',
                      borderRadius: '10px 0px 0px 10px',
                      width: 15,
                      height: 18,
                      marginTop: -5,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Image
                      src={ArrowW}
                      width={13}
                      height={13}
                      preview={false}
                      style={{ marginTop: -10 }}
                    />
                  </div>
                </div>
                <div
                  style={{ display: 'flex', justifyContent: 'space-evenly' }}
                >
                  <div>
                    <Image
                      src={Bluetooth}
                      width={10}
                      height={10}
                      preview={false}
                    />
                  </div>
                  <div>
                    <Image
                      src={Battery}
                      width={10}
                      height={10}
                      preview={false}
                    />
                    <p
                      style={{
                        color: '#0A0606',
                        fontSize: 5,
                        marginTop: -5,
                      }}
                    >
                      95%
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div
              style={{
                width: 185,
                height: 40,
                backgroundColor: '#fff',
                margin: 17.5,
                padding: 10,
                borderRadius: 5,
                display: 'flex',
                justifyContent: 'space-evenly',
              }}
            >
              <div>
                <p style={{ fontSize: 8, color: '#0A0606' }}>
                  The device is not
                </p>
                <p style={{ fontSize: 8, color: '#0A0606' }}>Connected</p>
              </div>
              <div style={{}}>
                <p
                  style={{
                    fontSize: 8,
                    color: '#0A0606',
                    fontWeight: 'bold',
                    marginLeft: 5,
                  }}
                >
                  12°c
                </p>
              </div>
              <p
                style={{
                  fontSize: 8,
                  color: '#0A0606',
                  fontWeight: 'bold',
                  marginTop: -6,
                  marginRight: -15,
                }}
              >
                View
              </p>
            </div>
            <div
              style={{
                backgroundColor: '#fff',
                padding: 10,
                margin: '-10px 20px 60px 20px',
                borderRadius: 10,
                boxShadow: '0px 0px 20px rgba(0, 0, 0, 0.5)',
              }}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <p style={{ fontSize: 8, fontWeight: 'bold' }}>My Chillbaby</p>
                {/* <p style={{ fontSize: 8, fontWeight: 'bold' }}>View All</p> */}
              </div>
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'center',
                    width: 150,
                    height: 120,

                    position: 'relative',
                    backgroundImage: `url(${previewurl})`,
                    backgroundSize: 'cover',
                    backgroundRepeat: 'no-repeat',
                    padding: 10,
                  }}
                >
                  <div />
                  {/* <Image src={previewurl} width={500} height={125} /> */}
                </div>
              </div>
            </div>
            <div
              style={{
                backgroundColor: '#9E999B',
                width: 180,
                height: 35,
                margin: '-50px 20px 20px 20px',
                borderRadius: 20,
                padding: 5,
                display: 'flex',
                justifyContent: 'space-evenly',
              }}
            >
              <div
                style={{
                  backgroundColor: '#DBD9DA',
                  borderRadius: 50,
                  padding: 5,
                }}
              >
                <Image
                  src={Home}
                  preview={false}
                  width={15}
                  height={15}
                  style={{ marginTop: -5 }}
                />
              </div>
              <Image src={Dashboard} width={15} height={15} preview={false} />
              {/* <Image src={Graph} width={15} height={15} /> */}
              <Image src={Setting} width={15} height={15} preview={false} />
            </div>
          </div>
        </div>
      );
    }
  }

  const settinghandleOpenChange = () => {
    setsettingVisible(!settingvisible);
  };

  const generatehandleopenchnage = () => {
    setGenerateVisible(!generateVisible);
  };
  // settings close
  const settinghandlePopoverClose = () => {
    setsettingVisible(false);
  };
  // send Open
  const sendhandleOpenChange = () => {
    setsendVisible(!sendvisible);
  };
  // send close
  const sendhandlePopoverClose = () => {
    setsendVisible(false);
  };

  // const handleZoomIn = () => {
  //   const stageSize = {
  //     height: KonvaBoard2.obj.height(),
  //     width: KonvaBoard2.obj.width(),
  //   };

  //   if (zoomLevel < 200 && stageSize.height < 350) {
  //     setZoomLevel(zoomLevel + 10);

  //     // // Update the stage size
  //     // const updatedStageSize = {
  //     //   height: Math.min(stageSize.height * 1.1, 350), // increase height by 10% or restrict to 350
  //     //   width: stageSize.width * 1.1, // increase width by 10%
  //     // };

  //     // setHeight(updatedStageSize.height);
  //     // setWidth(updatedStageSize.width);

  //     // // Apply the updated size to the stage or KonvaBoard2 object
  //     // KonvaBoard2.obj.height(updatedStageSize.height);
  //     // KonvaBoard2.obj.width(updatedStageSize.width);
  //   }
  // };

  // const handleZoomOut = () => {
  //   const stageSize = {
  //     height: KonvaBoard2.obj.height(),
  //     width: KonvaBoard2.obj.width(),
  //   };
  //   if (zoomLevel > 10) {
  //     setZoomLevel(zoomLevel - 10);

  //     // // Update the stage size
  //     // const updatedStageSize = {
  //     //   height: stageSize.height * 0.9, // decrease height by 10%
  //     //   width: stageSize.width * 0.9, // decrease width by 10%
  //     // };
  //     // setHeight(updatedStageSize.height);
  //     // setWidth(updatedStageSize.width);

  //     // // Apply the updated size to the stage or KonvaBoard2 object
  //     // KonvaBoard2.obj.height(updatedStageSize.height);
  //     // KonvaBoard2.obj.width(updatedStageSize.width);
  //   }
  // };

  const handleZoomIn = () => {
    if (zoomLevel < 200) {
      setZoomLevel(zoomLevel + 10);
      console.log('this is zoomin', zoomLevel);
    }
  };

  const handleZoomOut = () => {
    if (zoomLevel > 10) {
      setZoomLevel(zoomLevel - 10);
      console.log('this is zoomout', zoomLevel);
    }
  };

  useEffect(() => {
    KonvaBoard2.obj.scale({ x: zoomLevel / 100, y: zoomLevel / 100 });
  }, [zoomLevel]);

  // useEffect(() => {
  //   console.log('KonvaBoard2.obj scale, h,w');
  //   console.log(KonvaBoard2.obj.scaleX());
  //   console.log(KonvaBoard2.obj.scaleY());
  //   console.log(KonvaBoard2.obj.height());
  //   console.log(KonvaBoard2.obj.width());
  //   const scale = { x: zoomLevel / 100, y: zoomLevel / 100 };
  //   const stageSize = {
  //     height: KonvaBoard2.obj.height() * scale.x,
  //     width: KonvaBoard2.obj.width() * scale.y,
  //   };
  //   KonvaBoard2.obj.scale(scale);
  //   KonvaBoard2.obj.height(stageSize.height);
  //   KonvaBoard2.obj.width(stageSize.width);
  // }, [zoomLevel]);

  // const resetName = () => {
  //   setName('New Project'); // Set the name to the default value
  // };

  // const layerhandleBackground = () => {
  //   setlayerIsBackground(!layerisBackground);
  // };
  // const layerhandleGroup1 = () => {
  //   setlayerIsGroup1(!layerisGroup1);
  // };
  // const layerhandleGroup2 = () => {
  //   setlayerIsGroup2(!layerisGroup2);
  // };
  // const layerhandleGroup3 = () => {
  //   setlayerIsGroup3(!layerisGroup3);
  // };
  // layer open
  const layerhandleOpenChange = () => {
    setlayerVisible(!layervisible);
  };
  // layer close
  const layerhandlePopoverClose = () => {
    setlayerVisible(false);
  };
  const handleChange = (value) => {
    setSelectedOption(value);
    setSelectedDate(null); // Reset selected date when changing the option
    setSelectedTime(null); // Reset selected time when changing the option
  };
  const handleTestUser = (value) => {
    setSelectedUser(value);
  };

  const handleDateChange = (date, dateString) => {
    setSelectedDate(dateString);
  };
  const handleTimeChange = (time, timeString) => {
    setSelectedTime(timeString);
  };
  const [loadingUrl, setLoadingUrl] = useState(false);

  const artboardSizeType = localStorage.getItem('selectedCardDescription');
  const placementType = localStorage.getItem('selectedPlacement');

  const handleTestUsersSend = async () => {
    KonvaShape.removeTransformer();
    const kp1 = KonvaBoard2.toJSON();
    const parsedKp1 = JSON.parse(kp1);

    const imageUrlValue = parsedKp1.attrs?.stageImg;
    if (!parsedKp1.children || parsedKp1.children.length === 0) {
      Notification(
        'warning',
        'WhiteBoard is empty. Add something to the board.',
      );
      setsendVisible(false);
      return;
    }

    if (audienceType !== 'feed_post') {
      Notification('error', 'Campaign Type should be feed post');
      setsendVisible(false);
      return;
    }

    setLoadingUrl(true);

    // Convert base64 to Blob
    const byteCharacters = atob(imageUrlValue.split(',')[1]);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: 'image/png' });
    const fileUrl = URL.createObjectURL(blob);
    console.log(fileUrl, 'fileUrl');

    const campaignSend = {
      campaign_name: Name,
      campaign_type: audienceType,
      description: 'This is a test campaign',
      post_title: 'Sale 2023',
      post_file: fileUrl,
      post_subtitle: 'Everything on sale now',
      message: '10% off on all',
    };

    try {
      const response = await getApiData(
        'campaigns/add-campaign',
        campaignSend,
        'POST',
      );

      if (response.success === true) {
        console.log('this is response:', response);
        handleCampaignTest();
      } else {
        Notification('error', 'Campaign name already in use');
        setLoadingUrl(false);
        setsendVisible(false);
      }
    } catch (error) {
      Notification('error', 'Error sending the campaign');
      console.log('Error:', error);
      setsendVisible(false);
      setLoadingUrl(false);
    }
  };

  const handleCampaignTest = async () => {
    const kp1 = KonvaBoard2.toJSON();
    console.log('this is kp1', kp1);

    const parsedKp1 = JSON.parse(kp1); // Parse the JSON string

    const imageUrlValue = parsedKp1.attrs?.stageImg;

    const formData = new FormData();
    formData.append('artboard_size', artboardSizeType);
    formData.append('campaign_type', audienceType);
    formData.append('placement', placementType);
    formData.append('post_file', imageUrlValue);

    console.log('this is data:', formData);

    try {
      const response = await getApiData(
        'campaigns/post-campaign-test-users',
        formData,
        'POST',
        {},
        true,
      );

      if (response.success) {
        console.log('this is response:', response);
        Notification('success', 'Post Sent to Test Campaign Successfully');
        setsendVisible(false);
        history.push({ pathname: 'dashboard/marketing-new' });
      } else {
        Notification('error', 'Error sending the campaign');
        setsendVisible(false);
      }
    } catch (error) {
      Notification('error', 'Error sending the campaign');
      console.log('Error:', error);
      setsendVisible(false);
      setLoadingUrl(false);
    } finally {
      setLoadingUrl(false);
    }
  };

  /**
   * Send campaign now button api called
   */
  const handleCampaignSend = async () => {
    // Remove any active transformers
    KonvaShape.removeTransformer();

    const konvaBoard = KonvaBoard2.toJSON();
    const parsedKonvaData = JSON.parse(konvaBoard);
    const imageUrlValue = parsedKonvaData.attrs?.stageImg;

    if (!parsedKonvaData.children || parsedKonvaData.children.length === 0) {
      Notification(
        'warning',
        'WhiteBoard is empty. Add something to the board.',
      );
      return;
    }

    if (!imageUrlValue) {
      Notification('error', 'No image found to send.');
      return;
    }

    if (audienceType !== 'feed_post') {
      Notification('error', 'Campaign Type should be feed post');
      return;
    }

    const campaignSend = {
      user_id: uData?.id,
      artboard_size: artboardSizeType, // message_position column value
      placement: placementType,
      campaign_name: editableText || 'Campaign Title',
      campaign_type: audienceType,
      campaign_tags: '',
      description: '',
      post_title: editableText || 'Campaign Title',
      post_file: imageUrlValue,
      post_subtitle: 'Campaign Subtitle',
      message: 'Campaign Message',
      // segment_id: 123, // Replace with dynamic segment ID
      // attachment_type: 'image', // Replace with dynamic attachment type
      // launch_url: 'https://example.com', // Replace with dynamic URL if available
      // icon_url: 'https://example.com/icon.png', // Replace with dynamic icon URL
      // triggers: 'on_click', // Replace with dynamic triggers
      start_date: selectedDate || null,
      // end_date: selectedTime || null,
      // message_type: 'info', // Replace with dynamic message type
      // button: { text: 'Click Me', url: 'https://example.com' }, // Replace with dynamic button data
      // text: { content: 'Sample Text' }, // Replace with dynamic text data
      // category_id: 1, // Replace with dynamic category ID
    };

    try {
      const response = await getApiData(
        'campaigns/post-campaign-user',
        campaignSend,
        'POST',
      );

      if (response.success) {
        console.log('this is response:', response);
        Notification('success', 'Post sent successfully');
        setsendVisible(false);

        // ⏳ Add 1 second delay before redirect
        setTimeout(() => {
          history.push({ pathname: 'dashboard/marketing-new' });
        }, 1000);
      } else {
        Notification('error', 'Campaign name already in use');
        setsendVisible(false);
      }
    } catch (error) {
      Notification('error', 'Error sending the campaign');
      console.log('Error:', error);
      setsendVisible(false);
      setLoadingUrl(false);
    } finally {
      setLoadingUrl(false);
    }
  };

  // complete this handleSend function as per this file data
  const handleSend = async () => {
    // Remove any active transformers
    KonvaShape.removeTransformer();

    const konvaBoard = KonvaBoard2.toJSON();
    const parsedKonvaData = JSON.parse(konvaBoard);
    const imageUrlValue = parsedKonvaData.attrs?.stageImg;

    if (!parsedKonvaData.children || parsedKonvaData.children.length === 0) {
      Notification(
        'warning',
        'WhiteBoard is empty. Add something to the board.',
      );
      return;
    }

    if (!imageUrlValue) {
      Notification('error', 'No image found to send.');
      return;
    }

    if (audienceType !== 'feed_post') {
      Notification('error', 'Campaign Type should be feed post');
      return;
    }

    if (!selectedDate || !selectedTime) {
      Notification('warning', 'Please select a date and time.');
      return;
    }

    const campaignPayload = {
      user_id: uData?.id,
      artboard_size: artboardSizeType, // message_position column value
      placement: placementType,
      campaign_name: editableText || 'Campaign Title',
      campaign_type: audienceType,
      campaign_tags: '',
      description: '',
      post_title: editableText || 'Campaign Title',
      post_file: imageUrlValue,
      post_subtitle: 'Campaign Subtitle',
      message: 'Campaign Message',
      // segment_id: 123, // Replace with dynamic segment ID
      // attachment_type: 'image', // Replace with dynamic attachment type
      // launch_url: 'https://example.com', // Replace with dynamic URL if available
      // icon_url: 'https://example.com/icon.png', // Replace with dynamic icon URL
      // triggers: 'on_click', // Replace with dynamic triggers
      // message_type: 'info', // Replace with dynamic message type
      // button: { text: 'Click Me', url: 'https://example.com' }, // Replace with dynamic button data
      // text: { content: 'Sample Text' }, // Replace with dynamic text data
      // category_id: 1, // Replace with dynamic category ID
    };

    const scheduledData = {
      ...campaignPayload,
      start_date: `${selectedDate} ${selectedTime}`,
    };

    try {
      const response = await getApiData(
        'campaigns/post-campaign-user',
        scheduledData,
        'POST',
      );

      if (response.success) {
        console.log('this is response:', response);
        Notification('success', 'Post schedule successfully');
        setsendVisible(false);

        // ⏳ Add 1 second delay before redirect
        setTimeout(() => {
          history.push({ pathname: 'dashboard/marketing-new' });
        }, 1000);
      } else {
        Notification('error', 'Campaign name already in use');
        setsendVisible(false);
      }
    } catch (error) {
      Notification('error', 'Error sending the campaign');
      console.log('Error:', error);
      setsendVisible(false);
      setLoadingUrl(false);
    } finally {
      setLoadingUrl(false);
    }
  };

  const settingonChange1 = (checked) => {
    console.log(`switch to ${checked}`);
  };
  const layerhandleHeading = () => {
    setlayerIsHeading(!layerisHeading);
  };

  const handleVirtical = () => {
    setIsClicked(true);
    setIsClicked1(false);
  };

  const handleHorizontal = () => {
    setIsClicked(false);
    setIsClicked1(true);
  };
  const handleStandard = () => {
    setIsClicked(false);
    setIsClicked1(false);
    setIsClicked2(true);
    setIsClicked3(false);
    setIsClicked4(false);
    setIsClicked5(false);
    setIsClicked6(false);
    setIsClicked7(false);
    setWidth(380);
    setHeight(280);
  };
  const handlePostCard = () => {
    setIsClicked(false);
    setIsClicked1(false);
    setIsClicked2(false);
    setIsClicked3(true);
    setIsClicked4(false);
    setIsClicked5(false);
    setIsClicked6(false);
    setIsClicked7(false);
    setWidth(525);
    setHeight(300);
  };
  const handleBusinessCard = () => {
    setIsClicked(false);
    setIsClicked1(false);
    setIsClicked2(false);
    setIsClicked3(false);
    setIsClicked4(true);
    setIsClicked5(false);
    setIsClicked6(false);
    setIsClicked7(false);
    setWidth(600);
    setHeight(260);
  };
  const handleA4 = () => {
    setIsClicked(false);
    setIsClicked1(false);
    setIsClicked2(false);
    setIsClicked3(false);
    setIsClicked4(false);
    setIsClicked5(true);
    setIsClicked6(false);
    setIsClicked7(false);
    setWidth(250);
    setHeight(280);
  };
  const handleA5 = () => {
    setIsClicked(false);
    setIsClicked1(false);
    setIsClicked2(false);
    setIsClicked3(false);
    setIsClicked4(false);
    setIsClicked5(false);
    setIsClicked6(true);
    setIsClicked7(false);
    setWidth(280);
    setHeight(280);
  };
  const handlePosterSmall = () => {
    setIsClicked(false);
    setIsClicked1(false);
    setIsClicked2(false);
    setIsClicked3(false);
    setIsClicked4(false);
    setIsClicked5(false);
    setIsClicked6(false);
    setIsClicked7(true);
    setWidth(200);
    setHeight(300);
  };
  // download popOver
  const [postData, setPostData] = useState({ title: '', body: '' });

  const downloadhandleOpenChange1 = () => {
    setdownloadVisible1(!downloadvisible1);
  };

  const downloadhandlePopoverClose1 = () => {
    setdownloadVisible1(false);
  };

  const [removeBackground, setRemoveBackground] = useState(false);
  const [downloadImageWidth, setDownloadImageWidth] = useState(80);
  const [downloadImageHeight, setDownloadImageHeight] = useState(80);

  const downloadonBGRemove = (checked) => {
    setRemoveBackground(checked);
  };
  const downloadimageSettings = (value) => {
    console.log('changed', value);
  };

  const downloadonimageSettings = (value) => {
    console.log('changed', value);
    setDownloadImageWidth(value);
  };

  const downloadonimageSettingHeight = (value) => {
    console.log('changed', value);
    setDownloadImageHeight(value);
  };

  const [optimizeQuality, setOptimizeQuality] = useState(false);

  const downloadonOQ = (checked) => {
    setOptimizeQuality(checked);
  };

  const [switchChecked, setSwitchChecked] = useState(false);

  const downloadonChange2 = async (checked, event) => {
    KonvaShape.removeTransformer();
    const kb2 = KonvaBoard2.toJSON();
    setSavedTemp(JSON.stringify(KonvaBoard2.toJSON()));
    setSavedTemp(kb2);

    console.log('kb2', kb2);
    console.log('savedtemp', savedTemp);

    const parsedKb2 = JSON.parse(kb2);
    if (!parsedKb2.children || parsedKb2.children.length === 0) {
      Notification(
        'warning',
        'WhiteBoard is empty. Add something to the board.',
      );
      setdownloadVisible1(false);
      return;
    }

    const data = {
      name: Name,
      stage_json: kb2,
      stage_width: width,
      stage_height: height,
    };
    console.log('this is data:', data);

    try {
      const response = await getApiData('ds-templates/save', data, 'POST');

      if (response.success && typeof response.data === 'object') {
        setdownloadVisible1(false);
        Notification('success', 'Post saved Successfully');
        console.log('this is response:', response);
      }
    } catch (error) {
      setdownloadVisible1(false);
      Notification('error', 'Error in saving the Post');

      console.log(error);
    }
  };

  const history = useHistory();
  const handleLeftArrow = () => {
    history.push('/dashboard');
  };

  const [parentHeight, setParentHeight] = useState('30vh'); // State for parent div height

  const handleImageClick = (image) => {
    if (image.id === 'camera' || image.id === 'video' || image.id === 'gif') {
      return;
    }
    setSelectedImage(image.id);
    setSelectedImageDrawer(image.id);
    setSelectedRightSideDrawer(image.id === 'upload');
    setSelectedRightsideTextDrawer(image.id === 'text');
    setSelectedImageSrc(image.src);
    setParentHeight('60vh');
  };

  const isImageSelected = (image) => {
    return selectedImage === image.id;
  };

  const isSelectedImageDrawer = (image) => {
    return selectedImageDrawer === image.id;
  };

  // useEffect(() => {
  //   KonvaShape.onShapeClickCallback = () => {
  //     setSelectedImageDrawer('elements');
  //     // setSelectedRightsideTextDrawer(false);
  //     // setSelectedRightSideDrawer(false);
  //   };

  //   return () => {
  //     KonvaShape.onShapeClickCallback = null; // Clean up the callback when the component unmounts
  //   };
  // }, [setSelectedImageDrawer]);

  const handleExpandToggle = () => {
    setIsExpanded(!isExpanded);
  };

  const drawerWidth = isExpanded ? '97vw' : '20vw';

  const drawerStyle = {
    transform: selectedImageDrawer ? 'translateX(3vw)' : 'none',
    height: '60vh',
    top: '25%',
    backgroundColor: 'fff',
    // marginBottom: '8%', // Default marginBottom value
  };

  const rightSideDrawerStyle = {
    top: '25%',
    // marginBottom: '8%',
    height: '60vh',
  };

  const rightSideTextDrawerStyle = {
    top: '25%',
    /* marginBottom: '8%', */
    height: window.innerWidth > 1300 ? '47vh' : '60vh',
  };

  const onChange = (value) => {
    console.log('changed', value);
  };

  const handleCustomWidth = (value) => {
    setNewWidth(value);
  };

  const handleCustomHeight = (value) => {
    setNewHeight(value);
  };

  const handleConfirm = () => {
    setWidth(newWidth);
    setHeight(newHeight);
  };

  const settingpopoverContent = (
    <div style={{ maxHeight: '500px', overflowY: 'auto' }}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <h3 style={{ fontSize: '15px', color: '#0A0606' }}>SETTINGS</h3>

        <CloseOutlined
          onClick={settinghandlePopoverClose}
          style={{ cursor: 'pointer' }}
        />
      </div>

      <div
        style={{
          fontSize: '12px',
          color: '#0A0606',
          marginTop: '20px',
        }}
      >
        <p>Artboard Size</p>
        {/* <div
          style={{
            display: 'flex',
            gap: '20px',
            marginTop: '-10px',
            marginLeft: '120px',
            justifyContent: 'space-between',
          }}
        >
          <p
            style={{
              fontSize: '10px',
              color: '#0A0606',
              marginTop: '-5px',
            }}
          >
            {' '}
            Orientation
          </p>
          <div style={{ display: 'flex' }}>
            <div
              style={{
                borderRadius: '4px',
                background: isClicked ? '#007AFF' : '#fff',
                // height: '6.5vh',
                // width: '3vw',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginTop: '-14px',
                marginLeft: '-10px',
                padding: '2px 2px 2px 2px',

                cursor: 'pointer',
              }}
              onClick={handleVirtical}
            >
              <Image
                src={RotateLeft}
                preview={false}
                style={{
                  fontSize: '20px',
                  transform: 'rotate(90deg)',
                  filter: isClicked
                    ? ' brightness(0) saturate(100%) invert(100%)'
                    : 'none',
                }}
              />
            </div>
            <div
              style={{
                borderRadius: '4px',
                background: isClicked1 ? '#007AFF' : '#fff',
                // height: '6.5vh',
                // width: '3vw',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginTop: '-14px',
                cursor: 'pointer',
                padding: '0px 5px 0px 5px',
              }}
              onClick={handleHorizontal}
            >
              <Image
                src={RotateLeft}
                preview={false}
                style={{
                  fontSize: '20px',
                  filter: isClicked1
                    ? ' brightness(0) saturate(100%) invert(100%) '
                    : 'none',
                }}
              />
            </div>
          </div>
        </div> */}
      </div>

      <div
        style={{
          display: 'flex',
          // background: isClicked2 ? '#007AFF' : '#fff',
          background: '#F7F7F7',
          justifyContent: 'space-between',
          padding: '0px 10px 0px 10px',
          marginTop: '8px',
          alignItems: 'center',
          cursor: 'default',
        }}
        onClick={handleStandard}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <div>
            <Image
              src={PathLeft}
              preview={false}
              style={{
                width: '20px',
                height: '20px',
                fontSize: '16px',
                filter: isClicked2
                  ? 'sepia(100%) hue-rotate(200deg) saturate(10000%) brightness(80%)'
                  : 'brightness(0) saturate(100%) invert(79%) sepia(7%) hue-rotate(231deg) brightness(102%) contrast(101%)',
              }}
            />
          </div>
          <div style={{ marginLeft: '10px', fontSize: '12px' }}>
            <p>Standard</p>
          </div>
        </div>
        <div style={{ fontSize: '10px', color: '#0A0606' }}>
          1200
          <CloseOutlined />
          1200px
        </div>
      </div>
      <div
        style={{
          display: 'flex',
          cursor: 'default',
          // background: isClicked3 ? '#007AFF' : '#fff',
          background: '#F7F7F7',
          justifyContent: 'space-between',
          padding: '0px 10px 0px 10px',
          marginTop: '5px',
          alignItems: 'center',
        }}
        onClick={handlePostCard}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <div>
            <Image
              src={G1875}
              preview={false}
              style={{
                width: '20px',
                height: '20px',
                fontSize: '16px',
                filter: isClicked3
                  ? 'sepia(100%) hue-rotate(200deg) saturate(10000%) brightness(80%)'
                  : 'brightness(0) saturate(100%) invert(79%) sepia(7%) hue-rotate(231deg) brightness(102%) contrast(101%)',
              }}
            />
          </div>
          <div style={{ marginLeft: '10px', fontSize: '12px' }}>
            <p>PostCard</p>
          </div>
        </div>
        <p style={{ fontSize: '10px', color: '#0A0606' }}>
          4<sup>"</sup>
          <CloseOutlined />6<sup>"</sup>
        </p>
      </div>
      <div
        style={{
          display: 'flex',
          cursor: 'default',
          // background: isClicked4 ? '#007AFF' : '#fff',
          background: '#F7F7F7',
          justifyContent: 'space-between',
          padding: '0px 10px 0px 10px',
          marginTop: '8px',
          alignItems: 'center',
        }}
        onClick={handleBusinessCard}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <div>
            <Image
              src={BusinessCard}
              preview={false}
              style={{
                width: '20px',
                height: '20px',
                fontSize: '16px',
                filter: isClicked4
                  ? 'sepia(100%) hue-rotate(200deg) saturate(10000%) brightness(80%)'
                  : 'brightness(0) saturate(100%) invert(79%) sepia(7%) hue-rotate(231deg) brightness(102%) contrast(101%)',
              }}
            />
          </div>
          <div style={{ marginLeft: '8px', fontSize: '12px' }}>
            <p>Business Card</p>
          </div>
        </div>
        <div style={{ fontSize: '10px', color: '#0A0606' }}>
          2<sup>"</sup>
          <CloseOutlined />
          3.5<sup>"</sup>
        </div>
      </div>
      <div
        style={{
          display: 'flex',
          cursor: 'default',
          // background: isClicked5 ? '#007AFF' : '#fff',
          background: '#F7F7F7',
          justifyContent: 'space-between',
          padding: '0px 10px 0px 10px',
          marginTop: '8px',
          alignItems: 'center',
        }}
        onClick={handleA4}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <div>
            <Image
              src={Layer2123}
              preview={false}
              style={{
                width: '20px',
                height: '20px',
                fontSize: '16px',
                filter: isClicked5
                  ? 'sepia(100%) hue-rotate(200deg) saturate(10000%) brightness(80%)'
                  : 'brightness(0) saturate(100%) invert(79%) sepia(7%) hue-rotate(231deg) brightness(102%) contrast(101%)',
              }}
            />
          </div>
          <div style={{ marginLeft: '8px', fontSize: '12px' }}>
            <p>A4</p>
          </div>
        </div>
        <div
          style={{
            justifyContent: 'space-between',
            fontSize: '10px',
            color: '#0A0606',
          }}
        >
          210
          <CloseOutlined />
          297mm
        </div>
      </div>
      <div
        style={{
          display: 'flex',
          cursor: 'default',
          // background: isClicked6 ? '#007AFF' : '#fff',
          background: '#F7F7F7',
          justifyContent: 'space-between',
          padding: '0px 10px 0px 10px',
          marginTop: '8px',
          alignItems: 'center',
        }}
        onClick={handleA5}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <div>
            <Image
              src={Layer2123}
              preview={false}
              style={{
                width: '20px',
                height: '20px',

                fontSize: '16px',
                filter: isClicked6
                  ? 'sepia(100%) hue-rotate(200deg) saturate(10000%) brightness(80%)'
                  : 'brightness(0) saturate(100%) invert(79%) sepia(7%) hue-rotate(231deg) brightness(102%) contrast(101%)',
              }}
            />
          </div>
          <div style={{ marginLeft: '8px', fontSize: '12px' }}>
            <p>A5</p>
          </div>
        </div>
        <div
          style={{
            justifyContent: 'space-between',

            fontSize: '10px',
            color: '#0A0606',
          }}
        >
          148
          <CloseOutlined />
          210mm
        </div>
      </div>
      <div
        style={{
          display: 'flex',
          cursor: 'default',
          // background: isClicked7 ? '#007AFF' : '#fff',
          background: '#F7F7F7',
          justifyContent: 'space-between',
          padding: '0px 10px 0px 10px',
          marginTop: '8px',
          alignItems: 'center',
        }}
        onClick={handlePosterSmall}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <div>
            <Image
              src={Group9122}
              preview={false}
              style={{
                width: '20px',
                height: '20px',
                fontSize: '16px',
                filter: isClicked7
                  ? 'sepia(100%) hue-rotate(200deg) saturate(10000%) brightness(80%)'
                  : 'brightness(0) saturate(100%) invert(79%) sepia(7%) hue-rotate(231deg) brightness(102%) contrast(101%)',
              }}
            />
          </div>
          <div>
            <p style={{ marginLeft: '8px', fontSize: '12px' }}>Poster Small</p>
          </div>
        </div>
        <div style={{ fontSize: '10px', color: '#0A0606' }}>
          16<sup>"</sup>
          <CloseOutlined />
          20<sup>"</sup>
        </div>
      </div>
      {/* <div>
        <p
          style={{
            textDecoration: 'underline',
            textAlign: 'center',
            background: '#F7F7F7',
            paddingBottom: '5px',
            color: '#2A77F4',
            fontSize: '12px',
            marginTop: '5px',
          }}
        >
          Load More
        </p>
      </div> */}
      <div style={{ marginTop: '10px' }}>
        <p style={{ fontSize: '12px', color: '#0A0606' }}>Custom Size</p>
      </div>
      <div
        style={{
          background: '#F7F7F7',
          display: 'flex',
          justifyContent: 'space-between',
          padding: '0px 10px 0px 10px',
        }}
      >
        <InputNumber
          style={{
            width: '114px',
            height: '28px',
            marginTop: '8px',
          }}
          min={100}
          max={380}
          defaultValue={newWidth}
          onChange={handleCustomWidth}
        />
        <InputNumber
          style={{
            width: '114px',
            height: '28px',
            marginTop: '8px',
            marginLeft: '5px',
          }}
          min={100}
          max={300}
          defaultValue={newHeight}
          onChange={handleCustomHeight}
        />
      </div>
      <div
        style={{
          background: '#F7F7F7',
          display: 'flex',
          justifyContent: 'space-between',
          padding: '0px 10px 0px 10px',
        }}
      >
        <Button
          style={{
            width: '100%',
            height: '28px',
            background: '#2A77F480',
            fontSize: '12px',
            color: '#FFFFFF',
            marginTop: '3px',
            borderRadius: '4px',
          }}
          onClick={handleConfirm}
        >
          Confirm
        </Button>
      </div>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <p
          style={{
            fontSize: '12px',
            color: '#0A0606',
            marginTop: '10px',
          }}
        >
          Trim View
        </p>
        <Switch
          style={{ marginTop: '7px' }}
          defaultChecked
          onChange={settingonChange1}
        />
      </div>
      <Divider style={{ marginTop: '5px' }} />
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <p
          style={{
            fontSize: '12px',
            color: '#0A0606',
            marginTop: '-10px',
            justifyContent: 'center',
          }}
        >
          Show Grid
        </p>
        <Switch
          style={{ marginTop: '-7px' }}
          defaultChecked
          onChange={settingonChange1}
        />
      </div>
      <Divider style={{ marginTop: '5px' }} />
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <p
          style={{
            fontSize: '12px',
            color: '#0A0606',
            marginTop: '-10px',
          }}
        >
          Split Testing(A/B)
        </p>
        <Switch
          style={{ marginTop: '-13px' }}
          defaultChecked
          onChange={settingonChange1}
        />
      </div>
    </div>
  );
  const { TextArea } = Input;
  const [value, setValue] = useState('');
  const [generateUrl, setGenerateUrl] = useState('');
  const [loadingG, setLoadingG] = useState(false);
  const [loading, setLoading] = useState(false);
  const [imageGenerated, setImageGenerated] = useState(false);

  useEffect(() => {
    console.log('generateUrl', generateUrl);
  }, [generateUrl]);

  const handleGenerateAiImage = async () => {
    const trimmedValue = value.trim();
    if (!trimmedValue) {
      Notification('error', 'Input cannot be empty.');
      return;
    }
    setLoadingG(true);
    const ImageData = {
      prompt: value,
    };

    try {
      const response = await getApiData(
        'generative-ai/generate-image',
        ImageData,
        'POST',
      );

      if (response.success) {
        const generatedBase64 = response.image; // Base64-encoded image data
        const imageAI = `data:image/png;base64,${generatedBase64}`;
        // Convert base64 to blob
        // const byteCharacters = atob(generatedBase64);
        // const byteArrays = [];
        // for (let i = 0; i < byteCharacters.length; i++) {
        //   byteArrays.push(byteCharacters.charCodeAt(i));
        // }
        // const imageBlob = new Blob([new Uint8Array(byteArrays)], {
        //   type: 'image/png',
        // });

        // // Create an object URL from the blob
        // const objectUrl = URL.createObjectURL(imageBlob);

        // Store the object URL in Redux or other state management
        store.dispatch(addGenerateImage({ generatedSrc: imageAI }));

        Notification('success', 'AI image generated successfully');
      }
    } catch (error) {
      console.error(error);
      Notification('error', 'Error generating the AI image');
    } finally {
      setLoadingG(false);
    }
  };

  const generatepopovercontent = (
    <div style={{ padding: '10px' }}>
      <TextArea
        value={value}
        onChange={(e) => setValue(e.target.value)}
        placeholder="Enter Text"
        autoSize
        showCount
      />
      <Button
        type="primary"
        style={{
          background: value
            ? 'linear-gradient(137deg, #5087FF 0%, #29EFC4 100%)'
            : 'gray', // Change background color when disabled
          color: '#fff',
          alignItems: 'center',
          marginTop: '15px',
          borderRadius: '10px',
          marginLeft: '4px',
        }}
        icon={<Image preview={false} src={Magic} width={27} height={20} />}
        onClick={handleGenerateAiImage}
        loading={loadingG}
        disabled={!value}
        // disabled={loading}
      >
        <span style={{ marginLeft: '4px' }}>Generate Image</span>
      </Button>
      {loading && (
        <Spin
          style={{ marginTop: '10px' }}
          tip="Generating image as per your prompt..."
        />
      )}
      {imageGenerated && (
        <p style={{ color: 'blue' }}>Image has been generated!</p>
      )}
    </div>
  );

  const { TabPane } = Tabs;
  const [selectedSocialMedia, setSelectedSocialMedia] = useState(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const handleContinue = () => {
    console.log('continue button is clicked');
  };
  const handleIconClick = (socialMedia) => {
    setSelectedSocialMedia(socialMedia);
    setShowConfirmation(true);
  };

  const handleBack = () => {
    setSelectedSocialMedia(null);
    setShowConfirmation(false);
  };

  const [users, setUsers] = useState([]);

  const userData1 = async () => {
    try {
      const response = await getApiData(
        'users/get-test-campaign-users',
        {},
        'GET',
      );
      if (response.success && Array.isArray(response.data)) {
        await processResponse(response.data);
        console.log('response.data', response.data);
      }
    } catch (error) {
      console.error(error);
      // Handle error state here (e.g., display an error message to the user)
    }
  };
  const processResponse = async (data) => {
    setUsers(data);
    console.log('this is test users api response', data);
  };
  useEffect(() => {
    userData1();
  }, []);
  console.log(users);

  const sendpopoverContent2 = (
    <div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
        }}
      >
        <h3 style={{ fontSize: '15px', color: '#0A0606' }}>SEND CAMPAIGN</h3>
        <CloseOutlined
          onClick={sendhandlePopoverClose}
          style={{ cursor: 'pointer' }}
        />
      </div>
      {/* <Tabs defaultActiveKey="send" animated={true} centered> */}
      {/* <TabPane tab="Send" key="send"> */}
      <p style={{ margin: '10px 0px' }}>Send</p>
      <div>
        <Select
          value={selectedOption}
          style={{
            width: '15.1vw',
            height: '40px',
            // marginLeft: '-8px',
            color: '#0A0606',
          }}
          onChange={handleChange}
          suffixIcon={<CaretDownOutlined />}
        >
          <Option value="test users">Send test campaign</Option>
          <Option value="schedule campaign">Schedule campaign</Option>
          <Option
            value="campaign"
            style={{
              background:
                'linear-gradient(to right, rgb(20,135,255),  rgb(41,239,196))',
              color: '#ffff',
              // WebkitBackgroundClip: 'text',
              // WebkitTextFillColor: 'transparent',
            }}
          >
            Send campaign now
          </Option>
        </Select>

        {selectedOption === 'schedule campaign' && (
          <div>
            <DatePicker
              value={selectedDate ? moment(selectedDate, 'YYYY-MM-DD') : null}
              onChange={handleDateChange}
              style={{
                width: '15.1vw',
                marginBottom: '10px',
                // marginLeft: '-8px',
              }}
            />
            {selectedDate && (
              <TimePicker
                value={selectedTime ? moment(selectedTime, 'h:mm A') : null}
                onChange={handleTimeChange}
                format="h:mm A"
                style={{
                  width: '15.1vw',
                  marginBottom: '10px',
                  // marginLeft: '-8px',
                }}
              />
            )}
            <Button
              style={{
                width: '15.1vw',
                height: '40px',
                color: '#fff',
                background: '#2A77F4 0% 0% no-repeat padding-box',
                borderRadius: '4px',
              }}
              type="primary"
              onClick={handleSend}
            >
              Send
            </Button>
          </div>
        )}

        {selectedOption === 'campaign' && (
          <div>
            <Button
              style={{
                width: '15.1vw',
                height: '40px',
                color: '#fff',
                // background: '#2A77F4 0% 0% no-repeat padding-box',
                background:
                  'linear-gradient(138deg, rgb(20, 135, 255) 8%, rgb(41, 239, 196) 90%)',
                borderRadius: '4px',
              }}
              type="primary"
              onClick={handleCampaignSend}
            >
              Send
            </Button>
          </div>
        )}
        {selectedOption === 'test users' && (
          <div>
            <Button
              style={{
                width: '15.1vw',
                height: '40px',
                color: '#fff',
                background: '#2A77F4 0% 0% no-repeat padding-box',
                borderRadius: '4px',
              }}
              type="primary"
              onClick={handleTestUsersSend}
              loading={loadingUrl} // Add the loading prop to the Button component
            >
              Send
            </Button>
          </div>
        )}
      </div>
      {/* </TabPane> */}
      {/* <TabPane tab="Publish" key="publish">
          {showConfirmation ? (
            <div
              style={{
                padding: '10px',
                background: '#e2e2e2',
                borderRadius: '10px',
              }}
            >
              <p>Do you want to send it on {selectedSocialMedia}?</p>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginTop: '20px',
                }}
              >
                <Button
                  onClick={handleBack}
                  style={{ borderRadius: '10px', marginLeft: '10px' }}
                >
                  Back
                </Button>
                <Button
                  type="primary"
                  onClick={handleContinue}
                  style={{ borderRadius: '10px' }}
                >
                  Continue
                </Button>
              </div>
            </div>
          ) : (
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-evenly',
                alignItems: 'center',
                marginTop: '10px',
                marginBottom: '10px',
                width: '15.1vw',
              }}
            >
              {selectedSocialMedia !== 'Facebook' && (
                <FacebookFilled
                  style={{ fontSize: '25px', cursor: 'pointer' }}
                  onClick={() => handleIconClick('Facebook')}
                />
              )}
              {selectedSocialMedia !== 'Instagram' && (
                <InstagramFilled
                  style={{ fontSize: '25px', cursor: 'pointer' }}
                  onClick={() => handleIconClick('Instagram')}
                />
              )}
              {selectedSocialMedia !== 'Twitter' && (
                <TwitterCircleFilled
                  style={{ fontSize: '25px', cursor: 'pointer' }}
                  onClick={() => handleIconClick('Twitter')}
                />
              )}
            </div>
          )}
        </TabPane> */}
      {/* </Tabs> */}
    </div>
  );

  const downloadURI = (uri, name) => {
    const link = document.createElement('a');
    link.download = name;
    link.href = uri;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handlePNGClick = () => {
    KonvaShape.removeTransformer();

    // Get the canvas element
    const canvas = KonvaBoard2.obj.toCanvas();

    // Define the image quality based on the state of the switch
    const quality = optimizeQuality ? 1.0 : 0.5;
    console.log('height and width');

    // canvas.width = downloadImageWidth;
    // canvas.height = downloadImageHeight;

    // Convert the canvas to a data URL (PNG) with specified quality
    const dataURL = canvas.toDataURL('image/png', quality);

    // Trigger the download
    downloadURI(dataURL, 'Image.png');
  };

  const handleJPGClick = () => {
    KonvaShape.removeTransformer();

    // Get the canvas element
    const canvas = KonvaBoard2.obj.toCanvas();

    // Create a temporary canvas with the same dimensions as the KonvaBoard2 canvas
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = canvas.width;
    tempCanvas.height = canvas.height;

    // Get the 2D context of the temporary canvas
    const ctx = tempCanvas.getContext('2d');

    if (!removeBackground) {
      // Draw a white background on the temporary canvas if the switch is unchecked
      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
    }

    ctx.drawImage(canvas, 0, 0);

    const quality = optimizeQuality ? 1.0 : 0.5;

    const dataURL = tempCanvas.toDataURL('image/jpeg', quality);

    // Trigger the download
    downloadURI(dataURL, 'Image.jpeg');
  };

  const downloadpopoverContent = (
    <div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <h3 style={{ fontSize: '15px', color: '#0A0606' }}>DOWNLOAD OPTIONS</h3>
        <CloseOutlined
          onClick={downloadhandlePopoverClose1}
          style={{ cursor: 'pointer', marginTop: '10px' }}
        />
      </div>
      {/* Content of the popover */}
      <p style={{ fontSize: '12px', color: '#0A0606' }}>Size</p>
      <div style={{ background: '#F7F7F7', padding: '0px 10px 10px 10px' }}>
        <div style={{ display: 'flex' }}>
          <p
            style={{
              // marginLeft: '16px',
              color: '#0A0606',
              fontSize: '12px',
              marginTop: '14px',
            }}
          >
            width:
          </p>
          <InputNumber
            style={{
              width: '60px',
              height: '28px',
              marginTop: '8px',
              marginLeft: '5px',
            }}
            min={1}
            max={100}
            value={downloadImageWidth}
            onChange={downloadonimageSettings}
          />
          <p
            style={{
              marginLeft: '16px',
              color: '#0A0606',
              fontSize: '12px',
              marginTop: '14px',
              marginLeft: '5px',
            }}
          >
            Units:
          </p>
          <InputNumber
            type="number"
            style={{
              width: '57px',
              height: '28px',
              marginTop: '8px',
              marginLeft: '10px',
            }}
            min={1}
            max={10}
            defaultValue={3}
            onChange={downloadimageSettings}
          />
        </div>
        <div style={{ display: 'flex' }}>
          <p
            style={{
              // marginLeft: '16px',
              color: '#0A0606',
              fontSize: '12px',
              marginTop: '14px',
              // marginLeft: '5px',
            }}
          >
            Height:
          </p>
          <InputNumber
            style={{
              width: '60px',
              height: '28px',
              marginTop: '8px',
              marginLeft: '5px',
            }}
            min={1}
            max={100}
            defaultValue={80}
            onChange={downloadonimageSettingHeight}
          />
          <p
            style={{
              marginLeft: '16px',
              color: '#0A0606',
              fontSize: '12px',
              marginTop: '14px',
            }}
          >
            Dpi:
          </p>
          <InputNumber
            style={{
              width: '57px',
              height: '28px',
              marginTop: '8px',
              marginLeft: '5px',
            }}
            min={1}
            max={100}
            defaultValue={80}
            onChange={downloadimageSettings}
          />
        </div>
      </div>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <p style={{ fontSize: '12px', color: '#0A0606', marginTop: '14px' }}>
          Remove Background
        </p>
        <Switch
          style={{ marginTop: '14px' }}
          defaultChecked={removeBackground}
          onChange={downloadonBGRemove}
        />
      </div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginTop: '14px',
        }}
      >
        <p style={{ fontSize: '12px', color: '#0A0606' }}>Optimize Quality</p>
        <Switch defaultChecked={optimizeQuality} onChange={downloadonOQ} />
      </div>
      <div style={{ marginTop: '8px', width: '220px' }}>
        <div style={{ background: '#F7F7F7' }}>
          <p
            style={{
              fontSize: '12px',
              color: '#0A0606',
              marginLeft: '16px',
              marginTop: '10px',
            }}
          >
            For Digital Use
          </p>

          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              padding: '10px',
            }}
          >
            <div>
              <Button
                onClick={handlePNGClick}
                style={{
                  width: '90px',
                  height: '28px',
                  background: '#2A77F480',
                  fontSize: '12px',
                  color: '#FFF',
                  borderRadius: '5px',
                }}
              >
                PNG
              </Button>
            </div>
            <div>
              <Button
                onClick={handleJPGClick}
                style={{
                  width: '90px',
                  height: '28px',
                  background: '#2A77F480',
                  fontSize: '12px',
                  color: '#FFF',
                  borderRadius: '5px',
                }}
              >
                JPG
              </Button>
            </div>
          </div>
        </div>
      </div>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <p style={{ fontSize: '12px', color: '#0A0606', marginTop: '16px' }}>
          Save the design as a template
        </p>
        <Switch
          style={{ marginTop: '14px' }}
          defaultUnChecked
          onChange={(checked) => {
            if (checked) {
              downloadonChange2();
            }
          }}
        />
      </div>
    </div>
  );
  // layers
  const handleDragEnd = (result) => {
    console.log('draging');
  };
  // tojson from json

  // console.log(savedTemp);
  // const handleImport = () => {
  //   console.log(savedTemp);
  //   const json = JSON.parse(savedTemp);
  //   KonvaBoard2.fromJSON(json);
  //   console.log(json);
  // };
  useEffect(() => {
    KonvaText.onTextClickCallback = () => {
      setSelectedRightsideTextDrawer(true);
      setSelectedRightSideDrawer(false);
    };

    return () => {
      KonvaText.onTextClickCallback = null; // Clean up the callback when the component unmounts
    };
  }, [setSelectedRightsideTextDrawer]);

  useEffect(() => {
    KonvaUploadImage.onImageClickCallback = () => {
      setSelectedRightSideDrawer(true);
      setSelectedRightsideTextDrawer(false);
    };

    return () => {
      KonvaUploadImage.onImageClickCallback = null; // Clean up the callback when the component unmounts
    };
  }, [setSelectedRightSideDrawer]);

  // Initialize stacks to store undo and redo elements
  const handleUndo = () => {
    if (
      KonvaBoard2.undoStack.length === 0 &&
      KonvaBoard2.redoStack.length === 0
    ) {
      KonvaBoard2.clear();
      return;
    }
    if (KonvaBoard2.undoStack.length === 0) {
      console.log('Undo stack is empty');
      return;
    }
    console.log('Undo clicked');
    const lastChild = KonvaBoard2.undoStack.pop();
    if (lastChild.attr) {
      if (lastChild.attr.includes('align_')) {
        lastChild.element.setAttr('x', lastChild.attrprev.x);
        lastChild.element.setAttr('y', lastChild.attrprev.y);
      } else {
        lastChild.element.setAttr(lastChild.attr, lastChild.attrprev);
      }

      lastChild.element.cache();
    } else {
      lastChild.element.obj.getLayer().remove();
    }
    KonvaBoard2.redoStack.unshift(lastChild);
    console.log('Removed last child element:', lastChild);
    console.log('Removed last child element:', KonvaBoard2.undoStack);
  };

  const handleRedo = () => {
    if (KonvaBoard2.redoStack.length === 0) {
      console.log('Redo stack is empty');
      return;
    }
    console.log('Redo clicked');
    const lastRedoChild = KonvaBoard2.redoStack.shift();
    if (lastRedoChild.attr) {
      if (lastRedoChild.attr.includes('align_')) {
        lastRedoChild.element.setAttr('x', lastRedoChild.attrValue.x);
        lastRedoChild.element.setAttr('y', lastRedoChild.attrValue.y);
      } else {
        lastRedoChild.element.setAttr(
          lastRedoChild.attr,
          lastRedoChild.attrValue,
        );
      }
      lastRedoChild.element.cache();
    } else {
      KonvaBoard2.obj.add(lastRedoChild.element.obj.getLayer());
    }
    // KonvaBoard2.redoStack.pop();
    KonvaBoard2.undoStack.push(lastRedoChild);
    console.log('Redoing last child element:', lastRedoChild);
    console.log('Redoing last child element:', KonvaBoard2.redoStack);
  };

  // document.addEventListener('keydown', (event) => {
  //   if (event.ctrlKey && event.key === 'z') {
  //     event.preventDefault(); // Prevent browser's default behavior (like undoing text)
  //     handleUndo();
  //   } else if (event.ctrlKey && event.key === 'y') {
  //     event.preventDefault(); // Prevent browser's default behavior (like redoing text)
  //     handleRedo();
  //   }
  // });

  const handleDraerClose = () => {
    setSelectedImageDrawer(null);
    setParentHeight('30vh');
  };

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        marginBottom: '-50px',
        height: '100vh',
        width: '100%',
        backgroundColor: '#f7f7f7',
      }}
    >
      {audienceType === 'in_app_message' ? (
        <Modal
          centered
          open={modalOpen}
          onCancel={() => setModalOpen(false)}
          footer={[
            <Button key="create" type="primary" onClick={handleCreate}>
              Create
            </Button>,
          ]}
          width="60%"
        >
          <ArtBoardSizePop onCardSelect={handleCardSelect} />
        </Modal>
      ) : null}
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '10px 30px 0px 30px',
          zIndex: 5,
        }}
      >
        <Link to="/dashboard">
          <Image
            src={logoPng}
            alt="Logo"
            style={{ width: 160 }}
            preview={false}
          />
        </Link>
        <div
          style={{ display: 'flex', flexDirection: 'row', gap: 40, zIndex: 5 }}
        >
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              gap: 20,
              alignItems: 'center',
              zIndex: 2,
            }}
          >
            <Popover
              placement="bottom"
              content={playpopoverContent}
              trigger="click"
              overlayStyle={{ width: '256px' }}
              visible={playvisible}
              onVisibleChange={setplayVisible}
            >
              <Image
                src={PlaySVG}
                preview={false}
                type="primary"
                onClick={playhandleopenChange}
                style={{ cursor: 'pointer' }}
              />
            </Popover>
            <Popover
              placement="bottom"
              content={settingpopoverContent}
              trigger="click"
              overlayStyle={{ width: '256px' }}
              visible={settingvisible}
              onVisibleChange={setsettingVisible}
            >
              <Image
                src={SettingSVG}
                preview={false}
                type="primary"
                onClick={settinghandleOpenChange}
                style={{ cursor: 'pointer' }}
              />
            </Popover>
            <Popover
              placement="bottom"
              trigger="click"
              content={sendpopoverContent2}
              // overlayStyle={{ width: '256px', height: '244px' }}
              visible={sendvisible}
              onVisibleChange={setsendVisible}
            >
              <Image
                src={SendSVG}
                preview={false}
                type="primary"
                onClick={sendhandleOpenChange}
                style={{ cursor: 'pointer' }}
              />
            </Popover>
            <Popover
              placement="bottom"
              content={downloadpopoverContent}
              overlayStyle={{
                width: '256px',
                height: '365px',
                position: 'absolute',
              }}
              trigger="click"
              visible={downloadvisible1}
              onVisibleChange={setdownloadVisible1}
            >
              <Image
                src={DownloadSVG}
                preview={false}
                type="primary"
                onClick={downloadhandleOpenChange1}
                style={{ cursor: 'pointer' }}
                title="DOWNLOAD OPTIONS"
              />
            </Popover>
          </div>
          <MarketingWrapper>
            <ul className="isoRight">
              <li className="isoUser">
                <TopbarUser />
              </li>
            </ul>
          </MarketingWrapper>
        </div>
      </div>
      <div
        style={{
          marginTop: '2vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          paddingLeft: 100,
          zIndex: 5,
        }}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            flexDirection: 'row',
            justifyContent: 'center',
            marginLeft: '-80px',
            zIndex: 5,
          }}
        >
          <Image
            src={EditSVG}
            alt="Edit"
            onClick={handleEditImageClick}
            style={{ cursor: 'pointer', zIndex: 5 }}
            preview={false}
          />
          <Input
            type="text"
            value={editableText}
            onChange={(e) => {
              setEditableText(e.target.value);
              setName(e.target.value); // Store the value in the "Name" variable
            }}
            disabled={!isInputEnabled}
            bordered={isInputBordered}
            style={{
              color: '#0A0606',
              fontWeight: 'bold',
              paddingRight: 0,
              borderRadius: '10px',
              marginLeft: '10px',
              zIndex: 5,
            }}
          />
        </div>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            flexDirection: 'row',
            position: 'absolute',
            left: 30,
            zIndex: 5,
          }}
        >
          <ArrowLeftOutlined
            style={{ color: '#2A77F4', fontSize: 16 }}
            onClick={handleLeftArrow}
          />
          <p
            style={{
              margin: '0px 0px 0px 10px',
              color: '#0A0606',
              fontSize: 14,
              fontWeight: 'bold',
            }}
          >
            Back to Dashboard
          </p>
        </div>
      </div>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginTop: '4.25vh',
          height: '100%',
          width: '100%',
          zIndex: 2,
        }}
      >
        {/* Center Artboard */}
        <div style={{ zIndex: 1 }}>
          <Provider store={store}>
            <KonvaCanvas
              images={whiteboardImages}
              width={width}
              height={height}
              zoomLevel={zoomLevel}
              style={{ position: 'relative' }}
            />
          </Provider>
        </div>
        {/* Side panel */}
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            width: '3vw',
            height: parentHeight,
            backgroundColor: '#ffffff',
            position: 'absolute',
            alignItems: 'center',
            justifyContent: 'center',
            left: 0,
            top: parentHeight === '30vh' ? null : '25%',
            alignItems: 'center',
            boxShadow: '0px 0px 20px #0000001A',
            zIndex: 2,
          }}
        >
          <div
            style={{
              display: 'flex',
              zIndex: 2,

              flexDirection: 'column',
            }}
          >
            {images.map((image) => (
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: '3vw',
                  height: '6vh',
                  background: isImageSelected(image)
                    ? 'linear-gradient(137deg, #5087FF 0%, #29EFC4 100%)'
                    : 'none',
                  zIndex: 2,
                }}
              >
                <Image
                  key={image.id}
                  src={image.src}
                  preview={false}
                  onClick={() => handleImageClick(image)}
                  style={{
                    cursor: 'pointer',
                    width: '1.4vw',
                    filter: isImageSelected(image)
                      ? 'brightness(0) saturate(100%) invert(100%)'
                      : 'none',
                  }}
                />
              </div>
            ))}
            {images.map((image) => (
              <Drawer
                key={image.id}
                title={
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      zIndex: 2,
                    }}
                  >
                    <span style={{ fontSize: 14 }}>{image.title}</span>
                    {/* {(image.id === 'template' || image.id === 'elements') && (
                      <div>
                        {isExpanded ? (
                          <LeftOutlined onClick={handleExpandToggle} />
                        ) : (
                          <RightOutlined onClick={handleExpandToggle} />
                        )}
                      </div>
                    )} */}
                  </div>
                }
                placement="left"
                onClose={handleDraerClose}
                visible={isSelectedImageDrawer(image)}
                mask={false}
                width={drawerWidth}
                style={drawerStyle}
                bodyStyle={{ padding: 10 }}
                headerStyle={{ padding: 10 }}
              >
                {images
                  .filter((image) => image.id === selectedImage)
                  .map((image) => (
                    <div key={image.id}>
                      {React.cloneElement(image.content, {
                        Name,
                        setEditableText,
                        setName,
                        aud_data,
                      })}
                    </div>
                  ))}
              </Drawer>
            ))}
            {selectedRightSideDrawer && (
              <Drawer
                title={
                  <div
                    style={{
                      display: 'flex',
                      backgroundColor: '#FFFFFF',
                      width: 175,
                      padding: 10,
                      zIndex: 2,
                    }}
                  >
                    <span>
                      <Image
                        width={20}
                        height={20}
                        src={Paintbucket}
                        preview={false}
                      />
                    </span>
                    <span
                      style={{ color: '#0A0606', marginLeft: 5, fontSize: 14 }}
                    >
                      {
                        images.find((image) => image.id === 'upload')
                          .rightSideDrawer.title
                      }
                    </span>
                  </div>
                }
                placement="right"
                onClose={() => setSelectedRightSideDrawer(false)}
                visible={selectedRightSideDrawer}
                mask={false}
                width="19.5vw"
                style={rightSideDrawerStyle}
                bodyStyle={{ padding: 0, margin: 0 }}
                headerStyle={{ padding: 0 }}
              >
                {
                  images.find((image) => image.id === 'upload').rightSideDrawer
                    .content
                }
              </Drawer>
            )}
            {selectedRightsideTextDrawer && (
              <Drawer
                title={
                  <div
                    style={{
                      display: 'flex',
                      width: 130,
                      zIndex: 2,
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        backgroundColor: '#FFFFFF',
                        padding: 5,
                        marginLeft: 0,
                        borderRadius: '5px 5px 0px 0px',
                        zIndex: 2,
                      }}
                    >
                      <span>
                        <Image
                          width={15}
                          height={15}
                          src={fontSizeI}
                          style={{ marginLeft: 0 }}
                        />
                      </span>
                      <span
                        style={{
                          color: '#0A0606',
                          fontSize: 13,
                          marginLeft: 5,
                        }}
                      >
                        {
                          images.find((image) => image.id === 'text')
                            .rightSideTextDrawer.title
                        }
                      </span>
                    </div>
                  </div>
                }
                placement="right"
                onClose={() => setSelectedRightsideTextDrawer(false)}
                visible={selectedRightsideTextDrawer}
                mask={false}
                width="19.5vw"
                style={rightSideTextDrawerStyle}
                bodyStyle={{ padding: 0, margin: 0 }}
                headerStyle={{ padding: 0 }}
                customProp1={height}
                customProp2={width}
              >
                {
                  images.find((image) => image.id === 'text')
                    .rightSideTextDrawer.content
                }
              </Drawer>
            )}
          </div>
        </div>
      </div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          marginTop: '8.5vh',
          gap: 10,
          zIndex: 2,
        }}
      >
        <div
          style={{
            position: 'absolute',
            left: 200,
            marginTop: '-10px',
            zIndex: 2,
          }}
        >
          <Popover
            placement="top"
            content={generatepopovercontent}
            trigger="click"
            overlayStyle={{ width: '256px' }}
            visible={generateVisible}
            onVisibleChange={setGenerateVisible}
          >
            <Button
              type="primary"
              style={{
                background: 'linear-gradient(137deg, #5087FF 0%, #29EFC4 100%)',
                color: '#FFFFFF',
                alignItems: 'center',
                borderRadius: '10px',
              }}
              icon={
                <Image preview={false} src={LightBulb} width={26} height={20} />
              }
              onClick={generatehandleopenchnage}
            >
              Generative AI
            </Button>
          </Popover>
        </div>

        <div
          style={{
            display: 'flex',
            backgroundColor: '#fff',
            width: '5vw',
            height: '5.5vh',
            justifyContent: 'space-around',
            alignItems: 'center',
            borderRadius: 4,
            boxShadow: '0px 0px 30px #0000001A',
            zIndex: 2,
          }}
        >
          <Image
            src={UndoSVG}
            preview={false}
            style={{ cursor: 'pointer' }}
            onClick={handleUndo}
          />
          <Image
            src={RedoSVG}
            preview={false}
            style={{ cursor: 'pointer' }}
            onClick={handleRedo}
          />
        </div>
        <div
          style={{
            position: 'relative',
            marginTop: '0px',
            left: 0,
            display: 'flex',
            backgroundColor: '#fff',
            width: '7.8vw',
            height: '5.5vh',
            justifyContent: 'space-around',
            alignItems: 'center',
            borderRadius: 4,
            boxShadow: '0px 0px 30px #0000001A',
            zIndex: 2,
          }}
        >
          <MinusOutlined
            style={{ color: '#A9ABBC', cursor: 'pointer' }}
            onClick={handleZoomOut}
          />
          <div
            style={{
              display: 'flex',
              backgroundColor: '#f7f7f7',
              width: '4.3vw',
              height: '4.1vh',
              justifyContent: 'center',
              textAlign: 'center',
              alignItems: 'center',
              borderRadius: 4,
              zIndex: 2,
            }}
          >
            <p style={{ color: '#A9ABBC' }}>{`${zoomLevel}%`}</p>
          </div>
          <PlusOutlined
            style={{ color: '#A9ABBC', cursor: 'pointer' }}
            onClick={handleZoomIn}
          />
        </div>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'row',
            position: 'absolute',
            right: 80,
            gap: 20,
            zIndex: 2,
          }}
        >
          {/* <p style={{ color: '#A9ABBC' }}>Project color</p>
          <div
            style={{
              backgroundColor: '#fff',
              width: '4.1vh',
              height: '4.1vh',
              borderRadius: 100,
              boxShadow: '0px 0px 10px #0000001A',
              zIndex: 2,
            }}
          />
          <div
            style={{
              display: 'flex',
              backgroundColor: '#fff',
              width: '4.1vh',
              height: '4.1vh',
              borderRadius: 100,
              boxShadow: '0px 0px 10px #0000001A',
              justifyContent: 'center',
              marginLeft: '-28px',
              zIndex: 2,
            }}
          >
            <Image
              src={ColPalSVG}
              preview={false}
              onClick={() => {}}
              style={{ cursor: 'pointer', width: '1vw' }}
            />
          </div> */}
          {/* <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: '#fff',
              gap: 5,
              padding: '0px 5px 0px 5px',
              height: '4.1vh',
              borderRadius: 4,
              boxShadow: '0px 0px 10px #0000001A',
              cursor: 'pointer',
            }}
          >
            <Popover
              placement="top"
              trigger="click"
              content={
                <LayerContent
                  layerhandlePopoverClose={layerhandlePopoverClose}
                  layervisible={layervisible}
                />
              }
            >
              <Image
                onClick={layerhandleOpenChange}
                visible={layervisible}
                onVisibleChange={setlayerVisible}
                src={LayerSVG}
                preview={false}
                type="primary"
                style={{ cursor: 'pointer' }}
              />
            </Popover>
            <p style={{ color: '#A9ABBC' }}>Layers</p>
          </div> */}
          <Popover
            placement="top"
            content={
              <LayerContent
                layerhandlePopoverClose={layerhandlePopoverClose}
                layervisible={layervisible}
              />
            }
            trigger="click"
            visible={layervisible}
            onVisibleChange={setlayerVisible}
          >
            <Button
              type="text"
              icon={<Image preview={false} src={LayerSVG} />}
              onClick={layerhandleOpenChange}
              style={{
                background: '#fff',
                color: '#A9ABBC',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                marginTop: '-5px',
                borderRadius: '10px',
              }}
            >
              <span style={{ marginLeft: '4px' }}>Layers</span>
            </Button>
          </Popover>
        </div>
      </div>
    </div>
  );
}

export default DesignStudio;

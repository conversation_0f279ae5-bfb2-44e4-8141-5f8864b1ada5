import React, { useState } from 'react';
import {
  Col,
  Row,
  Card,
  Avatar,
  Dropdown,
  Space,
  Divider,
  Typography,
} from 'antd';

import {
  MenuOutlined,
  AppstoreFilled,
  DownOutlined,
  StarFilled,
  EllipsisOutlined,
  CopyOutlined,
  SettingFilled,
} from '@ant-design/icons';

export default function MarketingOld() {
  const items = [
    {
      label: 'Value',
      key: '0',
    },
    {
      label: 'Audience size',
      key: '1',
    },
    {
      label: 'Conversion rate',
      key: '2',
    },
    {
      label: 'Quantity',
      key: '3',
    },
    {
      label: 'linkoo',
      key: '4',
    },
    {
      label: 'Reebaby',
      key: '5',
    },
    {
      label: 'Bugaboo AQM',
      key: '6',
    },
    {
      label: 'Maxi-Seat',
      key: '7',
    },
    {
      label: 'Lckle-CSA',
      key: '8',
    },
    {
      label: 'TO-DO',
      key: '9',
    },
    {
      label: 'Pending',
      key: '10',
    },
    {
      label: 'Completed',
      key: '11',
    },
    {
      label: 'Internal projects',
      key: '12',
    },
    {
      label: 'External Projects',
      key: '13',
    },
  ];
  const [selectedIcon, setSelectedIcon] = useState('AppstoreFilled');
  const filteredItems = items.filter(
    (item) => parseInt(item.key, 10) >= 3 && parseInt(item.key, 10) <= 8,
  );
  const filteredItems1 = items.filter(
    (item) => parseInt(item.key, 10) >= 0 && parseInt(item.key, 10) <= 3,
  ); // get items with key values between '0' and '4'
  const filteredItems2 = items.filter(
    (item) => parseInt(item.key, 10) >= 9 && parseInt(item.key, 10) <= 11,
  );
  const filteredItems3 = items.filter((item) => parseInt(item.key, 10) >= 12);
  return (
    <div style={{ display: 'flex' }}>
      <div style={{ width: '75%', height: '100%' }}>
        <div style={{ background: '#FFF' }}>
          <div style={{ display: 'flex' }}>
            <AppstoreFilled
              style={{
                width: '24px',
                height: '24px',
                fontSize: '25px',
                color:
                  selectedIcon === 'AppstoreFilled' ? '#2A77F4' : '#A9ABBC',
                marginLeft: '23px',
                marginTop: '28px',
              }}
              onClick={() => setSelectedIcon('AppstoreFilled')}
            />
            <MenuOutlined
              style={{
                width: '24px',
                fontSize: '23px',
                height: '24px',
                color: selectedIcon === 'MenuOutlined' ? '#2A77F4' : '#A9ABBC',
                marginLeft: '25px',
                marginTop: '28px',
              }}
              onClick={() => setSelectedIcon('MenuOutlined')}
            />
            <p
              style={{
                marginTop: '25px',
                marginLeft: '245px',
                fontFamily: 'Rubik',
                fontSize: '14px',
                color: '#A9ABBC',
                marginBottom: '-5px',
              }}
            >
              Clients
            </p>
            <p
              style={{
                marginTop: '25px',
                marginLeft: '55px',
                fontFamily: 'Rubik',
                fontSize: '14px',
                color: '#A9ABBC',
                marginBottom: '-5px',
              }}
            >
              Label
            </p>
            <p
              style={{
                marginTop: '25px',
                marginLeft: '70px',
                fontFamily: 'Rubik',
                fontSize: '14px',
                color: '#A9ABBC',
                marginBottom: '-5px',
              }}
            >
              Category
            </p>
            <p
              style={{
                marginTop: '25px',
                marginLeft: '50px',
                fontFamily: 'Rubik',
                fontSize: '14px',
                color: '#A9ABBC',
                marginBottom: '-5px',
              }}
            >
              sort
            </p>
          </div>
          <Dropdown
            menu={{
              items: filteredItems,
            }}
            trigger={['click']}
          >
            <Space
              style={{
                color: '#0A0606',
                marginLeft: '340px',
                fontSize: '13px',
                fontFamily: 'Rubik',
              }}
            >
              All Clients
              <DownOutlined />
            </Space>
          </Dropdown>
          <Dropdown
            menu={{
              items: filteredItems2,
            }}
            trigger={['click']}
          >
            <Space
              style={{
                color: '#0A0606',
                fontSize: '13px',
                marginLeft: '20px',
                fontFamily: 'Rubik',
              }}
            >
              Inprogress
              <DownOutlined />
            </Space>
          </Dropdown>
          <Dropdown
            menu={{
              items: filteredItems3,
            }}
            trigger={['click']}
          >
            <Space
              style={{
                color: '#0A0606',
                fontSize: '13px',
                marginLeft: '24px',
                fontFamily: 'Rubik',
              }}
            >
              Internal
              <DownOutlined />
            </Space>
          </Dropdown>
          <Dropdown
            menu={{
              items: filteredItems1,
            }}
            trigger={['click']}
          >
            <Space
              style={{
                color: '#0A0606',
                fontSize: '13px',
                marginLeft: '35px',
                fontFamily: 'Rubik',
              }}
            >
              By Activity
              <DownOutlined />
            </Space>
          </Dropdown>
          <div>
            <div
              style={{
                height: '100%',
                width: '59vw',
                paddingTop: '29.86px',
                paddingLeft: '10px',
                paddingRight: '10px',
                paddingBottom: '55px',
                overflowX: 'hidden',
                overflowY: 'hidden',
                marginTop: '10px',
                whitespace: 'nowrap',
                display: selectedIcon === 'AppstoreFilled' ? 'block' : 'none',
              }}
            >
              <Row gutter={[16, 16]}>
                <Col span={12} md={6}>
                  <Card
                    style={{
                      height: '34vh',
                      width: '14vw',
                      borderRadius: '12px',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                      }}
                    >
                      <h4
                        style={{
                          color: '#0A0606',
                          fontFamily: 'Rubik',
                          letterSpacing: '0px',
                          fontSize: '14px',
                          marginLeft: '5px',
                        }}
                      >
                        Video Campaign
                      </h4>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                        marginTop: '5px',
                      }}
                    >
                      <p
                        style={{
                          color: '#A9ABBC',
                          fontSize: '12px',
                          fontFamily: 'Rubik',
                          marginLeft: '10px',
                        }}
                      >
                        - Application Concept
                      </p>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                        marginTop: '5px',
                      }}
                    >
                      <p
                        style={{
                          color: '#A9ABBC',
                          fontSize: '12px',
                          fontFamily: 'Rubik',
                          marginLeft: '10px',
                        }}
                      >
                        - Website Concept
                      </p>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                        marginTop: '5px',
                      }}
                    >
                      <Avatar
                        style={{ marginTop: '15px', marginLeft: '10px' }}
                        src={
                          <img
                            src="https://cdn-icons-png.flaticon.com/512/201/201634.png"
                            alt="User"
                          />
                        }
                      />
                      <Avatar
                        style={{ marginTop: '15px', marginLeft: '8px' }}
                        src={
                          <img
                            src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTWzw8_87ASKSkDuQx7gbTSbjJtcxUQ7aXD9Q&usqp=CAU"
                            alt="User"
                          />
                        }
                      />
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        marginLeft: '-15px',
                        marginRight: '-10px',
                      }}
                    >
                      <p
                        style={{
                          color: '#A9ABBC',
                          fontSize: '11px',
                          fontFamily: 'Rubik',
                          marginTop: '10px',
                          marginLeft: '5px',
                        }}
                      >
                        Activity recently
                      </p>
                      <StarFilled
                        style={{
                          fontSize: '16px',
                          color: '#FFC107',
                          marginTop: '10px',
                          marginLeft: '15px',
                        }}
                      />
                      <EllipsisOutlined
                        style={{
                          fontSize: '20px',
                          color: '#A9ABBC',
                          marginTop: '10.5px',
                          marginLeft: '5px',
                        }}
                      />
                    </div>
                  </Card>
                </Col>
                <Col span={12} md={6}>
                  <Card
                    style={{
                      height: '34vh',
                      width: '14vw',
                      borderRadius: '12px',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                      }}
                    >
                      <h4
                        style={{
                          color: '#0A0606',
                          fontFamily: 'Rubik',
                          letterSpacing: '0px',
                          fontSize: '14px',
                          marginLeft: '5px',
                        }}
                      >
                        NPS
                      </h4>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                        marginTop: '5px',
                      }}
                    >
                      <p
                        style={{
                          color: '#A9ABBC',
                          fontSize: '12px',
                          fontFamily: 'Rubik',
                          marginLeft: '10px',
                        }}
                      >
                        - Statistics and Overview
                      </p>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                        marginTop: '5px',
                      }}
                    >
                      <p
                        style={{
                          color: '#F76A4F',
                          fontSize: '12px',
                          fontFamily: 'Rubik',
                          marginLeft: '10px',
                        }}
                      >
                        <EMAIL>
                      </p>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                        marginTop: '5px',
                      }}
                    >
                      <Avatar
                        style={{ marginTop: '15px', marginLeft: '10px' }}
                        src={
                          <img
                            src="https://cdn-icons-png.flaticon.com/512/201/201634.png"
                            alt="User"
                          />
                        }
                      />
                      <Avatar
                        style={{ marginTop: '15px', marginLeft: '8px' }}
                        src={
                          <img
                            src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTWzw8_87ASKSkDuQx7gbTSbjJtcxUQ7aXD9Q&usqp=CAU"
                            alt="User"
                          />
                        }
                      />
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        marginLeft: '-15px',
                        marginRight: '-10px',
                      }}
                    >
                      <p
                        style={{
                          color: '#A9ABBC',
                          fontSize: '11px',
                          fontFamily: 'Rubik',
                          marginTop: '10px',
                          marginLeft: '5px',
                        }}
                      >
                        1 day ago
                      </p>
                      <StarFilled
                        style={{
                          fontSize: '16px',
                          color: '#FFC107',
                          marginTop: '10px',
                          marginLeft: '50px',
                        }}
                      />
                      <EllipsisOutlined
                        style={{
                          fontSize: '20px',
                          color: '#A9ABBC',
                          marginTop: '10.5px',
                          marginLeft: '5px',
                        }}
                      />
                    </div>
                  </Card>
                </Col>
                <Col span={12} md={6}>
                  <Card
                    style={{
                      height: '34vh',
                      width: '14vw',
                      borderRadius: '12px',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                      }}
                    >
                      <h4
                        style={{
                          color: '#0A0606',
                          fontFamily: 'Rubik',
                          letterSpacing: '0px',
                          fontSize: '14px',
                          marginLeft: '5px',
                        }}
                      >
                        Internal
                      </h4>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                        marginTop: '5px',
                      }}
                    >
                      <p
                        style={{
                          color: '#A9ABBC',
                          fontSize: '12px',
                          fontFamily: 'Rubik',
                          marginLeft: '10px',
                        }}
                      >
                        - Application Concept
                      </p>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                        marginTop: '5px',
                      }}
                    >
                      <p
                        style={{
                          color: '#A9ABBC',
                          fontSize: '12px',
                          fontFamily: 'Rubik',
                          marginLeft: '10px',
                        }}
                      >
                        - Website Concept
                      </p>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                        marginTop: '5px',
                      }}
                    >
                      <Avatar
                        style={{ marginTop: '15px', marginLeft: '10px' }}
                        src={
                          <img
                            src="https://cdn-icons-png.flaticon.com/512/201/201634.png"
                            alt="User"
                          />
                        }
                      />
                      <Avatar
                        style={{ marginTop: '15px', marginLeft: '8px' }}
                        src={
                          <img
                            src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTWzw8_87ASKSkDuQx7gbTSbjJtcxUQ7aXD9Q&usqp=CAU"
                            alt="User"
                          />
                        }
                      />
                      <Avatar
                        style={{ marginTop: '15px', marginLeft: '8px' }}
                        src={
                          <img
                            src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSEMpUS3NxMp4i7QkSKNn40Oax_w-mLBFsoPA&usqp=CAU"
                            alt="User"
                          />
                        }
                      />
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        marginLeft: '-15px',
                        marginRight: '-10px',
                      }}
                    >
                      <p
                        style={{
                          color: '#A9ABBC',
                          fontSize: '11px',
                          fontFamily: 'Rubik',
                          marginTop: '10px',
                          marginLeft: '5px',
                        }}
                      >
                        1 day ago
                      </p>
                      <StarFilled
                        style={{
                          fontSize: '16px',
                          color: '#FFC107',
                          marginTop: '10px',
                          marginLeft: '45px',
                        }}
                      />
                      <EllipsisOutlined
                        style={{
                          fontSize: '20px',
                          color: '#A9ABBC',
                          marginTop: '10.5px',
                          marginLeft: '5px',
                        }}
                      />
                    </div>
                  </Card>
                </Col>
                <Col span={12} md={6}>
                  <Card
                    style={{
                      height: '34vh',
                      width: '14vw',
                      borderRadius: '12px',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                      }}
                    >
                      <h4
                        style={{
                          color: '#0A0606',
                          fontFamily: 'Rubik',
                          letterSpacing: '0px',
                          fontSize: '14px',
                          marginLeft: '5px',
                        }}
                      >
                        Growth Hacking
                      </h4>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                        marginTop: '5px',
                      }}
                    >
                      <p
                        style={{
                          color: '#A9ABBC',
                          fontSize: '12px',
                          fontFamily: 'Rubik',
                          marginLeft: '10px',
                        }}
                      >
                        Ideas, challenges and tests
                      </p>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                        marginTop: '10px',
                      }}
                    >
                      <Avatar
                        style={{ marginTop: '15px', marginLeft: '10px' }}
                        src={
                          <img
                            src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSZlAcC_1n7gulS21qRrKRo-FYLW4xLt9y2eA&usqp=CAU"
                            alt="User"
                          />
                        }
                      />
                      <Avatar
                        style={{ marginTop: '15px', marginLeft: '8px' }}
                        src={
                          <img
                            src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTWzw8_87ASKSkDuQx7gbTSbjJtcxUQ7aXD9Q&usqp=CAU"
                            alt="User"
                          />
                        }
                      />
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        marginLeft: '-15px',
                        marginRight: '-10px',
                      }}
                    >
                      <p
                        style={{
                          color: '#A9ABBC',
                          fontSize: '11px',
                          fontFamily: 'Rubik',
                          marginTop: '10px',
                          marginLeft: '5px',
                        }}
                      >
                        2 day ago
                      </p>
                      <StarFilled
                        style={{
                          fontSize: '16px',
                          color: '#A9ABBC',
                          marginTop: '10px',
                          marginLeft: '50px',
                        }}
                      />
                      <EllipsisOutlined
                        style={{
                          fontSize: '20px',
                          color: '#A9ABBC',
                          marginTop: '10.5px',
                          marginLeft: '5px',
                        }}
                      />
                    </div>
                  </Card>
                </Col>
                <Col span={12} md={6}>
                  <Card
                    style={{
                      height: '34vh',
                      width: '14vw',
                      borderRadius: '12px',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                      }}
                    >
                      <h4
                        style={{
                          color: '#0A0606',
                          fontFamily: 'Rubik',
                          letterSpacing: '0px',
                          fontSize: '14px',
                          marginLeft: '5px',
                        }}
                      >
                        Product Marketing
                      </h4>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                        marginTop: '5px',
                      }}
                    >
                      <p
                        style={{
                          color: '#A9ABBC',
                          fontSize: '12px',
                          fontFamily: 'Rubik',
                          marginLeft: '10px',
                        }}
                      >
                        All things marketing
                      </p>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                        marginTop: '30px',
                      }}
                    >
                      <Avatar
                        style={{ marginTop: '15px', marginLeft: '10px' }}
                        src={
                          <img
                            src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSZlAcC_1n7gulS21qRrKRo-FYLW4xLt9y2eA&usqp=CAU"
                            alt="User"
                          />
                        }
                      />
                      <Avatar
                        style={{ marginTop: '15px', marginLeft: '8px' }}
                        src={
                          <img
                            src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTWzw8_87ASKSkDuQx7gbTSbjJtcxUQ7aXD9Q&usqp=CAU"
                            alt="User"
                          />
                        }
                      />
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                      }}
                    >
                      <p
                        style={{
                          color: '#A9ABBC',
                          fontSize: '11px',
                          fontFamily: 'Rubik',
                          marginTop: '10px',
                          marginLeft: '5px',
                        }}
                      >
                        1 week ago
                      </p>
                      <StarFilled
                        style={{
                          fontSize: '16px',
                          color: '#A9ABBC',
                          marginTop: '10px',
                          marginLeft: '43px',
                        }}
                      />
                      <EllipsisOutlined
                        style={{
                          fontSize: '20px',
                          color: '#A9ABBC',
                          marginTop: '10.5px',
                          marginLeft: '5px',
                        }}
                      />
                    </div>
                  </Card>
                </Col>
                <Col span={12} md={6}>
                  <Card
                    style={{
                      height: '34vh',
                      width: '14vw',
                      borderRadius: '12px',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                      }}
                    >
                      <h4
                        style={{
                          color: '#0A0606',
                          fontFamily: 'Rubik',
                          letterSpacing: '0px',
                          fontSize: '14px',
                          marginLeft: '5px',
                        }}
                      >
                        Mobile App Design
                      </h4>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                        marginTop: '5px',
                      }}
                    >
                      <p
                        style={{
                          color: '#A9ABBC',
                          fontSize: '12px',
                          fontFamily: 'Rubik',
                          marginLeft: '10px',
                        }}
                      >
                        Prototypes, mockups, client <br />
                        and collabaration
                      </p>
                    </div>

                    <div
                      style={{
                        display: 'flex',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                      }}
                    >
                      <Avatar
                        style={{ marginTop: '10px', marginLeft: '10px' }}
                        src={
                          <img
                            src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSZlAcC_1n7gulS21qRrKRo-FYLW4xLt9y2eA&usqp=CAU"
                            alt="User"
                          />
                        }
                      />
                      <Avatar
                        style={{ marginTop: '10px', marginLeft: '8px' }}
                        src={
                          <img
                            src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTWzw8_87ASKSkDuQx7gbTSbjJtcxUQ7aXD9Q&usqp=CAU"
                            alt="User"
                          />
                        }
                      />
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        marginLeft: '-15px',
                        marginRight: '-15px',
                      }}
                    >
                      <p
                        style={{
                          color: '#A9ABBC',
                          fontSize: '11px',
                          fontFamily: 'Rubik',
                          marginTop: '8px',
                          marginLeft: '8px',
                        }}
                      >
                        1 week ago
                      </p>
                      <StarFilled
                        style={{
                          fontSize: '16px',
                          color: '#FFC107',
                          marginTop: '8px',
                          marginLeft: '40px',
                        }}
                      />
                      <EllipsisOutlined
                        style={{
                          fontSize: '20px',
                          color: '#A9ABBC',
                          marginTop: '8.5px',
                          marginLeft: '5px',
                        }}
                      />
                    </div>
                  </Card>
                </Col>
                <Col span={12} md={6}>
                  <Card
                    style={{
                      height: '34vh',
                      width: '14vw',
                      borderRadius: '12px',
                      border: '6px',
                      borderStyle: 'solid',
                      borderColor: '#7070701A',
                    }}
                  >
                    <div>
                      <CopyOutlined
                        style={{
                          fontSize: '40px',
                          color: '#A9ABBC',
                          marginTop: '30px',
                          marginLeft: '35px',
                        }}
                      />
                      <h4
                        style={{
                          color: '#A9ABBC',
                          fontFamily: 'Rubik',
                          fontSize: '12px',
                          marginLeft: '25px',
                          marginTop: '10px',
                        }}
                      >
                        Make a copy
                      </h4>
                    </div>
                  </Card>
                </Col>
                <Col span={12} md={6}>
                  <Card
                    style={{
                      height: '34vh',
                      width: '14vw',
                      borderRadius: '12px',
                    }}
                    cover={
                      <img
                        style={{
                          height: '34vh',
                          width: '14vw',
                          borderRadius: '12px',
                        }}
                        src="https://png.pngtree.com/background/20210710/original/pngtree-may-fourth-youth-day-geometric-blue-background-picture-image_1036932.jpg"
                        alt="Chill Baby"
                      />
                    }
                  />
                </Col>
              </Row>
            </div>

            {/* 1 */}
            <div
              style={{
                height: '500px',
                width: '100%',
                paddingTop: '29.5px',
                paddingBottom: '40px',
                paddingLeft: '23px',
                paddingRight: '23px',
                overflowX: 'hidden',
                overflowY: 'overflow',
                whitespace: 'nowrap',
                display: selectedIcon === 'MenuOutlined' ? 'block' : 'none',
              }}
            >
              <Card
                style={{
                  width: '56.25vw',
                  height: '14.3vh',
                  marginLeft: '-10px',
                  borderRadius: '12px',
                  marginTop: '20px',
                }}
              >
                <div style={{ display: 'flex', flexDirection: 'row' }}>
                  <div style={{ marginTop: '-40px' }}>
                    <h3
                      style={{
                        color: '#0A0606',
                        fontSize: '14px',
                        fontFamily: 'Rubik',
                        letterSpacing: '0px',
                        marginTop: '24px',
                        marginLeft: '10px',
                      }}
                    >
                      Video campaign
                    </h3>
                    <p
                      style={{
                        color: '#A9ABBC',
                        fontSize: '12px',
                        fontFamily: 'Rubik',
                        marginLeft: '10px',
                      }}
                    >
                      - Application Concept
                    </p>
                    <p
                      style={{
                        color: '#A9ABBC',
                        fontFamily: 'Rubik',
                        fontSize: '12px',
                        marginLeft: '10px',
                        marginRight: '-30px',
                      }}
                    >
                      - Website Concept
                    </p>
                  </div>
                  <div>
                    <Avatar
                      style={{ marginTop: '2px', marginLeft: '260px' }}
                      src={
                        <img
                          src="https://cdn-icons-png.flaticon.com/512/201/201634.png"
                          alt="User"
                        />
                      }
                    />
                    <Avatar
                      style={{ marginTop: '2px', marginLeft: '10px' }}
                      src={
                        <img
                          src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTWzw8_87ASKSkDuQx7gbTSbjJtcxUQ7aXD9Q&usqp=CAU"
                          alt="User"
                        />
                      }
                    />
                  </div>
                  <div>
                    <p
                      style={{
                        color: '#A9ABBC',
                        fontFamily: 'Rubik',
                        fontSize: '12px',
                        marginLeft: '40px',
                        marginTop: '4px',
                      }}
                    >
                      Activity recently
                    </p>
                  </div>
                  <div>
                    <StarFilled
                      style={{
                        color: '#FFC107',
                        marginLeft: '20px',
                        marginTop: '4px',
                        fontSize: '20px',
                      }}
                    />
                  </div>
                </div>
              </Card>
              {/* 2 */}
              <Card
                style={{
                  width: '56.25vw',
                  height: '14.3vh',
                  marginLeft: '-10px',
                  borderRadius: '12px',
                  marginTop: '20px',
                }}
              >
                <div style={{ display: 'flex', flexDirection: 'row' }}>
                  <div style={{ marginTop: '-40px' }}>
                    <h3
                      style={{
                        color: '#0A0606',
                        fontSize: '14px',
                        fontFamily: 'Rubik',
                        letterSpacing: '0px',
                        marginTop: '24px',
                        marginLeft: '10px',
                      }}
                    >
                      NPS
                    </h3>
                    <p
                      style={{
                        color: '#A9ABBC',
                        fontSize: '12px',
                        fontFamily: 'Rubik',
                        marginLeft: '10px',
                      }}
                    >
                      - Statistics and Overview
                    </p>
                    <p
                      style={{
                        color: '#F76A4F',
                        fontFamily: 'Rubik',
                        fontSize: '12px',
                        marginLeft: '10px',
                        marginRight: '-30px',
                      }}
                    >
                      <EMAIL>
                    </p>
                  </div>
                  <div>
                    <Avatar
                      style={{ marginTop: '2px', marginLeft: '240px' }}
                      src={
                        <img
                          src="https://cdn-icons-png.flaticon.com/512/201/201634.png"
                          alt="User"
                        />
                      }
                    />
                    <Avatar
                      style={{ marginTop: '2px', marginLeft: '10px' }}
                      src={
                        <img
                          src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTWzw8_87ASKSkDuQx7gbTSbjJtcxUQ7aXD9Q&usqp=CAU"
                          alt="User"
                        />
                      }
                    />
                  </div>
                  <div>
                    <p
                      style={{
                        color: '#A9ABBC',
                        fontFamily: 'Rubik',
                        fontSize: '12px',
                        marginLeft: '40px',
                        marginTop: '4px',
                      }}
                    >
                      1 day ago
                    </p>
                  </div>
                  <div>
                    <StarFilled
                      style={{
                        color: '#FFC107',
                        marginLeft: '60px',
                        marginTop: '4px',
                        fontSize: '20px',
                      }}
                    />
                  </div>
                </div>
              </Card>
              <Card
                style={{
                  width: '56.25vw',
                  height: '14.3vh',
                  marginLeft: '-10px',
                  borderRadius: '12px',
                  marginTop: '20px',
                }}
              >
                <div style={{ display: 'flex', flexDirection: 'row' }}>
                  <div style={{ marginTop: '-40px' }}>
                    <h3
                      style={{
                        color: '#0A0606',
                        fontSize: '14px',
                        fontFamily: 'Rubik',
                        letterSpacing: '0px',
                        marginTop: '24px',
                        marginLeft: '10px',
                      }}
                    >
                      Internal
                    </h3>
                    <p
                      style={{
                        color: '#A9ABBC',
                        fontSize: '12px',
                        fontFamily: 'Rubik',
                        marginLeft: '10px',
                      }}
                    >
                      - Application Concept
                    </p>
                    <p
                      style={{
                        color: '#A9ABBC',
                        fontFamily: 'Rubik',
                        fontSize: '12px',
                        marginLeft: '10px',
                        marginRight: '-30px',
                      }}
                    >
                      - Website Concept
                    </p>
                  </div>
                  <div>
                    <Avatar
                      style={{ marginTop: '2px', marginLeft: '220px' }}
                      src={
                        <img
                          src="https://cdn-icons-png.flaticon.com/512/201/201634.png"
                          alt="User"
                        />
                      }
                    />
                    <Avatar
                      style={{ marginTop: '2px', marginLeft: '10px' }}
                      src={
                        <img
                          src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSZlAcC_1n7gulS21qRrKRo-FYLW4xLt9y2eA&usqp=CAU"
                          alt="User"
                        />
                      }
                    />
                    <Avatar
                      style={{ marginTop: '2px', marginLeft: '10px' }}
                      src={
                        <img
                          src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTWzw8_87ASKSkDuQx7gbTSbjJtcxUQ7aXD9Q&usqp=CAU"
                          alt="User"
                        />
                      }
                    />
                  </div>
                  <div>
                    <p
                      style={{
                        color: '#A9ABBC',
                        fontFamily: 'Rubik',
                        fontSize: '12px',
                        marginLeft: '40px',
                        marginTop: '4px',
                      }}
                    >
                      1 day ago
                    </p>
                  </div>
                  <div>
                    <StarFilled
                      style={{
                        color: '#FFC107',
                        marginLeft: '60px',
                        marginTop: '4px',
                        fontSize: '20px',
                      }}
                    />
                  </div>
                </div>
              </Card>
              {/* 3 */}
              <Card
                style={{
                  width: '56.25vw',
                  height: '14.3vh',
                  marginLeft: '-10px',
                  borderRadius: '12px',
                  marginTop: '20px',
                }}
              >
                <div style={{ display: 'flex', flexDirection: 'row' }}>
                  <div style={{ marginTop: '-40px' }}>
                    <h3
                      style={{
                        color: '#0A0606',
                        fontSize: '14px',
                        fontFamily: 'Rubik',
                        letterSpacing: '0px',
                        marginTop: '24px',
                        marginLeft: '10px',
                      }}
                    >
                      Growth Hacking
                    </h3>
                    <p
                      style={{
                        color: '#A9ABBC',
                        fontSize: '12px',
                        fontFamily: 'Rubik',
                        marginLeft: '10px',
                      }}
                    >
                      Ideas, challenges and tests
                    </p>
                  </div>
                  <div>
                    <Avatar
                      style={{ marginTop: '2px', marginLeft: '230px' }}
                      src={
                        <img
                          src="https://cdn-icons-png.flaticon.com/512/201/201634.png"
                          alt="User"
                        />
                      }
                    />
                    <Avatar
                      style={{ marginTop: '2px', marginLeft: '10px' }}
                      src={
                        <img
                          src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTWzw8_87ASKSkDuQx7gbTSbjJtcxUQ7aXD9Q&usqp=CAU"
                          alt="User"
                        />
                      }
                    />
                  </div>
                  <div>
                    <p
                      style={{
                        color: '#A9ABBC',
                        fontFamily: 'Rubik',
                        fontSize: '12px',
                        marginLeft: '40px',
                        marginTop: '4px',
                      }}
                    >
                      1 day ago
                    </p>
                  </div>
                  <div>
                    <StarFilled
                      style={{
                        color: '#FFC107',
                        marginLeft: '60px',
                        marginTop: '4px',
                        fontSize: '20px',
                      }}
                    />
                  </div>
                </div>
              </Card>
              {/* 4 */}
              <Card
                style={{
                  width: '56.25vw',
                  height: '14.3vh',
                  marginLeft: '-10px',
                  borderRadius: '12px',
                  marginTop: '20px',
                }}
              >
                <div style={{ display: 'flex', flexDirection: 'row' }}>
                  <div style={{ marginTop: '-40px' }}>
                    <h3
                      style={{
                        color: '#0A0606',
                        fontSize: '14px',
                        fontFamily: 'Rubik',
                        letterSpacing: '0px',
                        marginTop: '24px',
                        marginLeft: '10px',
                      }}
                    >
                      Product Marketing
                    </h3>
                    <p
                      style={{
                        color: '#A9ABBC',
                        fontSize: '12px',
                        fontFamily: 'Rubik',
                        marginLeft: '10px',
                      }}
                    >
                      All things marketing
                    </p>
                  </div>
                  <div>
                    <Avatar
                      style={{ marginTop: '2px', marginLeft: '260px' }}
                      src={
                        <img
                          src="https://cdn-icons-png.flaticon.com/512/201/201634.png"
                          alt="User"
                        />
                      }
                    />
                    <Avatar
                      style={{ marginTop: '2px', marginLeft: '10px' }}
                      src={
                        <img
                          src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTWzw8_87ASKSkDuQx7gbTSbjJtcxUQ7aXD9Q&usqp=CAU"
                          alt="User"
                        />
                      }
                    />
                  </div>
                  <div>
                    <p
                      style={{
                        color: '#A9ABBC',
                        fontFamily: 'Rubik',
                        fontSize: '12px',
                        marginLeft: '40px',
                        marginTop: '4px',
                      }}
                    >
                      1 week ago
                    </p>
                  </div>
                  <div>
                    <StarFilled
                      style={{
                        color: '#FFC107',
                        marginLeft: '50px',
                        marginTop: '4px',
                        fontSize: '20px',
                      }}
                    />
                  </div>
                </div>
              </Card>
              {/* 5 */}
              <Card
                style={{
                  width: '56.25vw',
                  height: '14.3vh',
                  marginLeft: '-10px',
                  borderRadius: '12px',
                  marginTop: '20px',
                }}
              >
                <div style={{ display: 'flex', flexDirection: 'row' }}>
                  <div style={{ marginTop: '-40px' }}>
                    <h3
                      style={{
                        color: '#0A0606',
                        fontSize: '14px',
                        fontFamily: 'Rubik',
                        letterSpacing: '0px',
                        marginTop: '24px',
                        marginLeft: '10px',
                      }}
                    >
                      Mobile App Design
                    </h3>
                    <p
                      style={{
                        color: '#A9ABBC',
                        fontSize: '12px',
                        fontFamily: 'Rubik',
                        marginLeft: '10px',
                      }}
                    >
                      Prototypes, mockups, client and collabaration
                    </p>
                  </div>
                  <div>
                    <Avatar
                      style={{ marginTop: '2px', marginLeft: '130px' }}
                      src={
                        <img
                          src="https://cdn-icons-png.flaticon.com/512/201/201634.png"
                          alt="User"
                        />
                      }
                    />
                    <Avatar
                      style={{ marginTop: '2px', marginLeft: '10px' }}
                      src={
                        <img
                          src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTWzw8_87ASKSkDuQx7gbTSbjJtcxUQ7aXD9Q&usqp=CAU"
                          alt="User"
                        />
                      }
                    />
                  </div>
                  <div>
                    <p
                      style={{
                        color: '#A9ABBC',
                        fontFamily: 'Rubik',
                        fontSize: '12px',
                        marginLeft: '40px',
                        marginTop: '4px',
                      }}
                    >
                      1 week ago
                    </p>
                  </div>
                  <div>
                    <StarFilled
                      style={{
                        color: '#FFC107',
                        marginLeft: '50px',
                        marginTop: '4px',
                        fontSize: '20px',
                      }}
                    />
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>
      {/* satya's code */}
      <div>
        <div style={{ width: '25%' }}>
          <div
            style={{
              width: '21vw',
              background: '#fff',
              marginLeft: '16px',
              paddingBottom: '40px',
            }}
          >
            <div style={{ display: 'flex' }}>
              <div>
                <h6 style={{ marginTop: '20px', marginLeft: '20px' }}>
                  Hello.
                </h6>
                <h4
                  style={{
                    marginTop: '4px',
                    marginLeft: '20px',
                    color: '#black',
                  }}
                >
                  Alexander
                </h4>
              </div>
              <div>
                <Avatar
                  style={{ marginLeft: '85px', marginTop: '3px' }}
                  src={
                    <img
                      src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTWzw8_87ASKSkDuQx7gbTSbjJtcxUQ7aXD9Q&usqp=CAU"
                      alt="User"
                    />
                  }
                />
                <SettingFilled
                  style={{
                    marginTop: '30px',
                    marginRight: '15px',
                    marginLeft: '5px',
                    color: '#2A77F4',
                  }}
                />
              </div>
            </div>
            <Divider />
            <div>
              <div>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-evenly',
                    marginLeft: '-26px',
                  }}
                >
                  <p
                    style={{
                      fontFamily: 'Rubik',
                      fontSize: '12px',
                      color: '#A9ABBC',
                    }}
                  >
                    Total Projects
                  </p>
                  <p
                    style={{
                      fontFamily: 'Rubik',
                      fontSize: '12px',
                      color: '#A9ABBC',
                    }}
                  >
                    Completed
                  </p>
                </div>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-evenly',
                    marginLeft: '-30px',
                    marginRight: '-10px',
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      marginTop: '14px',
                      marginLeft: '-25px',
                    }}
                  >
                    <Typography
                      style={{
                        color: '#6770E6',
                      }}
                    >
                      |
                    </Typography>
                    <h4 style={{ marginLeft: '6px' }}>180</h4>
                    <Typography
                      style={{
                        color: '#4EA8A8',
                        marginLeft: '87px',
                      }}
                    >
                      |
                    </Typography>
                    <h4 style={{ marginLeft: '6px' }}>171</h4>
                  </div>
                </div>
              </div>
              <div>
                <div
                  style={{
                    display: 'flex',
                    marginTop: '30px',
                  }}
                >
                  <p
                    style={{
                      fontFamily: 'Rubik',
                      fontSize: '12px',
                      color: '#A9ABBC',
                      marginLeft: '30px',
                    }}
                  >
                    In Progress
                  </p>
                  <p
                    style={{
                      fontFamily: 'Rubik',
                      fontSize: '12px',
                      color: '#A9ABBC',
                      marginLeft: '58px',
                    }}
                  >
                    Out of Sechudle
                  </p>
                </div>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-evenly',
                    marginLeft: '-30px',
                    marginRight: '-10px',
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      marginTop: '14px',
                      marginLeft: '-25px',
                    }}
                  >
                    <Typography
                      style={{
                        color: '#EEA75F',
                      }}
                    >
                      |
                    </Typography>
                    <h4 style={{ marginLeft: '6px' }}>162</h4>
                    <Typography
                      style={{
                        color: '#E25B5C',
                        marginLeft: '87px',
                      }}
                    >
                      |
                    </Typography>
                    <h4 style={{ marginLeft: '6px' }}>144</h4>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            style={{
              background: '#fff',
              width: '21vw',
              marginTop: '10px',
              marginLeft: '16px',
              padding: '5px',
              // height: '50%',
            }}
          >
            <div>
              <p
                style={{
                  marginLeft: '20px',
                  color: '#A9ABBC',
                  fontSize: '12px',
                  marginTop: '10px',
                  fontFamily: 'Rubik',
                }}
              >
                Activity
              </p>
              <h4
                style={{
                  marginLeft: '20px',
                  fontFamily: 'Rubik',
                  fontSize: '14px',
                  color: '#0A0606',
                }}
              >
                Feed
              </h4>
            </div>
            <div style={{ display: 'flex' }}>
              <div>
                <Avatar
                  style={{
                    marginTop: '20px',
                    marginLeft: '8px',
                    height: '8vh',
                    width: '3.5vw',
                  }}
                  src={
                    <img
                      src="https://img.freepik.com/premium-vector/3d-smiling-women-avatar-happy-face-icon-character-vector_313242-1220.jpg?w=740"
                      alt="User"
                    />
                  }
                />
              </div>
              <div
                style={{
                  marginLeft: '5px',
                  marginTop: '28px',
                  fontSize: '11px',
                  background: '#0000001A',
                  fontFamily: 'Rubik',
                  borderRadius: '10px',
                  padding: '5px',
                }}
              >
                <p>
                  <b style={{ color: '#0A0606' }}>Completed task</b> proper mood
                  <br />
                  board for website branding
                </p>
              </div>
            </div>
            <div
              style={{
                marginTop: '28px',
                marginLeft: '130px',
                background: '#F8EF99',
                borderRadius: '10px 10px 0 10px',
                fontSize: '11px',
                color: '#0A0606',
                width: '7.4vw',
                padding: '10px',
                alignItems: 'center',
              }}
            >
              <h4 style={{ textAlign: 'center' }}>Coffe Breake</h4>
            </div>
            <div style={{ display: 'flex' }}>
              <div>
                <Avatar
                  style={{
                    marginTop: '20px',
                    marginLeft: '8px',
                    height: '8vh',
                    width: '3.5vw',
                  }}
                  src={
                    <img
                      src="https://img.freepik.com/premium-vector/3d-smiling-women-avatar-happy-face-icon-character-vector_313242-1220.jpg?w=740"
                      alt="User"
                    />
                  }
                />
              </div>
              <div
                style={{
                  marginLeft: '1px',
                  marginTop: '28px',
                  fontSize: '11px',
                  background: '#0000001A',
                  fontFamily: 'Rubik',
                  borderRadius: '10px',
                  padding: '4px',
                }}
              >
                <p>
                  <b style={{ color: '#0A0606' }}>Overdued task</b> Search
                  references
                  <br />
                  for multicolored background
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

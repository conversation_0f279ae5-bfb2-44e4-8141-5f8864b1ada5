/* eslint-disable import/no-cycle */

// import Konva from 'konva';
import { createSlice } from '@reduxjs/toolkit';
import FontCard from '@chill/assets/images/textfont1.jpg';
import FontCard2 from '@chill/assets/images/textfont2.jpg';
import availableShapes from '../Shapes';

import KonvaBoard2 from '../KonvaBoard2';

const initialState = {
  layer: null,
  images: null,
};
const layerSlice = createSlice({
  name: 'layer',
  initialState,
  reducers: {
    setLayerRef: (state, action) => {
      console.log('layer updated');
      // console.log(action.payload.layer);
      return {
        ...state,
        layer: action.payload.layer,
      };
    },
    setEditText: (state, action) => {
      console.log('Text updated');
      console.log(action.payload.editText);
      return {
        ...state,
        editText: action.payload.editText,
      };
    },
    addText: () => {
      console.log('add text slice');
      // console.log(state);
      // console.log('action', { action });
      const newText = KonvaBoard2.getClassObject(`Text`, {
        x: 100,
        y: 50,
        text: 'Heading',
        fontSize: 24,
        fontFamily: 'Arial',
        fill: 'black',
        draggable: true,
        stroke: 'black', // Set the outline color
        strokeWidth: 0, // Set the outline (border) width
        fontStyle: 'normal', // Add font style here
        align: 'center',
        verticalAlign: 'top',
        letterSpacing: 0, // Set the letter spacing value
        lineSpacing: 10,
        isHeading: true,
      });
      const newLayer = KonvaBoard2.getClassObject(`Layer`, {
        name: newText.obj.attrs.name,
      });
      console.log(KonvaBoard2.obj);
      newLayer.obj.add(newText.obj);
      KonvaBoard2.obj.add(newLayer.obj);
      KonvaBoard2.undoStack.push({ element: newText });
    },
    addParagraph: () => {
      console.log('add text slice');
      // console.log(state);
      // console.log('action', { action });
      const newText = KonvaBoard2.getClassObject(`Text`, {
        x: 60,
        y: 100,
        text: 'This is a sample paragraph. Use it to add \n multiple lines of text for your in-app \n message, feed post, or other design\n projects which require more \n information',
        fontSize: 15,
        fontFamily: 'Arial',
        fill: 'black',
        draggable: true,
        stroke: 'black', // Set the outline color
        strokeWidth: 0, // Set the outline (border) width
        fontStyle: 'normal', // Add font style here
        align: 'center',
        verticalAlign: 'top',
        letterSpacing: 0, // Set the letter spacing value
        lineSpacing: 10,
      });
      const newLayer = KonvaBoard2.getClassObject(`Layer`, {
        name: newText.obj.attrs.name,
      });
      console.log(KonvaBoard2.obj);
      // children[0].children
      newLayer.obj.add(newText.obj);
      KonvaBoard2.obj.add(newLayer.obj);
      KonvaBoard2.undoStack.push({ element: newText });
    },
    addParagraph1: () => {
      console.log('add text slice');
      // console.log(state);
      // console.log('action', { action });
      const newText = KonvaBoard2.getClassObject(`Text`, {
        x: 60,
        y: 100,
        text: 'This is a sample paragraph. Use it to add \n multiple lines of text for your in-app \n message, feed post, or other design\n projects which require more \n information',
        fontSize: 15,
        fontFamily: 'Arial',
        fill: 'black',
        draggable: true,
        align: 'left',
        stroke: 'black',
        strokeWidth: 0,
        fontStyle: 'normal',
        verticalAlign: 'top',
        letterSpacing: 0, // Set the letter spacing value
        lineSpacing: 10,
      });
      const newLayer = KonvaBoard2.getClassObject(`Layer`, {
        name: newText.obj.attrs.name,
      });
      console.log(KonvaBoard2.obj);
      // children[0].children
      newLayer.obj.add(newText.obj);
      KonvaBoard2.obj.add(newLayer.obj);
      KonvaBoard2.undoStack.push({ element: newText });
    },
    addParagraph2: () => {
      console.log('add text slice');
      // console.log(state);
      // console.log('action', { action });
      const newText = KonvaBoard2.getClassObject(`Text`, {
        x: 60,
        y: 100,
        text: 'This is a sample paragraph. Use it to add \n multiple lines of text for your in-app \n message, feed post, or other design\n projects which require more \n information',
        fontSize: 15,
        fontFamily: 'Arial',
        fill: 'black',
        draggable: true,
        align: 'center',
        stroke: 'black', // Set the outline color
        strokeWidth: 0, // Set the outline (border) width
        fontStyle: 'normal',
        verticalAlign: 'top',
        letterSpacing: 0, // Set the letter spacing value
        lineSpacing: 10,
      });
      const newLayer = KonvaBoard2.getClassObject(`Layer`, {
        name: newText.obj.attrs.name,
      });
      console.log(KonvaBoard2.obj);
      // children[0].children
      newLayer.obj.add(newText.obj);
      KonvaBoard2.obj.add(newLayer.obj);
      KonvaBoard2.undoStack.push({ element: newText });
    },
    addParagraph3: () => {
      console.log('add text slice');
      // console.log(state);
      // console.log('action', { action });
      const newText = KonvaBoard2.getClassObject(`Text`, {
        x: 60,
        y: 100,
        text: 'This is a sample paragraph. Use it to add \n multiple lines of text for your in-app \n message, feed post, or other design\n projects which require more \n information',
        fontSize: 15,
        fontFamily: 'Arial',
        fill: 'black',
        draggable: true,
        align: 'right',
        stroke: 'black', // Set the outline color
        strokeWidth: 0, // Set the outline (border) width
        fontStyle: 'normal', // Add font style here
        verticalAlign: 'top',
        letterSpacing: 0, // Set the letter spacing value
        lineSpacing: 10,
      });
      const newLayer = KonvaBoard2.getClassObject(`Layer`, {
        name: newText.obj.attrs.name,
      });
      console.log(KonvaBoard2.obj);
      // children[0].children
      newLayer.obj.add(newText.obj);
      KonvaBoard2.obj.add(newLayer.obj);
      KonvaBoard2.undoStack.push({ element: newText });
    },
    addParagraph4: () => {
      console.log('add text slice');
      // console.log(state);
      // console.log('action', { action });
      const newText = KonvaBoard2.getClassObject(`Text`, {
        x: 60,
        y: 100,
        text: 'This is a sample paragraph. Use it to add \n multiple lines of text for your in-app \n message, feed post, or other design\n projects which require more \n information',
        fontSize: 15,
        fontFamily: 'Arial',
        fill: 'black',
        draggable: true,
        align: 'justify',
        stroke: 'black', // Set the outline color
        strokeWidth: 0, // Set the outline (border) width
        fontStyle: 'normal', // Add font style here
        verticalAlign: 'top',
        letterSpacing: 0, // Set the letter spacing value
        lineSpacing: 10,
      });
      const newLayer = KonvaBoard2.getClassObject(`Layer`, {
        name: newText.obj.attrs.name,
      });
      console.log(KonvaBoard2.obj);
      // children[0].children
      newLayer.obj.add(newText.obj);
      KonvaBoard2.obj.add(newLayer.obj);
      KonvaBoard2.undoStack.push({ element: newText });
    },
    addButton: (state, action) => {
      console.log('add button slice');
      console.log(state);
      console.log(action);
      const { text, url } = action.payload;
      console.log('KonvaLabel');
      const button = KonvaBoard2.getClassObject(`Label`, {
        x: 300,
        y: 50,
        fill: 'black',
        draggable: true,
        url,
        borderRadius: '20px',
      });
      const tag = KonvaBoard2.getClassObject(`Tag`, {
        fill: 'blue',
        lineJoin: 'round',
        shadowColor: 'yellow',
        cornerRadius: 10,
      });

      const buttonText = KonvaBoard2.getClassObject(`Buttontext`, {
        text,
        fontFamily: 'roboto',
        fontSize: 18,
        padding: 10,
        fill: 'black',
      });

      console.log('button add');
      console.log(tag);
      console.log(buttonText);

      button.add(tag.obj);
      button.add(buttonText.obj);
      console.log(text);
      console.log(url);
      // KonvaBoard2.obj.children[0].add(button.obj);
      // KonvaBoard2.obj.children[0].batchDraw();
      const newLayer = KonvaBoard2.getClassObject(`Layer`, {
        name: button.obj.attrs.name,
      });
      console.log(KonvaBoard2.obj);
      newLayer.obj.add(button.obj);
      KonvaBoard2.obj.add(newLayer.obj);
      KonvaBoard2.undoStack.push({ element: button });
    },
    addUploadImage: (state, action) => {
      const { imageSrc } = action.payload;
      console.log('upload image :');
      console.log(action);
      const img = new window.Image();
      img.src = imageSrc;
      img.onload = () => {
        const aspectRatio = img.width / img.height;
        const width = 150;
        const height = width / aspectRatio;
        const uploadImage = KonvaBoard2.getClassObject(`Image`, {
          x: 50,
          y: 50,
          image: img,
          draggable: true,
          imageSource: imageSrc,
          width,
          height,
        });
        console.log(uploadImage);
        const newLayer = KonvaBoard2.getClassObject(`Layer`, {
          name: uploadImage.obj.attrs.name,
        });
        console.log(KonvaBoard2.obj);
        newLayer.obj.add(uploadImage.obj);
        KonvaBoard2.obj.add(newLayer.obj);
        console.log('Image added');
        console.log(KonvaBoard2.obj);
        KonvaBoard2.undoStack.push({ element: uploadImage });
      };
    },
    addTextImg: (state, action) => {
      // const { imageSrc } = action.payload;
      console.log('upload image :');
      console.log(action);
      const img = new window.Image();
      // img.src = imageSrc;
      const uploadImage = KonvaBoard2.getClassObject(`Image`, {
        x: 50,
        y: 50,
        image: img,
        draggable: true,
        imageSource: FontCard,
        width: 150,
        height: 150,
      });
      console.log(uploadImage);

      const newLayer = KonvaBoard2.getClassObject(`Layer`, {
        name: uploadImage.obj.attrs.name,
      });
      console.log(KonvaBoard2.obj);
      newLayer.obj.add(uploadImage.obj);
      KonvaBoard2.obj.add(newLayer.obj);
      console.log('Image added');
      console.log(KonvaBoard2.obj);
      KonvaBoard2.undoStack.push({ element: uploadImage });
    },
    addShape: (state, action) => {
      console.log('add shape :', action);
      const { shapeId, url, height, width } = action.payload;
      console.log('shapeId:', shapeId);
      console.log('availableShapes:', availableShapes);
      // const selectedShape = availableShapes.find(
      //   (shape) => shape.id === shapeId,
      // );
      // console.log('selectedShape:', selectedShape);
      // console.log('selectedShape:', selectedShape.component);
      // const newShape = selectedShape.component;
      const newShape = KonvaBoard2.getClassObject(
        `Shape`,
        {
          // ...selectedShape.component.props,
          x: 50, // Set the initial position (you can customize this as per your requirement).
          y: 50,
          draggable: true,
          shapeId,
          url,
          height,
          width,
        }, // Make the shape draggable if needed.
      );
      console.log(newShape);
      const newLayer = KonvaBoard2.getClassObject(`Layer`, {
        name: newShape.obj.attrs.name,
      });
      console.log(KonvaBoard2.obj);
      newLayer.obj.add(newShape.obj);
      KonvaBoard2.obj.add(newLayer.obj);
      console.log('Shape added');
      console.log(KonvaBoard2.obj);
      KonvaBoard2.undoStack.push({ element: newShape });
    },
    addTextSampleImg: (state, action) => {
      // const { imageSrc } = action.payload;
      console.log('upload image :');
      console.log(action);
      const img = new window.Image();
      // img.src = imageSrc;
      const uploadImage = KonvaBoard2.getClassObject(`Image`, {
        x: 50,
        y: 50,
        image: img,
        draggable: true,
        imageSource: FontCard2,
        width: 150,
        height: 150,
      });
      console.log(uploadImage);

      const newLayer = KonvaBoard2.getClassObject(`Layer`, {
        name: uploadImage.obj.attrs.name,
      });
      console.log(KonvaBoard2.obj);
      newLayer.obj.add(uploadImage.obj);
      KonvaBoard2.obj.add(newLayer.obj);
      console.log('Image added');
      console.log(KonvaBoard2.obj);
      KonvaBoard2.undoStack.push({ element: uploadImage });
    },
    addGenerateImage: (state, action) => {
      const { generatedSrc } = action.payload;
      console.log('upload image :', generatedSrc);
      console.log(action);
      const img = new window.Image();
      // img.crossOrigin = '*';
      img.src = generatedSrc;
      img.onload = () => {
        // const aspectRatio = img.width / img.height;
        // const width = 150;
        // const height = width / aspectRatio;
        const height = KonvaBoard2.obj.height();
        const width = KonvaBoard2.obj.width();
        const AiImage = KonvaBoard2.getClassObject(`Image`, {
          x: 0,
          y: 0,
          image: img,
          draggable: true,
          imageSource: generatedSrc,
          width,
          height,
          isAiImage: true,
        });
        console.log(AiImage);
        const newLayer = KonvaBoard2.getClassObject(`Layer`, {
          name: AiImage.obj.attrs.name,
        });
        console.log(KonvaBoard2.obj);
        newLayer.obj.add(AiImage.obj);
        KonvaBoard2.obj.add(newLayer.obj);
        console.log('Image added');
        console.log(KonvaBoard2.obj);
        KonvaBoard2.undoStack.push({ element: AiImage });
      };
      img.crossOrigin = 'Anonymous';
    },
  },
});

export const {
  setLayerRef,
  addText,
  addParagraph,
  addParagraph1,
  addParagraph2,
  addParagraph3,
  addParagraph4,
  setEditText,
  addButton,
  addUploadImage,
  addTextImg,
  addShape,
  addTextSampleImg,
  addTemplate,
  addGenerateImage,
} = layerSlice.actions;

export const selectLayerRef = (state) => state.layer;

export const selectEditText = (state) => state.editText;

export default layerSlice.reducer;

import styled from 'styled-components';
import withDirection from '@chill/lib/helpers/rtl';
import { palette } from 'styled-theme';
import { borderRadius } from '@chill/lib/helpers/style_utils';

const MarketingWrapper = withDirection(styled.div`

///////////////////////////////////
.radio-toolbar {
  margin: 10px 10px 10px 3px;
}

padding: 20px 40px;
.downLoadBtn {
  width: 142px;
  display: flex;
  gap: 13px;
  align-items: center;
  jusry-content: center;
  height: 56px;
  background: #FAFAFA;
  border-color: #FAFAFA;
  gap: 8px;
  border-radius: 12px;
    span {
      
      font-family: Inter;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      letter-spacing: 0.2px;
      text-align: left;
      border-color: transparent;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #718096;
    }
  ::hover {
    border-color: transparent;
    color: #718096;
  }
}
.thisAc {
  flex-direction: row;
  display: flex;
  margin: 10px;
  margin-top: 10px;
  justify-content: space-between;
}
.radio-toolbar input[type="radio"] {
  opacity: 0;
  position: fixed;
  width: 0;
}
& .radioButtonSelect {
  display: inline-table;
  margin-right: 20px
}

.mainDiv {
  border: 2px solid #ddd;
  padding: 10px;
  margin-bottom: 5px;
  margin-top: 5px;
  border-radius: 5px;
}
///////////////////
  .marketingText {
    font-weight: 400;
    text-transform: uppercase;
  }
  .hiddenCol {
    padding-bottom: 16px;
    display: flex;
    justify-content: flex-end;
    @media (min-width: 767px) {
      display: none;
    }
  }
  .isoAddNew {
    display: flex;
    justify-content: flex-end;
   
  }
  .addNewUser {
    display: flex;
    justify-content: flex-end;
    cursor: pointer;
  }
  .filterIcon {
    padding-left: 12px;
    padding-right: 12px;
    color: rgb(0, 0, 0);
    margin: 0px 12px;
    border-left: 1px solid #eee;
    border-right: 1px solid #eee;
  }
  .filterIcon1 {
    transform: rotate(90deg);
  }
  .isoTab{
    display: flex; 
    justify-content: center;
    @media (max-width: 281px){
      paxdding-right: 16px;
    }
  }
  .isoTab > div > div::before {
    position: unset;
  }
  .isoTab > div > div > div > div > div {
    padding: 0px;
  }
  .isoTab > div > div > div > div {
    padding-bottom: 20px;
  }
  .isoTab > div > div:nth-child(1) {
    margin: 0px;
  }
  .isoTab > div > div:nth-child(2) {
    display: none;
  }
  .iconStyle {
    display: flex;
    align-items: center;
    height: 100%;
    margin-bottom: 20px;
  }
  .marketingMainDiv {
    align-items: flex-start
  }
  .listNameView {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .nameWithImg {
    width: 30px;
    height: 30px;
    background-color: #eee;
    margin-right: 12px;
    border-radius: 30px;
  }
  .btnPadding {
    padding-left: 40px;
    padding-right: 40px
  }
  .popMainDiv {
    display: flex;
    flex-direction: column;
  }
  .isoDropdownLink {
    color: #000;
    padding: 8px 0px;
  }
  .iconImg {
    width: 16px;
    height: 16px;
    margin-right: 12px;
  }
  .iconPadding {
    display: flex;
    padding-right: 7px;
    @media (max-width: 281px) {
      display: none;
    }
  }
  .createSegmentP {
    display: flex;
    align-items: center;
    flex-direction: row;
  }
  & .chartPadding {
    padding: 20px 20px 20px 20px;
    margin-left: 10px;
    border-radius: 25px;
    background-color: #fff;
    box-shadow: 5px 5px 5px 5px #0000;
  }
  & .titleStyle {
    font-weight: 500;
    font-size: 14px;
    text-transform: uppercase;
  }
  & .titleStyleMap {
    font-weight: 500;
    font-size: 14px;
    text-transform: uppercase;
    opacity: 0.8;
    margin-top: 5px;
  }
  & .rowMargin {
    margin-right: 0px;
  }
  .overViewContainer {
    height: 100%;
    width: 100%;
    padding: 20px 17px 20px 15px;
    // background: linear-gradient(
    //   112deg,
    //   rgba(80, 135, 255, 1) 14%,
    //   rgba(41, 239, 196, 1) 86%
    // );
    border-radius: 11px;
    // margin: 0px 0px 20px;
    box-shadow: 2px 1px 10px -10px #ccc;
    color: white;
    background: linear-gradient(181.35deg, rgba(251, 251, 251, 0.5) 1.15%, rgba(255, 255, 255, 0) 98.91%);
    backdrop-filter: blur(30px)

    
  }
  .changeBtn {
    border-radius: 8px;
    font-weight: 500;
    border: 0px;
    color: #fff
    background: linear-gradient(90deg, #039be5 38%, #03a9f4 90%);
    &:hover {
      background: linear-gradient(90deg, #039be5 38%, #03a9f4 90%);
    }
  }
  .rowMargin {
    margin-bottom: 5px
    margin-top: 5px
  }
  .paddingbottom {
    padding-bottom: 10px;
  }
  .paddingTop {
    padding-top: 10px;
  }
  .btnCenter {
    display: flex;
    justify-content: center;
    width:100%;
    align-items: center;
    padding-bottom: 5px;
  }
  & .overViewText {
    font-size: 15px;
    color: white;
    font-weight: 400;
    margin-bottom: 5px;
  }
  & .overViewTextA {
    font-size: 15px;
    color: white;
    font-weight: 400;
    margin-left: 15px;
  }
  .isoIconWrapper1 {
    display: flex;
    
    // align-items: center;
    // justify-content: center;
    padding: 5px 5px 0px;

    i {
      font-size: 36px;
    }
  }
  .marginWrapper1 {
    margin-top: 20px;
    margin-left: -5px;
  }
  & .liText {
    margin-left: 16px;
    span {
      font-family: Inter;
      font-size: 11px;
      font-weight: 300;
      line-height: 15.4px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #FFFFFF;
    }
  }
  .previewText {
    width: 100%;
    // height: 80px;
    background-color: #fff;
    margin: 80px 0px;
    border-radius: 10px;
    // padding: 10px;
    overflow: hidden;
    box-shadow: 0px 0px 0px 1px #dcdcdc;
  }
  .previewText > div > div {
    width:100%;
  }
  .pushMainView{
    position: absolute;
    top: 60px;
    width: 94%;
    
  }
  .topPreviewText {
    top: 32px;
    background: #fff;
    position: absolute;
    width: 94%;
    border-radius: 10px;
    padding-bottom: 10px;
    box-shadow: 0px 0px 0px 1px #dcdcdc;
  }
  .bottomPreviewText {
    bottom: 10px;
    background: #fff;
    position: absolute;
    width: 94%;
    border-radius: 10px;
    padding-bottom: 10px;
    box-shadow: 0px 0px 0px 1px #dcdcdc;
  }
  .centerPreviewText {
    background: #fff;
    position: absolute;
    width: 94%;
    border-radius: 10px;
    padding-bottom: 10px;
    box-shadow: 0px 0px 0px 1px #dcdcdc;
  }
  
  .pushPreviewText{
    position: absolute;
    top: 0px;
    padding-bottom: 10px;
    border-radius:10px;
    background-color:#fff;
    width:94%;
    box-shadow: 0px 0px 0px 1px #dcdcdc;
  }
  .fullPreViewText{
    position: absolute;
    top: 32px;
    padding-bottom: 10px;
    border-radius:10px;
    background-color:#fff;
    width:94%;
    box-shadow: 0px 0px 0px 1px #dcdcdc;
  }
  .pushPreviewText > div:nth-child(2){
    width:100%;
  }
  .previewBtn{
    margin-right: 8px;
    overflow:hidden;
    width: 100%;
    height: 100%;
    margin: 10px 10px 0px;
    box-shadow: 1px 1px 1px 1px #0000;
    border-radius:10px;
    // background: linear-gradient(90deg, #039be5 38%, #03a9f4 90%);
    // border: 0px;
    // &:hover {
    //   background: linear-gradient(90deg, #039be5 38%, #03a9f4 90%);
    // }
    & > span{
      // color:#fff;
      overflow: hidden;
      display: -webkit-box;   
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }
  }

  & .buttonMargin {
    margin: 0px -11px;
  }
  
  & .postCircle {
    width: 20px;
    height: 20px;
    border-radius: 30px;
    background-color: #ccc;
  }
  .mainAppDiv{
    position: relative;
    width: 100%;
    // top: 32px;
    border-radius: 10px;
    display: flex;
    justify-content: center;
    overflow: hidden;
    align-items: center;
  }
  & .mainAppDiv > div:nth-child(1) > div:nth-child(2){
    width:100%;
  }
  & .imagePreviewStyle {
    width: 100%;
    height: 17vh;
  }
  & .imageFullPreviewStyle {
    width: 100%;
    height: 300px;
  }
  & .attachments {
    color: rgb(17, 114, 236),
    display: flex,
    cursor: pointer;
  }
  & .padding {
    padding-left: 5px;
  }
  & .title {
    font-size: 12px;
    font-weight: 400;
    padding-left: 5px;
    overflow: hidden;
    display: -webkit-box;   
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
  & .subTitle {
    color: #000;
    font-size: 13px;
    padding-left: 5px;
    overflow: hidden;
    display: -webkit-box;   
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
  & .message {
    color:#808080;
    font-size: 12px;
    padding-left: 5px;
    overflow: hidden;
    display: -webkit-box;   
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
  & .upload {
    margin-top: 20px;
  }
  & .boxRowSTyle {
    margin-top 30px;
  }
  & .boxesStyle {
    @media (max-width:767px) {
      padding-left: 0px !important;
      padding-right: 0px !important;
    }
  }
  .messageTypeWrapper {
    padding: 10px 0px 5px 0px;
    // border-color: #adadad;
    // margin: 0px 25px 0px 0px;
    // justify-content: center;
    // align-items: center;
    // text-align: center;
    // border-width: 1px;
    // border-style: solid;
  }
  & .messageTypeSvg {
    fill: black;
  }
  & .messageTypeSvg:hover {
    fill: orange;
  }
  & .mainPreview {
    position:relative;
    width: 100%;
    height: 58vh;
    background-color: #fff;
    border-radius: 14px;
    margin-top: 16px;
    margin-bottom:16px;
    display: flex;
    justify-content: center;
    @media (min-width: 279px) and (max-width: 654px){
      height: 225px;
    }
  }
  & .previewUpperPart1 {
    background-color: #eee;
    padding: 8px 12px;
    position: absolute;
    border-radius: 0px 0px 10px 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
    @media (min-width: 279px) and (max-width: 654px){
      padding: 6px 6px;
      border-radius: 0px 0px 6px 6px;
    }
  }
  & .icon1{
    width: 8px;
    height: 8px;
    background-color: #808080;
    border-radius: 10px;
    @media (min-width: 279px) and (max-width: 654px){
      width: 4px;
      height: 4px;
    }
  }
  & .icon2{
    width: 60px;
    height: 8px;
    background-color: #808080;
    border-radius: 10px;
    margin-left: 5px;
    @media (min-width: 279px) and (max-width: 654px){
      width: 34px !important;
      height: 6px !important;
    }
  }

  .icon3View{
    position: absolute;
    top: 10px;
    right:8px
    @media (min-width: 279px) and (max-width: 654px){
     top:6px;
    }
  }
  .icon3{
    width: 20px;
    height: 8px;
    background-color: #808080;
    border-radius: 3px;
    @media (min-width: 279px) and (max-width: 654px){
      width: 14px;
      height: 6px;
    }
  }
  .feedView{
    position: absolute;
    top: 80px;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0px 10px;
  }
  .popMainDiv {
    display: flex;
    flex-direction: column;
  }
  .isoDropdownLink {
    color: #000;
    padding: 8px 0px;
  }
  .iconImg {
    width: 16px;
    height: 16px;
    margin-right: 12px;
  }
  .mobileView{
    padding: 0px 60px;
    @media  (min-width: 279px) and (max-width: 654px){
      padding: 0px 40px !important;
    }
    @media (min-width: 539px) and (max-width: 721px){
      padding: 0px 84px !important;
    }
  }
  .isoTabs > div:nth-child(1){
    margin: 0px;
  }
  .colHeight{
    padding:16px 18px 0px 0px !important;
    overflow-y: scroll;
    height: 100vh
  }
  .titlePadding{
    padding: 3px 16px;
  }
  .popMainDiv {
    display: flex;
    flex-direction: column;
  }
  .isoDropdownLink {
    color: #000;
    padding: 8px 0px;
    cursor: pointer;
  }

  .topMarketingBox{
    margin: 0px;
    @media (min-width:768px){
      // margin: 0px 5px;
    }
      .mainBox-div {
        height: 85px;
        padding: 12px 21px;
        gap: 0;
        border-radius: 12px;
        background-color: #FFFFFF;
        box-shadow: 0 8px 32px 0 rgba(51, 38, 174, 0.08);

        display: flex;
        flex-direction: row;
        justify-content: space-between;
        .leftBoxDiv {
         
          .innerLeftDiv {
          display: flex;
          flex-direction: column;
          gap: 3px;
            .label {
             span {
              font-family: Inter;
              font-size: 14px;
              font-weight: 500;
              line-height: 22px;
              text-align: left;
              text-underline-position: from-font;
              text-decoration-skip-ink: none;
              color: #8E95A9;
             }
             }
             .label-val {
                 font-family: Inter;
                 font-size: 20px;
                 font-weight: 600;
                 line-height: 40px;
                 text-align: left;
                 text-underline-position: from-font;
                 text-decoration-skip-ink: none;
                 color: #1C2A53;
              }
          }
        }
        .rightBoxDiv {
          display: flex;
          flex-direction: row;
          gap: 11px;
          .innerRightDiv {
            display: flex;
            flex-direction: column;
            gap: 3px;
            .label {
              font-family: Inter;
              font-size: 14px;
              font-weight: 700;
              line-height: 22px;
              text-align: center;
              text-underline-position: from-font;
              text-decoration-skip-ink: none;
            }
            .label-val {
              font-family: Inter;
              font-size: 20px;
              font-weight: 600;
              line-height: 40px;
              text-align: left;
              text-underline-position: from-font;
              text-decoration-skip-ink: none;
              color: #1C2A53;
            }
          }
        }
      }
  }
  .chartCol{
    background-color: #FFFFFF;
    padding: 18px;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    // margin-left: 27px !important;
  }
  .overViewCol{
    // margin: 0px 13px !important;
    padding: 24px 24px 22px 27px !important;
    border-radius: 12px;
    // @media (min-width: 767px){
    //   padding-right: 16px !important;
    // }
  }

  .isoTabRow{
    padding: 16px 16px 0px;
  }


  .isCampaignTabRow {
    padding: 24px;
    border-radius: 16px;
    border: 1px solid #EEEFF2;
    background-color: #fff;

    .downLoad-filter {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      align-items: center;
      // padding-left: 16px;

      @media (max-width: 1600px) {
        justify-content: flex-end;
        gap: 15px;
        flex-wrap: wrap;
      }
     
    }
      .caimpaignfilterSelection {
        width: 100%;
        .caimpaignfilterSelection-div {
          width: 100%;
          padding: 16px;
          display: flex;
          gap: 16px;
          flex-wrap: wrap;
          align-items: center;
          justify-content: flex-end;
          .caimpaignfilterSelection-select {
          max-width: 243px;
           .ant-select-selector {
            
              height: 56px;
               border-radius: 12px;
              background-color: #FAFAFA;
              border-color: #FAFAFA;
               .ant-select-selection-placeholder {
                  position: relative;
                  top: 16px;
                  font-family: Inter;
                  font-size: 16px;
                  font-weight: 500;
                  line-height: 24px;
                  letter-spacing: 0.20000000298023224px;
                  text-align: left;
                  text-underline-position: from-font;
                  text-decoration-skip-ink: none;

                  color: #718096;
               }
                .ant-select-selection-item {
                  position: relative;
                  top: 16px;
                    font-family: Inter;
                  font-size: 16px;
                  font-weight: 500;
                  line-height: 24px;
                  letter-spacing: 0.20000000298023224px;
                  text-align: left;
                  text-underline-position: from-font;
                  text-decoration-skip-ink: none;
                  color: #171A1F;
               }
            }
            }
             .campaigndatePicker {
       height: 56px;
       max-width: 243px;
       border-radius: 12px;
        background-color: #FAFAFA;
        border-color: #FAFAFA;
         .ant-picker-input{
         input::placeholder {
            font-family: Inter;
            font-size: 16px;
            font-weight: 500;
            line-height: 24px;
            letter-spacing: 0.20000000298023224px;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;

            color: #718096;
      }}
        .ant-picker-input{
          input {
            font-family: Inter;
            font-size: 16px;
            font-weight: 500;
            line-height: 24px;
            letter-spacing: 0.20000000298023224px;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #171A1F;
      }}
      }
        }
      }
    .searchInputCampaign {
      min-width: 100%;
      background-color: #FAFAFA;
      border-color: #FAFAFA;
      height: 56px;
      border-radius: 12px;
       .ant-input-prefix {
        margin-right: 12px;
       }
       .ant-input::placeholder {
        font-family: Inter;
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: 0.2px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #A0AEC0;
      }
        .ant-input {
        font-family: Inter;
        font-size: 16px;
        font-weight: 500;
      background-color: #FAFAFA;
        line-height: 24px;
        letter-spacing: 0.2px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #171A1F;
      }
    }

     

    .tab-col {
      width: 100%;
    
      .campaignTab {
         .ant-tabs-nav::before {
           border-bottom: 1px solid #EEEFF2;
         }
         .ant-tabs-ink-bar-animated {
           background-color: #6C5DD3;
           height: 2px;
         }
         .ant-tabs-tab-active {
           .ant-tabs-tab-btn {
             span {
               font-family: Inter;
               font-size: 16px;
               font-weight: 800 !important;
               line-height: 24px;
               text-align: left;
               text-underline-position: from-font;
               text-decoration-skip-ink: none;
               color: #6C5DD3 !important;
              letter-spacing: 0.30000001192092896px;
             }
           }
         }
         .ant-tabs-tab {
         margin-bottom: 8px;
           .ant-tabs-tab-btn {
             span {
               font-family: Inter;
               font-size: 16px;
               font-weight: 500;
               line-height: 24px;
               text-align: left;
               text-underline-position: from-font;
               text-decoration-skip-ink: none;
               color: #718096;
               letter-spacing: 0.20000000298023224px;
             }
           }
         }
       }
    }

  }

  .brandDiv{
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
  }
  //////////////////////////////////////////////////////////////////
 
  .isoRight {
    display: flex;
    align-items: center;

    & > li {
      margin-left: ${(props) => (props['data-rtl'] === 'rtl' ? '35px' : '0')};
      margin-right: ${(props) => (props['data-rtl'] === 'rtl' ? '0' : '35px')};
      cursor: pointer;
      line-height: normal;
      position: relative;
      display: inline-block;

      @media only screen and (max-width: 360px) {
        margin-left: ${(props) => (props['data-rtl'] === 'rtl' ? '25px' : '0')};
        margin-right: ${(props) =>
          props['data-rtl'] === 'rtl' ? '0' : '25px'};
      }

      &:last-child {
        margin: 0;
      }

      i {
        font-size: 24px;
        color: ${palette('text', 0)};
        line-height: 1;
      }

      .isoRightIcon {
        padding: 5px;
        font-size: 21px;
        margin-top: 3px;

        & span.ant-badge {
          font-size: 21px;
        }
      }

      .isoIconWrapper {
        position: relative;
        line-height: normal;

        span.badge {
          font-size: 12px;
          color: #fff;
          background-color: ${palette('error', 0)};
          min-width: 20px;
          height: 20px;
          padding: 0 6px;
          display: -webkit-inline-flex;
          display: -ms-inline-flex;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          user-select: none;
          line-height: 20px;
          position: absolute;
          top: -5px;
          left: 20px;
          font-weight: bold;
          right: ${(props) =>
            props['data-rtl'] === 'rtl' ? '10px' : 'inherit'};
          ${borderRadius('10px')};
        }
      }

      &.isoUser {
        .isoImgWrapper {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          background-color: ${palette('grayscale', 9)};
          ${borderRadius('50%')};

          img {
            height: 100%;
            object-fit: cover;
          }

          .userActivity {
            width: 10px;
            height: 10px;
            display: block;
            background-color: ${palette('color', 3)};
            position: absolute;
            bottom: 0;
            right: 3px;
            border: 1px solid #ffffff;
            ${borderRadius('50%')};
          }
        }

        .isoDropdownLink {
          display: flex;

          & > span {
            margin-right: 15px;
          }

          .userDetail {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
          }
        }
      }
    }
  }
`);
export default MarketingWrapper;

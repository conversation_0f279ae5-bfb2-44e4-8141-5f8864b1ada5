/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable no-unused-vars */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
import React, { useState, useEffect } from 'react';
import { Input } from 'antd';
import {
  CloseOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  LockOutlined,
  UnlockOutlined,
  UnderlineOutlined,
  DeleteOutlined,
  HolderOutlined,
} from '@ant-design/icons';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import KonvaBoard2 from './KonvaBoard2';

const LayerContent = (props) => {
  console.log('layer triggered');
  console.log(props);
  const { layerhandlePopoverClose } = props;
  console.log(KonvaBoard2?.obj);
  const [stage, setStage] = useState(KonvaBoard2?.obj);
  // const [eyeOpen, setEyeOpen] = useState(true);
  const [lockOpen, setlockOpen] = useState(true);
  const [editingLayerId, setEditingLayerId] = useState(null);
  const [layerVisibility, setLayerVisibility] = useState({}); // State to store visibility status of each layer
  const [layerLockStatus, setLayerLockStatus] = useState({}); // State to store lock status of each layer
  const [layerName, setLayerName] = useState('');
  const [layerNames, setLayerNames] = useState({});
  const [eyeColors, setEyeColors] = useState({});

  // useEffect(() => {
  //   // Initialize the visibility and lock status for each layer
  //   if (stage) {
  //     const visibility = {};
  //     const lockStatus = {};
  //     // const updatedLayers = Array.from(stage.children);
  //     stage.children.forEach((layer) => {
  //       visibility[layer.attrs.id] = layer.visible(); // Set initial visibility status to true for all layers
  //       lockStatus[layer.attrs.id] = !layer.children[0].attrs.draggable; // Set initial lock status to true for all layers
  //       console.log(
  //         'layer.children[0].draggable()',
  //         layer.children[0].draggable(),
  //       );
  //     });
  //     setLayerVisibility(visibility);
  //     setLayerLockStatus(lockStatus);
  //   }
  // }, [stage]);

  useEffect(() => {
    // Reset the state when a new layer is added
    setStage(KonvaBoard2?.obj);
    const initialColors = {};
    stage.children.forEach((layer) => {
      initialColors[layer.attrs.id] = layer.visible() ? 'blue' : 'red';
    });
    setEyeColors(initialColors);
    setlockOpen(true);
    setLayerVisibility({});
    setLayerLockStatus({});
    console.log(`updating konvaStage layers `);
    if (stage) {
      const visibility = {};
      const lockStatus = {};
      // const updatedLayers = Array.from(stage.children);
      stage.children.forEach((layer) => {
        visibility[layer.attrs.id] = layer.visible(); // Set initial visibility status to true for all layers
        lockStatus[layer.attrs.id] = !layer.children[0].attrs.draggable; // Set initial lock status to true for all layers
        console.log(
          'layer.children[0].draggable()',
          layer.children[0].draggable(),
        );
      });
      setLayerVisibility(visibility);
      setLayerLockStatus(lockStatus);
    }
  }, [props]);

  const onDragEnd = (result) => {
    if (!result.destination) return;

    const sourceIndex = result.source.index;
    const destinationIndex = result.destination.index;

    const updatedLayers = Array.from(stage.children);
    const [movedLayer] = updatedLayers.splice(sourceIndex, 1);
    updatedLayers.splice(destinationIndex, 0, movedLayer);

    updatedLayers.forEach((layer, index) => {
      if (layer.getParent()) {
        layer.setZIndex(index + 1);
      }
    });

    setStage((prevStage) => ({
      ...prevStage,
      children: updatedLayers,
    }));

    console.log(`${sourceIndex} => ${destinationIndex}`);
    // setLayerVisibility(true);
  };

  const onDragStart = (result) => {
    console.log('onDragStart');
    console.log(result);
  };

  // layer visible
  const handleLayervisible = (id) => {
    console.log('clicked');
    setEyeColors((prevColors) => ({
      ...prevColors,
      [id]: prevColors[id] === 'blue' ? 'red' : 'blue',
    }));
    setLayerVisibility((prevVisibility) => ({
      ...prevVisibility,
      [id]: !prevVisibility[id], // Toggle the visibility status of the layer
    }));
    const tempLayer = KonvaBoard2.obj.children.find((e) => e.attrs.id === id);
    tempLayer.visible(!tempLayer.visible());
  };

  // layer Lock Dragging
  const handleLayerLock = (id) => {
    console.log('clicked');
    setlockOpen(!lockOpen);
    setLayerLockStatus((prevLockStatus) => ({
      ...prevLockStatus,
      [id]: !prevLockStatus[id], // Toggle the lock status of the layer
    }));
    const tempLayer = KonvaBoard2.obj.children.find((e) => e.attrs.id === id);
    const draggableElement = tempLayer.children[0];
    draggableElement.draggable(!draggableElement.draggable());
  };

  // layer delete
  const handleLayerDelete = (id) => {
    const layerIndex = KonvaBoard2.obj.children.findIndex(
      (e) => e.attrs.id === id,
    );
    if (layerIndex !== -1) {
      // KonvaBoard2.obj.children[layerIndex].remove();
      // stage?.children.splice(layerIndex, 1);
      KonvaBoard2.obj.children[layerIndex].destroy();
      setStage(KonvaBoard2.obj);
      const newLayerNames = { ...layerNames };
      const newLayerVisibility = { ...layerVisibility };
      const newLayerLockStatus = { ...layerLockStatus };

      delete newLayerNames[id];
      delete newLayerVisibility[id];
      delete newLayerLockStatus[id];

      setLayerNames(newLayerNames);
      setLayerVisibility(newLayerVisibility);
      setLayerLockStatus(newLayerLockStatus);

      console.log('Layer deleted.');
    } else {
      console.log('Layer not found.');
    }
  };

  // layer click
  const handleLayerClick = () => {
    // const tempLayer = KonvaBoard2.obj.children.find((e) => e.attrs.id === id);
    // tempLayer.children[0].draggableProps(false);
  };

  if (!stage) {
    return null;
  }
  return (
    <div
      style={{
        display: 'flex flex-vertical',
        justifyContent: 'space-between',
        height: '40vh',
        overflowY: 'auto',
        width: '15vw',
      }}
    >
      <p
      // style={{
      //   justifyContent: 'center',
      //   alignItems: 'center',
      //   fontWeight: 'bold',
      // }}
      >
        LAYERS
      </p>
      {/* <CloseOutlined
          onClick={layerhandlePopoverClose}
          style={{ cursor: 'pointer', marginTop: '5px' }}
        /> */}

      <DragDropContext onDragStart={onDragStart} onDragEnd={onDragEnd}>
        <Droppable droppableId="droppable-layers">
          {(provided) => (
            <ul ref={provided.innerRef} {...provided.droppableProps}>
              {stage?.children?.map((layer, index) =>
                !layer ? null : (
                  <Draggable
                    key={layer.attrs.id}
                    draggableId={layer.attrs.id}
                    index={index}
                    isDragDisabled={false} // Enable dragging for this layer
                    // clone // Add the clone property
                  >
                    {(providedDraggable) => (
                      <div
                        ref={providedDraggable.innerRef}
                        {...providedDraggable.draggableProps}
                        {...providedDraggable.dragHandleProps}
                        style={{
                          // marginTop: '15px',
                          fontSize: '12px',
                          ...providedDraggable.draggableProps.style,
                        }}
                      >
                        <div
                          style={{
                            display: 'flex',
                            background: '#0000001A',
                            justifyContent: 'space-between',
                            padding: '5px 10px 5px 10px',
                            marginTop: '8px',
                            alignItems: 'center',
                            borderRadius: '4px 4px 4px 4px',
                          }}
                          onClick={handleLayerClick}
                        >
                          <HolderOutlined
                            style={{ fontSize: '20px', color: '#000' }}
                          />
                          <p
                            style={{
                              marginLeft: '15px',
                              fontSize: '12px',
                              color: 'black',
                              cursor: 'pointer',
                            }}
                            onClick={() => {
                              setEditingLayerId(layer.attrs.id);
                              setLayerName(
                                layerNames[layer.attrs.id] || layer.attrs.id,
                              );
                            }}
                          >
                            {editingLayerId === layer.attrs.id ? (
                              <Input
                                style={{
                                  marginLeft: '15px',
                                  fontSize: '12px',
                                  color: 'black',
                                }}
                                value={layerNames[layer.attrs.id]}
                                onChange={(e) => {
                                  const newLayerNames = { ...layerNames };
                                  newLayerNames[layer.attrs.id] =
                                    e.target.value;
                                  setLayerNames(newLayerNames);
                                }}
                                autoFocus
                                onBlur={() => {
                                  if (
                                    layerNames[editingLayerId]?.trim() === ''
                                  ) {
                                    // Set the layer name back to its previous value
                                    const newLayerNames = { ...layerNames };
                                    newLayerNames[editingLayerId] =
                                      editingLayerId;
                                    setLayerNames(newLayerNames);
                                  }
                                  setEditingLayerId(null);
                                }}
                                onPressEnter={() => {
                                  if (
                                    layerNames[editingLayerId]?.trim() === ''
                                  ) {
                                    // Set the layer name back to its previous value
                                    const newLayerNames = { ...layerNames };
                                    newLayerNames[editingLayerId] =
                                      editingLayerId;
                                    setLayerNames(newLayerNames);
                                  }
                                  setEditingLayerId(null);
                                }}
                              />
                            ) : (
                              <span title={layerNames[layer.attrs.name]}>
                                {layerNames[layer.attrs.name]?.length > 10
                                  ? `${layerNames[layer.attrs.name].slice(
                                      0,
                                      10,
                                    )}...`
                                  : layerNames[layer.attrs.name] ||
                                    layer.attrs.name}
                              </span>
                            )}
                          </p>
                          <EyeOutlined
                            type="button"
                            onClick={() => handleLayervisible(layer.attrs.id)}
                            style={{
                              marginRight: '10px',
                              marginLeft: '10px',
                              cursor: 'pointer',
                              color: eyeColors[layer.attrs.id] || 'blue', // Apply color based on layer's color
                            }}
                          />

                          {/* {layerVisibility[layer.attrs.id] ? (
                            <EyeOutlined
                              style={{
                                marginRight: '10px',
                                marginLeft: '10px',
                                cursor: 'pointer',
                                color: 'blue', // Apply color based on visibility state
                              }}
                              onClick={() => handleLayervisible(layer.attrs.id)}
                            />
                          ) : (
                            <EyeInvisibleOutlined
                              style={{
                                marginRight: '10px',
                                marginLeft: '10px',
                                cursor: 'pointer',
                                color: 'blue', // Apply color based on visibility state
                              }}
                              onClick={() => handleLayervisible(layer.attrs.id)}
                            />
                          )} */}
                          {/* <LockOutlined
                            onClick={() => handleLayerLock(layer.attrs.id)}
                            style={{
                              cursor: 'pointer',
                              color: 'blue', // Apply color based on disability state
                            }}
                          /> */}
                          {layerLockStatus[layer.attrs.id] ? ( // Check lock status for the layer
                            <LockOutlined
                              style={{
                                cursor: 'pointer',
                                color: 'blue', // Apply color based on visibility state
                              }}
                              onClick={() => handleLayerLock(layer.attrs.id)}
                            />
                          ) : (
                            <UnlockOutlined
                              style={{
                                cursor: 'pointer',
                                color: 'blue', // Apply color based on visibility state
                              }}
                              onClick={() => handleLayerLock(layer.attrs.id)}
                            />
                          )}
                          <DeleteOutlined
                            onClick={() => handleLayerDelete(layer.attrs.id)}
                            style={{
                              cursor: 'pointer',
                              marginRight: '10px',
                              marginLeft: '10px',
                              color: 'red', // Apply color based on disability state
                            }}
                          />
                        </div>
                      </div>
                    )}
                  </Draggable>
                ),
              )}
              {provided.placeholder}
            </ul>
          )}
        </Droppable>
      </DragDropContext>
    </div>
  );
};

export default LayerContent;

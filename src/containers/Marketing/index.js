/* eslint-disable new-cap */
/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-no-bind */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-param-reassign */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable global-require */
/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable import/extensions */
import React, { useEffect, useState } from 'react';
import {
  Col,
  Row,
  Tabs,
  Input,
  Select,
  Switch,
  Button,
  Menu,
  Dropdown,
  Typography,
  DatePicker,
} from 'antd';
import IntlMessages from '@chill/components/utility/intlMessages';
import TableWrapper from '@chill/assets/styles/AntTables.styles';
import {
  FilterOutlined,
  SwapOutlined,
  MoreOutlined,
  DownOutlined,
} from '@ant-design/icons';
import {
  Bar,
  BarChart,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  Rectangle,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';
import theme from '@chill/config/theme/default';
import basicStyle from '@chill/assets/styles/constants';
import { TabPane } from '@chill/components/uielements/tabs';
import LinkBox from '@chill/components/LinkBox';
import Chart from 'react-google-charts';
import moment from 'moment';
import { injectIntl } from 'react-intl';
import Popover from '@chill/components/uielements/popover';
import getApiData from '@chill/lib/helpers/apiHelper';
import { findIndex, isArray, isEmpty, isObject } from 'lodash-es';
import Popconfirms from '@chill/components/Feedback/Popconfirm';
import Notification from '@chill/components/Notification';
import { useSelector } from 'react-redux';
import { getLanguageList } from '@chill/lib/helpers/utility';
import { Column } from '@ant-design/charts';
import editSVG from '@chill/assets/images/sidebarIcons/viewProduct.svg';
import exchangeSVG from '@chill/assets/images/exchange.svg';
import editAction from '@chill/assets/images/edit-action.png';
import speechBubleSVG from '@chill/assets/images/speech-bubble.svg';
import goalSVG from '@chill/assets/images/goal.svg';
import percentSVG from '@chill/assets/images/percentage.svg';
import plusIcon from '@chill/assets/images/Plus.png';
import minusIcon from '@chill/assets/images/minus.png';
import searchBlackImg from '@chill/assets/images/Search-black.png';
import filterImg from '@chill/assets/images/Filter.png';
import horizonalDots from '@chill/assets/images/more-horizontal.png';
import horizonalPurpleDots from '@chill/assets/images/more-horizontal-purple.png';
import calendarFilterIcon from '@chill/assets/images/calendar-picker.png';
import overViewBackImg from '@chill/assets/images/over-view-background.png';
import expandIcon from '@chill/assets/images/up-arrow.png';
import blackblackdownArrow from '@chill/assets/images/primary-down-arrow.png';
import { useHistory } from 'react-router-dom';
import downloadFile from '@chill/assets/images/download-file.png';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import PageTitleHeader from '@chill/components/uielements/PageTitleHeader';
import {
  greenBackgroundTag,
  orangeBackgroundTag,
} from '@chill/lib/helpers/utilityData';
import styled from 'styled-components';
import { Popover as ReactPopOver } from 'react-tiny-popover';
import CustomerDrawer from '../Customers/CustomerDrawer';
import MarketingDashboard from './MarketingDashboard';
import MarketingDrawer from './MarketingDrawer';
// import StickerWidget from '../Widgets/Sticker/StickerWidget';
import MarketingWrapper from './Marketing.Styles';
import IsoWidgetsWrapper from '../Widgets/WidgetsWrapper';
// import MarketingCreateNew from './MarketingCreateNew';

const defaultData = {
  drawerVisible: false,
  visible: false,
  type: 'feed_post',
  drawerType: '',
  sendTo: '',
  postTitle: '',
  postTitlePushMsg: '',
  postTitleInAppMsg: '',
  postSubTitle: '',
  postSubTitlePushMsg: '',
  appName: '',
  message: '',
  messagePushMsg: '',
  launchUrl: '',
  date: moment(new Date()).format('ll'),
  time: moment(new Date()).format('HH:MM'),
  file: '',
  fileUrl: '',
  selectfileUrl: '',
  fileName: '',
  iconUrl: '',
  attachmentsUrl: '',
  messageType: 'center',
  colorType: '',
  textColor: '#000000',
  text1: '',
  fontSize: '16',
  textAlign: 'center',
  backColor: '#1172EC',
  btnCnt: [],
  btnText: '',
  buttonFontSize: '16',
  buttonTextAlign: 'center',
  buttonSize: '100px',
  buttonTextColor: '#000',
  buttonUrl: '',
  startDate: '',
  endDate: '',
  singleData: {},
  initObj: {},
  campaignTitle: '',
  campaignName: '',
  campaignDesc: '',
  status: {
    autoAdd: false,
  },
  userCount: 0,
  attachmentLike: false,
  errorMsg: '',
  attachmentypeArr: ['Video link', 'Webpage'],
  attachmentType: '',
};

/**
 *
 * @module Marketing
 */

const { RangePicker } = DatePicker;
const { Text } = Typography;

function Marketing(props) {
  const languages = useSelector((state) => state.Language.languages);
  const [state, setState] = useState(defaultData);

  const { initObj } = state;
  const [segmentList, setSegmentList] = useState();
  const [categoryList, setCategoryList] = useState();
  const { drawerVisible, drawerType, visible } = state;
  const [edit, setEdit] = React.useState(false);
  const [isIndex, setIsIndex] = useState(null);
  console.log(`hello ~ file: index.js:156 ~ Marketing ~ isIndex:`, isIndex);
  const [actionMenu, setActionMenu] = React.useState(false);
  // const [singleItem, setSingleItem] = React.useState({});
  const [overviewVisible, setOverViewVisibility] = useState(false);
  const [durationVisible, setDurationVisibility] = useState(false);
  const [duration, setDuration] = useState(
    <IntlMessages id="dashboard.thisweek" />,
  );
  const [stEndDate, setStEndDate] = useState({
    stDate: '',
    endDate: '',
  });
  const [periodType, setPeriodType] = useState('this_week');
  const [overView, setOverView] = useState(
    <IntlMessages id="dashboard.messagesent" />,
  );
  const [overViewType, setOverViewType] = useState('message');
  const [dasboardVisible, setDasboardVisible] = useState(false);
  const [countries, setCountries] = useState([]);
  const [marketingState, setMarketingState] = useState({
    marketing: [],
    loader: false,
  });
  const [sort, setSort] = useState(false);
  const [statusSort, setStatusSort] = useState('');
  // const [clickSort, setClickSort] = useState('');
  const [filterMode, setFilterMode] = React.useState(false);
  const [filter, setFilter] = React.useState({ pagination: {}, filters: {} });
  const [buttonLoader, setButtonLoader] = useState(false);
  const { language } = useSelector((st) => st.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);
  const [campaignData, setCampaignData] = useState({});

  const brandLists = useSelector((st) => st.Auth.list);
  const userData = useSelector((st) => st.Auth.userData);
  const uData = isObject(userData) ? { ...userData } : {};
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [filterTab, setFilterTab] = useState('All');
  const [filterByType, setFilterByType] = useState('');
  const [filterByStatus, setFilterByStatus] = useState('');
  const [selectedTab, setSelectedTab] = useState('');
  const [filterByName, setFilterByName] = useState('');
  const showCreateDrop =
    uData.user_type === 'brand_admin' ||
    uData.user_type === 'marketing_manager' ||
    uData.user_type === 'manager';
  const [openCreateDropdown, setOpenCreateDropdown] = useState(false);
  console.log(
    `hello ~ file: index.js:177 ~ Marketing ~ openCreateDropdown:`,
    openCreateDropdown,
  );
  const user = isObject(uData) ? uData : {};
  const userId = isObject(user) && user.id ? user.id : '';
  const dWidth = window.innerWidth;

  const history = useHistory();

  const marketingBoxes = [
    {
      key: 'totalVal',
      text: 'marketing.totalValue',
      link: '',
      fontColor: '#04B8FF',
      bgColor: '#ffffffff',
      count: !isEmpty(campaignData) ? campaignData.totalSales : 0,
      boxObj: !isEmpty(campaignData)
        ? campaignData?.tileDetails?.totalSalesDetails
        : {},
      img: percentSVG,
    },
    {
      key: 'totalSales',
      text: 'marketing.NumbCampaigns',
      link: '',
      fontColor: '#00D9C0',
      bgColor: '#ffffffff',
      count: !isEmpty(campaignData) ? campaignData.totalSales : 0,
      img: exchangeSVG,
      boxObj: !isEmpty(campaignData)
        ? campaignData?.tileDetails?.campaignDetails
        : {},
    },
    {
      key: 'sentMessages',
      text: 'marketing.messagesSent',
      fontColor: '#2753E1',
      bgColor: '#ffffffff',
      count: !isEmpty(campaignData) ? campaignData.getSentMessages : 0,
      img: speechBubleSVG,
      boxObj: !isEmpty(campaignData)
        ? campaignData?.tileDetails?.getMessageCountDetails
        : {},
    },
    {
      key: 'totalClicks',
      text: 'marketing.clicks',
      fontColor: '#988FF5',
      bgColor: '#ffffffff',
      count: !isEmpty(campaignData) ? campaignData.totalClicks : 0,
      boxObj: !isEmpty(campaignData)
        ? campaignData?.tileDetails?.totalClickDetails
        : {},
      img: goalSVG,
    },
  ];

  // this function for get country list
  /** this function for get country list
   * @function getCountryList
   * @param {object} data {}
   */
  async function getCountryList() {
    try {
      const response = await getApiData('getCountryList', {}, 'POST');
      if (response.success && isArray(response.data)) {
        setCountries(response.data);
      } else {
        Notification('error', response.message);
      }
    } catch (error) {
      console.log('error ===', error);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  // this function for get campaign data
  /** this function for get campaign data
   * @function getCampaignData
   * @param {object} data period_type, brand_id, chart_type
   */
  async function getCampaignData(data = {}) {
    data.period_type = periodType;
    data.brand_id = selectedProduct;
    data.chart_type = overViewType;
    try {
      const response = await getApiData(
        'campaigns/get-campaign-data',
        data,
        'POST',
      );
      if (response.success && isObject(response.data)) {
        setCampaignData(response.data);
      } else {
        Notification('error', response.message || messageArray['err.wenWrong']);
      }
    } catch (error) {
      console.log('error ===', error);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  useEffect(() => {
    getCountryList();
    getLanguageList();
    getCampaignData();
  }, [visible, periodType, selectedProduct, overViewType]);

  const { rowStyle } = basicStyle;
  function handleVisible(e) {
    setActionMenu((visiblePre) => !visiblePre);
    // if (!e) {
    // }
    setIsIndex(null);
  }
  const renderTabBar = (prop, DefaultTabBar) => (
    // <Sticky bottomOffset={80}>
    // {({ style }) => (
    <DefaultTabBar
      {...prop}
      className="site-custom-tab-bar"
      style={{ marginBottom: 0 }}
    />
    // )}
    // </Sticky>
  );
  function messagesSent() {
    setOverViewVisibility((visiblePre) => !visiblePre);
  }

  function durationChange() {
    setDurationVisibility((visiblePre) => !visiblePre);
  }

  // this function for get products
  /** this function for get marketing data
   * @function getMarketingList
   * @param {object} data brand_id, is_archived, status, campaign_name, campaign_type,
   */
  async function getMarketingList(data = {}) {
    setMarketingState((p) => ({
      ...p,
      loader: true,
    }));
    data.brand_id = selectedProduct;

    if (filterTab !== 'All') {
      if (filterTab === 'Archive') {
        data.is_archived = 1;
      } else {
        data.status = filterTab;
      }
    } else {
      data.status = null;
    }

    if (filterByName !== '') {
      data.search_value = filterByName;
    } else {
      data.search_value = null;
    }
    if (stEndDate?.stDate && stEndDate?.endDate) {
      data.start_date = stEndDate?.stDate;
      data.end_date = stEndDate?.endDate;
    }

    if (filterByType !== '') {
      data.campaign_type = filterByType;
    } else {
      data.campaign_type = null;
    }

    if (filterByStatus !== '') {
      data.status = filterByStatus;
    } else {
      data.status = null;
    }
    try {
      const response = await getApiData('campaigns/index', data, 'POST');
      if (response.success && isArray(response.data)) {
        setMarketingState((p) => ({
          ...p,
          marketing: isArray(response.data) ? response.data : [],
          loader: false,
        }));
        const pagination =
          filter && filter.pagination ? { ...filter.pagination } : {};
        pagination.total = response.total_count || 0;
        pagination.pageSize = response.per_page || 25;
        pagination.current = response.page || 1;
        setFilter((f) => ({ ...f, pagination }));
      } else {
        setMarketingState((p) => ({
          ...p,
          loader: false,
        }));
      }
    } catch (err) {
      setMarketingState((p) => ({
        ...p,
        loader: false,
      }));
    }
  }

  // this function for get customers
  /** this function for get customers
   * @function getSegmentList
   * @param {object} data is_count
   */
  async function getSegmentList() {
    try {
      const response = await getApiData(
        'getSegmentList',
        { is_count: 1 },
        'POST',
      );
      if (response.success) {
        setSegmentList(response.data);
      } else {
        setSegmentList(response.data);
      }
    } catch (err) {
      setSegmentList((preState) => ({
        ...preState,
        customerLoad: false,
      }));
    }
  }

  // this function for get category
  /** this function for get category
   * @function getCategoryList
   *
   */
  async function getCategoryList() {
    try {
      const response = await getApiData('campaigns-category/index', {}, 'POST');
      if (response.success) {
        setCategoryList(response.data);
      } else {
        setCategoryList(response.data);
      }
    } catch (err) {
      console.log('er=====', err);
      setCategoryList((preState) => ({
        ...preState,
        customerLoad: false,
      }));
    }
  }

  /** this function for delete marketing
   * @function deleteMarketing
   * @param {object} data campaign_id
   */
  async function deleteMarketing() {
    const data = {
      campaign_id:
        isObject(state) &&
        isObject(state.initObj) &&
        !isEmpty(state.initObj) &&
        state.initObj.id,
    };
    const marketingArr = isArray(marketingState.marketing)
      ? marketingState.marketing
      : [];
    setMarketingState({
      loader: true,
    });
    try {
      const response = await getApiData(
        'campaigns/remove-campaign',
        data,
        'POST',
      );
      let msgTitle = 'error';
      if (response.success) {
        msgTitle = 'success';
        // fetchDataFilters();
        getMarketingList();
      } else {
        setMarketingState((p) => ({
          ...p,
          marketing: marketingArr,
          loader: false,
        }));
      }
      Notification(msgTitle, response.message);
    } catch (error) {
      console.log('error ===', error);
      setMarketingState((p) => ({
        ...p,
        marketing: marketingArr,
        loader: false,
      }));
    }
  }

  /** this function for archive marketing
   * @function archiveMarketing
   * @param {object} data campaign_id
   */
  async function archiveMarketing() {
    const data = {
      campaign_id:
        isObject(state) &&
        isObject(state.initObj) &&
        !isEmpty(state.initObj) &&
        state.initObj.id,
    };
    const marketingArr = isArray(marketingState.marketing)
      ? marketingState.marketing
      : [];
    setMarketingState({
      loader: true,
    });
    try {
      const response = await getApiData(
        'campaigns/archived-campaign',
        data,
        'POST',
      );
      let msgTitle = 'error';
      if (response.success) {
        msgTitle = 'success';
        // fetchDataFilters();
        getMarketingList();
      } else {
        setMarketingState((p) => ({
          ...p,
          marketing: marketingArr,
          loader: false,
        }));
      }
      Notification(msgTitle, response.message);
    } catch (error) {
      console.log('error ===', error);
      setMarketingState((p) => ({
        ...p,
        marketing: marketingArr,
        loader: false,
      }));
    }
  }
  useEffect(() => {
    getMarketingList();
    getSegmentList();
    getCategoryList();
    if (drawerType !== 'duplicate') {
      setState((p) => ({ ...p, file: '', fileUrl: '', fileName: '' }));
    }
  }, [drawerVisible, selectedProduct, visible]);

  useEffect(() => {
    const page =
      filter && filter.pagination && filter.pagination.current
        ? filter.pagination.current
        : 1;
    const flt = filter ? { ...filter } : {};
    getMarketingList({ page, ...flt.filters });
  }, [sort, selectedProduct, statusSort]);

  useEffect(() => {
    getMarketingList();
  }, [filterTab, filterByName, filterByType, filterByStatus, stEndDate]);

  // this function for generate pdf
  const generatePDFWithTable = (data) => {
    console.log(
      `hello ~ file: index.js:512 ~ generatePDFWithTable ~ data:`,
      data,
    );
    const pdf = new jsPDF();

    // Table Columns
    const tableColumn = [
      'ID',
      'Campaign Name',
      'Created By',
      'Created On',
      'Type',
      'Status',
      'Clicks',
    ];

    // Table Rows
    const tableRows = data.map((campaign, index) => [
      index + 1, // ID (index)
      campaign.campaign_name || '-',
      campaign.createdBy || '-',
      new Date(campaign.createdAt).toLocaleString() || '-',
      campaign.campaign_type || '-',
      campaign.status || '-',
      campaign.clicks || '0',
    ]);

    // Add Title
    pdf.setFontSize(14);
    pdf.text('Campaign Details', 14, 15);

    // Add Table
    pdf.autoTable({
      head: [tableColumn],
      body: tableRows,
      startY: 20, // Start position
      styles: { fontSize: 10, cellPadding: 3 },
      theme: 'grid', // Table styling
      headStyles: { fillColor: [41, 128, 185], textColor: [255, 255, 255] },
    });

    // Save PDF
    pdf.save('campaigns_list.pdf');
  };

  async function downLoadPdfData() {
    const data = {
      user_id: userId,
    };
    try {
      const response = await getApiData(
        'campaigns/download-campaign-data',
        data,
        'GET',
      );
      console.log(
        `hello ~ file: index.js:519 ~ downLoadPdfData ~ response:`,
        response,
      );

      if (response.success) {
        generatePDFWithTable(response.data);
      } else {
        Notification('error', response.message || messageArray['err.wenWrong']);
      }
    } catch (error) {
      console.error('Error downloading PDF:', error);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  const ActionWrapper = styled.div`
    display: flex;
    align-items: center;
    justify-content: center;
    .popMainDiv {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2px;
      justify-content: center;
      .isoDropdownLink {
        width: 148.93px;
        height: 32px;
        padding: 8px;
        gap: 10px;
        border-radius: 6px;
        background: #ffffff;
        span {
          font-family: Inter;
          font-size: 13px;
          font-weight: 400;
          line-height: 16px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #242533;
        }
      }
      .isoDropdownLink:hover {
        background: #5d42ff14;
        span {
          font-family: Inter;
          font-size: 13px;
          font-weight: 600;
          line-height: 16px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #644ded;
        }
      }
    }
  `;

  const content = (item) => {
    console.log(`hello ~ file: index.js:692 ~ content ~ item:`, item);
    return (
      <ActionWrapper>
        <div className="popMainDiv">
          <div
            className="isoDropdownLink"
            onClick={() => {
              setDasboardVisible(!dasboardVisible);
            }}
          >
            {/* <img
              className="iconImg"
              src={editSVG}
              style={{ width: 16, height: 16, marginRight: 12 }}
              alt="noIcon"
              /> */}
            <IntlMessages id="View" />
          </div>
          <div
            className="isoDropdownLink"
            onClick={() => {
              setIsIndex(null);

              // history.push({
              //   pathname: '/dashboard/marketing/audience',
              //   state: { data: item },
              // });
              // setDasboardVisible(!dasboardVisible);
            }}
          >
            {/* <img
              className="iconImg"
              src={editSVG}
              style={{ width: 16, height: 16, marginRight: 12 }}
              alt="noIcon"
              /> */}
            <IntlMessages id="Edit" />
          </div>
          <div
            className="isoDropdownLink"
            onClick={() => {
              archiveMarketing();
              setIsIndex(null);
            }}
          >
            {/* <img
              className="iconImg"
              src={archivePng}
              style={{ width: 16, height: 16, marginRight: 12 }}
              alt="noIcon"
            /> */}
            <IntlMessages
              id={
                item.is_archived === 1 ? 'action.unarchive' : 'action.archive'
              }
            />
          </div>

          <div className="isoDropdownLink">
            <Popconfirms
              title={<IntlMessages id="Sure to delete?" />}
              okText={<IntlMessages id="DELETE" />}
              cancelText={<IntlMessages id="No" />}
              onConfirm={() => {
                deleteMarketing();
                setIsIndex(null);
              }}
              onCancel={null}
            >
              {/* <DeleteOutlined style={{ paddingRight: 12, fontSize: 16 }} /> */}
              <IntlMessages id="Delete" />
            </Popconfirms>
          </div>
          <div
            className="isoDropdownLink"
            onClick={() => {
              setIsIndex(null);
              setState(() => ({
                ...defaultData,
                drawerVisible: true,
                drawerType: 'duplicate',
                type: item.campaign_type,
                initObj: item,
              }));
            }}
          >
            {/* <img
              className="iconImg"
              src={duplicateSVG}
              style={{ width: 16, height: 16, marginRight: 12 }}
              alt="noIcon"
              /> */}
            <IntlMessages id="action.duplicatecampaign" />
          </div>
        </div>
      </ActionWrapper>
    );
  };

  const contentCreateNew = () => {
    return (
      <MarketingWrapper>
        <div className="popMainDiv">
          <div
            className="isoDropdownLink"
            onClick={() => {
              archiveMarketing();
            }}
          >
            {/* <img
              className="iconImg"
              src={archivePng}
              style={{ width: 16, height: 16, marginRight: 12 }}
              alt="noIcon"
            /> */}
            <IntlMessages id="action.archive" />
          </div>
          <div
            className="isoDropdownLink"
            onClick={() => {
              setDasboardVisible(!dasboardVisible);
            }}
          >
            {/* <img
              className="iconImg"
              src={editSVG}
              style={{ width: 16, height: 16, marginRight: 12 }}
              alt="noIcon"
            /> */}
            <IntlMessages id="action.viewcampaign" />
          </div>
          <div className="isoDropdownLink">
            {/* <img
              className="iconImg"
              src={duplicateSVG}
              style={{ width: 16, height: 16, marginRight: 12 }}
              alt="noIcon"
            /> */}
            <IntlMessages id="action.duplicatecampaign" />
          </div>
          <div className="isoDropdownLink">
            <Popconfirms
              title={<IntlMessages id="Sure to delete?" />}
              okText={<IntlMessages id="DELETE" />}
              cancelText={<IntlMessages id="No" />}
              onConfirm={() => {
                deleteMarketing();
                // setIsIndex(null);
              }}
              onCancel={null}
            >
              {/* <DeleteOutlined style={{ paddingRight: 12, fontSize: 16 }} /> */}
              <IntlMessages id="Delete Marketing" />
            </Popconfirms>
          </div>
        </div>
      </MarketingWrapper>
    );
  };

  function handleVisibleChange(item) {
    console.log('handleVisibleChange(item); ==>', item);
    setIsIndex(item.id);
  }

  // this function for enable/disable the post avaibility
  /** this function for enable/disable the post avaibility
   * @function handlePost
   * @param {object} data campaign_id
   */
  async function handlePost(data) {
    const id = isObject(data) && !isEmpty(data) && data.id ? data.id : null;

    try {
      const response = await getApiData(
        'campaigns/change-campaign-status',
        { campaign_id: id },
        'POST',
      );
      if (
        response.success &&
        isObject(response.data) &&
        !isEmpty(response.data)
      ) {
        Notification('success', response.message);
        const marketingArr = isArray(marketingState.marketing)
          ? [...marketingState.marketing]
          : [];

        if (isArray(marketingArr) && marketingArr.length > 0) {
          const index = findIndex(marketingArr, (li) => li.id === data.id);
          if (index > -1) {
            marketingArr[index] = response.data;
          }
          setMarketingState((pre) => ({
            ...pre,
            marketing: marketingArr,
          }));
        }
      } else {
        Notification('error', response.message || messageArray['err.wenWrong']);
      }
    } catch (error) {
      console.log('error ===', error);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  const onClick = ({ key }) => {
    setSelectedTab(key);
    setState(() => ({
      ...defaultData,
      drawerVisible: true,
      drawerType: 'add',
      initObj: {},
      btnCnt: [],
    }));

    console.log(`Click on item ${key}`);
  };

  const statusArray = [
    {
      key: 'delivered',
      label: <IntlMessages id="Delivered" />,
    },
    {
      key: 'expired',
      label: <IntlMessages id="Expired" />,
    },
    {
      key: 'live',
      label: <IntlMessages id="Live" />,
    },
  ];

  const items = [
    {
      key: 'feed_post',
      label: 'Feed Post',
    },
    {
      key: 'in_app_message',
      label: 'In-app Messages',
    },
    {
      key: 'push_message',
      label: 'Push Messages',
    },
  ];
  const columns = [
    {
      key: 'sort_action',
      title: <img src={editAction} alt="action" />,
      dataIndex: 'action',
      rowKey: 'action',
      className: 'campaign-cell',
      width: 50,
      fixed: 'left',
      render: (text, item) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="marketing.action" />
            </span>
            <div className="mobile-lbl-val">
              <ReactPopOver
                content={
                  <div
                    style={{
                      margin: '10px 5px',
                      border: '1px solid #BBCCDE66',
                      width: 160,
                      borderRadius: 8,
                      backgroundColor: '#FDFDFD',
                      padding: '7px 5.17px 4px 5.9px',
                      boxShadow: '0px 0px 1px 0px #435A6F78',
                    }}
                  >
                    {content(item)}
                  </div>
                }
                isOpen={isIndex === item.id}
                onClickOutside={(e) => handleVisible(e)}
                arrowPointAtCenter
                className="action-popover"
                positions="right"
              >
                <Button
                  type="text"
                  onClick={() => {
                    handleVisibleChange(item);
                    setState({
                      ...state,
                      initObj: item,
                    });
                  }}
                >
                  <img
                    src={
                      isIndex === item.id ? horizonalPurpleDots : horizonalDots
                    }
                    alt="action"
                  />
                </Button>
              </ReactPopOver>
            </div>
          </div>
        );
      },
    },
    // {
    //   title: <IntlMessages id="No" />,
    //   dataIndex: 'id',
    //   rowKey: 'id',
    //   width: 50,
    //   className: 'campaign-cell',
    //   render: (text, data, index) => {
    //     return (
    //       <div>
    //         <span className="label">
    //           <IntlMessages id="No" />
    //         </span>
    //         <span className="mobile-lbl-val">{index + 1 || '-'}</span>
    //       </div>
    //     );
    //   },
    // },
    {
      key: 'sort_campaign_name',
      title: <IntlMessages id="marketing.campaignName" />,
      dataIndex: 'campaign_name',
      rowKey: 'campaign_name',
      width: 175,
      sorter: true,
      sortDirections: ['descend', 'ascend'],
      className: 'campaign-cell',

      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="marketing.campaignName" />
            </span>
            <div className="listNameView">
              <span className="mobile-lbl-val">{text || '-'}</span>
            </div>
          </div>
        );
      },
    },
    {
      key: 'sort_createdBy',
      title: <IntlMessages id="marketing.createdBy" />,
      dataIndex: 'createdBy',
      rowKey: 'createdBy',
      width: 175,
      className: 'campaign-cell',

      render: (text, data) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="marketing.createdBy" />
            </span>
            <div className="listNameView">
              <img
                src={data.user_profile}
                style={{
                  width: 30,
                  height: 30,
                  backgroundColor: '#eee',
                  marginRight: 12,
                  borderRadius: 30,
                }}
                alt=""
              />
              <span className="mobile-lbl-val">{text || '-'}</span>
            </div>
          </div>
        );
      },
    },
    // {
    //   key: 'sort_campaign_name',
    //   title: <IntlMessages id="marketing.sales" />,
    //   dataIndex: 'campSale',
    //   rowKey: 'campSale',
    //   width: 175,
    //   className: 'fullname-cell',
    //   render: (text) => {
    //     return (
    //       <div>
    //         <span className="label">
    //           <IntlMessages id="marketing.sales" />
    //         </span>
    //         <div className="listNameView">
    //           <span className="mobile-lbl-val">{text || '-'}</span>
    //         </div>
    //       </div>
    //     );
    //   },
    // },
    // {
    //   key: 'sort_campaign_name',
    //   title: <IntlMessages id="marketing.conversionRate" />,
    //   dataIndex: 'campRate',
    //   rowKey: 'campRate',
    //   width: 175,
    //   className: 'fullname-cell',
    //   render: (text) => {
    //     return (
    //       <div>
    //         <span className="label">
    //           <IntlMessages id="marketing.conversionRate" />
    //         </span>
    //         <div className="listNameView">
    //           <span className="mobile-lbl-val">{text || '-'}</span>
    //         </div>
    //       </div>
    //     );
    //   },
    // },
    // {
    //   key: 'sort_brand_name',
    //   title: <IntlMessages id="marketing.brandName" />,
    //   dataIndex: 'brand_name',
    //   rowKey: 'brand_name',
    //   width: 175,
    //   className: 'fullname-cell',
    //   render: (text, data) => {
    //     return (
    //       <div>
    //         <span className="label">
    //           <IntlMessages id="marketing.createdBy" />
    //         </span>
    //         <div className="listNameView">
    //           <img
    //             src={data.brand_profile}
    //             style={{
    //               width: 30,
    //               height: 30,
    //               backgroundColor: '#eee',
    //               marginRight: 12,
    //               borderRadius: 30,
    //             }}
    //             alt=""
    //           />
    //           <span className="mobile-lbl-val">{text || '-'}</span>
    //         </div>
    //       </div>
    //     );
    //   },
    // },
    {
      key: 'sort_createdAt',
      title: <IntlMessages id="marketing.createdOn" />,
      dataIndex: 'createdAt',
      rowKey: 'createdAt',
      width: 175,
      className: 'campaign-cell',

      sorter: true,
      render: (text) => {
        const date = moment(text).format('lll');
        return (
          <div>
            <span className="label">
              <IntlMessages id="marketing.createdOn" />
            </span>
            <span className="mobile-lbl-val">{date || '-'}</span>
          </div>
        );
      },
    },
    {
      key: 'sort_campaign_type',
      title: <IntlMessages id="marketing.type" />,
      dataIndex: 'campaign_type',
      rowKey: 'campaign_type',
      width: 175,
      className: 'campaign-type-cell',
      sorter: true,
      render: (text) => {
        const renderText =
          items.find((item) => item.key === text)?.label || '-';
        return (
          <div>
            <div style={greenBackgroundTag}>
              <Text ellipsis style={greenBackgroundTag.innerText}>
                <span>{renderText || '-'}</span>
              </Text>
            </div>
          </div>
        );
      },
    },
    {
      key: 'sort_status',
      title: <IntlMessages id="marketing.status" />,
      dataIndex: 'status',
      rowKey: 'status',
      width: 150,
      className: 'campaign-status-cell',

      sorter: true,
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="marketing.status" />
              <div>
                <SwapOutlined
                  className="filterIcon1"
                  style={{ color: sort ? theme.colors.primaryColor : '#000' }}
                  onClick={() => {
                    const status =
                      statusSort === ''
                        ? true
                        : statusSort === false
                        ? ''
                        : false;
                    console.log('Marketing -> status', status);
                    setStatusSort(status);
                  }}
                />
              </div>
            </span>
            <div style={orangeBackgroundTag}>
              <Text ellipsis style={orangeBackgroundTag.innerText}>
                <span>{text || '-'}</span>
              </Text>
            </div>
          </div>
        );
      },
    },
    // {
    //   title: <IntlMessages id="marketing.display.post" />,
    //   dataIndex: 'status',
    //   rowKey: 'status',
    //   width: 150,
    //   className: 'fullname-cell',
    //   render: (text, data) => {
    //     const bool =
    //       isObject(data) && !isEmpty(data) && data.is_disable
    //         ? data.is_disable
    //         : false;
    //     return (
    //       <div>
    //         <span className="label">
    //           <IntlMessages id="marketing.display.post" />
    //         </span>
    //         {data.campaign_type === 'feed_post' ? (
    //           <Switch checked={bool} onChange={() => handlePost(data)} />
    //         ) : (
    //           '-'
    //         )}
    //       </div>
    //     );
    //   },
    // },
    {
      key: 'sort_clicks',
      title: <IntlMessages id="marketing.clicks" />,
      dataIndex: 'clicks',
      rowKey: 'clicks',
      width: 100,
      className: 'campaign-cell',
      sorter: true,
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="common.action" />
            </span>
            <span className="mobile-lbl-val">{text || 0}</span>
          </div>
        );
      },
    },
    // {
    //   key: 'sort_likes',
    //   title: <IntlMessages id="marketing.likes" />,
    //   dataIndex: 'likes',
    //   rowKey: 'likes',
    //   width: 100,
    //   className: 'fullname-cell',
    //   sorter: true,
    //   render: (text, data) => {
    //     const isShow = data.campaign_type === 'feed_post';
    //     return (
    //       <div>
    //         <span className="label">
    //           <IntlMessages id="common.action" />
    //         </span>
    //         <span className="mobile-lbl-val">{isShow ? text : '-'}</span>
    //       </div>
    //     );
    //   },
    // },
    // {
    //   key: 'sort_failed',
    //   title: <IntlMessages id="marketing.failed" />,
    //   dataIndex: 'failed',
    //   rowKey: 'failed',
    //   width: 100,
    //   sorter: true,
    //   className: 'fullname-cell',
    //   render: (text) => {
    //     return (
    //       <div>
    //         <span className="label">
    //           <IntlMessages id="common.action" />
    //         </span>
    //         <span className="mobile-lbl-val">{text || 0}</span>
    //       </div>
    //     );
    //   },
    // },
    // {
    //   key: 'sort_deliveries',
    //   title: <IntlMessages id="marketing.deliveries" />,
    //   dataIndex: 'deliveries',
    //   rowKey: 'deliveries',
    //   width: 100,
    //   sorter: true,
    //   className: 'fullname-cell',
    //   render: (text) => {
    //     return (
    //       <div>
    //         <span className="label">
    //           <IntlMessages id="common.action" />
    //         </span>
    //         <span className="mobile-lbl-val">{text || 0}</span>
    //       </div>
    //     );
    //   },
    // },
  ];

  function renderTopBoxes() {
    return (
      <Row style={rowStyle} className="boxRowStyle" gutter={[22, 10]}>
        {marketingBoxes.map((widget, index) => {
          const rawData = [
            widget?.boxObj?.currentWeekCnt > 0
              ? widget?.boxObj?.currentWeekCnt
              : null,
            widget?.boxObj?.lastWeekCnt > 0
              ? widget?.boxObj?.lastWeekCnt
              : null,
          ];
          // const rawData = [10, 25, 0, 80];
          const checkNullVal =
            rawData?.map((value, i) =>
              value !== null ? { i, value } : null,
            ) || [];
          const lineChartArr = checkNullVal?.some((v) => v === null)
            ? []
            : checkNullVal;
          console.log(
            `hello ~ file: index.js:1393 ~ {marketingBoxes.map ~ widget:`,
            widget?.boxObj,
            lineChartArr,
            [(widget?.boxObj?.currentWeekCnt, widget?.boxObj?.lastWeekCnt)],
            checkNullVal,
          );
          return (
            <Col
              lg={12}
              xl={8}
              xxl={6}
              md={12}
              sm={24}
              xs={24}
              style={
                {
                  // marginBottom: 16,
                  // paddingRight: index === 0 || index === 2 ? 7 : 0,
                  // paddingLeft: index === 1 || index === 3 ? 7 : 0,
                }
              }
              className="boxesStyle"
              key={widget.text}
            >
              <IsoWidgetsWrapper className="topMarketingBox">
                <LinkBox link="">
                  <div className="mainBox-div">
                    <div className="leftBoxDiv">
                      <div className="innerLeftDiv">
                        <span className="label">
                          <IntlMessages id={widget.text} />
                        </span>
                        <span className="label-val">
                          {widget?.key === 'totalVal' ? '$' : ''}
                          {widget?.boxObj?.currentWeekCnt || 0}
                          {widget?.key === 'totalClicks' ? '% ' : ''}
                        </span>
                      </div>
                    </div>
                    <div className="rightBoxDiv">
                      <div className="innerRightDiv">
                        <span
                          className="label"
                          style={{
                            color: widget.fontColor,
                          }}
                        >
                          {widget?.boxObj?.cntDifferencePercentage}%
                        </span>
                        {!isEmpty(lineChartArr) && (
                          <div style={{ height: 100, width: 100 }}>
                            <ResponsiveContainer width="100%" height="100%">
                              <LineChart
                                width={500}
                                height={300}
                                data={lineChartArr}
                                margin={{
                                  top: 5,
                                  right: 30,
                                  left: 20,
                                  bottom: 5,
                                }}
                              >
                                {/* <CartesianGrid strokeDasharray="3 3" /> */}
                                {/* <XAxis dataKey="name" />
                    <YAxis /> */}
                                <Tooltip />
                                <defs>
                                  {/* Define a filter for box-shadow */}
                                  <filter
                                    id="shadow"
                                    x="-50%"
                                    y="-50%"
                                    width="200%"
                                    height="200%"
                                  >
                                    <feDropShadow
                                      dx="0"
                                      dy="4"
                                      stdDeviation="2"
                                      floodColor="#21965329"
                                    />
                                  </filter>
                                </defs>
                                {/* <Legend /> */}
                                <Line
                                  type="monotone"
                                  dataKey="value"
                                  strokeWidth={3}
                                  style={{ filter: 'url(#shadow)' }}
                                  stroke={widget.fontColor}
                                  dot={(p) =>
                                    p.index === lineChartArr?.length - 1 ? (
                                      <circle {...p} r={5} />
                                    ) : null
                                  }
                                />
                              </LineChart>
                            </ResponsiveContainer>
                          </div>
                        )}

                        {/* graph */}
                      </div>
                    </div>
                  </div>
                  {/* <StickerWidget
                    number={widget.count || 0}
                    text={<IntlMessages id={widget.text} />}
                    icon={widget.icon}
                    fontColor={widget.fontColor}
                    bgColor={widget.bgColor}
                    image={widget.img}
                  /> */}
                </LinkBox>
              </IsoWidgetsWrapper>
            </Col>
          );
        })}
      </Row>
    );
  }

  function onDateChange(e, type) {
    if (e != null) {
      const date1 = moment(e[0]).format('YYYY-MM-DD');
      const date2 = moment(e[1]).format('YYYY-MM-DD');
      setStEndDate({ stDate: date1, endDate: date2 });
      console.log('date range---------------------', date1, date2);
    } else {
      setStEndDate({ stDate: '', endDate: '' });
    }
  }

  function renderSearchBar() {
    return (
      <div style={{ padding: '24px 15px' }}>
        <Row>
          <Col xxl={19} xl={17} lg={14} sm={12} md={24} xs={24}>
            <Input
              onChange={(e) => {
                const val = e && e.target ? e.target.value : '';

                setFilterByName(val);
              }}
              className="searchInputCampaign"
              value={filter.campaign_name}
              allowClear
              prefix={<img src={searchBlackImg} alt="search" />}
              placeholder={messageArray['marketing.campaignName']}
            />
          </Col>
          <Col
            xxl={5}
            xl={7}
            lg={10}
            sm={12}
            md={24}
            xs={24}
            className="downLoad-filter"
            style={{
              justifyContent: 'flex-end',
              gap: '16px',
            }}
          >
            <Button
              className="downLoadBtn"
              style={{
                width: '113px',
              }}
              onClick={() => setFilterMode(!filterMode)}
            >
              <img src={filterImg} alt="Filters" />
              <IntlMessages id="common.filter" />
            </Button>

            <Button className="downLoadBtn" onClick={() => downLoadPdfData()}>
              <img src={downloadFile} alt="download" />
              <IntlMessages id="common.download" />
            </Button>
          </Col>
          {filterMode && (
            <Col xs={24} className="caimpaignfilterSelection">
              <div className="caimpaignfilterSelection-div">
                <RangePicker
                  allowclear
                  format="YYYY-MMM-DD"
                  // disabledDate={disabledDate}
                  onChange={(val) => {
                    onDateChange(val);
                  }}
                  className="campaigndatePicker"
                  // suffixIcon={false}
                  suffixIcon={<img src={calendarFilterIcon} alt="calender" />}
                  // value={dateRange !== '' ? dateRange : ''}
                  // defaultValue={[fromDate, toDate]}
                />
                <Select
                  style={{ width: '100%' }}
                  onChange={(val) => {
                    setFilterByType(val);
                  }}
                  placeholder={messageArray['marketing.type']}
                  allowClear
                  className="caimpaignfilterSelection-select"
                >
                  {items?.map((item) => (
                    <Select.Option key={item?.key} value={item?.key}>
                      {item?.label}
                    </Select.Option>
                  ))}
                </Select>
                <Select
                  style={{ width: '100%' }}
                  onChange={(val) => {
                    setFilterByStatus(val);
                  }}
                  className="caimpaignfilterSelection-select"
                  placeholder="Status"
                  allowClear
                >
                  {statusArray?.map((item) => (
                    <Select.Option key={item?.key} value={item?.key}>
                      {item?.label}
                    </Select.Option>
                  ))}
                </Select>
              </div>
            </Col>
          )}
        </Row>
      </div>
    );
  }

  const addNew = () => {
    return (
      <MarketingWrapper>
        <div className="iconStyle marketingMainDiv">
          <span>
            <p
              className="addNewUser"
              style={{
                color: theme.colors.primaryColor,
                alignItems: 'center',
                // display:'flex'
              }}
            >
              <SwapOutlined
                className="filterIcon1"
                style={{ color: sort ? theme.colors.primaryColor : '#000' }}
                onClick={() => {
                  setSort(!sort);
                }}
              />
              <FilterOutlined
                className="filterIcon"
                style={{
                  color: filterMode ? theme.colors.primaryColor : '#000',
                }}
                onClick={() => {
                  setFilterMode((pre) => !pre);
                  setFilterByName('');
                  setFilterByType('');
                  setFilterByStatus('');
                }}
              />
            </p>
          </span>
        </div>
      </MarketingWrapper>
    );
  };

  const customerOverviewContent = (
    <ActionWrapper>
      <div className="popMainDiv">
        <div
          className="isoDropdownLink"
          onClick={() => {
            setOverView(<IntlMessages id="dashboard.messagesent" />);
            setOverViewVisibility(!overviewVisible);
            setOverViewType('message');
          }}
          role="button"
          onKeyPress={() => {
            setOverView(<IntlMessages id="dashboard.messagesent" />);
            setOverViewVisibility(!overviewVisible);
            setOverViewType('message');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.messagesent" />
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setOverView(<IntlMessages id="dashboard.totalclick" />);
            setOverViewVisibility(!overviewVisible);
            setOverViewType('click');
          }}
          role="button"
          onKeyPress={() => {
            setOverView(<IntlMessages id="dashboard.totalclick" />);
            setOverViewVisibility(!overviewVisible);
            setOverViewType('click');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.totalclick" />
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setOverView(<IntlMessages id="dashboard.totalshares" />);
            setOverViewVisibility(!overviewVisible);
            setOverViewType('share');
          }}
          role="button"
          onKeyPress={() => {
            setOverView(<IntlMessages id="dashboard.totalshares" />);
            setOverViewVisibility(!overviewVisible);
            setOverViewType('share');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.totalshares" />
        </div>
      </div>
    </ActionWrapper>
  );

  const customerDurationContent = (
    <ActionWrapper>
      <div className="popMainDiv">
        <div
          className="isoDropdownLink"
          onClick={() => {
            setDuration(<IntlMessages id="dashboard.thisweek" />);
            setDurationVisibility(!durationVisible);
            setPeriodType('this_week');
          }}
          role="button"
          onKeyPress={() => {
            setDuration(<IntlMessages id="dashboard.thisweek" />);
            setDurationVisibility(!durationVisible);
            setPeriodType('this_week');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.thisweek" />
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setDuration(<IntlMessages id="dashboard.lastweek" />);
            setDurationVisibility(!durationVisible);
            setPeriodType('week');
          }}
          role="button"
          onKeyPress={() => {
            setDuration(<IntlMessages id="dashboard.lastweek" />);
            setDurationVisibility(!durationVisible);
            setPeriodType('week');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.lastweek" />
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setDuration(<IntlMessages id="dashboard.lastMonth" />);
            setDurationVisibility(!durationVisible);
            setPeriodType('month');
          }}
          role="button"
          onKeyPress={() => {
            setDuration(<IntlMessages id="dashboard.lastMonth" />);
            setDurationVisibility(!durationVisible);
            setPeriodType('month');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.lastMonth" />
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setDuration(<IntlMessages id="dashboard.lastSixMonth" />);
            setDurationVisibility(!durationVisible);
            setPeriodType('six_month');
          }}
          role="button"
          onKeyPress={() => {
            setDuration(<IntlMessages id="dashboard.lastSixMonth" />);
            setDurationVisibility(!durationVisible);
            setPeriodType('six_month');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.lastSixMonth" />
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setDuration(<IntlMessages id="dashboard.thisyear" />);
            setDurationVisibility(!durationVisible);
            setPeriodType('this_year');
          }}
          role="button"
          onKeyPress={() => {
            setDuration(<IntlMessages id="dashboard.thisyear" />);
            setDurationVisibility(!durationVisible);
            setPeriodType('this_year');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.thisyear" />
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setDuration(<IntlMessages id="dashboard.lastYear" />);
            setDurationVisibility(!durationVisible);
            setPeriodType('year');
          }}
          role="button"
          onKeyPress={() => {
            setDuration(<IntlMessages id="dashboard.lastYear" />);
            setDurationVisibility(!durationVisible);
            setPeriodType('year');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.lastYear" />
        </div>
      </div>
    </ActionWrapper>
  );

  // this function for handle pagination
  /** this function for handle pagination and sort
   * @function onChange
   * @param {object} data page, sort
   */
  function onChange(pagination, fltr, sorter) {
    const pager = filter && filter.pagination ? { ...filter.pagination } : {};
    const sorts = filter && filter.filters ? { ...filter.filters } : {};
    pager.current = pagination.current;
    setFilter((f) => ({ ...f, pagination: pager, filters: sorts }));
    getMarketingList({
      page: pagination.current,
      sort: sorter?.order
        ? {
            field: sorter?.field,
            order: sorter?.order,
          }
        : null,
      ...filter.filters,
    });
    setSort(false);
  }

  const config = {
    data: !isEmpty(campaignData) ? campaignData.sentMessageChart : [],
    xField: 'day',
    yField: 'delivered', // need to change deliverd

    label: {
      text: (d) => `${(d.frequency * 100).toFixed(1)}%`,
      textBaseline: 'bottom',
    },
    axis: {
      y: {
        labelFormatter: '.0%',
      },
    },
    style: {
      radiusTopLeft: 50,
      borderRadius: 50,
      height: 190,
      radiusTopRight: 50,
    },
    // meta: {
    //   day: { alias: 'day' },
    //   value: { alias: ' ' },
    // },
  };

  return (
    <>
      {!dasboardVisible ? (
        <>
          <MarketingWrapper>
            <Row gutter={24}>
              <Col lg={24} sm={24} md={24} xs={24}>
                <div className="brandDiv">
                  {uData.user_type === 'chillbaby_admin' ||
                  uData.user_type === 'chillbaby_user' ? (
                    <Select
                      className="brandDropdown"
                      style={{
                        width: dWidth <= 1000 ? '200px' : '200px',
                      }}
                      placeholder={<IntlMessages id="dropdown.product" />}
                      allowClear
                      onChange={(val) => setSelectedProduct(val)}
                    >
                      {isArray(brandLists) && brandLists.length > 0
                        ? brandLists.map((bl) => {
                            return (
                              <Select.Option value={bl.id}>
                                {bl.full_name}
                              </Select.Option>
                            );
                          })
                        : null}
                    </Select>
                  ) : null}
                </div>
              </Col>
            </Row>
            <Row gutter={[40, 21]}>
              <Col xs={24} className="pageTitle">
                <PageTitleHeader
                  title={<IntlMessages id="title.campaignMarketing" />}
                />
              </Col>
              <Col
                xs={24}
                style={{
                  paddingRight: '0px',
                }}
              >
                {renderTopBoxes()}
              </Col>
              <Col lg={11} sm={24} md={11} xs={24}>
                <div
                  className="overViewCol"
                  style={{
                    background: `url(${overViewBackImg}) no-repeat center center`,
                    height: '100%',
                    backgroundSize: 'cover',
                  }}
                >
                  <div className="overViewContainer">
                    {/* <h2 className="overViewText">
                    <IntlMessages id="overviewTitle" />
                  </h2> */}
                    <ul style={{ listStyle: 'initial' }}>
                      <li className="liText">
                        <IntlMessages id="overview1" />
                      </li>
                      <li className="liText">
                        <IntlMessages id="overview2Detail" />
                      </li>
                      <li className="liText">
                        <IntlMessages id="overview3" />
                      </li>
                      <li className="liText">
                        <IntlMessages id="overview3Detail" />
                      </li>
                      <li className="liText">
                        <IntlMessages id="overview4" />
                      </li>
                      <li className="liText">
                        <IntlMessages id="overview4Detail" />
                      </li>
                    </ul>
                  </div>
                </div>
              </Col>
              <Col
                lg={13}
                md={13}
                sm={24}
                xs={24}
                style={{
                  maxHeight: '350px',
                }}
              >
                <div className="chartCol">
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginBottom: 16,
                      position: 'relative',
                    }}
                  >
                    <Popover
                      content={customerOverviewContent}
                      trigger="click"
                      visible={overviewVisible}
                      onVisibleChange={messagesSent}
                      placement="bottomLeft"
                    >
                      <h4
                        style={{
                          cursor: 'pointer',
                          fontWeight: '400',
                          fontSize: window.innerWidth <= 400 ? '12px' : '15px',
                        }}
                      >
                        {overView}
                        {!overviewVisible ? (
                          <img
                            src={blackblackdownArrow}
                            alt="collapse arrow"
                            style={{ marginLeft: '5px' }}
                          />
                        ) : (
                          <img
                            src={expandIcon}
                            alt="expand arrow"
                            style={{
                              marginLeft: '5px',
                              width: '12px',
                              height: '7px',
                            }}
                          />
                        )}
                      </h4>
                    </Popover>
                    <Popover
                      content={customerDurationContent}
                      trigger="click"
                      visible={durationVisible}
                      onVisibleChange={durationChange}
                      placement="bottomRight"
                    >
                      <p
                        style={{
                          cursor: 'pointer',
                          fontSize: window.innerWidth <= 400 ? '12px' : '15px',
                        }}
                      >
                        {duration}
                        {!durationVisible ? (
                          <img
                            src={blackblackdownArrow}
                            alt="collapse arrow"
                            style={{ marginLeft: '5px' }}
                          />
                        ) : (
                          <img
                            src={expandIcon}
                            alt="expand arrow"
                            style={{
                              marginLeft: '5px',
                              width: '12px',
                              height: '7px',
                            }}
                          />
                        )}
                      </p>
                    </Popover>
                  </div>

                  <ResponsiveContainer width="100%" height={250}>
                    <BarChart
                      data={campaignData?.sentMessageChart?.map((item) => ({
                        ...item,
                        value: item?.delivered, // Mapping `delivered` to `value`
                      }))}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                      style={{
                        fontSize: 12,
                        fontWeight: 400,
                      }}
                    >
                      {/* <CartesianGrid strokeDasharray="3 3" /> */}
                      <XAxis
                        dataKey="day"
                        tick={{
                          fontFamily: 'Inter',
                          fontSize: 8,
                          fontWeight: 700,
                          lineHeight: '12px',
                          textAlign: 'left',
                          textUnderlinePosition: 'from-font',
                          textDecorationSkipInk: 'none',
                          color: '#161A29',
                        }}
                      />
                      <YAxis
                        tick={{
                          fontFamily: 'Inter',
                          fontSize: 8,
                          fontWeight: 700,
                          lineHeight: '12px',
                          textAlign: 'left',
                          textUnderlinePosition: 'from-font',
                          textDecorationSkipInk: 'none',
                          color: '#161A29',
                        }}
                        interval={0} // Ensures all ticks are displayed
                      />
                      <Tooltip />
                      {/* <Legend content={<CustomLegend />} /> */}
                      <Bar
                        dataKey="value"
                        displayName=""
                        fill="#161A29"
                        borderRadius={50}
                        radius={[20, 20, 0, 0]}
                        barSize={5}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </Col>
              <Col xs={24}>
                <Row className="isCampaignTabRow">
                  {/* <Col className="isoTab" xl={8} lg={8} sm={24} md={12} xs={24}></Col> */}
                  <Col
                    xl={showCreateDrop ? 20 : 24}
                    lg={showCreateDrop ? 20 : 24}
                    sm={showCreateDrop ? 20 : 24}
                    md={showCreateDrop ? 20 : 24}
                    xs={24}
                    className="tab-col"
                  >
                    <Tabs
                      defaultActiveKey=""
                      renderTabBar={renderTabBar}
                      style={{ paddingLeft: 16 }}
                      onChange={(key) => setFilterTab(key)}
                      className="campaignTab"
                    >
                      <TabPane
                        tab={<IntlMessages id="tab.allCampaign" />}
                        key="All"
                      />
                      <TabPane
                        tab={<IntlMessages id="tab.delivered" />}
                        key="Delivered"
                      />
                      <TabPane
                        tab={<IntlMessages id="tab.live" />}
                        key="Live"
                      />
                      <TabPane
                        tab={<IntlMessages id="tab.expired" />}
                        key="Expired"
                      />
                      <TabPane
                        tab={<IntlMessages id="tab.archived" />}
                        key="Archive"
                      />
                    </Tabs>
                  </Col>

                  {uData.user_type === 'brand_admin' ||
                  uData.user_type === 'marketing_manager' ||
                  uData.user_type === 'manager' ? (
                    <Col
                      xs={4}
                      style={{
                        display: 'flex',
                        justifyContent: 'flex-end',
                        borderBottom: '1px solid #EEEFF2',
                        padding: '0px 15px',
                      }}
                    >
                      <Dropdown
                        overlayStyle={{
                          backgroundColor: '#09295D',
                          color: '#fff',
                        }}
                        overlay={
                          <div
                            style={{
                              border: '1px solid #BBCCDE66',
                              boxShadow: '0.2px 2px 7px 0px #0000000F',
                              borderRadius: 8,
                              backgroundColor: '#09295D',
                              padding: '7px 6px',
                            }}
                          >
                            {items?.map((item) => (
                              <div
                                style={{
                                  padding: '8px',
                                  cursor: 'pointer',
                                }}
                              >
                                <div
                                  onClick={() => {
                                    setSelectedTab(item?.key);
                                    setState(() => ({
                                      ...defaultData,
                                      drawerVisible: true,
                                      drawerType: 'add',
                                      initObj: {},
                                      btnCnt: [],
                                    }));
                                    console.log(`Click on item`);
                                    localStorage.setItem(
                                      'audienceType',
                                      item?.key,
                                    );
                                    history.push({
                                      pathname: '/dashboard/marketing/audience',
                                    });

                                    // setOpenCreateDropdown(false);
                                  }}
                                >
                                  <span
                                    style={{
                                      fontFamily: 'Inter',
                                      fontSize: 13,
                                      fontWeight: 400,
                                      lineHeight: '16px',
                                      textAlign: 'left',
                                      textUnderlinePosition: 'from-font',
                                      textDecorationSkipInk: 'none',
                                      color: '#FDFDFD',
                                    }}
                                  >
                                    {item?.label}
                                  </span>
                                </div>
                              </div>
                            ))}
                          </div>
                        }
                        onOpenChange={() =>
                          setOpenCreateDropdown(!openCreateDropdown)
                        }
                        // open={openCreateDropdown}
                        trigger={['click']}
                        style={{
                          borderRadius: '8px !important',
                        }}
                        // menu={{ items, onClick }}
                      >
                        <div>
                          <Button
                            type="primary"
                            style={{
                              backgroundColor: '#02204E',
                              display: 'flex',
                              alignItems: 'center',
                              border: 'none',
                              height: '40px',
                              justifyContent: 'center',
                              width: '155px',
                              gap: '14px',
                              borderRadius: '8px',
                            }}
                          >
                            {/* <IntlMessages id="marketing.createNew" /> */}
                            <span
                              style={{
                                fontFamily: 'Inter',
                                fontSize: '14px',
                                fontWeight: 600,
                                lineHeight: '16.94px',
                                textAlign: 'left',
                                textUnderlinePosition: 'from-font',
                                textDecorationSkipInk: 'none',
                                color: '#FFFFFF',
                              }}
                            >
                              <IntlMessages id="marketing.createNew" />
                            </span>
                            {openCreateDropdown ? (
                              <img
                                src={minusIcon}
                                style={{ marginLeft: '5px' }}
                                color="#fff"
                                size={18}
                              />
                            ) : (
                              <img
                                src={plusIcon}
                                style={{ marginLeft: '5px' }}
                                color="#fff"
                                size={18}
                              />
                            )}
                          </Button>
                        </div>
                      </Dropdown>
                    </Col>
                  ) : null}
                  {/* <Col xl={2} lg={2} md={2} xs={24} className="isoAddNew">
                    {addNew()}
                  </Col> */}
                  <Col xs={24}>{renderSearchBar()}</Col>
                  <Col xs={24} style={{ padding: '0px 15px' }}>
                    <TableWrapper
                      scroll={{ x: 1400 }}
                      loading={marketingState.loader}
                      rowKey={(record) => record.id}
                      dataSource={marketingState.marketing}
                      // dataSource={isArray(markettingList) ? markettingList : []}
                      columns={columns}
                      onChange={onChange}
                      className="campaignTable"
                      pagination={filter.pagination || {}}
                      showSorterTooltip={false}
                    />
                  </Col>
                </Row>
              </Col>
            </Row>
            {/* <div style={{ padding: '0 20px 0 20px' }}>
              <MarketingCreateNew />
            </div> */}
          </MarketingWrapper>
          <MarketingDrawer
            visible={drawerVisible}
            state={state}
            setState={setState}
            drawerType={drawerType}
            propIntl={props}
            initVal={initObj}
            segmentList={segmentList}
            setSegmentList={setSegmentList}
            categoryList={categoryList}
            setCategoryList={setCategoryList}
            setButtonLoader={setButtonLoader}
            buttonLoader={buttonLoader}
            edit={edit}
            selectedTab={selectedTab}
          />
          {visible && (
            <CustomerDrawer
              initialValues={initObj}
              // view={view}
              countries={countries}
              visible={visible}
              setState={setState}
              state={state}
              languages={languages}
              drawerType={drawerType}
            />
          )}
        </>
      ) : (
        <MarketingDashboard
          visible={dasboardVisible}
          setDashboardVisible={setDasboardVisible}
          initialValues={initObj}
        />
      )}
    </>
  );
}

export default injectIntl(Marketing);

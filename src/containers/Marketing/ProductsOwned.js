import React, { useState } from 'react';
import image1 from '@chill/assets/images/cute-baby-boy-scooter-autumnal-park.jpg';
import image2 from '@chill/assets/images/full-shot-smiley-girl-with-tricycle.jpg';
import image3 from '@chill/assets/images/funny-kid-rain-boots-playing-rain-park.jpg';
import image4 from '@chill/assets/images/image4.jpg';
import image5 from '@chill/assets/images/image5.jpg';
import { Checkbox, Image, Tabs } from 'antd';

const { TabPane } = Tabs;

function ProductsOwned() {
  const [isCheckedInclude, setIsCheckedInclude] = useState([]);
  const [isCheckedExclude, setIsCheckedExclude] = useState([]);

  const images = [
    { id: 1, src: image1 },
    { id: 2, src: image2 },
    { id: 3, src: image3 },
    { id: 4, src: image4 },
    { id: 5, src: image5 },
  ];

  const handleIncludeImageClick = (id) => {
    setIsCheckedInclude((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id],
    );
    setIsCheckedExclude((prev) => prev.filter((item) => item !== id));
  };

  const handleExcludeImageClick = (id) => {
    setIsCheckedExclude((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id],
    );
    setIsCheckedInclude((prev) => prev.filter((item) => item !== id));
  };

  const handleIncludeCheckboxClick = (id) => {
    setIsCheckedInclude((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id],
    );
    setIsCheckedExclude((prev) => prev.filter((item) => item !== id));
  };

  const handleExcludeCheckboxClick = (id) => {
    setIsCheckedExclude((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id],
    );
    setIsCheckedInclude((prev) => prev.filter((item) => item !== id));
  };

  const includeTabImages = images.filter(
    (image) => !isCheckedExclude.includes(image.id),
  );

  const excludeTabImages = images.filter(
    (image) => !isCheckedInclude.includes(image.id),
  );

  return (
    <Tabs defaultActiveKey="1">
      <TabPane tab="Include" key="1">
        <div
          style={{
            display: 'flex',

            marginTop: 10,
            gap: 10,
            overflowX: 'scroll',
          }}
        >
          {includeTabImages.map((image) => (
            <div style={{ position: 'relative', display: 'inline-block' }}>
              <Image
                style={{ width: '23vh', height: '23vh', borderRadius: 6 }}
                src={image.src}
                preview={false}
                onClick={() => handleIncludeImageClick(image.id)}
              />
              <Checkbox
                checked={isCheckedInclude.includes(image.id)}
                style={{ position: 'absolute', top: 4, right: 6, zIndex: 1 }}
                onClick={() => handleIncludeCheckboxClick(image.id)}
              />
            </div>
          ))}
        </div>
      </TabPane>
      <TabPane tab="Exclude" key="2">
        <div
          style={{
            display: 'flex',

            marginTop: 10,
            gap: 10,
            overflowX: 'scroll',
          }}
        >
          {excludeTabImages.map((image) => (
            <div style={{ position: 'relative', display: 'inline-block' }}>
              <Image
                style={{ width: '23vh', height: '23vh', borderRadius: 6 }}
                src={image.src}
                preview={false}
                onClick={() => handleExcludeImageClick(image.id)}
              />
              <Checkbox
                checked={isCheckedExclude.includes(image.id)}
                style={{ position: 'absolute', top: 4, right: 6, zIndex: 1 }}
                onClick={() => handleExcludeCheckboxClick(image.id)}
              />
            </div>
          ))}
        </div>
      </TabPane>
    </Tabs>
  );
}
export default ProductsOwned;

/* eslint-disable react/jsx-no-bind */
/* eslint-disable no-shadow */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/jsx-props-no-spreading */
import React from 'react';
import { <PERSON><PERSON>, Col, Drawer, Row, Collapse, Tabs } from 'antd';
import IntlMessages from '@chill/components/utility/intlMessages';
import Form from '@chill/components/uielements/form';
import { BottomViewWrapper } from '@chill/assets/styles/drawerFormStyles';
import { PlusOutlined } from '@ant-design/icons';
import Checkbox from 'antd/lib/checkbox/Checkbox';
import { TabPane } from '@chill/components/uielements/tabs';
import MarketingWrapper from './Marketing.Styles';
import { styles } from '../Widgets/config';
import RenderData from './RenderTab';

function MarketingDrawer(props) {
  const {
    visible,
    state,
    setState = () => {},
    drawerType,
    expandIconPosition = 'left',
    propIntl,
    initVal,
    segmentList,
    categoryList,
    setCategoryList,
    buttonLoader,
    setButtonLoader = () => {},
    selectedTab,
  } = props;
  const genExtra = () => (
    <PlusOutlined
      onClick={(event) => {
        // If you don't want click extra trigger collapse, you can prevent this:
        event.stopPropagation();
      }}
    />
  );

  const renderTabBar = (props, DefaultTabBar) => (
    <DefaultTabBar {...props} className="site-custom-tab-bar" />
  );
  const { Panel } = Collapse;
  const onClose = () => {
    setState((p) => ({ ...p, drawerVisible: false, type: 'feed_post' }));
  };
  function onChange(e) {
    console.log(`checked = ${e.target.check}`);
  }
  const text = `
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
`;
  function callback(key) {
    console.log(key);
  }

  const renderdata = (type) => {
    return (
      <RenderData
        type={type}
        state={state}
        setState={setState}
        propIntl={propIntl}
        initVal={initVal}
        segmentList={segmentList}
        categoryList={categoryList}
        buttonLoader={buttonLoader}
        setCategoryList={setCategoryList}
        setButtonLoader={setButtonLoader}
      />
    );
  };
  const [form] = Form.useForm();
  // id={
  //   drawerType === "add"
  //     ? "marketing.createNew"
  //     : drawerType === "category"
  //     ? "marketing.createNew"
  //     : drawerType === "duplicate"
  //     ? "action.duplicatecampaign"
  //     : "common.filters"
  // }
  return (
    <div>
      <Drawer
        title="Create Campaign"
        width={
          drawerType === 'add' ||
          drawerType === 'category' ||
          drawerType === 'duplicate' ||
          drawerType === 'category-delete'
            ? 1000
            : 400
        }
        style={{ height: window.innerHeight - 40, marginTop: 70 }}
        onClose={onClose}
        visible={visible}
        bodyStyle={{ paddingBottom: 0 }}
        destroyOnClose
        form={form}
      >
        <MarketingWrapper>
          {drawerType === 'add' ||
          drawerType === 'category' ||
          drawerType === 'category-delete' ||
          drawerType === 'duplicate' ? (
            <>
              <Tabs
                defaultActiveKey={
                  drawerType === 'duplicate' && initVal?.campaign_type
                    ? initVal?.campaign_type
                    : selectedTab || ''
                }
                renderTabBar={renderTabBar}
                className="isoTabs"
                onChange={(activeKey) => {
                  setState((p) => ({
                    ...p,
                    type: activeKey,
                  }));
                }}
              >
                <TabPane
                  tab={<IntlMessages id="tab.feedpost" />}
                  key="feed_post"
                  disabled={
                    drawerType === 'duplicate' &&
                    initVal?.campaign_type !== 'feed_post'
                  }
                >
                  {renderdata('feed')}
                </TabPane>
                <TabPane
                  tab={<IntlMessages id="tab.inappmessage" />}
                  key="in_app_message"
                  disabled={
                    drawerType === 'duplicate' &&
                    initVal?.campaign_type !== 'in_app_message'
                  }
                >
                  {renderdata('inApp')}
                </TabPane>
                <TabPane
                  tab={<IntlMessages id="tab.pushmessage" />}
                  key="push_message"
                  disabled={
                    drawerType === 'duplicate' &&
                    initVal?.campaign_type !== 'push_message'
                  }
                >
                  {renderdata('push')}
                </TabPane>
              </Tabs>
            </>
          ) : (
            <>
              <Collapse
                defaultActiveKey={['1']}
                onChange={callback}
                bordered={false}
                expandIconPosition={expandIconPosition}
              >
                <Panel
                  showArrow={false}
                  header="STATUS"
                  key="1"
                  extra={genExtra()}
                  style={{ ...styles.panelStyle }}
                >
                  <div
                    style={{ justifyContent: 'space-between' }}
                    className="checkboxStyle"
                  >
                    <Checkbox onChange={onChange}>
                      <span style={{ fontWeight: '400' }}>
                        <IntlMessages id="common.active" />
                      </span>
                    </Checkbox>

                    <span style={{ fontWeight: '400' }}>800</span>
                  </div>
                  <div style={{ marginTop: '10px' }} className="checkboxStyle">
                    <Checkbox onChange={onChange}>
                      <span style={{ fontWeight: '400' }}>
                        <IntlMessages id="common.inActive" />
                      </span>
                    </Checkbox>
                    <span style={{ fontWeight: '400' }}>1200</span>
                  </div>
                </Panel>
                <Panel
                  showArrow={false}
                  header="COUNTRY"
                  key="2"
                  extra={genExtra()}
                  style={{ ...styles.panelStyle }}
                >
                  <div>{text}</div>
                </Panel>
                <Panel
                  showArrow={false}
                  header="LANGUAGE"
                  key="3"
                  extra={genExtra()}
                  style={{ ...styles.panelStyle }}
                >
                  <div>{text}</div>
                </Panel>
                <Panel
                  showArrow={false}
                  header="USER TAGS"
                  key="4"
                  extra={genExtra()}
                  style={{ ...styles.panelStyle }}
                >
                  <div>{text}</div>
                </Panel>
                <Panel
                  showArrow={false}
                  header="PRODUCTS"
                  key="5"
                  extra={genExtra()}
                  style={{ ...styles.panelStyle }}
                >
                  <div>{text}</div>
                </Panel>
              </Collapse>
              <br />
              <BottomViewWrapper className="bottomBtnWrapper">
                <Row gutter={24}>
                  <Col lg={12}>
                    <Form.Item>
                      <Button type="primary" htmlType="submit">
                        <IntlMessages id="common.submit" />
                      </Button>
                    </Form.Item>
                  </Col>
                  <Col lg={12}>
                    <Form.Item>
                      <Button
                        // disabled={submitLoader}
                        onClick={onClose}
                        style={{ marginRight: 8 }}
                      >
                        <IntlMessages id="common.cancel" />
                      </Button>
                    </Form.Item>
                  </Col>
                </Row>
              </BottomViewWrapper>
            </>
          )}
        </MarketingWrapper>
      </Drawer>
    </div>
  );
}

export default MarketingDrawer;

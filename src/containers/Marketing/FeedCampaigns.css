/* FeedCampaigns Responsive CSS */

/* Base responsive grid container */
.responsiveGrid {
  display: flex;
  width: 100%;
  min-height: 100vh;
  transition: all 0.3s ease;
}

/* Widget spacing for consistent padding */
.widgetSpacing {
  padding: 16px;
  margin: 8px 0;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Chart container for responsive content areas */
.chartContainer {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Header controls for responsive navigation */
.headerControls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  flex-wrap: wrap;
  gap: 12px;
}

/* Touch-friendly targets */
.touch-target {
  min-width: 44px !important;
  min-height: 44px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.touch-target:hover {
  transform: scale(1.05);
  opacity: 0.8;
}

.touch-target:active {
  transform: scale(0.95);
}

/* Responsive card grid */
.responsive-card-grid {
  display: grid;
  gap: 16px;
  width: 100%;
  padding: 0 16px;
}

/* Mobile styles (320px - 768px) */
@media (max-width: 767px) {
  .responsiveGrid {
    flex-direction: column;
    padding: 0;
  }

  .widgetSpacing {
    margin: 8px 16px;
    padding: 16px;
    border-radius: 12px;
  }

  .chartContainer {
    padding: 20px 16px;
    margin: 0 16px;
    border-radius: 12px;
  }

  .headerControls {
    padding: 16px;
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    border-radius: 0;
  }

  .responsive-card-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 0;
  }

  /* Mobile filter overlay */
  .filter-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 1000 !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
  }

  .filter-content {
    width: 85% !important;
    height: 100vh !important;
    margin-left: auto !important;
    background: #fff !important;
    padding: 24px !important;
    overflow-y: auto !important;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3) !important;
  }

  /* Mobile typography */
  h1, h2, h3, h4, h5, h6 {
    font-size: 1.1em;
    line-height: 1.4;
    margin-bottom: 12px;
  }

  /* Mobile buttons and inputs */
  .ant-btn {
    min-height: 44px !important;
    padding: 8px 16px !important;
    font-size: 16px !important;
  }

  .ant-input {
    min-height: 44px !important;
    font-size: 16px !important;
    padding: 8px 12px !important;
  }

  .ant-select {
    min-height: 44px !important;
  }

  .ant-select-selector {
    min-height: 44px !important;
    padding: 8px 12px !important;
  }
}

/* Tablet styles (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .responsiveGrid {
    flex-direction: row;
    gap: 16px;
  }

  .widgetSpacing {
    margin: 12px;
    padding: 20px;
  }

  .chartContainer {
    padding: 24px;
    margin: 0 12px;
  }

  .headerControls {
    padding: 16px 24px;
    flex-direction: row;
    gap: 16px;
  }

  .responsive-card-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    padding: 0 12px;
  }

  /* Tablet typography */
  h1, h2, h3, h4, h5, h6 {
    font-size: 1.05em;
    line-height: 1.3;
  }
}

/* Desktop styles (1024px+) */
@media (min-width: 1024px) {
  .responsiveGrid {
    flex-direction: row;
    gap: 20px;
  }

  .widgetSpacing {
    margin: 16px;
    padding: 24px;
  }

  .chartContainer {
    padding: 32px;
    margin: 0 16px;
  }

  .headerControls {
    padding: 20px 32px;
    flex-direction: row;
    gap: 20px;
  }

  .responsive-card-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    padding: 0 16px;
  }

  /* Desktop hover effects */
  .ant-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }

  .touch-target:hover {
    transform: scale(1.1);
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .touch-target {
    min-width: 48px !important;
    min-height: 48px !important;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for keyboard navigation */
.touch-target:focus,
.ant-btn:focus,
.ant-input:focus,
.ant-select:focus {
  outline: 2px solid #2A77F4 !important;
  outline-offset: 2px !important;
}

/* Print styles */
@media print {
  .filter-overlay,
  .headerControls {
    display: none !important;
  }
  
  .responsiveGrid {
    flex-direction: column !important;
  }
  
  .responsive-card-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

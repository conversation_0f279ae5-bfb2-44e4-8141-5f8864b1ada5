/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-no-bind */
/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable react/jsx-props-no-spreading */
import React, { useEffect, useState } from 'react';
import { Drawer, Form, Input, Row, Select } from 'antd';
import { useSelector } from 'react-redux';
import IntlMessages from '@chill/components/utility/intlMessages';
import { BottomViewWrapper } from '@chill/assets/styles/drawerFormStyles';
import getApiData from '@chill/lib/helpers/apiHelper';
import Button from '@chill/components/uielements/CButton';
import Notification from '@chill/components/Notification';
import CountryPicker from '@chill/components/uielements/countryPicker';
import useResetFormOnClose from '@chill/lib/hooks/useResetForm';
import { isArray } from 'lodash';
import BrandStyle from './BrandIntegration.styles';

export default function BrandIntegrationForm(props) {
  const {
    visible,
    setState = () => {},
    updateData,
    initialValues,
    edit,
    setEdit = () => null,
  } = props;
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);
  const initVal = initialValues || {};
  const [btnLoader, setBtnLoader] = useState(false);
  const [form] = Form.useForm();
  const { Option } = Select;
  const [seviceTypeList, setServiceTypeList] = useState([
    { id: 1, name: 'Customer-Service' },
    { id: 2, name: 'E-commerce' },
  ]);
  const [integrationList, setIntegrationTypeList] = useState([]);
  const [brandState, setBrandState] = React.useState({
    brands: [],
    brandLoad: false,
  });

  const [isShopify, setShopify] = useState(false);

  // set form fields if not edit reset fields
  useEffect(() => {
    if (!edit) {
      form.resetFields();
      setShopify(false);
    } else if (initialValues.integration_type === 'shopify') {
      setShopify(true);
    }
  }, [visible]);

  // this function for get brand list
  async function getBrandList() {
    try {
      const response = await getApiData('getBrandListDropdown');

      if (response.success) {
        console.log('======getBrandListDropdown', response.data);
        if (response.data) {
          const { data } = response;
          data.splice(0, 1);
          setBrandState((preState) => ({
            ...preState,
            brands: isArray(data) ? data : [],
            brandLoad: false,
          }));
        }
      } else {
        setBrandState((preState) => ({
          ...preState,
          brandLoad: false,
        }));
      }
    } catch (error) {
      console.log('error ===', error);
      setBrandState((preState) => ({
        ...preState,
        brandLoad: false,
      }));
    }
  }

  React.useEffect(() => {
    getBrandList();
  }, []);
  useResetFormOnClose({ visible, initVal, form });

  const onClose = (type = '', data = {}) => {
    form.resetFields();
    setState((p) => ({ ...p, visible: false, view: false }));
    setEdit(false);
    if (type === 'success') {
      updateData(data);
    } else {
      setState((p) => ({ ...p, initObj: {} }));
    }
  };

  async function addBrand(values) {
    const url = edit ? 'integrations/update' : 'integrations/add-integrations';
    try {
      const response = await getApiData(url, values, 'POST');
      if (response.success) {
        Notification('success', response.message);
        onClose('success', response.data);
      } else {
        Notification('error', response.message);
      }
      setBtnLoader(false);
    } catch (error) {
      console.log(error);
      setBtnLoader(false);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  function validate(values) {
    const obj = values;
    // obj.type = 'brand';
    if (edit) obj.id = initVal.id;
    setBtnLoader(true);
    addBrand(obj);
  }

  function onChangeServiceSelect(value) {
    console.log(`selected ${value}`);
    setState((p) => ({ ...p, sendTo: value }));
    console.log('service List v ==>', value);
    form.setFieldsValue({ integration_type: undefined });
    if (value === 'Customer-Service') {
      setShopify(false);
      setIntegrationTypeList([{ id: 2, name: 'shopify chat' }]);
    } else {
      // setShopify(true);
      setIntegrationTypeList([{ id: 1, name: 'shopify' }]);
    }
  }
  function onChangeIntegrationSelect(value) {
    console.log(`selected ------------- ${value}`);
    if (value === 'shopify') {
      setShopify(true);
    } else {
      setShopify(false);
    }
    setState((p) => ({ ...p, sendTo: value }));
    console.log('Integration List v ==>', value);
  }
  function onChangeBrandSelect(value) {
    console.log(`selected ${value}`);
    setState((p) => ({ ...p, sendTo: value }));
    console.log('Brand List v ==>', value);
  }
  function onBlur() {
    console.log('blur');
  }

  function onFocus() {
    console.log('focus');
  }

  function onSearch(val) {
    console.log('search:', val);
  }

  return (
    <Drawer
      title={
        <IntlMessages
          id={edit ? 'brand.editIntegration' : 'title.addIntegration'}
        />
      }
      width={450}
      style={{ height: window.innerHeight - 60, marginTop: 70 }}
      onClose={onClose}
      visible={visible}
      bodyStyle={{ paddingBottom: 80 }}
      destroyOnClose
      form={form}
    >
      <BrandStyle>
        <Form
          layout="vertical"
          form={form}
          onFinish={validate}
          initialValues={initVal}
        >
          <Form.Item
            className="formLabel"
            label={<IntlMessages id="antTable.title.name" />}
            name="brand_id"
            rules={[
              {
                required: true,
                message: <IntlMessages id="req.name" />,
              },
            ]}
          >
            <Select
              showSearch
              placeholder={<IntlMessages id="integration.chooseBrand" />}
              optionFilterProp="children"
              onChange={onChangeBrandSelect}
              onFocus={onFocus}
              onBlur={onBlur}
              onSearch={onSearch}
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {brandState.brands &&
                brandState.brands.map((data) => (
                  <Option value={data.id}>{data.full_name}</Option>
                ))}
            </Select>
          </Form.Item>
          <Form.Item
            className="formLabel"
            label={<IntlMessages id="input.serviceType" />}
            name="integrations"
            rules={[
              {
                required: true,
                message: <IntlMessages id="req.serviceType" />,
              },
            ]}
          >
            <Select
              showSearch
              placeholder={<IntlMessages id="integration.chooseService" />}
              optionFilterProp="children"
              onChange={onChangeServiceSelect}
              onFocus={onFocus}
              onBlur={onBlur}
              onSearch={onSearch}
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {seviceTypeList &&
                seviceTypeList.map((data) => (
                  <Option value={data.name}>{data.name}</Option>
                ))}
            </Select>
          </Form.Item>
          <Form.Item
            className="formLabel"
            label={<IntlMessages id="input.integrationType" />}
            name="integration_type"
            rules={[
              {
                required: true,
                message: <IntlMessages id="req.integrationType" />,
              },
            ]}
          >
            <Select
              // value={selectedInt}
              showSearch
              placeholder={<IntlMessages id="integration.chooseIntegration" />}
              optionFilterProp="children"
              onChange={onChangeIntegrationSelect}
              onFocus={onFocus}
              onBlur={onBlur}
              onSearch={onSearch}
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {integrationList &&
                integrationList.map((data) => (
                  <Option value={data.name}>{data.name}</Option>
                ))}
            </Select>
          </Form.Item>
          {isShopify && (
            <>
              <Form.Item
                label={<IntlMessages id="input.shopUrl" />}
                name="shop_url"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.shopUrl" />,
                  },
                ]}
              >
                <Input placeholder="https://shop.myshopify.com/" />
              </Form.Item>
              <Form.Item
                label={<IntlMessages id="input.shopToken" />}
                name="shop_token"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.shopToken" />,
                  },
                ]}
              >
                <Input placeholder="shpat_ABCde94c3f3899e5adfef6ab78d90e1" />
              </Form.Item>
            </>
          )}
          <BottomViewWrapper className="bottomBtnWrapper">
            <Row gutter={12} style={{ width: '100%' }}>
              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={btnLoader}
                  disabled={btnLoader}
                  style={{ marginRight: 20 }}
                >
                  <IntlMessages
                    id={edit ? 'common.changes' : 'common.submit'}
                  />
                </Button>
              </Form.Item>
              <Form.Item>
                <div
                  className="cancelBtnStyle"
                  onClick={onClose}
                  role="button"
                  onKeyPress=""
                  tabIndex="-1"
                  style={{ cursor: 'pointer' }}
                >
                  <IntlMessages id="common.cancel" />
                </div>
              </Form.Item>
            </Row>
          </BottomViewWrapper>
        </Form>
      </BrandStyle>
    </Drawer>
  );
}

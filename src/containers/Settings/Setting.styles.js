import styled from 'styled-components';
import WithDirection from '@chill/lib/helpers/rtl';

const SettingStyle = styled.div`
  height: 100%;
  .isoLeftCol {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    padding: 20px 24px;
  }
  & .isoLeftColUName {
    display: flex;
    flex-direction: column;
    padding-left: 10px;
  }
  & .settingText {
    color: #000;
    border-bottom: solid;
    padding-bottom: 22px;
    border-color: #1e90ff;
    border-width: 3px;
    @media (max-width: 405px) {
      padding-bottom: 44px;
    }
  }
  & .userName {
    font-size: 18px;
    font-weight: 400;
  }
  & .isoLeftColRow {
    padding: 20px 24px 10px;
    display: flex;
    flex-direction: column;
  }
  & .isoLeftColRowText {
    padding-bottom: 16px;
  }
  & .isoLeftColIP {
    padding: 0px 24px 20px;
    display: flex;
    flex-direction: column;
  }
  & .isoRightCol {
    padding: 10px 24px 0px;
  }
  & .isoRightHeader {
    display: flex;
    flex-direction: column;
    padding-top: 20px;
  }
  & .isoRightChangeText {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
  }
  & .isoRightFormBtn {
    text-align: right;
    margin-bottom: 16px;
    align-self: flex-end;
  }
  & .isoRightHeaderTitle {
    padding-bottom: 6px;
    font-size: 20px;
  }
  & .formLabel {
    margin-top: 14px;
  }
  & .grayText {
    color: #808080;
  }
  & .plusAddUser {
    color: #1e90ff;
    display: flex;
    flex-direction: row-reverse;
    justify-content: end;
    & > span {
      font-size: 18px;
      margin-top: 1px;
      margin-right: 6px;
      @media (max-width: 405px) {
        display: none;
      }
    }
  }
  & .horizontalDevider {
    margin: 0;
  }
  & .isoRightColRow {
    padding-top: 20px;
  }
  & .changePasswordText {
    font-size: 16px;
    font-weight: 500;
    color: #000;
  }
  & .changeBtn {
    border-radius: 10px;
    font-weight: 500;
    border: 0px;
    background: linear-gradient(90deg, #039be5 38%, #03a9f4 90%);
    &:hover {
      background: linear-gradient(90deg, #039be5 38%, #03a9f4 90%);
    }
  }
  & .selectImg > span {
    font-size: 12px;
    color: #1e90ff;
    width: 22px;
    height: 22px;
    position: absolute;
    z-index: 1;
    right: 0px;
    bottom: 0px;
    background: #f5fffa;
    border-radius: 50px;
    padding: 5px;
  }
  .relativeClass {
    position: relative;
  }
`;

export default WithDirection(SettingStyle);

/* eslint-disable react/jsx-no-bind */
/* eslint-disable no-param-reassign */
/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
import React, { useState } from 'react';
import { Row, Col, Divider, Form, Input, Button, Avatar } from 'antd';
import { useSelector, useDispatch } from 'react-redux';
import ImageUploading from 'react-images-uploading';
import { isObject } from 'lodash';
import moment from 'moment';
import authActions from '@chill/redux/auth/actions';
import AntIcon from '@chill/components/antdIcon';
import { UserOutlined } from '@ant-design/icons';
import getApiData from '@chill/lib/helpers/apiHelper';
import IntlMessages from '@chill/components/utility/intlMessages';
import CountryPicker from '@chill/components/uielements/countryPicker';
import Notification from '@chill/components/Notification';
import SettingStyle from './Setting.styles';
import ChangePassword from '../ChangePassword';

/**
 *
 * @module Setting
 */
const Setting = (prop) => {
  const { tab } = prop;
  const uData = useSelector((state) => state.Auth.userData);
  const user = isObject(uData) ? uData : {};
  const imgUrl = isObject(user) && user.user_profile ? user.user_profile : '';
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const { setUserObj } = authActions;

  const [isImages, setImages] = React.useState([]);
  const [btnLoader, setBtnLoader] = useState(false);
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  async function updateImage(values) {
    try {
      const response = await getApiData('user/update-profile', values, 'POST');
      if (response.success) {
        Notification('success', response.message);
        dispatch(setUserObj(response.data));
      } else {
        Notification('error', response.message);
      }
      setBtnLoader(false);
    } catch (error) {
      console.log(error);
      setBtnLoader(false);
      Notification('error', messageArray['err.wenWrong']);
    }
  }
  /** this function use for change Admin account profile image
   * @function onChange
   * @param {array} data image_url
   */
  const onChange = (image) => {
    setImages(image);
    const obj = { user_profile: image[0].data_url, type: 'image' };
    updateImage(obj);
  };

  /** this function use for change Admin account detail
   * @function updateSetting
   * @param {object} data fullName, email, phone, type
   */
  async function updateSetting(values) {
    values.type = 'profile';
    try {
      const response = await getApiData('user/update-profile', values, 'POST');
      if (response.success) {
        Notification('success', response.message);
        dispatch(setUserObj(response.data));
        form.resetFields();
      } else {
        Notification('error', response.message);
      }
      setBtnLoader(false);
    } catch (error) {
      console.log(error);
      setBtnLoader(false);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  function validate(values) {
    const obj = values;
    setBtnLoader(true);
    updateSetting(obj);
  }

  const prefixSelector = (
    <Form.Item
      name="phone_code"
      className="countryFormItem"
      style={{ marginBottom: -2 }}
    >
      <CountryPicker valType="dial_code" className="countryPicker" />
    </Form.Item>
  );

  return (
    <div style={{ backgroundColor: '#fff', height: '100%' }}>
      <SettingStyle>
        <Row style={{ height: '100%', borderTop: '1px solid #e6e6e6' }}>
          {tab === 'settings' ? (
            <>
              <Col
                xs={24}
                sm={11}
                md={8}
                lg={8}
                xl={6}
                style={{ paddingTop: 10 }}
              >
                <div className="isoLeftCol">
                  {/* user image selector */}
                  <ImageUploading
                    value={isImages}
                    onChange={onChange}
                    dataURLKey="data_url"
                  >
                    {({ onImageUpload }) => (
                      <div
                        className="relativeClass"
                        role="button"
                        onKeyPress={onImageUpload}
                        tabIndex="-1"
                        onClick={onImageUpload}
                      >
                        <div className="selectImg">
                          <AntIcon name="camera" />
                        </div>
                        {isImages === '' ? (
                          <Avatar
                            src={imgUrl}
                            size={54}
                            icon={<UserOutlined />}
                          />
                        ) : (
                          isImages.map((image) => (
                            <img
                              src={image.data_url}
                              alt=""
                              style={{
                                width: 54,
                                height: 54,
                                borderRadius: 27,
                              }}
                            />
                          ))
                        )}
                      </div>
                    )}
                  </ImageUploading>
                  <div className="isoLeftColUName">
                    <text className="userName">{user.full_name || 'User'}</text>
                    <text className="grayText">{user.user_type || '-'}</text>
                  </div>
                </div>

                <Divider className="horizontalDevider" />

                <Row className="isoLeftColRowText">
                  <Col className="isoLeftColRow">
                    <text className="grayText">
                      <IntlMessages id="LAST LOGIN" />
                    </text>
                    <text>
                      {moment(user.last_login_at).format(
                        'DD MMM, YYYY, HH:MM a',
                      )}
                    </text>
                  </Col>
                </Row>

                <Row className="isoLeftColRowText">
                  <Col className="isoLeftColIP">
                    <text className="grayText">
                      <IntlMessages id="LOGIN IP" />
                    </text>
                    <text>{user.last_login_ip}</text>
                  </Col>
                  <Divider className="horizontalDevider" />
                </Row>
              </Col>

              <Divider
                type="vertical"
                className="horizontalDevider"
                style={{ height: 'revert' }}
              />
            </>
          ) : null}

          {tab === 'settings' ? (
            <Col
              className="isoRightCol"
              xs={24}
              sm={11}
              md={14}
              lg={14}
              xl={17}
            >
              <div>
                <Row>
                  <Col className="isoRightHeader">
                    <text className="isoRightHeaderTitle">
                      <IntlMessages id="SECURITY SETTINGS" />
                    </text>
                    <text className="grayText">
                      <IntlMessages id="These settings" />
                    </text>
                  </Col>
                </Row>

                <Row className="isoRightColRow">
                  <Col xs={24} sm={24} md={14} lg={14} xl={14}>
                    <Form
                      layout="vertical"
                      form={form}
                      onFinish={validate}
                      initialValues={{
                        full_name: uData.brand_name,
                        email: uData.email,
                        phone: uData.phone,
                        phone_code: uData.phone_code || '+44',
                      }}
                    >
                      <Form.Item
                        label={<IntlMessages id="USERNAME" />}
                        name="full_name"
                        rules={[
                          {
                            required: true,
                            message: <IntlMessages id="req.username" />,
                          },
                        ]}
                      >
                        <Input placeholder={messageArray.USERNAME} />
                      </Form.Item>
                      <Form.Item
                        className="formLabel"
                        label={<IntlMessages id="EMAIL" />}
                        name="email"
                        rules={[
                          {
                            type: 'email',
                            required: true,
                            message: <IntlMessages id="req.email" />,
                          },
                        ]}
                      >
                        <Input placeholder={messageArray.EMAIL} />
                      </Form.Item>
                      <Form.Item
                        className="formLabel"
                        label={<IntlMessages id="PHONE NUMBER" />}
                        name="phone"
                        rules={[
                          {
                            required: true,
                            message: <IntlMessages id="req.phonenumber" />,
                          },
                          {
                            max: 12,
                            message: <IntlMessages id="err.max.10digit" />,
                          },
                          {
                            min: 4,
                            message: <IntlMessages id="err.max.10digit" />,
                          },
                        ]}
                      >
                        <Input
                          style={{ width: '100%' }}
                          min={1}
                          maxLength={15}
                          type="number"
                          addonBefore={prefixSelector}
                        />
                      </Form.Item>
                      <Row className="isoRightColRow">
                        <Col
                          className="isoRightChangeText"
                          xs={24}
                          sm={24}
                          md={24}
                          lg={12}
                          xl={12}
                        />
                        <Col
                          className="isoRightFormBtn"
                          xs={24}
                          sm={24}
                          md={24}
                          lg={12}
                          xl={12}
                        >
                          <Form.Item>
                            <Button
                              className="changeBtn"
                              type="primary"
                              htmlType="submit"
                              loading={btnLoader}
                              disabled={btnLoader}
                            >
                              <IntlMessages id="common.save" />
                            </Button>
                          </Form.Item>
                        </Col>
                      </Row>
                    </Form>
                  </Col>
                </Row>
              </div>
            </Col>
          ) : (
            <Col xs={24}>
              <ChangePassword />
            </Col>
          )}
        </Row>
      </SettingStyle>
    </div>
  );
};

export default Setting;

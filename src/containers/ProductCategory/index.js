/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-no-bind */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable no-param-reassign */
/* eslint-disable global-require */
/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import React from 'react';
import { Col, Row, Tabs, Divider, Select } from 'antd';
import { findIndex, isArray, isEmpty, isObject } from 'lodash';
import { useSelector } from 'react-redux';
import {
  FilterOutlined,
  PlusCircleOutlined,
  SwapOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import theme from '@chill/config/theme/default';
import { TabPane } from '@chill/components/uielements/tabs';
import TableWrapper from '@chill/assets/styles/AntTables.styles';
import IntlMessages from '@chill/components/utility/intlMessages';
import Popover from '@chill/components/uielements/popover';
import getApiData from '@chill/lib/helpers/apiHelper';
import Popconfirms from '@chill/components/Feedback/Popconfirm';
import Notification from '@chill/components/Notification';
import moment from 'moment';
import ProductForm from './ProductForm';
import ProductStyles from './Products.styles';

/**
 *
 * @module ProductsCategories
 */
const Products = () => {
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const [actionMenu, setActionMenu] = React.useState(false);
  const [isIndex, setIsIndex] = React.useState(null);
  const [state, setState] = React.useState({
    initObj: {},
    visible: false,
    visibleCategory: false,
  });
  const [productState, setProductState] = React.useState({
    products: [],
    productLoad: false,
  });
  const [filter, setFilter] = React.useState({ pagination: {}, filters: {} });
  const [productListType, setProductListType] = React.useState('1');
  const [filterMode, setFilterMode] = React.useState(false);
  const [categories, setCategories] = React.useState([]);
  const [sort, setSort] = React.useState(false);

  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  // this function for get products
  /** this function for get products
   * @function getProductList
   * @param {object} data type, sort
   */
  async function getProductList(data = {}) {
    if (productListType === '1') {
      data.type = '';
    } else if (productListType === '2') {
      data.type = '';
    } else {
      data.type = '';
    }
    setProductState((p) => ({
      ...p,
      productLoad: true,
    }));
    if (sort) {
      data.sort = 'ASC';
    } else {
      data.sort = 'DESC';
    }
    try {
      const response = await getApiData('category/index', data, 'POST');
      if (response.success && isArray(response.data)) {
        setProductState((preState) => ({
          ...preState,
          products: isArray(response.data) ? response.data : [],
          productLoad: false,
        }));
        const pagination =
          filter && filter.pagination ? { ...filter.pagination } : {};
        pagination.total = response.total_count || 0;
        pagination.pageSize = response.per_page || 10;
        pagination.current = response.page || 1;
        setFilter((f) => ({ ...f, pagination }));
      } else {
        setProductState((preState) => ({
          ...preState,
          productLoad: false,
        }));
      }
    } catch (err) {
      setProductState((preState) => ({
        ...preState,
        productLoad: false,
      }));
    }
  }

  // this function for get products category
  /** this function for get products category
   * @function getProductCategories
   * @param {object} data {}
   */
  async function getProductCategories() {
    try {
      const response = await getApiData('getProductCategories', {}, 'POST');
      if (response.success) {
        const data =
          isArray(response.data) && response.data.length > 0
            ? response.data
            : [];
        setCategories(data);
      } else {
        Notification('error', response.message);
      }
    } catch (error) {
      console.log(error);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  React.useEffect(() => {
    getProductList();
    getProductCategories();
  }, []);

  React.useEffect(() => {
    const page =
      filter && filter.pagination && filter.pagination.current
        ? filter.pagination.current
        : 1;
    const flt = filter ? { ...filter } : {};
    getProductList({ page, ...flt.filters });
  }, [sort]);

  React.useEffect(() => {
    if (!filterMode) {
      getProductList({});
    }
  }, [filterMode]);

  // this function filters data and get updated list of product
  /** this function filters data and get updated list of product
   * @function fetchDataFilters
   * @param {object} data page, Categories
   */
  function fetchDataFilters() {
    const page =
      filter && filter.pagination && filter.pagination.current
        ? filter.pagination.current
        : 1;
    const filters = filter && filter.filters ? filter.filters : {};
    getProductList({ page, ...filters });
  }

  // this function for delete products
  /** this function for delete products
   * @function deleteProduct
   * @param {object} data category_id
   */
  async function deleteProduct() {
    const data = {
      category_id:
        isObject(state) &&
        isObject(state.initObj) &&
        !isEmpty(state.initObj) &&
        state.initObj.id,
    };
    const productAry = isArray(productState.products)
      ? productState.products
      : [];
    setProductState({
      productLoad: true,
    });
    try {
      const response = await getApiData('category/delete', data, 'POST');
      let msgTitle = 'error';
      if (response.success) {
        msgTitle = 'success';
        fetchDataFilters();
      } else {
        setProductState((pre) => ({
          ...pre,
          products: productAry,
          productLoad: false,
        }));
      }
      Notification(msgTitle, response.message);
    } catch (error) {
      console.log('error ===', error);
      setProductState((pre) => ({
        ...pre,
        products: productAry,
        productLoad: false,
      }));
    }
  }

  // fun. for visible PopOver
  function handleVisible() {
    setActionMenu((visiblePre) => !visiblePre);
  }

  // this function for handle pagination
  /** this function for handle pagination
   * @function onChange
   * @param {object} data page
   */
  function onChange(pagination) {
    const pager = filter && filter.pagination ? { ...filter.pagination } : {};
    pager.current = pagination.current;
    setFilter((f) => ({ ...f, pagination: pager }));
    getProductList({
      page: pagination.current,
      ...filter.filters,
    });
    setSort(false);
  }

  // this function for update new product data
  /** this function for update new product data
   * @function updateData
   * @param {object} data id
   */
  function updateData(data) {
    const productAry = isArray(productState.products)
      ? [...productState.products]
      : [];
    const edit = !isEmpty(state.initObj) && data.id;
    const dataIndex = findIndex(productAry, { id: data.id });
    if (edit && dataIndex > -1) {
      productAry[dataIndex] = data;
    } else {
      getProductList();
    }
    setProductState({
      products: productAry,
    });
    setState({ initObj: {}, visible: false, view: false });
  }

  // Popover content or list
  const content = (
    <ProductStyles>
      <div className="popMainDiv">
        <div
          role="button"
          className="isoDropdownLink"
          onKeyPress=""
          tabIndex="-1"
          onClick={() => {
            setState({ ...state, visible: true });
            setIsIndex(null);
          }}
        >
          <EditOutlined style={{ paddingRight: 12, fontSize: 16 }} />
          <IntlMessages id="Editcategory" />
        </div>
        <div className="isoDropdownLink">
          <Popconfirms
            title={<IntlMessages id="Sure to delete?" />}
            okText={<IntlMessages id="DELETE" />}
            cancelText={<IntlMessages id="No" />}
            onConfirm={() => {
              deleteProduct();
              setIsIndex(null);
            }}
            onCancel={null}
          >
            <DeleteOutlined style={{ paddingRight: 12, fontSize: 16 }} />
            <IntlMessages id="DeleteCategory" />
          </Popconfirms>
        </div>
      </div>
    </ProductStyles>
  );

  const renderTabBar = (props, DefaultTabBar) => (
    <DefaultTabBar {...props} className="site-custom-tab-bar" />
  );

  // set item id in state
  function handleVisibleChange(item1) {
    setIsIndex(item1.id);
  }

  const columns = [
    {
      title: <IntlMessages id="No" />,
      dataIndex: 'id',
      rowKey: 'id',
      width: 50,
      className: 'fullname-cell',
      render: (text, data, index) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="No" />
            </span>
            <span className="mobile-lbl-val">{index + 1 || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Product category" />,
      dataIndex: 'category_name',
      rowKey: 'category_name',
      width: 280,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Product category" />
            </span>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <span className="mobile-lbl-val">{text || '-'}</span>
            </div>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="common.createdAt" />,
      dataIndex: 'createdAt',
      rowKey: 'createdAt',
      width: 250,
      className: 'fullname-cell',
      render: (text) => {
        const date = moment(text).format('lll');
        return (
          <div>
            <span className="label">
              <IntlMessages id="common.createdAt" />
            </span>
            <span className="mobile-lbl-val">{date || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Action" />,
      dataIndex: 'action',
      rowKey: 'action',
      className: 'fullname-cell',
      width: 50,
      fixed: 'right',
      render: (text, item) => {
        const isBrandNull =
          item.brand_id === '' ||
          item.brand_id === undefined ||
          item.brand_id === null;
        return (
          <div>
            <span className="label">
              <IntlMessages id="Action" />
            </span>
            <span className="mobile-lbl-val">
              {isBrandNull ? (
                <p style={{ color: '#000', fontSize: 15, paddingLeft: 10 }}>
                  -
                </p>
              ) : (
                <Popover
                  content={content}
                  trigger="click"
                  visible={isIndex === item.id ? !actionMenu : false}
                  onVisibleChange={handleVisible}
                  arrowPointAtCenter
                  placement="bottomRight"
                >
                  <MoreOutlined
                    onClick={() => {
                      handleVisibleChange(item);
                      setState({ ...state, initObj: item });
                    }}
                    style={{ color: '#000', fontSize: 24 }}
                  />
                </Popover>
              )}
            </span>
          </div>
        );
      },
    },
  ];

  // Add Product View
  const addNew = () => {
    return (
      <ProductStyles>
        <div className="iconStyle marketingMainDiv">
          <span>
            <p
              className="addNewUser"
              style={{
                color: theme.colors.primaryColor,
                alignItems: 'center',
                // display:'flex'
              }}
            >
              <SwapOutlined
                className="filterIcon1"
                style={{ color: sort ? theme.colors.primaryColor : '#000' }}
                onClick={() => {
                  setSort(!sort);
                }}
              />
              <FilterOutlined
                className="filterIcon"
                style={{
                  color: filterMode ? theme.colors.primaryColor : '#000',
                }}
                onClick={() => {
                  setFilterMode((pre) => !pre);
                  setFilter({ filters: {} });
                }}
              />
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                }}
                onClick={() => setState((p) => ({ ...p, visible: true }))}
              >
                <p className="iconPadding" style={{ paddingRight: 8 }}>
                  <PlusCircleOutlined />
                </p>
                <IntlMessages id="product.category" />
              </div>
            </p>
          </span>
        </div>
      </ProductStyles>
    );
  };

  // this function renders search view
  function renderSearchBar() {
    return (
      <div style={{ padding: '15px' }}>
        <Row gutter={10}>
          <Col lg={8} sm={24} md={12} xs={24}>
            <Select
              style={{ width: '100%' }}
              onChange={(val) => {
                const flt = filter ? { ...filter } : {};
                flt.filters.category_id = val || '';
                setFilter(flt);
                getProductList(flt.filters);
              }}
              placeholder={messageArray['input.category.placeholder']}
              allowClear
            >
              {categories.map((item) => (
                <Select.Option key={`categories${item.id}`} value={item.id}>
                  {item.category_name}
                </Select.Option>
              ))}
            </Select>
          </Col>
        </Row>
      </div>
    );
  }

  const { initObj, visible, view, visibleCategory } = state;
  return (
    <ProductStyles>
      <Row gutter={24}>
        <Col xl={24} lg={24} sm={24} md={24} xs={24}>
          <Row className="isoTabRow">
            <Col xl={8} lg={8} sm={8} md={4} xs={8}>
              <div className="marketingText">
                <IntlMessages id="ProductCategory" />
              </div>
            </Col>
            <Col sm={16} xs={16} className="hiddenCol">
              {addNew()}
            </Col>
            <Col className="isoTab" xl={8} lg={8} sm={24} md={12} xs={24}>
              <Tabs
                defaultActiveKey=""
                renderTabBar={renderTabBar}
                style={{ paddingLeft: 16 }}
              >
                <TabPane tab={<IntlMessages id="tab.all" />} key="1" />
                {/* <TabPane tab={<IntlMessages id="tab.new" />} key="2" />
                <TabPane tab={<IntlMessages id="tab.outofstock" />} key="3" /> */}
              </Tabs>
            </Col>

            <Col xl={8} lg={8} md={8} className="isoAddNew">
              {addNew()}
            </Col>
          </Row>
          <Divider className="horizontalDevider" />
          {filterMode ? renderSearchBar() : null}
          <Row>
            <Col xl={24} lg={24} sm={24} md={24} xs={24}>
              <TableWrapper
                loading={productState.productLoad}
                rowKey={(record) => record.id}
                dataSource={productState.products}
                onChange={onChange}
                columns={columns}
                className="invoiceListTable"
                pagination={filter.pagination || {}}
                showSorterTooltip={false}
              />
            </Col>
          </Row>
        </Col>
      </Row>
      <ProductForm
        initialValues={initObj}
        visible={visible}
        view={view}
        addCategory={() => {
          setState((p) => ({ ...p, visibleCategory: !visibleCategory }));
        }}
        onClose={(type, data) => {
          if (type === 'success') {
            updateData(data);
          } else {
            setState((p) => ({
              ...p,
              initObj: {},
              visible: false,
              view: false,
            }));
          }
        }}
        categories={categories}
      />
    </ProductStyles>
  );
};

export default Products;

/* eslint-disable react/jsx-no-bind */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable no-undef */
/* eslint-disable no-unused-vars */
/* eslint-disable no-nested-ternary */
/* eslint-disable global-require */
/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
import React, { useEffect, useState } from 'react';
import { Col, Drawer, Input, Row, Select, Upload, Form, Icon } from 'antd';
import { isEmpty, isNull, isObject } from 'lodash';
import { useSelector } from 'react-redux';
import IntlMessages from '@chill/components/utility/intlMessages';
import {
  BottomViewWrapper,
  FormWrapper,
} from '@chill/assets/styles/drawerFormStyles';
import {
  PaperClipOutlined,
  DeleteOutlined,
  FilterOutlined,
  SwapOutlined,
  PlusCircleOutlined,
} from '@ant-design/icons';
import CButton from '@chill/components/uielements/CButton';
import InputNumber from '@chill/components/uielements/InputNumber';
import { getBase64 } from '@chill/lib/helpers/utility';
import Notification from '@chill/components/Notification';
import getApiData from '@chill/lib/helpers/apiHelper';
import Editor from '@chill/components/uielements/editor';
import useResetFormOnClose from '@chill/lib/hooks/useResetForm';
import theme from '@chill/config/theme/default';
import MarketingWrapper from '../Marketing/Marketing.Styles';

function ProductForm(props) {
  const { initialValues, onClose, visible, view, categories, addCategory } =
    props;
  const edit = !isEmpty(initialValues);
  const initVal = initialValues || {};
  console.log('ProductForm -> initialValues', initialValues);
  const [form] = Form.useForm();
  const [attachments, setAttachments] = useState({});
  const [btnLoader, setBtnLoader] = useState(false);
  const [productImg, setProductImg] = useState({});
  const [isImage, setIsImage] = useState({});
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  useResetFormOnClose({ visible, initVal, form });

  useEffect(() => {
    if (!edit) {
      form.resetFields();
    }
    console.log('initVal ===', initVal);
    if (isObject(initVal) && !isEmpty(initVal)) {
      setAttachments(initVal.file_data);
    }
  }, [visible]);

  // this function reset form data when close modal
  function handleForm(type = '', data = {}) {
    form.resetFields();
    onClose(type, data);
  }

  // this function for add new products
  async function addProduct(values) {
    // const formData = new FormData();
    // Object.keys(values).map((k) => formData.append(k, values[k]));

    const data = { category_name: values.category_name };
    console.log('addProduct -> values', values);
    if (edit) {
      data.category_id = values.category_id;
    }
    const url = edit ? 'category/update' : 'category/add-category';
    try {
      const response = await getApiData(url, data, 'POST');
      if (response.success) {
        Notification('success', response.message);
        handleForm('success', response.data);
      } else {
        Notification('error', response.message);
      }
      setBtnLoader(false);
    } catch (error) {
      console.log(error);
      setBtnLoader(false);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  // this function for validate data
  function validate(values) {
    const valid = true;

    const obj = values;
    if (edit) obj.category_id = initVal.id;

    if (valid) {
      setBtnLoader(true);
      addProduct(obj);
    }
  }

  return (
    <Drawer
      title={
        <>
          <IntlMessages
            id={
              view
                ? 'product.detail.title'
                : edit
                ? 'Edit Productcategory'
                : 'Add Productcategory'
            }
          />
        </>
      }
      width={500}
      style={{ height: window.innerHeight - 60, marginTop: 70 }}
      onClose={handleForm}
      visible={visible}
      bodyStyle={{ paddingBottom: 80 }}
      destroyOnClose
    >
      <FormWrapper>
        <Form
          form={form}
          onFinish={validate}
          layout="vertical"
          initialValues={initVal}
        >
          <Row
            gutter={[12, 20]}
            style={{
              marginBottom: 20,
            }}
          >
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="Product category" />}
                name="category_name"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.product.category" />,
                  },
                  {
                    max: 50,
                    message: <IntlMessages id="err.max.50char" />,
                  },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <BottomViewWrapper className="bottomBtnWrapper">
            <Row gutter={12} style={{ width: '100%' }}>
              <Col xs={24}>
                <Form.Item className="btn-form">
                  <CButton
                    className="submitBtnStyle"
                    htmlType="submit"
                    loading={btnLoader}
                    disabled={btnLoader}
                    style={{ marginRight: 20 }}
                  >
                    <IntlMessages
                      id={edit ? 'common.changes' : 'common.submit'}
                    />
                  </CButton>
                </Form.Item>
              </Col>
              {/* <Form.Item className="btn-form">
                <div
                  className="cancelBtnStyle"
                  onClick={handleForm}
                  role="button"
                  onKeyPress=""
                  tabIndex="-1"
                >
                  <IntlMessages id={view ? 'common.done' : 'common.cancel'} />
                </div>
              </Form.Item> */}
            </Row>
          </BottomViewWrapper>
        </Form>
      </FormWrapper>
    </Drawer>
  );
}

export default ProductForm;

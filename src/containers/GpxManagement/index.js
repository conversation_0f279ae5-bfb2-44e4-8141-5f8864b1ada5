/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
import React, { useEffect } from 'react';
import { Col, Row, Divider, Input, Select } from 'antd';
import { isArray, isEmpty, isObject } from 'lodash';
// import { useSelector } from 'react-redux';
import {
  FilterOutlined,
  PlusCircleOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import theme from '@chill/config/theme/default';
import TableWrapper from '@chill/assets/styles/AntTables.styles';
import IntlMessages from '@chill/components/utility/intlMessages';
import Popover from '@chill/components/uielements/popover';
import getApiData from '@chill/lib/helpers/apiHelper';
import Popconfirms from '@chill/components/Feedback/Popconfirm';
import Notification from '@chill/components/Notification';
import GpxForm from './gpxForm';
import GpxStyles from './gpxManagement.styles';

const { Option } = Select;

/**
 * GpxManagement module for managing GPX data, including fetching, deleting, and displaying trailers.
 * @module GpxManagement
 */
const GpxManagement = () => {
  // const { language } = useSelector((state) => state.LanguageSwitcher);
  const [actionMenu, setActionMenu] = React.useState(false);
  const [isIndex, setIsIndex] = React.useState(null);
  const [state, setState] = React.useState({
    initObj: {},
    visible: false,
  });
  const [gpxState, setGpxState] = React.useState({
    list: [],
    loading: false,
  });
  const [filter, setFilter] = React.useState({ pagination: {}, filters: {} });
  const [filterMode, setFilterMode] = React.useState(false);
  const [sort, setSort] = React.useState(false);
  const [currentPage, setCurrentPage] = React.useState(0);

  // Array of location type options for GPX files
  const locationTypeArr = [
    { id: 'city', type: 'City' },
    { id: 'off_rode', type: 'Off road' },
    { id: 'countryside', type: 'Countryside' },
    { id: 'beach', type: 'Beach' },
    { id: 'coastal_route', type: 'Coastal route' },
    { id: 'lakes', type: 'Lakes' },
    { id: 'forest', type: 'Forest' },
  ];

  /**
   * Fetches the list of GPX items with optional sorting or filters
   * @function getGpxList
   * @param {object} data - An object containing page, sort, and filter data
   */
  async function getGpxList(data = {}) {
    setGpxState((p) => ({
      ...p,
      loading: true,
    }));

    try {
      const response = await getApiData('gpx-list', data);
      if (response.success && isArray(response.data)) {
        setGpxState((preState) => ({
          ...preState,
          list: isArray(response.data) ? response.data : [],
          loading: false,
        }));
        const pagination =
          filter && filter.pagination ? { ...filter.pagination } : {};
        pagination.total = response.total_count || 0;
        pagination.pageSize = response.per_page || 10;
        pagination.current = response.page || 1;
        setFilter((f) => ({ ...f, pagination }));
      } else {
        setGpxState((preState) => ({
          ...preState,
          loading: false,
        }));
      }
    } catch (err) {
      setGpxState((preState) => ({
        ...preState,
        loading: false,
      }));
    }
  }

  /**
   * Updates the GPX list based on the applied filters.
   * @function fetchDataFilters
   */
  function fetchDataFilters() {
    const page =
      filter && filter.pagination && filter.pagination.current
        ? filter.pagination.current
        : 1;
    const filters = filter && filter.filters ? filter.filters : {};
    getGpxList({ page, ...filters });
  }

  // Triggers fetching of GPX data when sorting changes
  useEffect(() => {
    fetchDataFilters();
  }, [sort]);

  /**
   * Deletes a selected GPX item
   * @function deleteGpx
   * @param {object} data - Contains the trailer's ID to be deleted
   */
  async function deleteGpx() {
    const formData = new FormData();
    formData.append(
      'gpx_id',
      isObject(state.initObj) && !isEmpty(state.initObj) && state.initObj.id,
    );
    const trailerAry = isArray(gpxState.list) ? gpxState.list : [];
    setGpxState({
      loading: true,
    });
    try {
      const response = await getApiData(
        'delete-gpx',
        formData,
        'POST',
        {},
        true,
      );
      let msgTitle = 'error';
      if (response.success) {
        msgTitle = 'success';
        fetchDataFilters();
      } else {
        setGpxState((pre) => ({
          ...pre,
          list: trailerAry,
          loading: false,
        }));
      }
      Notification(msgTitle, response.message);
    } catch (error) {
      setGpxState((pre) => ({
        ...pre,
        list: trailerAry,
        loading: false,
      }));
    }
  }

  /**
   * Toggles the visibility of the action menu for a selected GPX item
   * @function handleVisible
   */
  function handleVisible() {
    setActionMenu((visiblePre) => !visiblePre);
  }

  /**
   * Handles pagination changes and fetches updated GPX list
   * @function onChange
   * @param {object} pagination - Contains pagination details (current page, page size, etc.)
   */
  const onChange = (pagination) => {
    const pager = filter && filter.pagination ? { ...filter.pagination } : {};
    setCurrentPage((pagination.current - 1) * pagination.pageSize);
    pager.current = pagination.current;
    setFilter((f) => ({ ...f, pagination: pager }));
    getGpxList({
      page: pagination.current,
      ...filter.filters,
    });
    setSort(false);
  };

  // Popover content for actions
  const content = (
    <GpxStyles>
      <div className="popMainDiv">
        <div
          role="button"
          className="isoDropdownLink"
          onKeyPress=""
          tabIndex="-1"
          onClick={() => {
            setState({ ...state, visible: true });
            setIsIndex(null);
          }}
        >
          <EditOutlined style={{ paddingRight: 12, fontSize: 16 }} />
          <IntlMessages id="Edit GPX" />
        </div>
        <div className="isoDropdownLink">
          <Popconfirms
            title={<IntlMessages id="Sure to delete?" />}
            okText={<IntlMessages id="DELETE" />}
            cancelText={<IntlMessages id="No" />}
            onConfirm={() => {
              deleteGpx();
              setIsIndex(null);
            }}
            onCancel={null}
          >
            <DeleteOutlined style={{ paddingRight: 12, fontSize: 16 }} />
            <IntlMessages id="Delete GPX" />
          </Popconfirms>
        </div>
      </div>
    </GpxStyles>
  );

  // Category options for filtering
  const cateArr = [
    { id: 1, val: 'Easy' },
    { id: 2, val: 'Intermediate' },
    { id: 3, val: 'Difficult' },
  ];

  /**
   * Updates the selected item id for menu visibility.
   * @function handleVisibleChange
   * @param {object} item1 - The item whose id will be set.
   */
  function handleVisibleChange(item1) {
    setIsIndex(item1.id);
  }

  // Columns definition for the GPX table
  const columns = [
    {
      title: <IntlMessages id="No" />,
      dataIndex: 'id',
      rowKey: 'id',
      width: 10,
      className: 'fullname-cell',
      render: (text, data, index) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="No" />
            </span>
            <span className="mobile-lbl-val">
              {index + currentPage + 1 || '-'}
            </span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="antTable.title" />,
      dataIndex: 'title',
      rowKey: 'title',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Product Name" />
            </span>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <span className="mobile-lbl-val">{text || '-'}</span>
            </div>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Category" />,
      dataIndex: 'category',
      rowKey: 'category',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Category" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Tag" />,
      dataIndex: 'tag',
      rowKey: 'tag',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Tag" />
            </span>
            <span className="mobile-lbl-val">{text.join(', ') || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Distance" />,
      dataIndex: 'distance',
      rowKey: 'distance',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="distance" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },

    {
      title: <IntlMessages id="Loaction Type" />,
      dataIndex: 'location_type',
      rowKey: 'location_type',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        const location = locationTypeArr.find((item) => item.id === text);
        return (
          <div>
            <span className="label">
              <IntlMessages id="location_type" />
            </span>
            <span className="mobile-lbl-val">
              {location ? location.type : '-'}
            </span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Elevation Gain" />,
      dataIndex: 'elevation_gain',
      rowKey: 'elevation_gain',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="elevation_gain" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Action" />,
      dataIndex: 'action',
      rowKey: 'action',
      className: 'fullname-cell',
      width: 50,
      fixed: 'right',
      render: (text, item) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Action" />
            </span>
            <span className="mobile-lbl-val">
              <Popover
                content={content}
                trigger="click"
                visible={isIndex === item.id ? !actionMenu : false}
                onVisibleChange={() => handleVisible()}
                arrowPointAtCenter
                placement="bottomRight"
              >
                <MoreOutlined
                  onClick={() => {
                    handleVisibleChange(item);
                    setState({ ...state, initObj: item });
                  }}
                  style={{ color: '#000', fontSize: 24 }}
                />
              </Popover>
            </span>
          </div>
        );
      },
    },
  ];

  /**
   * Handles adding a new GPX item.
   * This function is triggered when the user clicks the "Add New" button.
   * It initializes the form state and shows the form modal for adding a new item.
   *
   * @function addNew
   */
  const addNew = () => {
    return (
      <GpxStyles>
        <div className="iconStyle marketingMainDiv">
          <span>
            <p
              className="addNewUser"
              style={{
                color: theme.colors.primaryColor,
                alignItems: 'center',
              }}
            >
              <FilterOutlined
                className="filterIcon"
                style={{
                  color: filterMode ? theme.colors.primaryColor : '#000',
                }}
                onClick={() => {
                  setFilterMode((pre) => !pre);
                  setFilter({ filters: {} });
                }}
              />
              <div
                role="button"
                tabIndex="0"
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                }}
                onClick={() => setState({ visible: true })}
                onKeyDown={() => setState({ visible: true })}
              >
                <p className="iconPadding" style={{ paddingRight: 8 }}>
                  <PlusCircleOutlined />
                </p>
                <IntlMessages id="common.addgpx" />
              </div>
            </p>
          </span>
        </div>
      </GpxStyles>
    );
  };

  /**
   * Renders the search bar to filter GPX items by title, tag, or category
   * @function renderSearchBar
   */
  function renderSearchBar() {
    return (
      <div style={{ padding: '15px' }}>
        <Row gutter={10}>
          <Col lg={6} sm={24} md={12} xs={24} style={{ marginBottom: 8 }}>
            <Input.Search
              style={{ minWidth: '100%' }}
              onChange={(e) => {
                const val = e && e.target ? e.target.value : '';
                const flt = filter ? { ...filter } : {};
                flt.filters.title = val;
                setFilter(flt);
                getGpxList(flt.filters);
              }}
              value={filter.title}
              allowClear
              placeholder="Enter Title"
            />
          </Col>
          <Col lg={6} sm={24} md={12} xs={24} style={{ marginBottom: 8 }}>
            <Input.Search
              style={{ minWidth: '100%' }}
              onChange={(e) => {
                const val = e && e.target ? e.target.value : '';
                const flt = filter ? { ...filter } : {};
                flt.filters.tag = val;
                setFilter(flt);
                getGpxList(flt.filters);
              }}
              value={filter.tag}
              allowClear
              placeholder="Enter Tag"
            />
          </Col>
          <Col lg={6} sm={24} md={12} xs={24} style={{ marginBottom: 8 }}>
            <Select
              onChange={(e) => {
                const val = e || '';
                const flt = filter ? { ...filter } : {};
                flt.filters.category = val;
                setFilter(flt);
                getGpxList(flt.filters);
              }}
              value={filter.category}
              allowClear
              style={{ width: '100%' }}
              placeholder={<IntlMessages id="common.category" />}
            >
              {cateArr?.map((status) => {
                return (
                  <Option key={status.id} value={status.val}>
                    {status.val}
                  </Option>
                );
              })}
            </Select>
          </Col>
          <Col lg={6} sm={24} md={12} xs={24} style={{ marginBottom: 8 }}>
            <Select
              onChange={(e) => {
                const val = e || '';
                const flt = filter ? { ...filter } : {};
                flt.filters.location_type = val;
                setFilter(flt);
                getGpxList(flt.filters);
              }}
              value={filter.location_type}
              allowClear
              style={{ width: '100%' }}
              placeholder={<IntlMessages id="common.locationType" />}
            >
              {locationTypeArr?.map((location) => {
                return (
                  <Option key={location.id} value={location.id}>
                    {location.type}
                  </Option>
                );
              })}
            </Select>
          </Col>
        </Row>
      </div>
    );
  }

  const { initObj, visible } = state;
  return (
    <GpxStyles>
      <Row className="isoTabRow">
        <Col xl={8} lg={8} sm={8} md={4} xs={8}>
          <div className="marketingText">
            <IntlMessages id="sidebar.gpxManagement" />
          </div>
        </Col>
        <Col sm={16} xs={16} className="hiddenCol">
          {addNew()}
        </Col>
        <Col xl={16} lg={16} md={20} xs={16} className="isoAddNew">
          {addNew()}
        </Col>
      </Row>
      <Divider className="horizontalDevider" />
      {filterMode ? renderSearchBar() : null}
      <TableWrapper
        loading={gpxState.loading}
        rowKey={(record) => record.id}
        dataSource={gpxState.list}
        onChange={onChange}
        columns={columns}
        className="invoiceListTable"
        pagination={filter.pagination || {}}
        showSorterTooltip={false}
        // scroll={{ x: 400 }}
      />
      <GpxForm
        initialValues={initObj}
        visible={visible}
        onClose={async (type) => {
          if (type === 'success') {
            fetchDataFilters();
            setState({ initObj: {}, visible: false });
          } else {
            setState({ initObj: {}, visible: false });
          }
        }}
      />
    </GpxStyles>
  );
};

export default GpxManagement;

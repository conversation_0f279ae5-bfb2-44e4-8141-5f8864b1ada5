/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
/* eslint-disable no-nested-ternary */

// Importing necessary libraries and components
import React, { useEffect, useState } from 'react';
import { Col, Drawer, Input, Row, Upload, Form, Icon, Select } from 'antd';
import { isEmpty, isString } from 'lodash';
import { useSelector } from 'react-redux';
import IntlMessages from '@chill/components/utility/intlMessages';
import {
  BottomViewWrapper,
  FormWrapper,
} from '@chill/assets/styles/drawerFormStyles';
import CButton from '@chill/components/uielements/CButton';
import Notification from '@chill/components/Notification';
import getApiData from '@chill/lib/helpers/apiHelper';
import useResetFormOnClose from '@chill/lib/hooks/useResetForm';
import { EnvironmentOutlined, UploadOutlined } from '@ant-design/icons';

// Destructure Option from Select for dropdowns
const { Option } = Select;

/**
 * GpxForm Component:
 * - Renders a Drawer containing a form for uploading and managing GPX files.
 * - Handles both Create and Edit modes.
 *
 * Props:
 * @param {object} props - Component properties
 * @param {object} props.initialValues - Initial values for the form fields (used in Edit mode)
 * @param {function} props.onClose - Callback function triggered when the Drawer closes
 * @param {boolean} props.visible - Determines whether the Drawer is visible
 * @param {boolean} props.view - If true, form is in view-only mode
 */
function GpxForm(props) {
  const { initialValues, onClose, visible, view } = props; // Props passed from the parent component
  const isEditMode = !isEmpty(initialValues); // Determines if the form is in edit mode
  const initVal = initialValues || {}; // Set initial values for the form
  const [form] = Form.useForm(); // Ant Design form instance

  // Component states
  const [btnLoader, setBtnLoader] = useState(false); // Loader state for submit button
  const [, setUploadedFile] = useState({}); // State for managing the uploaded GPX file
  const [selectedFile, setSelectedFile] = useState(null); // State for the currently selected file

  const { language } = useSelector((state) => state.LanguageSwitcher); // Redux state for the selected language
  const messageArray = require(`@chill/config/translation/locales/${language}.json`); // Dynamic import of language-specific translations
  useResetFormOnClose({ visible, initVal, form }); // Custom hook to reset form when modal closes

  // Array of category options for GPX files
  const cateArr = [
    { id: 1, val: 'Easy' },
    { id: 2, val: 'Intermediate' },
    { id: 3, val: 'Difficult' },
  ];

  // Array of location type options for GPX files
  const locationTypeArr = [
    { id: 'city', type: 'City' },
    { id: 'off_rode', type: 'Off road' },
    { id: 'countryside', type: 'Countryside' },
    { id: 'beach', type: 'Beach' },
    { id: 'coastal_route', type: 'Coastal route' },
    { id: 'lakes', type: 'Lakes' },
    { id: 'forest', type: 'Forest' },
  ];

  // Effect: Reset form fields and initialize trailer image when Drawer visibility changes
  useEffect(() => {
    if (!isEditMode) {
      form.resetFields(); // Reset fields for add mode
    }
    setUploadedFile({ imageUrl: initVal?.file_url, loading: false });
  }, [visible]);

  /**
   * File Validation: Ensures the uploaded file is of type GPX.
   * @param {File} file - File object selected for upload
   * @returns {boolean} - False to prevent auto-upload
   */
  const beforeUpload = (file) => {
    const fileExtension = file.name.toLowerCase().split('.').pop();

    const isGpxFile = fileExtension === 'gpx';

    if (!isGpxFile) {
      Notification('error', messageArray['err.gpx.upload']);
      return false;
    }

    setSelectedFile(file); // Set the file for further processing
    return false; // Prevent auto-upload by Upload component
  };

  /**
   * Reset Form: Clears form fields and selected image.
   * @param {string} [type=''] - Indicates success or cancellation
   * @param {object} [data={}] - Optional data to pass to the parent component
   */
  function handleForm(type = '', data = {}) {
    form.resetFields();
    setUploadedFile({});
    setSelectedFile(null);
    onClose(type, data);
  }

  /**
   * Add/Edit GPX File: Submits form data to create or update a GPX file.
   * @param {object} values - Form values
   */
  async function submitGpxFile(values) {
    const formData = new FormData(); // Prepare form data for API submission
    const url = isEditMode ? 'update-gpx' : 'create-gpx'; // API endpoint based on mode

    // Append all form values to the FormData object
    Object.keys(values).map((k) => formData.append(k, values[k]));

    if (isEditMode) {
      formData.append('gpx_id', initVal.id); // Include GPX ID in edit mode
    }

    // Replace file if a new file is selected for upload
    formData.delete('file_url');
    if (!isEmpty(selectedFile)) {
      formData.append('file_url', selectedFile);
    }

    try {
      const response = await getApiData(url, formData, 'POST', {}, true);
      if (response.success) {
        Notification('success', response.message);
        handleForm('success', response.data);
        setSelectedFile(null);
      } else {
        Notification('error', response.message);
      }
      setBtnLoader(false);
    } catch (error) {
      setSelectedFile({});
      setBtnLoader(false);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  /**
   * Validate Form Data: Validates the form and submits if valid.
   * @param {object} values - Form values
   */
  function validate(values) {
    const obj = values;

    // Check for file or pre-existing file URL
    if (selectedFile || initVal?.file_url) {
      obj.tag = isString(values.tag) ? values.tag : JSON.stringify(values.tag);

      submitGpxFile(obj);
    } else {
      Notification('error', messageArray['common.uploadImg']);
    }
  }

  // Button for GPX file upload with preview
  const uploadGpxButton = (
    <div>
      <Icon type="plus" />
      <div className="ant-upload-text">
        <UploadOutlined /> <IntlMessages id="common.uploadFile" />
      </div>
    </div>
  );

  const [options, setOptions] = useState([]);

  // Set the tags properly for edit mode
  useEffect(() => {
    if (isEditMode && initVal?.tag) {
      const tags = initVal?.tag;

      // Update the options state to separate values like ['test', 'route']
      setOptions(tags.map((tag) => ({ value: tag, label: tag })));
    }
  }, [isEditMode, initVal]);

  const tagHandleChange = (value) => {
    const newOptions = value
      .filter((v) => !options.some((option) => option.value === v))
      .map((v) => ({ value: v, label: v }));

    if (newOptions.length > 0) {
      setOptions((prevOptions) => [...prevOptions, ...newOptions]);
    }
  };

  return (
    <Drawer
      title={<IntlMessages id={isEditMode ? 'Edit GPX' : 'Add GPX'} />}
      width={500}
      style={{ height: window.innerHeight - 60, marginTop: 70 }}
      onClose={() => handleForm()}
      visible={visible}
      bodyStyle={{ paddingBottom: 80 }}
      destroyOnClose
    >
      <FormWrapper>
        <Form
          form={form}
          onFinish={(e) => validate(e)}
          layout="vertical"
          initialValues={initVal}
        >
          <Row
            gutter={[12, 20]}
            style={{
              marginBottom: 20,
            }}
          >
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="antTable.title" />}
                name="title"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.enterTitle" />,
                  },
                ]}
              >
                <Input placeholder="Enter Title" />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="common.tag" />}
                name="tag"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.tag" />,
                  },
                ]}
              >
                <Select
                  mode="tags"
                  placeholder={<IntlMessages id="common.entertag" />}
                  onChange={tagHandleChange}
                  options={options}
                />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="file_url"
                label={<IntlMessages id="Add File" />}
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="Pleae upload file" />,
                  },
                ]}
              >
                <Upload
                  className="usermanualstyle"
                  showUploadList={false}
                  listType="picture-card"
                  beforeUpload={beforeUpload}
                  disabled={!!view}
                  style={{ width: '100%' }}
                  accept=".gpx"
                >
                  {(selectedFile && !isEmpty(selectedFile)) ||
                  (initVal && !isEmpty(initVal?.file_url)) ? (
                    <Row>
                      <Col xs={24}>
                        <EnvironmentOutlined
                          style={{ fontSize: 50, color: 'red' }}
                        />
                      </Col>
                      <Col xs={24}>
                        <div className="_uploadTextContainer">
                          <div className="_uploadText">
                            {selectedFile?.name || initVal?.file_url}
                          </div>
                        </div>
                      </Col>
                    </Row>
                  ) : (
                    uploadGpxButton
                  )}
                </Upload>
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="Category" />}
                name="category"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.category" />,
                  },
                ]}
              >
                <Select placeholder={<IntlMessages id="common.category" />}>
                  {cateArr?.map((category) => {
                    return (
                      <Option key={category.id} value={category.val}>
                        {category.val}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="gpx.locationType" />}
                name="location_type"
                defaultValue={initVal?.location_type || null}
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.locationType" />,
                  },
                ]}
              >
                <Select placeholder={<IntlMessages id="common.locationType" />}>
                  {locationTypeArr?.map((location) => {
                    return (
                      <Option key={location.id} value={location.id}>
                        {location.type}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="input.desc.label" />}
                name="description"
                rules={[
                  {
                    required: true,
                    message: 'Please enter description',
                  },
                ]}
              >
                <Input.TextArea rows={3} placeholder="Enter Description..." />
              </Form.Item>
            </Col>
          </Row>
          <BottomViewWrapper className="bottomBtnWrapper">
            <Row gutter={12} style={{ width: '100%' }}>
              {view ? null : (
                <Col xs={24}>
                  <Form.Item className="btn-form">
                    <CButton
                      className="submitBtnStyle"
                      htmlType="submit"
                      loading={btnLoader}
                      disabled={btnLoader}
                      style={{ marginRight: 20 }}
                    >
                      <IntlMessages
                        id={isEditMode ? 'common.changes' : 'common.submit'}
                      />
                    </CButton>
                  </Form.Item>
                </Col>
              )}
              {/* <Form.Item className="btn-form">
                <div
                  className="cancelBtnStyle"
                  onClick={handleForm}
                  role="button"
                  onKeyPress=""
                  tabIndex="-1"
                >
                  <IntlMessages id={view ? 'common.done' : 'common.cancel'} />
                </div>
              </Form.Item> */}
            </Row>
          </BottomViewWrapper>
        </Form>
      </FormWrapper>
    </Drawer>
  );
}

export default GpxForm;

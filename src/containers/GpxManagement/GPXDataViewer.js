import React, { useEffect, useState } from 'react';
import { parseString } from 'xml2js';

const GPXDataViewer = ({ gpxFile }) => {
  console.log(
    `hello ~ file: GPXDataViewer.js:5 ~ GPXDataViewer ~ gpxFile:`,
    gpxFile,
  );
  const [gpxData, setGpxData] = useState(null);

  useEffect(() => {
    if (typeof gpxFile === 'string') {
      // Fetch file content if gpxFile is a URL
      fetch(gpxFile)
        .then((response) => {
          if (!response.ok) {
            throw new Error(`Failed to fetch GPX file: ${response.statusText}`);
          }
          return response.text(); // Get the file content as text
        })
        .then((data) => {
          parseString(data, (err, result) => {
            if (err) {
              console.error('Error parsing GPX file:', err);
            } else {
              setGpxData(result); // Store parsed data
            }
          });
        })
        .catch((error) => console.error('Fetch error:', error));
    } else if (gpxFile instanceof Blob) {
      // Handle local files using FileReader
      const reader = new FileReader();
      reader.onload = (e) => {
        parseString(e.target.result, (err, result) => {
          if (err) {
            console.error('Error parsing GPX file:', err);
          } else {
            setGpxData(result); // Store parsed data
          }
        });
      };
      reader.onerror = (e) => {
        console.error('FileReader error:', e);
      };
      reader.readAsText(gpxFile);
    } else {
      console.error('Invalid file type:', gpxFile);
    }
  }, [gpxFile]);

  if (!gpxData) return <div>Loading...</div>;

  return (
    <pre style={{ whiteSpace: 'pre-wrap' }}>
      {JSON.stringify(gpxData, null, 2)} {/* Display as JSON */}
    </pre>
  );
};

export default GPXDataViewer;

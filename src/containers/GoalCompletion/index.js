/* eslint-disable react/jsx-no-bind */
/* eslint-disable no-shadow */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable no-param-reassign */
/* eslint-disable global-require */
/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import React, { useState } from 'react';
import { Col, Row, Divider, Select } from 'antd';
import { findIndex, isArray, isEmpty, isObject } from 'lodash';
import { useSelector } from 'react-redux';
import {
  FilterOutlined,
  PlusCircleOutlined,
  SwapOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import theme from '@chill/config/theme/default';
import TableWrapper from '@chill/assets/styles/AntTables.styles';
import IntlMessages from '@chill/components/utility/intlMessages';
import Popover from '@chill/components/uielements/popover';
import getApiData from '@chill/lib/helpers/apiHelper';
import Popconfirms from '@chill/components/Feedback/Popconfirm';
import Notification from '@chill/components/Notification';
import AddGoal from './goalForm';
import DeviceStyles from './styles';

/**
 *
 * @module GoalCompletion
 */
const DeviceManagement = () => {
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const brandLists = useSelector((state) => state.Auth.list);
  const [actionMenu, setActionMenu] = React.useState(false);
  const [isIndex, setIsIndex] = React.useState(null);
  const [state, setState] = React.useState({
    initObj: {},
    visible: false,
  });
  const [deviceState, setDeviceState] = React.useState({
    devices: [],
    deviceLoad: false,
  });
  const [filter, setFilter] = React.useState({ pagination: {}, filters: {} });
  const [filterMode, setFilterMode] = React.useState(false);
  const [sort, setSort] = React.useState(false);
  const userData = useSelector((state) => state.Auth.userData);
  const uData = isObject(userData) ? { ...userData } : {};
  const [selectedProduct, setSelectedProduct] = useState(null);
  const dWidth = window.innerWidth;

  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  const goal = [
    {
      id: 1,
      goal_title: messageArray.newusergrowth,
    },
    {
      id: 2,
      goal_title: messageArray.activeusergrowth,
    },
    {
      id: 3,
      goal_title: messageArray.productperusergrowth,
    },
    {
      id: 4,
      goal_title: messageArray.campaignsopen,
    },
    {
      id: 5,
      goal_title: messageArray.campaignsclicks,
    },
  ];

  // this function for get devices
  /** this function for get devices.
   * @function getDeviceList
   * @param {object} data sort
   */
  async function getDeviceList(data = {}) {
    setDeviceState((p) => ({
      ...p,
      deviceLoad: true,
    }));
    data.brand_id = selectedProduct;
    if (sort) {
      data.sort = 'ASC';
    } else {
      data.sort = 'DESC';
    }

    try {
      const response = await getApiData('goals/index', data, 'POST');
      if (response.success && isArray(response.data)) {
        setDeviceState((preState) => ({
          ...preState,
          devices: isArray(response.data) ? response.data : [],
          deviceLoad: false,
        }));
        const pagination =
          filter && filter.pagination ? { ...filter.pagination } : {};
        pagination.total = response.total_count || 0;
        pagination.pageSize = response.per_page || 10;
        pagination.current = response.page || 1;
        setFilter((f) => ({ ...f, pagination }));
      } else {
        setDeviceState((preState) => ({
          ...preState,
          deviceLoad: false,
        }));
      }
    } catch (err) {
      setDeviceState((preState) => ({
        ...preState,
        deviceLoad: false,
      }));
    }
  }

  React.useEffect(() => {
    const page =
      filter && filter.pagination && filter.pagination.current
        ? filter.pagination.current
        : 1;
    const flt = filter ? { ...filter } : {};
    getDeviceList({ page, ...flt.filters });
  }, [sort, selectedProduct]);

  React.useEffect(() => {
    if (!filterMode) {
      getDeviceList({});
    }
  }, [filterMode]);

  // this function filters data and get updated list of device
  /** this function filters data and get updated list of device
   * @function fetchDataFilters
   * @param {object} data page, Goal title, Time period, Status
   */
  function fetchDataFilters() {
    const page =
      filter && filter.pagination && filter.pagination.current
        ? filter.pagination.current
        : 1;
    const filters = filter && filter.filters ? filter.filters : {};
    getDeviceList({ page, ...filters });
  }

  // this function for delete devices
  /** this function for delete devices,  Admin can delete only
   * @function deleteDevice
   * @param {object} data goal_id
   */
  async function deleteDevice() {
    const data = {
      goal_id:
        isObject(state) &&
        isObject(state.initObj) &&
        !isEmpty(state.initObj) &&
        state.initObj.id,
    };
    const deviceAry = isArray(deviceState.devices) ? deviceState.devices : [];
    setDeviceState({
      deviceLoad: true,
    });
    try {
      const response = await getApiData('goals/delete', data, 'POST');
      let msgTitle = 'error';
      if (response.success) {
        msgTitle = 'success';
        fetchDataFilters();
      } else {
        setDeviceState((pre) => ({
          ...pre,
          devices: deviceAry,
          deviceLoad: false,
        }));
      }
      Notification(msgTitle, response.message);
    } catch (error) {
      console.log('error ===', error);
      setDeviceState((pre) => ({
        ...pre,
        devices: deviceAry,
        deviceLoad: false,
      }));
    }
  }

  // fun. for visible PopOver
  function handleVisible() {
    setActionMenu((visiblePre) => !visiblePre);
  }

  // this function for handle pagination
  /** this function for handle pagination
   * @function onChange
   * @param {object} data page
   */
  function onChange(pagination) {
    const pager = filter && filter.pagination ? { ...filter.pagination } : {};
    pager.current = pagination.current;
    setFilter((f) => ({ ...f, pagination: pager }));
    getDeviceList({
      page: pagination.current,
      ...filter.filters,
    });
    setSort(false);
  }

  // this function for update new goal data
  /** this function for update new goal data, Admin can update only
   * @function updateData
   * @param {object} data id
   */
  function updateData(data) {
    const deviceAry = isArray(deviceState.devices)
      ? [...deviceState.devices]
      : [];
    const edit = !isEmpty(state.initObj) && data.id;
    const dataIndex = findIndex(deviceAry, { id: data.id });
    if (edit && dataIndex > -1) {
      deviceAry[dataIndex] = data;
    } else {
      getDeviceList();
    }
    setDeviceState({
      devices: deviceAry,
    });
    setState({ initObj: {}, visible: false });
  }

  // Popover content or list
  const content = (
    <DeviceStyles>
      <div className="popMainDiv">
        <div
          role="button"
          className="isoDropdownLink"
          onKeyPress=""
          tabIndex="-1"
          onClick={() => {
            setState({ ...state, visible: true });
            setIsIndex(null);
          }}
        >
          <EditOutlined style={{ paddingRight: 12, fontSize: 16 }} />
          <IntlMessages id="edit.goal" />
        </div>
        <div className="isoDropdownLink">
          <Popconfirms
            title={<IntlMessages id="Sure to delete?" />}
            okText={<IntlMessages id="DELETE" />}
            cancelText={<IntlMessages id="No" />}
            onConfirm={() => {
              deleteDevice();
              setIsIndex(null);
            }}
            onCancel={null}
          >
            <DeleteOutlined style={{ paddingRight: 12, fontSize: 16 }} />
            <IntlMessages id="delete.goal" />
          </Popconfirms>
        </div>
      </div>
    </DeviceStyles>
  );

  // set item id in state
  function handleVisibleChange(item1) {
    setIsIndex(item1.id);
  }

  const columns = [
    {
      title: <IntlMessages id="No" />,
      dataIndex: 'id',
      rowKey: 'id',
      width: 10,
      className: 'fullname-cell',
      render: (text, data, index) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="No" />
            </span>
            <span className="mobile-lbl-val">{index + 1 || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="goal.goaltitle" />,
      dataIndex: 'goal_title',
      rowKey: 'goal_title',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="goal.goaltitle" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="goal.increseby" />,
      dataIndex: 'increase_by',
      rowKey: 'increase_by',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="goal.increseby" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="goal.timePeriod" />,
      dataIndex: 'time_period',
      rowKey: 'time_period',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="goal.timePeriod" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="goal.status" />,
      dataIndex: 'status',
      rowKey: 'status',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="goal.status" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    // {
    //   title: <IntlMessages id="Action" />,
    //   dataIndex: 'action',
    //   rowKey: 'action',
    //   className: 'fullname-cell',
    //   width: 50,
    //   render: (text, item) => {
    //     return (
    //       <div>
    //         <span className="label">
    //           <IntlMessages id="Action" />
    //         </span>
    //         <span className="mobile-lbl-val">
    //           <Popover
    //             content={content}
    //             trigger="click"
    //             visible={isIndex === item.id ? !actionMenu : false}
    //             onVisibleChange={handleVisible}
    //             arrowPointAtCenter
    //             placement="bottomRight"
    //           >
    //             <MoreOutlined
    //               onClick={() => {
    //                 handleVisibleChange(item);
    //                 setState({ ...state, initObj: item });
    //               }}
    //               style={{ color: '#000', fontSize: 24 }}
    //             />
    //           </Popover>
    //         </span>
    //       </div>
    //     );
    //   },
    // },
  ];

  const action = {
    title: <IntlMessages id="Action" />,
    dataIndex: 'action',
    rowKey: 'action',
    className: 'fullname-cell',
    width: 50,
    render: (text, item) => {
      return (
        <div>
          <span className="label">
            <IntlMessages id="Action" />
          </span>
          <span className="mobile-lbl-val">
            <Popover
              content={content}
              trigger="click"
              visible={isIndex === item.id ? !actionMenu : false}
              onVisibleChange={handleVisible}
              arrowPointAtCenter
              placement="bottomRight"
            >
              <MoreOutlined
                onClick={() => {
                  handleVisibleChange(item);
                  setState({ ...state, initObj: item });
                }}
                style={{ color: '#000', fontSize: 24 }}
              />
            </Popover>
          </span>
        </div>
      );
    },
  };

  if (
    uData.user_type === 'brand_admin' ||
    uData.user_type === 'manager' ||
    uData.user_type === 'marketing_manager'
  ) {
    columns.splice(5, 0, action);
  }

  // Add device View
  const addNew = () => {
    return (
      <DeviceStyles>
        <div className="iconStyle marketingMainDiv">
          <div>
            {uData.user_type === 'chillbaby_admin' ||
            uData.user_type === 'chillbaby_user' ? (
              <Select
                className="brandDropdown"
                style={{
                  width: dWidth <= 1000 ? '180px' : '200px',
                }}
                placeholder={<IntlMessages id="dropdown.product" />}
                allowClear
                onChange={(val) => setSelectedProduct(val)}
              >
                {isArray(brandLists) && brandLists.length > 0
                  ? brandLists.map((bl) => {
                      return (
                        <Select.Option value={bl.id}>
                          {bl.full_name}
                        </Select.Option>
                      );
                    })
                  : null}
              </Select>
            ) : null}
          </div>
          <span>
            <p
              className="addNewUser"
              style={{
                color: theme.colors.primaryColor,
                alignItems: 'center',
              }}
            >
              <SwapOutlined
                className="filterIcon1"
                style={{ color: sort ? theme.colors.primaryColor : '#000' }}
                onClick={() => {
                  setSort(!sort);
                }}
              />
              <FilterOutlined
                className="filterIcon"
                style={{
                  color: filterMode ? theme.colors.primaryColor : '#000',
                }}
                onClick={() => {
                  setFilterMode((pre) => !pre);
                  setFilter({ filters: {} });
                }}
              />
              {userData.user_type === 'chillbaby_admin' ||
              userData.user_type === 'chillbaby_user' ? null : (
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}
                  onClick={() => setState({ visible: true })}
                >
                  <p className="iconPadding" style={{ paddingRight: 8 }}>
                    <PlusCircleOutlined />
                  </p>
                  <IntlMessages id="goal.addGoal" />
                </div>
              )}
            </p>
          </span>
        </div>
      </DeviceStyles>
    );
  };

  // this function renders search view
  function renderSearchBar() {
    return (
      <div style={{ padding: '15px' }}>
        <Row gutter={10}>
          <Col lg={8} sm={24} md={12} xs={24}>
            <Select
              style={{ width: '100%' }}
              onChange={(val) => {
                const flt = filter ? { ...filter } : {};
                flt.filters.goal_title = val || '';
                setFilter(flt);
                getDeviceList(flt.filters);
              }}
              placeholder={messageArray['goal.goaltitle']}
              allowClear
            >
              {goal.map((item) => (
                <Select.Option key={`type${item.id}`} value={item.goal_title}>
                  {item.goal_title}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col lg={8} sm={24} md={12} xs={24}>
            <Select
              style={{ width: '100%' }}
              onChange={(val) => {
                const flt = filter ? { ...filter } : {};
                flt.filters.time_period = val || '';
                setFilter(flt);
                getDeviceList(flt.filters);
              }}
              placeholder={messageArray['goal.timePeriod']}
              allowClear
            >
              {['week', 'month', 'year'].map((item) => (
                <Select.Option key={`type${item}`} value={item}>
                  {item}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col lg={8} sm={24} md={12} xs={24}>
            <Select
              style={{ width: '100%' }}
              onChange={(val) => {
                const flt = filter ? { ...filter } : {};
                flt.filters.status = val || '';
                setFilter(flt);
                getDeviceList(flt.filters);
              }}
              placeholder={messageArray['goal.status']}
              allowClear
            >
              {['In Process', 'Completed', 'Not Completed'].map((item) => (
                <Select.Option key={`type${item}`} value={item}>
                  {item}
                </Select.Option>
              ))}
            </Select>
          </Col>
        </Row>
      </div>
    );
  }

  const { initObj, visible } = state;
  return (
    <DeviceStyles>
      <Row className="isoTabRow">
        <Col xl={8} lg={8} sm={24} md={6} xs={24}>
          <div className="marketingText">
            <IntlMessages id="sidebar.goalcompletion" />
          </div>
        </Col>
        <Col sm={24} xs={24} className="hiddenCol">
          {addNew()}
        </Col>
        <Col xl={16} lg={16} md={18} className="isoAddNew">
          {addNew()}
        </Col>
      </Row>
      <Divider className="horizontalDevider" />
      {filterMode ? renderSearchBar() : null}
      <TableWrapper
        loading={deviceState.deviceLoad}
        rowKey={(record) => record.id}
        dataSource={deviceState.devices}
        onChange={onChange}
        columns={columns}
        className="invoiceListTable"
        pagination={filter.pagination || {}}
        showSorterTooltip={false}
      />
      <AddGoal
        initialValues={initObj}
        goalTitle={goal}
        visible={visible}
        onClose={(type, data) => {
          if (type === 'success') {
            updateData(data);
          } else {
            setState({ initObj: {}, visible: false });
          }
        }}
      />
    </DeviceStyles>
  );
};

export default DeviceManagement;

/* eslint-disable react/jsx-no-bind */
/* eslint-disable dot-notation */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable no-nested-ternary */
/* eslint-disable global-require */
import React, { useEffect, useState } from 'react';
import { Col, Drawer, Row, Form, Select, InputNumber } from 'antd';
import { isEmpty } from 'lodash';
import { useSelector } from 'react-redux';
import IntlMessages from '@chill/components/utility/intlMessages';
import {
  BottomViewWrapper,
  FormWrapper,
} from '@chill/assets/styles/drawerFormStyles';
import CButton from '@chill/components/uielements/CButton';
import Notification from '@chill/components/Notification';
import getApiData from '@chill/lib/helpers/apiHelper';
import useResetFormOnClose from '@chill/lib/hooks/useResetForm';

function AddGoal(props) {
  const { initialValues, onClose, visible, goalTitle } = props;
  const edit = !isEmpty(initialValues);
  const initVal = initialValues || {};
  const [form] = Form.useForm();
  const [btnLoader, setBtnLoader] = useState(false);
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  const Period = [
    {
      period_name: messageArray['week'],
    },
    {
      period_name: messageArray['month'],
    },
    {
      period_name: messageArray['year'],
    },
  ];

  const Status = [
    {
      status_name: messageArray['inprocess'],
    },
    {
      status_name: messageArray['completed'],
    },
    {
      status_name: messageArray['notcompleted'],
    },
  ];

  useResetFormOnClose({ visible, initVal, form });

  useEffect(() => {
    if (!edit) {
      form.resetFields();
    }
  }, [visible]);

  // this function reset form data when close modal
  function handleForm(type = '', data = {}) {
    form.resetFields();
    onClose(type, data);
  }

  // this function for add new devices
  async function addDevice(values) {
    const url = edit ? 'goals/update' : 'goals/add-goal';

    try {
      const response = await getApiData(url, values, 'POST');
      if (response.success) {
        Notification('success', response.message);
        handleForm('success', response.data);
      } else {
        Notification('error', response.message);
      }
      setBtnLoader(false);
    } catch (error) {
      console.log(error);
      setBtnLoader(false);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  // this function for validate data
  function validate(values) {
    const valid = true;
    const obj = values;
    if (edit) obj.goal_id = initVal.id;
    if (valid) {
      setBtnLoader(true);
      addDevice(obj);
    }
  }

  return (
    <Drawer
      title={<IntlMessages id={edit ? 'edit.goal' : 'createnewgoal'} />}
      width={500}
      style={{ height: window.innerHeight - 60, marginTop: 70 }}
      onClose={handleForm}
      visible={visible}
      bodyStyle={{ paddingBottom: 80 }}
      destroyOnClose
    >
      <FormWrapper>
        <Form
          form={form}
          onFinish={validate}
          layout="vertical"
          initialValues={initVal}
        >
          <Row
            gutter={[12, 20]}
            style={{
              marginBottom: 20,
            }}
          >
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="goal.goaltitle" />}
                name="goal_title"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.goaltitle" />,
                  },
                ]}
              >
                <Select>
                  {goalTitle.map((item) => (
                    <Select.Option
                      key={`goal${item.id}`}
                      value={item.goal_title}
                    >
                      {item.goal_title}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="goal.increseby" />}
                name="increase_by"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.goalincrease" />,
                  },
                ]}
              >
                <InputNumber maxLength={3} min={0} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="goal.timePeriod" />}
                name="time_period"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.goalperiod" />,
                  },
                ]}
              >
                <Select>
                  {Period.map((item) => (
                    <Select.Option
                      key={`period${item.id}`}
                      value={item.period_name}
                    >
                      {item.period_name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            {edit ? (
              <Col xs={24}>
                <Form.Item
                  label={<IntlMessages id="goal.status" />}
                  name="status"
                >
                  <Select>
                    {Status.map((item) => (
                      <Select.Option
                        key={`status${item.id}`}
                        value={item.status_name}
                      >
                        {item.status_name}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            ) : null}
          </Row>
          <BottomViewWrapper className="bottomBtnWrapper">
            <Row gutter={12} style={{ width: '100%' }}>
              <Col xs={24}>
                <Form.Item className="btn-form">
                  <CButton
                    className="submitBtnStyle"
                    htmlType="submit"
                    loading={btnLoader}
                    disabled={btnLoader}
                    style={{ marginRight: 20 }}
                  >
                    <IntlMessages
                      id={edit ? 'common.changes' : 'common.submit'}
                    />
                  </CButton>
                </Form.Item>
              </Col>
              {/* <Form.Item className="btn-form">
                <div
                  className="cancelBtnStyle"
                  onClick={handleForm}
                  role="button"
                  onKeyPress=""
                  tabIndex="-1"
                >
                  <IntlMessages id="common.cancel" />
                </div>
              </Form.Item> */}
            </Row>
          </BottomViewWrapper>
        </Form>
      </FormWrapper>
    </Drawer>
  );
}

export default AddGoal;

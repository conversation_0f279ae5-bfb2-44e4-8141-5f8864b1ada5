/* eslint-disable react/jsx-no-bind */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable no-param-reassign */
/* eslint-disable global-require */
/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import React from 'react';
import { Col, Row, Divider, Input } from 'antd';
import { findIndex, isArray, isEmpty, isObject } from 'lodash';
import { useSelector } from 'react-redux';
import {
  FilterOutlined,
  PlusCircleOutlined,
  SwapOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import theme from '@chill/config/theme/default';
import TableWrapper from '@chill/assets/styles/AntTables.styles';
import IntlMessages from '@chill/components/utility/intlMessages';
import Popover from '@chill/components/uielements/popover';
import getApiData from '@chill/lib/helpers/apiHelper';
import Popconfirms from '@chill/components/Feedback/Popconfirm';
import Notification from '@chill/components/Notification';
import FAQForm from './FAQForm';
import FAQStyles from './FAQ.styles';

/**
 *
 * @module FAQ
 */

const FAQ = () => {
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const [actionMenu, setActionMenu] = React.useState(false);
  const [isIndex, setIsIndex] = React.useState(null);
  const [state, setState] = React.useState({
    initObj: {},
    visible: false,
  });
  const [faqState, setFAQState] = React.useState({
    faqs: [],
    faqLoad: false,
  });
  const [filter, setFilter] = React.useState({ pagination: {}, filters: {} });
  const [filterMode, setFilterMode] = React.useState(false);
  const [sort, setSort] = React.useState(false);

  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  // this function for get faq's
  /** this function for get faq's list
   * @function getFAQList
   * @param {object} data sort
   */
  async function getFAQList(data = {}) {
    setFAQState((p) => ({
      ...p,
      faqLoad: true,
    }));
    if (sort) {
      data.sort = 'ASC';
    } else {
      data.sort = 'DESC';
    }

    try {
      const response = await getApiData('brand_faqs/index', data, 'POST');
      if (response.success && isArray(response.data)) {
        setFAQState((preState) => ({
          ...preState,
          faqs: isArray(response.data) ? response.data : [],
          faqLoad: false,
        }));
        const pagination =
          filter && filter.pagination ? { ...filter.pagination } : {};
        pagination.total = response.total_count || 0;
        pagination.pageSize = response.per_page || 10;
        pagination.current = response.page || 1;
        setFilter((f) => ({ ...f, pagination }));
      } else {
        setFAQState((preState) => ({
          ...preState,
          faqLoad: false,
        }));
      }
    } catch (err) {
      setFAQState((preState) => ({
        ...preState,
        faqLoad: false,
      }));
    }
  }

  React.useEffect(() => {
    const page =
      filter && filter.pagination && filter.pagination.current
        ? filter.pagination.current
        : 1;
    const flt = filter ? { ...filter } : {};
    getFAQList({ page, ...flt.filters });
  }, [sort]);

  React.useEffect(() => {
    if (!filterMode) {
      getFAQList({});
    }
  }, [filterMode]);

  // this function filters data and get updated list of faq
  /** this function filters data and get updated list of faq
   * @function fetchDataFilters
   * @param {object} data page, title
   */
  function fetchDataFilters() {
    const page =
      filter && filter.pagination && filter.pagination.current
        ? filter.pagination.current
        : 1;
    const filters = filter && filter.filters ? filter.filters : {};
    getFAQList({ page, ...filters });
  }

  // this function for delete faq
  /** this function for delete faq
   * @function deleteFAQ
   * @param {object} data faq_id
   */
  async function deleteFAQ() {
    const data = {
      faq_id:
        isObject(state) &&
        isObject(state.initObj) &&
        !isEmpty(state.initObj) &&
        state.initObj.id,
    };
    const faqAry = isArray(faqState.faqs) ? faqState.faqs : [];
    setFAQState({
      faqLoad: true,
    });
    try {
      const response = await getApiData('brand_faqs/remove-faq', data, 'POST');
      let msgTitle = 'error';
      if (response.success) {
        msgTitle = 'success';
        fetchDataFilters();
      } else {
        setFAQState((pre) => ({
          ...pre,
          faqs: faqAry,
          faqLoad: false,
        }));
      }
      Notification(msgTitle, response.message);
    } catch (error) {
      console.log('error ===', error);
      setFAQState((pre) => ({
        ...pre,
        faqs: faqAry,
        faqLoad: false,
      }));
    }
  }

  // fun. for visible PopOver
  function handleVisible() {
    setActionMenu((visiblePre) => !visiblePre);
  }

  // this function for handle pagination
  /** this function for handle pagination
   * @function onChange
   * @param {object} data page
   */
  function onChange(pagination) {
    const pager = filter && filter.pagination ? { ...filter.pagination } : {};
    pager.current = pagination.current;
    setFilter((f) => ({ ...f, pagination: pager }));
    getFAQList({
      page: pagination.current,
      ...filter.filters,
    });
    setSort(false);
  }

  // this function for update new faq data
  /** this function for update new faq data
   * @function updateData
   * @param {object} data id
   */
  function updateData(data) {
    const faqAry = isArray(faqState.faqs) ? [...faqState.faqs] : [];
    const edit = !isEmpty(state.initObj) && data.id;
    const dataIndex = findIndex(faqAry, { id: data.id });
    if (edit && dataIndex > -1) {
      faqAry[dataIndex] = data;
    } else {
      getFAQList();
    }
    setFAQState({
      faqs: faqAry,
    });
    setState({ initObj: {}, visible: false });
  }

  // Popover content or list
  const content = (
    <FAQStyles>
      <div className="popMainDiv">
        <div
          role="button"
          className="isoDropdownLink"
          onKeyPress=""
          tabIndex="-1"
          onClick={() => {
            setState({ ...state, visible: true });
            setIsIndex(null);
          }}
        >
          <EditOutlined style={{ paddingRight: 12, fontSize: 16 }} />
          <IntlMessages id="faq.edit" />
        </div>
        <div className="isoDropdownLink">
          <Popconfirms
            title={<IntlMessages id="Sure to delete?" />}
            okText={<IntlMessages id="DELETE" />}
            cancelText={<IntlMessages id="No" />}
            onConfirm={() => {
              deleteFAQ();
              setIsIndex(null);
            }}
            onCancel={null}
          >
            <DeleteOutlined style={{ paddingRight: 12, fontSize: 16 }} />
            <IntlMessages id="faq.delete" />
          </Popconfirms>
        </div>
      </div>
    </FAQStyles>
  );

  // set item id in state
  function handleVisibleChange(item1) {
    setIsIndex(item1.id);
  }

  const columns = [
    {
      title: <IntlMessages id="No" />,
      dataIndex: 'id',
      rowKey: 'id',
      width: 10,
      className: 'fullname-cell',
      render: (text, data, index) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="No" />
            </span>
            <span className="mobile-lbl-val">{index + 1 || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="antTable.title" />,
      dataIndex: 'title',
      rowKey: 'title',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="antTable.title" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="device.description" />,
      dataIndex: 'description',
      rowKey: 'description',
      width: 280,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="device.description" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Action" />,
      dataIndex: 'action',
      rowKey: 'action',
      className: 'fullname-cell',
      width: 50,
      fixed: 'right',
      render: (text, item) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Action" />
            </span>
            <span className="mobile-lbl-val">
              <Popover
                content={content}
                trigger="click"
                visible={isIndex === item.id ? !actionMenu : false}
                onVisibleChange={handleVisible}
                arrowPointAtCenter
                placement="bottomRight"
              >
                <MoreOutlined
                  onClick={() => {
                    handleVisibleChange(item);
                    setState({ ...state, initObj: item });
                  }}
                  style={{ color: '#000', fontSize: 24 }}
                />
              </Popover>
            </span>
          </div>
        );
      },
    },
  ];

  // Add FAQ View
  const addNew = () => {
    return (
      <FAQStyles>
        <div className="iconStyle marketingMainDiv">
          <span>
            <p
              className="addNewUser"
              style={{
                color: theme.colors.primaryColor,
                alignItems: 'center',
              }}
            >
              <SwapOutlined
                className="filterIcon1"
                style={{ color: sort ? theme.colors.primaryColor : '#000' }}
                onClick={() => {
                  setSort(!sort);
                }}
              />
              <FilterOutlined
                className="filterIcon"
                style={{
                  color: filterMode ? theme.colors.primaryColor : '#000',
                }}
                onClick={() => {
                  setFilterMode((pre) => !pre);
                  setFilter({ filters: {} });
                }}
              />
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                }}
                onClick={() => setState({ visible: true })}
              >
                <p className="iconPadding" style={{ paddingRight: 8 }}>
                  <PlusCircleOutlined />
                </p>
                <IntlMessages id="faq.addFaq" />
              </div>
            </p>
          </span>
        </div>
      </FAQStyles>
    );
  };

  // this function renders search view
  function renderSearchBar() {
    return (
      <div style={{ padding: '15px' }}>
        <Row gutter={10}>
          <Col lg={8} sm={24} md={12} xs={24} style={{ marginBottom: 8 }}>
            <Input.Search
              style={{ minWidth: '100%' }}
              onChange={(e) => {
                const val = e && e.target ? e.target.value : '';
                const flt = filter ? { ...filter } : {};
                flt.filters.title = val;
                setFilter(flt);
                getFAQList(flt.filters);
              }}
              value={filter.title}
              allowClear
              placeholder={messageArray['common.title']}
            />
          </Col>
        </Row>
      </div>
    );
  }

  const { initObj, visible } = state;
  return (
    <FAQStyles>
      <Row className="isoTabRow">
        <Col xl={8} lg={8} sm={8} md={4} xs={8}>
          <div className="marketingText">
            <IntlMessages id="sidebar.faq" />
          </div>
        </Col>
        <Col sm={16} xs={16} className="hiddenCol">
          {addNew()}
        </Col>
        <Col xl={16} lg={16} md={20} xs={16} className="isoAddNew">
          {addNew()}
        </Col>
      </Row>
      <Divider className="horizontalDevider" />
      {filterMode ? renderSearchBar() : null}
      <TableWrapper
        loading={faqState.faqLoad}
        rowKey={(record) => record.id}
        dataSource={faqState.faqs}
        onChange={onChange}
        columns={columns}
        className="invoiceListTable"
        pagination={filter.pagination || {}}
        showSorterTooltip={false}
      />
      <FAQForm
        initialValues={initObj}
        visible={visible}
        onClose={(type, data) => {
          if (type === 'success') {
            updateData(data);
          } else {
            setState({ initObj: {}, visible: false });
          }
        }}
      />
    </FAQStyles>
  );
};

export default FAQ;

import styled from 'styled-components';
import WithDirection from '@chill/lib/helpers/rtl';

const ProductStyles = styled.div`
  .isoTabText {
    padding-left: 16px;
  }
  .isoTabRow {
    padding: 16px 16px 0px;
  }
  .isoTab > div > div::before {
    position: unset;
    border-bottom-color: red;
  }
  .isoTab > div > div > div > div > div {
    padding: 0px;
  }
  .isoTab > div > div > div > div {
    padding-bottom: 16px;
  }
  .isoTab > div > div > div > div > div {
    margin: 0px 46px 0px 0px;
    @media (min-width: 768px) and (max-width: 1024px) {
      margin: 0px 20px 0px 0px;
    }
  }
  .isoTab > div > div:nth-child(1) {
    margin: 0px;
  }
  .isoTab > div > div:nth-child(2) {
    display: none;
  }
  .horizontalDevider {
    margin: 0px;
  }
  .isoFilterView {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
  }
  .isoFilterIcon {
    display: flex;
    flex-direction: row;
  }
  .hiddenCol {
    padding-bottom: 16px;
    display: flex;
    justify-content: flex-end;
    @media (min-width: 767px) {
      display: none;
    }
  }
  .isoAddProduct {
    border-left: solid;
    border-left-width: 1px;
    border-left-color: #eee;
    @media (max-width: 767px) {
      display: none;
    }
  }
  .isoFilter1 {
    padding: 0px 16px;
    border-right: solid;
    border-right-width: 1px;
    border-right-color: #eee;
    @media (min-width: 768px) and (max-width: 1024px) {
      padding: 0px 7px;
    }
  }
  & .isoFilter2 {
    padding: 0px 16px;
    @media (min-width: 768px) and (max-width: 1024px) {
      padding: 0px 6px;
    }
  }
  .popMainDiv {
    display: flex;
    flex-direction: column;
    & < div < div < div {
      background-color: red;
    }
  }
  .isoDropdownLink {
    color: #000;
    padding: 8px 0px;
    cursor: pointer;
  }
  .chartCol {
    background-color: #fff;
    text-align: center;
    display: flex;
    flex-direction: column;
    padding-top: 16px;
    border-radius: 14px;
  }
  .iconStyle {
    display: flex;
    align-items: center;
    height: 100%;
    margin-bottom: 20px;
  }
  .marketingMainDiv {
    align-items: flex-start;
  }
  .filterIcon {
    padding-left: 12px;
    padding-right: 12px;
    color: rgb(0, 0, 0);
    margin: 0px 12px;
    border-left: 1px solid #eee;
    border-right: 1px solid #eee;
  }
  .filterIcon1 {
    transform: rotate(90deg);
  }
  .iconPadding {
    display: flex;
    padding-right: 7px;
    @media (max-width: 281px) {
      display: none;
    }
  }
  .marketingText {
    font-weight: 400;
    text-transform: uppercase;
  }
  .hiddenCol {
    padding-bottom: 16px;
    display: flex;
    justify-content: flex-end;
    @media (min-width: 767px) {
      display: none;
    }
  }
  .isoTab {
    display: flex;
    justify-content: center;
    @media (max-width: 281px) {
      paxdding-right: 16px;
    }
  }
  .isoTab > div > div::before {
    position: unset;
  }
  .isoTab > div > div > div > div > div {
    padding: 0px;
  }
  .isoTab > div > div > div > div {
    padding-bottom: 20px;
  }
  .isoTab > div > div:nth-child(1) {
    margin: 0px;
  }
  .isoTab > div > div:nth-child(2) {
    display: none;
  }
  .isoAddNew {
    display: flex;
    justify-content: flex-end;
    @media (max-width: 767px) {
      display: none;
    }
  }
  .addNewUser {
    display: flex;
    justify-content: flex-end;
    cursor: pointer;
  }
  .chartStyle > div > svg > g > g:nth-child(2) {
    display: none;
  }
  .chartStyle > div > svg > g > g:nth-child(3) > text {
    dominant-baseline: text-before-edge;
  }
  .secondCol {
    padding: 22px 12px !important;
    @media (max-width: 1023px) {
      padding: 22px 30px !important;
    }
  }
`;

export default WithDirection(ProductStyles);

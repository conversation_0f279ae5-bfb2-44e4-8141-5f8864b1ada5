import styled from 'styled-components';
import WithDirection from '@chill/lib/helpers/rtl';

const ConfirmDeleteModal = styled.div`
  .deleteModal {
    width: 100%;
    height: 175px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .warningImgdiv {
      width: 78px;
      height: 78px;
      position: absolute;
      top: -40px;
      left: 3%;
    }
    .warningDescDiv {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 39px;
      max-width: 541px;
      .warningDiv {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 7px;
        .warningText {
          font-family: Inter;
          font-size: 30px;
          font-weight: 500;
          line-height: 36.31px;
          letter-spacing: -0.035em;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #242731;
        }
        .warningDesc {
          font-family: Inter;
          font-size: 16px;
          font-weight: 400;
          line-height: 20.8px;
          letter-spacing: 0.02em;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #242731;
        }
      }
      .buttonDiv {
        width: 100%;
        display: flex;
        gap: 21px;
        align-items: center;
        .cancelBtn {
          width: 138px;
          height: 40px;
          gap: 0;
          border-radius: 8px;
          background: #04b8ff;
          border-color: #04b8ff;
          span {
            font-family: Inter;
            font-size: 14px;
            font-weight: 600;
            line-height: 16.94px;
            text-align: center;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #ffffff;
          }
        }
        .deleteBtn {
          width: 185px;
          height: 40px;
          border-radius: 8px;
          background: #97b6c5;
          border-color: #97b6c5;
          span {
            font-family: Inter;
            font-size: 14px;
            font-weight: 600;
            line-height: 16.94px;
            text-align: center;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #ffffff;
          }
        }
      }
    }
  }
`;

export default WithDirection(ConfirmDeleteModal);

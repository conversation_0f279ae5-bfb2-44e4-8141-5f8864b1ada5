/* eslint-disable react/jsx-no-bind */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable no-param-reassign */
/* eslint-disable global-require */
/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import React from 'react';
import {
  Col,
  Row,
  Tabs,
  Modal,
  Typography,
  Button,
  Pagination,
  Input,
  notification,
} from 'antd';
import { findIndex, isArray, isEmpty } from 'lodash';
import { useSelector } from 'react-redux';
import { SwapOutlined } from '@ant-design/icons';
import theme from '@chill/config/theme/default';
import TableWrapper from '@chill/assets/styles/AntTables.styles';
import IntlMessages from '@chill/components/utility/intlMessages';
import Popover from '@chill/components/uielements/popover';
import getApiData from '@chill/lib/helpers/apiHelper';
import Notification from '@chill/components/Notification';
import { TabPane } from '@chill/components/uielements/tabs';
import archivePng from '@chill/assets/images/sidebarIcons/archive.png';
import warningPng from '@chill/assets/images/warning.png';
import deleteIcon from '@chill/assets/images/delete-blue.png';
import SearchInput from '@chill/components/SearchInput';
import rightArrow from '@chill/assets/images/right-arrow.png';
import threeDots from '@chill/assets/images/three-dots.png';
import { MdArrowBackIosNew, MdArrowForwardIos } from 'react-icons/md';
import DeviceForm from './DeviceForm';
import DeviceStyles from './Device.styles';
import ConfirmDeleteModalStyles from './ConfirmDeleteModal.styles';

const { Text } = Typography;

/**
 *
 * @module DeviceManagement
 */
const DeviceManagement = () => {
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const [actionMenu, setActionMenu] = React.useState(false);
  const [isIndex, setIsIndex] = React.useState(null);
  const [state, setState] = React.useState({
    initObj: {},
    visible: false,
  });
  const [deviceState, setDeviceState] = React.useState({
    devices: [],
    deviceLoad: false,
  });
  const [isopenModal, setIsopenModal] = React.useState(false);
  const [deleteLoader, setDeleteLoader] = React.useState(false);

  const [filter, setFilter] = React.useState({ pagination: {}, filters: {} });
  console.log(`hello ~ file: index.js:48 ~ DeviceManagement ~ filter:`, filter);
  const [filterTab, setFilterTab] = React.useState('Live');
  const [filterMode, setFilterMode] = React.useState(false);
  console.log(
    `hello ~ file: index.js:50 ~ DeviceManagement ~ setFilterMode:`,
    setFilterMode,
  );
  const [sort, setSort] = React.useState(false);
  const [checkedArr, setCheckedArr] = React.useState({});
  const [gotoPage, setGotoPage] = React.useState(1);

  console.log(
    `hello ~ file: index.js:68 ~ DeviceManagement ~ checkedArr:`,
    checkedArr,
  );

  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  // this function for get devices
  /** this function for get devices
   * @function getDeviceList
   * @param {object} data sort || {}
   */
  async function getDeviceList(data = {}) {
    setDeviceState((p) => ({
      ...p,
      deviceLoad: true,
    }));
    if (sort) {
      data.sort = 'ASC';
    } else {
      data.sort = 'DESC';
    }

    if (filterTab === 'Archive') {
      data.is_archive = 1;
    }

    try {
      const response = await getApiData('getUserProductData', data, 'POST');
      if (response.success && isArray(response.data)) {
        setDeviceState((preState) => ({
          ...preState,
          devices: isArray(response.data) ? response.data : [],
          deviceLoad: false,
        }));
        const pagination =
          filter && filter.pagination ? { ...filter.pagination } : {};
        pagination.total = response.total_count || 0;
        pagination.pageSize = response.per_page || 10;
        pagination.current = response.page || 1;
        setFilter((f) => ({ ...f, pagination }));
      } else {
        setDeviceState((preState) => ({
          ...preState,
          deviceLoad: false,
        }));
      }
    } catch (err) {
      setDeviceState((preState) => ({
        ...preState,
        deviceLoad: false,
      }));
    }
  }

  /**
   * Function to delete multiple rows from the device list table
   * @function deleteRows
   */
  async function deleteRows() {
    // check if the user has selected any rows to delete
    if (isEmpty(checkedArr)) {
      // if not, show an info notification
      notification.open({
        type: 'info',
        message: 'Please check which rows you can delete.',
      });
    } else {
      // if yes, proceed to delete the selected rows
      try {
        // set the delete loader to true
        setDeleteLoader(true);
        // call the API to delete the selected rows
        const response = await getApiData(
          'brand_devices/multiple-devices-remove',
          {
            device_id: checkedArr?.rowKeys,
          },
          'POST',
        );
        // if the response is successful
        if (response.success) {
          // call the getDeviceList function to refresh the table
          getDeviceList();
          // close the confirm modal
          setIsopenModal(false);
          // show a success notification
          notification.open({
            type: 'success',
            message: response?.message,
          });
        } else {
          // if the response is not successful, show an error notification
          notification.open({
            type: 'error',
            message: 'Something went wrong',
          });
        }
      } catch (err) {
        // if there is an error, show an error notification
        notification.open({
          type: 'error',
          message: 'Something went wrong',
        });
      } finally {
        // reset the delete loader to false
        setDeleteLoader(false);
      }
    }
  }

  React.useEffect(() => {
    const page =
      filter && filter.pagination && filter.pagination.current
        ? filter.pagination.current
        : 1;
    const flt = filter ? { ...filter } : {};
    getDeviceList({ page, ...flt.filters });
  }, [sort, filterTab]);

  React.useEffect(() => {
    if (!filterMode) {
      getDeviceList({});
    }
  }, [filterMode]);

  // this function filters data and get updated list of device
  /** this function filters data and get updated list of device
   * @function fetchDataFilters
   * @param {object} data page
   */
  function fetchDataFilters() {
    const page =
      filter && filter.pagination && filter.pagination.current
        ? filter.pagination.current
        : 1;
    const filters = filter && filter.filters ? filter.filters : {};
    getDeviceList({ page, ...filters });
  }

  // this function for delete devices
  /** this function for delete devices, Only admin can delete
   * @function deleteDevice
   * @param {object} data device_id
   */
  async function archiveDevice(item) {
    const data = {
      up_id: item.id,
    };
    const deviceAry = isArray(deviceState.devices) ? deviceState.devices : [];
    setDeviceState({
      deviceLoad: true,
    });
    try {
      const response = await getApiData(
        'archiveUnarchiveUserProduct',
        data,
        'POST',
      );
      let msgTitle = 'error';
      if (response.success) {
        msgTitle = 'success';
        fetchDataFilters();
      } else {
        setDeviceState((pre) => ({
          ...pre,
          devices: deviceAry,
          deviceLoad: false,
        }));
      }
      Notification(msgTitle, response.message);
    } catch (error) {
      console.log('error ===', error);
      setDeviceState((pre) => ({
        ...pre,
        devices: deviceAry,
        deviceLoad: false,
      }));
    }
  }

  // fun. for visible PopOver
  function handleVisible() {
    setActionMenu((visiblePre) => !visiblePre);
  }

  // this function for handle pagination
  function onChange(pagination) {
    console.log(
      `hello ~ file: index.js:193 ~ onChange ~ pagination:`,
      pagination,
    );
    // const pager = filter && filter.pagination ? { ...filter.pagination } : {};
    // pager.current = pagination.current;
    // setFilter((f) => ({ ...f, pagination: pager }));
    // getDeviceList({
    //   page: pagination.current,
    //   ...filter.filters,
    // });

    const pager = filter && filter.pagination ? { ...filter.pagination } : {};
    pager.current = pagination;
    setFilter((f) => ({ ...f, pagination: { ...pager } }));
    getDeviceList({
      page: pagination,
      ...filter.filters,
    });
    setSort(false);
  }

  // this function for update new device data
  /** this function for update new device data, Only Admin can edit
   * @function updateData
   * @param {object} data id
   */
  function updateData(data) {
    const deviceAry = isArray(deviceState.devices)
      ? [...deviceState.devices]
      : [];
    const edit = !isEmpty(state.initObj) && data.id;
    const dataIndex = findIndex(deviceAry, { id: data.id });
    if (edit && dataIndex > -1) {
      deviceAry[dataIndex] = data;
    } else {
      getDeviceList();
    }
    setDeviceState({
      devices: deviceAry,
    });
    setState({ initObj: {}, visible: false });
  }

  // Popover content or list
  const content = (e) => (
    <DeviceStyles>
      <div className="popMainDiv">
        <div
          className="isoDropdownLink"
          onClick={() => {
            archiveDevice(e);
            handleVisible();
          }}
        >
          <img
            className="iconImg"
            src={archivePng}
            style={{ width: 16, height: 16, marginRight: 12 }}
            alt="noIcon"
          />
          {filterTab === 'Archive' ? (
            <IntlMessages id="action.unarchive" />
          ) : (
            <IntlMessages id="action.archive" />
          )}
        </div>
      </div>
    </DeviceStyles>
  );

  // set item id in state
  function handleVisibleChange(item1) {
    setIsIndex(item1.id);
  }

  const columns = [
    {
      title: <IntlMessages id="No" />,
      dataIndex: 'id',
      rowKey: 'id',
      width: 10,
      className: 'fullname-cell',
      render: (text, data, index) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="No" />
            </span>
            <span
              className="mobile-lbl-val"
              style={{
                fontFamily: 'Inter',
                fontSize: 16,
                fontWeight: 400,
                lineHeight: '24px',
                textAlign: 'left',
                textUnderlinePosition: 'from-font',
                textDecorationSkipInk: 'none',
                color: '#242731',
              }}
            >
              {index + 1 || '-'}
            </span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="deviceData.name" />,
      dataIndex: 'customer_name',
      rowKey: 'customer_name',
      width: 180,
      className: 'fullname-cell',
      sorter: (a, b) => a.customer_name.localeCompare(b.customer_name),
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="deviceData.name" />
            </span>

            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="deviceData.email" />,
      dataIndex: 'customer_email',
      rowKey: 'customer_email',
      width: 280,
      className: 'fullname-cell',
      sorter: (a, b) => a.customer_email.localeCompare(b.customer_email),
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="deviceData.email" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="deviceData.bleSsid" />,
      dataIndex: 'device_ssid',
      rowKey: 'device_ssid',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="deviceData.bleSsid" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
            {/* <span className="mobile-lbl-val">Baby_Auto13472</span> */}
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="deviceData.macAddress" />,
      dataIndex: 'product_id',
      rowKey: 'product_id',
      width: 60,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="deviceData.macAddress" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="deviceData.mfgDate" />,
      dataIndex: 'mf_date',
      rowKey: 'mf_date',
      width: 60,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="deviceData.mfgDate" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="deviceData.serialNumber" />,
      dataIndex: 'sr_number',
      rowKey: 'sr_number',
      width: 60,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="deviceData.serialNumber" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="deviceData.productName" />,
      dataIndex: 'product_name',
      rowKey: 'product_name',
      width: 60,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="deviceData.productName" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="deviceData.productModal" />,
      dataIndex: 'product_model',
      rowKey: 'product_model',
      width: 60,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="deviceData.productModal" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="deviceData.firmwareVersion" />,
      dataIndex: 'version',
      rowKey: 'version',
      width: 60,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="deviceData.firmwareVersion" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Action" />,
      dataIndex: 'action',
      rowKey: 'action',
      className: 'fullname-cell',
      width: 50,
      fixed: 'right',
      render: (text, item) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Action" />
            </span>
            <span className="mobile-lbl-val">
              <Popover
                content={() => content(item)}
                trigger="click"
                open={isIndex === item.id ? !actionMenu : false}
                onOpenChange={handleVisible}
                arrowPointAtCenter
                placement="bottomRight"
              >
                <Button
                  type="text"
                  onClick={() => {
                    handleVisibleChange(item);
                    setState({ ...state, initObj: item });
                  }}
                  style={{
                    backgroundColor: 'transparent',
                    border: 'none',
                    padding: 0,
                  }}
                >
                  <img
                    src={threeDots}
                    alt="actionIcon"
                    style={{ fontSize: 16 }}
                  />
                </Button>
                {/* <MoreOutlined
                  onClick={() => {
                    handleVisibleChange(item);
                    setState({ ...state, initObj: item });
                  }}
                  style={{ color: '#000', fontSize: 24 }}
                /> */}
              </Popover>
            </span>
          </div>
        );
      },
    },
  ];

  // Add device View
  const addNew = () => {
    return (
      <DeviceStyles>
        <div className="iconStyle marketingMainDiv">
          <div
            className="addNewUser"
            style={{
              color: theme.colors.primaryColor,
              alignItems: 'center',
              marginTop: '13px',
            }}
          >
            <SwapOutlined
              className="filterIcon1"
              style={{
                fontSize: 24,
                color: sort ? theme.colors.primaryColor : '#000',
              }}
              onClick={() => {
                setSort(!sort);
              }}
              size={24}
            />
            {/* <FilterOutlined
                className="filterIcon"
                style={{
                  color: filterMode ? theme.colors.primaryColor : '#000',
                }}
                onClick={() => {
                  setFilterMode((pre) => !pre);
                  setFilter({ filters: {} });
                }}
              /> */}
          </div>
        </div>
      </DeviceStyles>
    );
  };

  // this function renders search view
  function renderSearchBar() {
    return (
      <div className="inputSearch">
        <Row gutter={10}>
          <Col xs={23}>
            <SearchInput
              onChange={(e) => {
                const val = e && e.target ? e.target.value : '';
                const flt = filter ? { ...filter } : {};
                flt.filters.search_value = val;
                setFilter(flt);

                getDeviceList(flt.filters);
              }}
              value={filter.search_value}
              allowClear
              placeholder={messageArray['common.Search']}
            />
          </Col>
          <Col
            xs={1}
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            {addNew()}
          </Col>
          {/* <Col lg={6} sm={24} md={12} xs={24} style={{ marginBottom: 8 }}>
            <Input.Search
              style={{ minWidth: '100%' }}
              onChange={(e) => {
                const val = e && e.target ? e.target.value : '';
                const flt = filter ? { ...filter } : {};
                flt.filters.customer_email = val;
                setFilter(flt);
                getDeviceList(flt.filters);
              }}
              value={filter.customer_email}
              allowClear
              placeholder={messageArray['deviceData.email']}
            />
          </Col>
          <Col lg={6} sm={24} md={12} xs={24} style={{ marginBottom: 8 }}>
            <Input.Search
              style={{ minWidth: '100%' }}
              onChange={(e) => {
                const val = e && e.target ? e.target.value : '';
                const flt = filter ? { ...filter } : {};
                flt.filters.device_ssid = val;
                setFilter(flt);
                getDeviceList(flt.filters);
              }}
              value={filter.device_ssid}
              allowClear
              placeholder={messageArray['deviceData.bleSsid']}
            />
          </Col>
          <Col lg={6} sm={24} md={12} xs={24} style={{ marginBottom: 8 }}>
            <Input.Search
              style={{ minWidth: '100%' }}
              onChange={(e) => {
                const val = e && e.target ? e.target.value : '';
                const flt = filter ? { ...filter } : {};
                flt.filters.device_name = val;
                setFilter(flt);
                getDeviceList(flt.filters);
              }}
              value={filter.device_name}
              allowClear
              placeholder={messageArray['deviceData.productName']}
            />
          </Col> */}
        </Row>
      </div>
    );
  }

  const onCloseDeleteModal = () => {
    setIsopenModal(false);
  };

  /**
   * A function that gets called when the user selects a row in the table
   * @param {array} selectedRowKeys - an array of the row keys that the user has selected
   * @param {array} selectedRows - an array of the rows that the user has selected
   */
  const rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      /**
       * Create a comma separated string of the selected row keys
       * @type {string}
       */
      const arr = selectedRows?.map((e) => e?.id).join(',');
      console.log(`hello ~ file: index.js:640 ~ DeviceManagement ~ arr:`, arr);
      /**
       * Set the state of the checkedArr object
       * @type {object}
       */
      setCheckedArr((p) => ({
        ...p,
        rowKeys: arr,
        rowarr: selectedRows,
      }));
      console.log(
        `selectedRowKeys: ${selectedRowKeys}`,
        'selectedRows: ',
        selectedRows,
      );
    },
  };

  const { initObj, visible, view } = state;
  return (
    <DeviceStyles>
      <Row className="main-content" gutter={[0, 5]}>
        <Col xs={24} className="title-col">
          <div className="titleText">
            <IntlMessages id="sidebar.deviceData" />
          </div>
        </Col>
        {/* <Col sm={16} xs={16} className="hiddenCol">
          {addNew()}
        </Col> */}
        <Col
          xs={24}
          className="search-col"
          style={{
            marginBottom: '8px',
          }}
        >
          <Tabs
            className="deviceTabs"
            defaultActiveKey="Live"
            onChange={(key) => setFilterTab(key)}
          >
            <TabPane tab={<IntlMessages id="tab.live" />} key="Live" />
            <TabPane tab={<IntlMessages id="tab.archive" />} key="Archive" />
          </Tabs>
        </Col>

        <Col
          xs={24}
          className="search-col"
          style={{
            marginBottom: '13px',
          }}
        >
          {renderSearchBar()}
        </Col>
        <Col xs={24} className="table-col">
          <TableWrapper
            loading={deviceState.deviceLoad}
            rowKey={(record) => record.id}
            dataSource={deviceState.devices}
            onChange={onChange}
            columns={columns}
            className="invoiceListTable"
            // pagination={filter.pagination || {}}
            pagination={false}
            showSorterTooltip={false}
            rowSelection={{
              type: 'checkbox',
              ...rowSelection,
            }}
          />
          <div className="table-pagnation">
            <Button
              type="text"
              className="removeRowBtn"
              onClick={() => {
                console.log(
                  `hello ~ file: index.js:729 ~ DeviceManagement ~ isEmpty(checkedArr):`,
                  isEmpty(checkedArr),
                );
                if (isEmpty(checkedArr)) {
                  notification.open({
                    type: 'info',
                    message: 'Please check which rows you can delete.',
                  });
                } else {
                  setIsopenModal(true);
                }
              }}
            >
              Remove Row(s) <img src={deleteIcon} alt="delete" />
            </Button>
            <Pagination
              defaultCurrent={filter?.pagination?.current}
              current={filter?.pagination?.current}
              onChange={(pgnation) => {
                onChange(pgnation);
              }}
              pageSize={filter?.pagination?.pageSize || 10}
              total={filter?.pagination?.total}
              showSizeChanger={false}
              showLessItems
              className="tabel-custom-pagination"
              nextIcon={<MdArrowForwardIos />}
              prevIcon={<MdArrowBackIosNew />}
              // showLessItems={wWidth <= 425}
            />
            <div className="gotoPagediv">
              <Text className="goToText">Go to page</Text>
              <Input
                type="number"
                min={1}
                max={filter?.pagination?.total}
                defaultValue={filter?.pagination?.current}
                onChange={(e) => {
                  const val = e && e.target ? Number(e.target.value) : '';
                  setGotoPage(val);
                }}
                className="gotoInput"
              />
              <Button
                type="text"
                onClick={() => {
                  const pager =
                    filter && filter.pagination ? { ...filter.pagination } : {};
                  pager.current = gotoPage;
                  setFilter((f) => ({ ...f, pagination: { ...pager } }));
                  getDeviceList({
                    page: gotoPage,
                    ...filter.filters,
                  });
                }}
                style={{
                  paddingLeft: '12px',
                }}
              >
                <img src={rightArrow} alt="arrow" />
              </Button>
            </div>
          </div>
        </Col>
      </Row>
      <DeviceForm
        initialValues={initObj}
        visible={visible}
        view={view}
        onClose={(type, data) => {
          if (type === 'success') {
            updateData(data);
          } else {
            setState({ initObj: {}, visible: false });
          }
        }}
      />
      <Modal
        open={isopenModal}
        width={740}
        onCancel={() => {
          onCloseDeleteModal();
        }}
        footer={false}
        closable={false}
        className="confirmDeleteModal"
      >
        <ConfirmDeleteModalStyles>
          <div className="deleteModal">
            <div className="warningImgdiv">
              <img src={warningPng} alt="warningPng" />
            </div>
            <div className="warningDescDiv">
              <div className="warningDiv">
                <Text className="warningText">Warning!</Text>
                <Text className="warningDesc">
                  You’re about to delete {checkedArr?.rowarr?.length} rows, this
                  may cause loss of important data!
                </Text>
              </div>
              <div className="buttonDiv">
                <Button
                  className="cancelBtn"
                  onClick={() => {
                    onCloseDeleteModal();
                  }}
                >
                  Cancel
                </Button>
                <Button
                  className="deleteBtn"
                  loading={deleteLoader}
                  onClick={() => deleteRows()}
                >
                  Confirm Deletion
                </Button>
              </div>
            </div>
          </div>
        </ConfirmDeleteModalStyles>
      </Modal>
    </DeviceStyles>
  );
};

export default DeviceManagement;

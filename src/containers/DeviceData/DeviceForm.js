/* eslint-disable react/jsx-no-bind */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable no-nested-ternary */
/* eslint-disable global-require */
import React, { useEffect, useState } from 'react';
import { Col, Drawer, Input, Row, Upload, Form, Icon, Select } from 'antd';
import { isArray, isEmpty } from 'lodash';
import { useSelector } from 'react-redux';
import IntlMessages from '@chill/components/utility/intlMessages';
import {
  BottomViewWrapper,
  FormWrapper,
} from '@chill/assets/styles/drawerFormStyles';
import CButton from '@chill/components/uielements/CButton';
import { getBase64 } from '@chill/lib/helpers/utility';
import Notification from '@chill/components/Notification';
import getApiData from '@chill/lib/helpers/apiHelper';
import useResetFormOnClose from '@chill/lib/hooks/useResetForm';

const { TextArea } = Input;

function DeviceForm(props) {
  const { initialValues, onClose, visible, view } = props;
  const edit = !isEmpty(initialValues);
  const initVal = initialValues || {};
  const [form] = Form.useForm();
  const [btnLoader, setBtnLoader] = useState(false);
  const [deviceImg, setDeviceImg] = useState({});
  const [selectedImg, setSelectedImg] = useState({});
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);
  const brandLists = useSelector((state) => state.Auth.list);

  useResetFormOnClose({ visible, initVal, form });

  useEffect(() => {
    if (!edit) {
      form.resetFields();
    }
  }, [visible]);

  const beforeUpload = (file) => {
    const validType =
      file.type === 'image/jpg' ||
      file.type === 'image/jpeg' ||
      file.type === 'image/png';

    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      Notification('error', messageArray['err.size']);
      return false;
    }

    if (!validType) {
      Notification('error', messageArray['common.img.upload']);
      return false;
    }

    getBase64(file, (imageUrl) => {
      setDeviceImg({ imageUrl, loading: false });
    });

    setSelectedImg(file);
    console.log('file', file);

    return false;
  };

  // this function reset form data when close modal
  function handleForm(type = '', data = {}) {
    form.resetFields();
    setDeviceImg({});
    setSelectedImg({});
    onClose(type, data);
  }

  // this function for add new devices
  async function addDevice(values) {
    const formData = new FormData();
    const url = edit ? 'brand_devices/update' : 'brand_devices/add-device';
    Object.keys(values).map((k) => formData.append(k, values[k]));
    if (!isEmpty(selectedImg)) {
      formData.append('device_image', selectedImg);
    }
    try {
      const response = await getApiData(url, formData, 'POST', {}, true);
      if (response.success) {
        Notification('success', response.message);
        handleForm('success', response.data);
      } else {
        Notification('error', response.message);
      }
      setBtnLoader(false);
    } catch (error) {
      console.log(error);
      setBtnLoader(false);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  // this function for validate data
  function validate(values) {
    let valid = true;

    const obj = values;

    // obj.device_image = initVal.device_image || '';
    if (isEmpty(selectedImg)) {
      obj.device_image = initVal.device_image || '';
      // obj.device_image = {
      //   uri: selectedImg,
      //   type: 'img',
      // };
    }

    if (!edit && isEmpty(selectedImg)) {
      Notification('error', messageArray['common.uploadImg']);
      valid = false;
    }

    if (edit) obj.device_id = initVal.id;

    if (valid) {
      setBtnLoader(true);
      addDevice(obj);
    }
  }

  const uploadButton = (
    <div>
      <Icon type="plus" />
      <div className="ant-upload-text">
        <IntlMessages id="common.upload" />
      </div>
    </div>
  );

  return (
    <Drawer
      title={<IntlMessages id={edit ? 'device.edit' : 'device.addDevice'} />}
      width={500}
      style={{ height: window.innerHeight - 60, marginTop: 70 }}
      onClose={handleForm}
      visible={visible}
      bodyStyle={{ paddingBottom: 80 }}
      destroyOnClose
    >
      <FormWrapper>
        <Form
          form={form}
          onFinish={validate}
          layout="vertical"
          initialValues={initVal}
        >
          <Row
            gutter={[12, 20]}
            style={{
              marginBottom: 20,
            }}
          >
            <Col>
              <Form.Item>
                <Upload
                  showUploadList={false}
                  listType="picture-card"
                  beforeUpload={beforeUpload}
                  disabled={!!view}
                >
                  {deviceImg.imageUrl || initVal.device_image ? (
                    <img
                      src={deviceImg.imageUrl || initVal.device_image}
                      alt="product"
                      style={{ width: '100%', height: '100%' }}
                    />
                  ) : (
                    uploadButton
                  )}
                </Upload>
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="device.nametitle" />}
                name="device_name"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.devicename" />,
                  },
                  {
                    max: 50,
                    message: <IntlMessages id="err.max.50char" />,
                  },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="device.bluetoothname" />}
                name="device_bluetooth_name"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.bluetoothname" />,
                  },
                  {
                    max: 50,
                    message: <IntlMessages id="err.max.50char" />,
                  },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="input.desc.label" />}
                name="device_description"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.description" />,
                  },
                ]}
              >
                <TextArea autoSize={{ maxRows: 5 }} />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="BRAND NAME" />}
                name="brand_id"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.brand" />,
                  },
                ]}
              >
                <Select
                  disabled={!!edit}
                  className="brandDropdown"
                  placeholder={<IntlMessages id="dropdown.product" />}
                >
                  {isArray(brandLists) && brandLists.length > 0
                    ? brandLists.map((item) => {
                        return (
                          <Select.Option
                            key={`brand${item.id}`}
                            value={item.id}
                          >
                            {item.full_name}
                          </Select.Option>
                        );
                      })
                    : null}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <BottomViewWrapper className="bottomBtnWrapper">
            <Row gutter={12} style={{ width: '100%' }}>
              {view ? null : (
                <Col xs={24}>
                  <Form.Item className="btn-form">
                    <CButton
                      className="submitBtnStyle"
                      htmlType="submit"
                      loading={btnLoader}
                      disabled={btnLoader}
                      style={{ marginRight: 20 }}
                    >
                      <IntlMessages
                        id={edit ? 'common.changes' : 'common.submit'}
                      />
                    </CButton>
                  </Form.Item>
                </Col>
              )}
              {/* <Form.Item className="btn-form">
                <div
                  className="cancelBtnStyle"
                  onClick={handleForm}
                  role="button"
                  onKeyPress=""
                  tabIndex="-1"
                >
                  <IntlMessages id={view ? 'common.done' : 'common.cancel'} />
                </div>
              </Form.Item> */}
            </Row>
          </BottomViewWrapper>
        </Form>
      </FormWrapper>
    </Drawer>
  );
}

export default DeviceForm;

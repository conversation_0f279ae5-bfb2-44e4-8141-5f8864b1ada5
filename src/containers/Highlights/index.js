/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
import React, { useEffect } from 'react';
import { Col, Row, Divider } from 'antd';
import { cloneDeep, isArray, isEmpty, isObject } from 'lodash';
import {
  PlusCircleOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import theme from '@chill/config/theme/default';
import TableWrapper from '@chill/assets/styles/AntTables.styles';
import IntlMessages from '@chill/components/utility/intlMessages';
import Popover from '@chill/components/uielements/popover';
import getApiData from '@chill/lib/helpers/apiHelper';
import Popconfirms from '@chill/components/Feedback/Popconfirm';
import Notification from '@chill/components/Notification';
import moment from 'moment';
import { MdOutlineCancel } from 'react-icons/md';
import HighlightsForm from './HighlightsForm';
import HighlightsStyles from './Highlights.styles';

/**
 *
 * @module Highlights
 */

const Highlights = () => {
  const [actionMenu, setActionMenu] = React.useState(false);
  const [isIndex, setIsIndex] = React.useState(null);
  const [state, setState] = React.useState({
    initObj: {},
    visible: false,
  });
  const [highlightsState, setHighlightsState] = React.useState({
    highlightss: [],
    highlightsLoad: false,
  });
  const [filter, setFilter] = React.useState({ pagination: {}, filters: {} });
  const [sort, setSort] = React.useState(false);
  const [currentPage, setCurrentPage] = React.useState(0);

  // Array of highlight type options
  const highlightTypeArr = [
    { id: 'distance', type: 'Distance' },
    { id: 'co2_equivalent', type: 'CO2 Equivalent' },
    { id: 'altitude', type: 'Altitude' },
    { id: 'average_speed', type: 'Average Speed' },
    { id: 'time_active', type: 'Time Active' },
    { id: 'increase', type: 'Increase' },
  ];

  // this function for get highlights's
  /** this function for get highlights's list
   * @function getHighlightsList
   * @param {object} data sort
   */
  async function getHighlightsList(data = {}) {
    setHighlightsState((p) => ({
      ...p,
      highlightsLoad: true,
    }));

    const url = 'manage-highlight/index';

    try {
      const response = await getApiData(url, data);
      if (response.success && isArray(response.data)) {
        setHighlightsState((preState) => ({
          ...preState,
          highlightss: isArray(response.data) ? response.data : [],
          highlightsLoad: false,
        }));
        const pagination =
          filter && filter.pagination ? { ...filter.pagination } : {};
        pagination.total = response.total_count || 0;
        pagination.pageSize = response.per_page || 10;
        pagination.current = response.page || 1;
        setFilter((f) => ({ ...f, pagination }));
      } else {
        setHighlightsState((preState) => ({
          ...preState,
          highlightsLoad: false,
        }));
      }
    } catch (err) {
      setHighlightsState((preState) => ({
        ...preState,
        highlightsLoad: false,
      }));
    }
  }

  // this function filters data and get updated list of highlights
  /** this function filters data and get updated list of highlights
   * @function fetchDataFilters
   * @param {object} data page, title
   */
  function fetchDataFilters() {
    const page =
      filter && filter.pagination && filter.pagination.current
        ? filter.pagination.current
        : 1;
    const filters = filter && filter.filters ? filter.filters : {};
    getHighlightsList({ page, ...filters });
  }

  useEffect(() => {
    fetchDataFilters();
  }, [sort]);

  // this function for delete highlights
  /** this function for delete highlights
   * @function deleteHighlights
   * @param {object} data highlights_id
   */
  async function deleteHighlights() {
    const data = {
      highlight_id:
        isObject(state) &&
        isObject(state.initObj) &&
        !isEmpty(state.initObj) &&
        state.initObj.id,
    };
    const highlightsAry = isArray(highlightsState.highlightss)
      ? highlightsState.highlightss
      : [];
    setHighlightsState({
      highlightsLoad: true,
    });
    try {
      const response = await getApiData(
        'manage-highlight/delete-highlight',
        data,
        'POST',
      );
      let msgTitle = 'error';
      if (response.success) {
        msgTitle = 'success';
        fetchDataFilters();
      } else {
        setHighlightsState((pre) => ({
          ...pre,
          highlightss: highlightsAry,
          highlightsLoad: false,
        }));
      }
      Notification(msgTitle, response.message);
    } catch (error) {
      console.log('error ===', error);
      setHighlightsState((pre) => ({
        ...pre,
        highlightss: highlightsAry,
        highlightsLoad: false,
      }));
    }
  }

  async function cancelHighlights(valId) {
    const data = {
      highlight_id: valId,
    };

    try {
      const response = await getApiData(
        'manage-highlight/cancel-highlight',
        data,
        'POST',
      );
      let msgTitle = 'error';
      if (response.success) {
        msgTitle = 'success';
        fetchDataFilters();
      }
      Notification(msgTitle, response.message);
    } catch (error) {
      console.log('error ===', error);
      Notification('error', 'Something went wrong');
    }
  }

  // fun. for visible PopOver
  function handleVisible() {
    setActionMenu((visiblePre) => !visiblePre);
  }

  // this function for handle pagination
  /** this function for handle pagination
   * @function onChange
   * @param {object} data page
   */
  function onChange(pagination) {
    const pager = filter && filter.pagination ? { ...filter.pagination } : {};
    setCurrentPage((pagination.current - 1) * pagination.pageSize);
    pager.current = pagination.current;
    setFilter((f) => ({ ...f, pagination: pager }));
    getHighlightsList({
      page: pagination.current,
      ...filter.filters,
    });
    setSort(false);
  }

  // Popover content or list
  const content = (item) => (
    <HighlightsStyles>
      <div className="popMainDiv">
        <div
          role="button"
          className="isoDropdownLink"
          onKeyPress=""
          tabIndex="-1"
          onClick={() => {
            setState({ ...state, visible: true });
            setIsIndex(null);
          }}
        >
          <EditOutlined style={{ paddingRight: 12, fontSize: 16 }} />
          <IntlMessages id="highlights.edit" />
        </div>
        <div
          role="button"
          className="isoDropdownLink"
          onKeyPress=""
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: 12,
            cursor: item?.status === 'cancelled' ? 'not-allowed' : 'pointer',
          }}
          tabIndex="-1"
          onClick={() => {
            if (item?.status !== 'cancelled') {
              cancelHighlights(item?.id);
              setIsIndex(null);
              console.log('first', item);
            }
          }}
        >
          <MdOutlineCancel
            color={item?.status === 'cancelled' ? 'grey' : 'red'}
            size={16}
          />
          <span
            style={{
              color: item?.status === 'cancelled' ? 'grey' : 'red',
            }}
          >
            {item?.status === 'cancelled' ? 'Cancelled' : 'Cancel'}
          </span>
        </div>
        <div className="isoDropdownLink">
          <Popconfirms
            title={<IntlMessages id="Sure to delete?" />}
            okText={<IntlMessages id="DELETE" />}
            cancelText={<IntlMessages id="No" />}
            onConfirm={() => {
              deleteHighlights();
              setIsIndex(null);
            }}
            onCancel={null}
          >
            <DeleteOutlined style={{ paddingRight: 12, fontSize: 16 }} />
            <IntlMessages id="highlights.delete" />
          </Popconfirms>
        </div>
      </div>
    </HighlightsStyles>
  );

  // set item id in state
  function handleVisibleChange(item1) {
    setIsIndex(item1.id);
  }

  const columns = [
    {
      title: <IntlMessages id="ID" />,
      dataIndex: 'id',
      rowKey: 'id',
      width: 10,
      className: 'fullname-cell',
      render: (text, data, index) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="ID" />
            </span>
            <span className="mobile-lbl-val">
              {index + currentPage + 1 || '-'}
            </span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="highlight.type" />,
      dataIndex: 'highlight_type',
      rowKey: 'highlight_type',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        const highlightType = highlightTypeArr.find((item) => item.id === text);
        return (
          <div>
            <span className="label">
              <IntlMessages id="highlight.type" />
            </span>
            <span className="mobile-lbl-val">
              {highlightType ? highlightType.type : '-'}
            </span>
          </div>
        );
      },
    },
    // {
    //   title: <IntlMessages id="common.co2_equivalent" />,
    //   dataIndex: 'co2_equivalent',
    //   rowKey: 'co2_equivalent',
    //   width: 180,
    //   className: 'fullname-cell',
    //   render: (text) => {
    //     return (
    //       <div>
    //         <span className="label">
    //           <IntlMessages id="common.co2_equivalent" />
    //         </span>
    //         <span className="mobile-lbl-val">{text || '-'}</span>
    //       </div>
    //     );
    //   },
    // },
    {
      title: <IntlMessages id="common.activities" />,
      dataIndex: 'activities',
      rowKey: 'activities',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="common.activities" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    // {
    //   title: <IntlMessages id="common.altitude" />,
    //   dataIndex: 'altitude',
    //   rowKey: 'altitude',
    //   width: 180,
    //   className: 'fullname-cell',
    //   render: (text) => {
    //     return (
    //       <div>
    //         <span className="label">
    //           <IntlMessages id="common.altitude" />
    //         </span>
    //         <span className="mobile-lbl-val">{text || '-'}</span>
    //       </div>
    //     );
    //   },
    // },
    // {
    //   title: <IntlMessages id="common.average_speed" />,
    //   dataIndex: 'average_speed',
    //   rowKey: 'average_speed',
    //   width: 180,
    //   className: 'fullname-cell',
    //   render: (text) => {
    //     return (
    //       <div>
    //         <span className="label">
    //           <IntlMessages id="common.average_speed" />
    //         </span>
    //         <span className="mobile-lbl-val">{text || '-'}</span>
    //       </div>
    //     );
    //   },
    // },
    {
      title: <IntlMessages id="common.buggy_mode" />,
      dataIndex: 'buggy_mode',
      rowKey: 'buggy_mode',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="common.buggy_mode" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    // {
    //   title: <IntlMessages id="common.time_active" />,
    //   dataIndex: 'time_active',
    //   rowKey: 'time_active',
    //   width: 180,
    //   className: 'fullname-cell',
    //   render: (text) => {
    //     return (
    //       <div>
    //         <span className="label">
    //           <IntlMessages id="common.time_active" />
    //         </span>
    //         <span className="mobile-lbl-val">{text || '-'}</span>
    //       </div>
    //     );
    //   },
    // },
    // {
    //   title: <IntlMessages id="common.increase" />,
    //   dataIndex: 'increase',
    //   rowKey: 'increase',
    //   width: 180,
    //   className: 'fullname-cell',
    //   render: (text) => {
    //     return (
    //       <div>
    //         <span className="label">
    //           <IntlMessages id="common.increase" />
    //         </span>
    //         <span className="mobile-lbl-val">{text || '-'}</span>
    //       </div>
    //     );
    //   },
    // },
    {
      title: <IntlMessages id="common.value" />,
      dataIndex: 'value',
      rowKey: 'value',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="common.value" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="common.metricType" />,
      dataIndex: 'metric_type',
      rowKey: 'metric_type',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="common.metricType" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="common.ruleType" />,
      dataIndex: 'rule_type',
      rowKey: 'rule_type',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="common.ruleType" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="common.challenge_status" />,
      dataIndex: 'challenge_status',
      rowKey: 'challenge_status',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="common.challenge_status" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="common.challenge_metric" />,
      dataIndex: 'challenge_metric',
      rowKey: 'challenge_metric',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="common.challenge_metric" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="common.award_type" />,
      dataIndex: 'award_type',
      rowKey: 'award_type',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="common.award_type" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Action" />,
      dataIndex: 'action',
      rowKey: 'action',
      className: 'fullname-cell',
      width: 50,
      fixed: 'right',
      render: (text, item) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Action" />
            </span>
            <span className="mobile-lbl-val">
              <Popover
                content={<div>{content(item)}</div>}
                trigger="click"
                visible={isIndex === item.id ? !actionMenu : false}
                onVisibleChange={() => handleVisible()}
                arrowPointAtCenter
                placement="bottomRight"
              >
                <MoreOutlined
                  onClick={() => {
                    handleVisibleChange(item);
                    const HData = cloneDeep(item);
                    HData.time_active = moment(item.time_active, 'HH:mm:ss');
                    setState({ ...state, initObj: HData });
                  }}
                  style={{ color: '#000', fontSize: 24 }}
                />
              </Popover>
            </span>
          </div>
        );
      },
    },
  ];

  // Add Highlights View
  const addNew = () => {
    return (
      <HighlightsStyles>
        <div className="iconStyle marketingMainDiv">
          <span>
            <p
              className="addNewUser"
              style={{
                color: theme.colors.primaryColor,
                alignItems: 'center',
              }}
            >
              <div
                role="button"
                tabIndex="0"
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                }}
                onClick={() => setState({ visible: true })}
                onKeyDown={() => setState({ visible: true })}
              >
                <p className="iconPadding" style={{ paddingRight: 8 }}>
                  <PlusCircleOutlined />
                </p>
                <IntlMessages id="highlights.add" />
              </div>
            </p>
          </span>
        </div>
      </HighlightsStyles>
    );
  };

  const { initObj, visible } = state;
  return (
    <HighlightsStyles>
      <Row className="isoTabRow">
        <Col xl={8} lg={8} sm={8} md={4} xs={8}>
          <div className="marketingText">
            <IntlMessages id="sidebar.highlights" />
          </div>
        </Col>
        <Col sm={16} xs={16} className="hiddenCol">
          {addNew()}
        </Col>
        <Col xl={16} lg={16} md={20} xs={16} className="isoAddNew">
          {addNew()}
        </Col>
      </Row>
      <Divider className="horizontalDevider" />
      <TableWrapper
        loading={highlightsState.highlightsLoad}
        rowKey={(record) => record.id}
        dataSource={highlightsState.highlightss}
        onChange={() => onChange()}
        columns={columns}
        className="invoiceListTable"
        pagination={filter.pagination || {}}
        showSorterTooltip={false}
      />
      <HighlightsForm
        initialValues={initObj}
        visible={visible}
        onClose={(type) => {
          if (type === 'success') {
            fetchDataFilters();
            setState({ initObj: {}, visible: false });
          } else {
            setState({ initObj: {}, visible: false });
          }
        }}
      />
    </HighlightsStyles>
  );
};

export default Highlights;

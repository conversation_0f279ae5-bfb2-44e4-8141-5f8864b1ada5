/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
/* eslint-disable no-nested-ternary */
import React, { useEffect, useState } from 'react';
import {
  Col,
  Drawer,
  Input,
  Row,
  Form,
  Select,
  Upload,
  DatePicker,
  // TimePicker,
} from 'antd';
import { isEmpty } from 'lodash';
import { useSelector } from 'react-redux';
import IntlMessages from '@chill/components/utility/intlMessages';
import {
  BottomViewWrapper,
  FormWrapper,
} from '@chill/assets/styles/drawerFormStyles';
import CButton from '@chill/components/uielements/CButton';
import Notification from '@chill/components/Notification';
import getApiData from '@chill/lib/helpers/apiHelper';
import useResetFormOnClose from '@chill/lib/hooks/useResetForm';
import {
  ruleTypes,
  heighlightsMetricTypes,
  challengeStatus,
  awardType,
} from '@chill/lib/helpers/utilityData';
import Icon, { FilePdfOutlined } from '@ant-design/icons'; // {FilePdfOutlined, UploadOutlined}
import { getBase64 } from '@chill/lib/helpers/utility';
import moment from 'moment';

// Destructure Option from Select for dropdowns
const { Option } = Select;

function HighlightsForm(props) {
  const { initialValues, onClose, visible, view } = props;
  const edit = !isEmpty(initialValues);
  const initVal = initialValues || {};
  const [form] = Form.useForm();
  const [btnLoader, setBtnLoader] = useState(false);
  const [selectedPdf, setSelectedPdf] = useState(null);
  console.log('🚀 ~ HighlightsForm ~ selectedPdf:', selectedPdf);
  const [selectedImg, setSelectedImg] = useState(null);
  const [challengeMetricReq, setChallengeMetricReq] = useState(true);
  const [showDatePickers, setShowDatePickers] = useState(false);
  const [heightlightImg, setHeightlightImg] = useState({});
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  // Array of highlight type options
  const highlightTypeArr = [
    { id: 'distance', type: 'Distance' },
    { id: 'co2_equivalent', type: 'CO2 Equivalent' },
    { id: 'altitude', type: 'Altitude' },
    { id: 'average_speed', type: 'Average Speed' },
    { id: 'time_active', type: 'Time Active' },
    { id: 'increase', type: 'Increase' },
  ];

  useResetFormOnClose({ visible, initVal, form });

  useEffect(() => {
    if (!edit) {
      form.resetFields();
    }
  }, [visible]);

  // this function reset form data when close modal
  function handleForm(type = '', data = {}) {
    form.resetFields();
    onClose(type, data);
    setHeightlightImg({});
    setSelectedImg({});
    setSelectedPdf({});
  }

  // this function for add new faqs
  async function addHighlights(val) {
    const values = val;
    const dateString = values.time_active;
    const date = new Date(dateString);
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    const formattedTime = `${hours}:${minutes}:${seconds}`;
    values.time_active = formattedTime;

    // Ensure `challenge_metric` is a valid number or default to 0
    values.challenge_metric = parseFloat(values.challenge_metric) || 0;

    const formData = new FormData();
    const url = edit
      ? 'manage-highlight/update-highlight'
      : 'manage-highlight/create-highlight';
    Object.keys(values).map((k) => formData.append(k, values[k]));
    if (!isEmpty(selectedImg)) {
      formData.append('image_url', selectedImg);
    }
    if (!isEmpty(selectedPdf)) {
      formData.append('coupon_code_pdf', selectedPdf);
    }
    formData.delete('challenge_start_date1');
    formData.delete('challenge_end_date2');

    try {
      const response = await getApiData(url, formData, 'POST', {}, true);
      if (response.success) {
        Notification('success', response.message);
        handleForm('success', response.data);
      } else {
        Notification('error', response.message);
      }
      setBtnLoader(false);
    } catch (error) {
      console.log(error);
      setBtnLoader(false);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  // this function for validate data
  function validate(values) {
    const obj = {
      ...values,
      challenge_start_date: moment(values.challenge_start_date1).format(
        'YYYY-MM-DD',
      ),
      challenge_end_date: moment(values.challenge_end_date2).format(
        'YYYY-MM-DD',
      ),
    };
    console.log(
      `hello ~ file: HighlightsForm.js:119 ~ validate ~ values:`,
      values,
      obj,
    );
    if (edit) obj.highlight_id = initVal.id;
    setBtnLoader(true);
    addHighlights(obj);
  }

  // const beforePdfUpload = (file) => {
  //   const validType = file.type === 'application/pdf';
  //   if (!validType) {
  //     Notification('error', messageArray['common.pdf.upload']);
  //     return false;
  //   }
  //   setSelectedPdf(file);
  //   return false;
  // };

  // const uploadPdfButton = (
  //   <div>
  //     <Icon type="plus" />
  //     <div className="ant-upload-text">
  //       <UploadOutlined /> <IntlMessages id="common.uploadFile" />
  //     </div>
  //   </div>
  // );

  const beforeUpload = (file) => {
    const validType =
      file.type === 'image/jpeg' ||
      file.type === 'image/jpg' ||
      file.type === 'image/png' ||
      file.type === 'application/pdf';

    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      Notification('error', messageArray['err.size']);
      return false;
    }

    if (!validType) {
      Notification('error', messageArray['common.img.all.Upload']);
      return false;
    }

    if (file.type === 'application/pdf') {
      setSelectedPdf(file);
      setHeightlightImg(null);

      // Reset the coupon_code_pdf value in initValues
      if (initVal && initVal.image_url) {
        initVal.image_url = null; // Reset the coupon_code_pdf value
      }
    } else {
      getBase64(file, (imageUrl) => {
        setHeightlightImg({ imageUrl, loading: false });
      });
      setSelectedImg(file);
      setSelectedPdf(null);

      // Reset the coupon_code_pdf value in initValues
      if (initVal && initVal.coupon_code_pdf) {
        initVal.coupon_code_pdf = null; // Reset the coupon_code_pdf value
      }
    }
    return false;
  };

  const uploadButton = (
    <div>
      <Icon type="plus" />
      <div className="ant-upload-text">
        <IntlMessages id="common.upload" />
      </div>
    </div>
  );

  const handleFormChange = (changedValues, allValues) => {
    console.log(
      `hello ~ file: HighlightsForm.js:177 ~ handleFormChange ~ allValues:`,
      allValues,
      changedValues,
    );
    if (changedValues?.challenge_status === 'between_2_date_periods') {
      setShowDatePickers(true);
      setChallengeMetricReq(true);
    } else if (changedValues.challenge_status === 'until_cancelled') {
      setChallengeMetricReq(false);
      setShowDatePickers(false);
    } else if (changedValues.challenge_status) {
      setShowDatePickers(false);
      setChallengeMetricReq(true);
    }
  };

  useEffect(() => {
    if (!isEmpty(initVal)) {
      setTimeout(() => {
        if (initVal?.challenge_status === 'between_2_date_periods') {
          setShowDatePickers(true);
          form.setFieldsValue({
            ...initVal,
            challenge_start_date1: moment(initVal?.challenge_start_date),
            challenge_end_date2: moment(initVal?.challenge_end_date),
          });
          console.log(
            `hello ~ file: HighlightsForm.js:205 ~ useEffect ~ initVal:`,
            initVal,
            moment(initVal?.challenge_start_date),
            moment(initVal?.challenge_start_date).format('YYYY-MM-DD'),
            form.getFieldsValue('challenge_start_date'),
          );
        }
      }, 2000);

      if (initVal.challenge_status === 'until_cancelled') {
        setChallengeMetricReq(false);
        setShowDatePickers(false);
      }
    }
  }, [initVal]);
  return (
    <Drawer
      title={<IntlMessages id={edit ? 'highlights.edit' : 'highlights.add'} />}
      width={500}
      style={{ height: window.innerHeight - 60, marginTop: 70 }}
      onClose={() => handleForm()}
      visible={visible}
      bodyStyle={{ paddingBottom: 80 }}
      destroyOnClose
    >
      <FormWrapper>
        <Form
          form={form}
          onFinish={(e) => validate(e)}
          layout="vertical"
          initialValues={initVal}
          onValuesChange={handleFormChange}
        >
          <Row
            gutter={[12, 20]}
            style={{
              marginBottom: 20,
            }}
          >
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="highlight.type" />}
                name="highlight_type"
                defaultValue={initVal?.highlight_type || null}
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.highlightType" />,
                  },
                ]}
              >
                <Select
                  placeholder={<IntlMessages id="common.highlightType" />}
                >
                  {highlightTypeArr?.map((location) => {
                    return (
                      <Option key={location.id} value={location.id}>
                        {location.type}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            {/* <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="common.distance" />}
                name="distance"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.distance" />,
                  },
                ]}
              >
                <Input placeholder="Distance" type="number" />
              </Form.Item>
            </Col> */}
            {/* <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="common.co2_equivalent" />}
                name="co2_equivalent"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.co2_equivalent" />,
                  },
                ]}
              >
                <Input placeholder="CO2 Equivalent" type="number" />
              </Form.Item>
            </Col> */}
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="common.value" />}
                name="value"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.value" />,
                  },
                ]}
              >
                <Input placeholder="Value" type="number" />
              </Form.Item>
            </Col>

            <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="common.metricType" />}
                name="metric_type"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.metricType" />,
                  },
                ]}
              >
                <Select
                  placeholder={<IntlMessages id="common.search.metricType" />}
                >
                  {heighlightsMetricTypes.map((status) => {
                    if (status.disabled) return null;
                    return (
                      <Select.Option key={status.value} value={status.value}>
                        {status.text}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="common.ruleType" />}
                name="rule_type"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.ruleType" />,
                  },
                ]}
              >
                <Select
                  placeholder={<IntlMessages id="common.search.ruleType" />}
                >
                  {ruleTypes.map((status) => {
                    if (status.disabled) return null;
                    return (
                      <Select.Option key={status.value} value={status.value}>
                        {status.text}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="Description" />}
                name="activities"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.description" />,
                  },
                ]}
              >
                <Input.TextArea rows={2} placeholder="Enter description..." />
              </Form.Item>
            </Col>
            {/* <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="common.altitude" />}
                name="altitude"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.altitude" />,
                  },
                ]}
              >
                <Input placeholder="Altitude" type="number" />
              </Form.Item>
            </Col>
            <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="common.average_speed" />}
                name="average_speed"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.average_speed" />,
                  },
                ]}
              >
                <Input placeholder="Average Speed" type="number" />
              </Form.Item>
            </Col> */}
            <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="common.buggy_mode" />}
                name="buggy_mode"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.buggy_mode" />,
                  },
                ]}
              >
                <Input placeholder="Buggy Mode" />
              </Form.Item>
            </Col>
            {/* <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="common.time_active" />}
                name="time_active"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.time_active" />,
                  },
                ]}
              >
                <TimePicker format="HH:mm:ss" className="_w-100" />
              </Form.Item>
            </Col>
            <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="common.increase" />}
                name="increase"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.increase" />,
                  },
                ]}
              >
                <Input placeholder="Increase" type="number" />
              </Form.Item>
            </Col> */}

            <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="common.challenge_status" />}
                name="challenge_status"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.challenge_status" />,
                  },
                ]}
              >
                <Select
                  placeholder={
                    <IntlMessages id="common.search.challenge_status" />
                  }
                >
                  {challengeStatus?.map((status) => {
                    if (status.disabled) return null;
                    return (
                      <Select.Option key={status.value} value={status.value}>
                        {status.text}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            {showDatePickers && (
              <>
                <Col xs={12}>
                  <Form.Item
                    label={<IntlMessages id="common.challengeStartDate" />}
                    name="challenge_start_date1"
                    rules={[
                      {
                        required: true,
                        message: <IntlMessages id="err.challengeStartDate" />,
                      },
                    ]}
                  >
                    <DatePicker
                      disabledDate={(current) =>
                        current && current < moment().startOf('day')
                      }
                      placeholder="Challenge Start Date"
                    />
                  </Form.Item>
                </Col>
                <Col xs={12}>
                  <Form.Item
                    label={<IntlMessages id="common.challengeEndDate" />}
                    name="challenge_end_date2"
                    rules={[
                      {
                        required: true,
                        message: <IntlMessages id="err.challengeEndDate" />,
                      },
                    ]}
                  >
                    <DatePicker
                      disabledDate={(current) =>
                        current && current < moment().startOf('day')
                      }
                      // format="YYYY-MM-DD"
                      placeholder="Challenge End Date"
                    />
                  </Form.Item>
                </Col>
              </>
            )}

            <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="common.challenge_metric" />}
                name="challenge_metric"
                rules={[
                  {
                    required: challengeMetricReq,
                    message: <IntlMessages id="err.challenge_metric" />,
                  },
                ]}
              >
                <Input placeholder="Challenge Metric" type="number" />
              </Form.Item>
            </Col>
            <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="common.award_type" />}
                name="award_type"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.award_type" />,
                  },
                ]}
              >
                <Select
                  placeholder={<IntlMessages id="common.search.award_type" />}
                >
                  {awardType.map((status) => {
                    if (status.disabled) return null;
                    return (
                      <Select.Option key={status.value} value={status.value}>
                        {status.text}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item label={<IntlMessages id="common.image_url" />}>
                <Upload
                  showUploadList={false}
                  listType="picture-card"
                  beforeUpload={beforeUpload}
                  disabled={!!view}
                  className="uploadImage-center"
                >
                  {(selectedPdf && !isEmpty(selectedPdf)) ||
                  (initVal && !isEmpty(initVal?.coupon_code_pdf)) ? (
                    <Row>
                      <Col xs={24}>
                        <FilePdfOutlined
                          style={{ fontSize: 50, color: 'red' }}
                        />
                      </Col>
                      <Col xs={24}>
                        <div className="_uploadTextContainer">
                          <div className="_uploadText">
                            {selectedPdf && !isEmpty(selectedPdf)
                              ? selectedPdf?.name
                                ? selectedPdf?.name
                                : initVal &&
                                  !isEmpty(initVal) &&
                                  initVal?.file_meta &&
                                  !isEmpty(initVal?.file_meta)
                                ? initVal?.file_meta?.filename
                                : ''
                              : initVal?.coupon_code_pdf}
                          </div>
                        </div>
                      </Col>
                    </Row>
                  ) : heightlightImg?.imageUrl || initVal?.image_url ? (
                    <img
                      src={heightlightImg?.imageUrl || initVal?.image_url}
                      alt="product"
                      style={{ width: '100%', height: '100%' }}
                    />
                  ) : (
                    uploadButton
                  )}
                </Upload>
              </Form.Item>
            </Col>
            {/* <Col xs={24}>
              <Form.Item label={<IntlMessages id="common.coupon_code_pdf" />}>
                <Upload
                  className="usermanualstyle"
                  showUploadList={false}
                  listType="picture-card"
                  beforeUpload={beforePdfUpload}
                  disabled={!!view}
                  style={{ width: '100%' }}
                >
                  {(selectedPdf && !isEmpty(selectedPdf)) ||
                  (initVal && !isEmpty(initVal?.coupon_code_pdf)) ? (
                    <Row>
                      <Col xs={24}>
                        <FilePdfOutlined
                          style={{ fontSize: 50, color: 'red' }}
                        />
                      </Col>
                      <Col xs={24}>
                        <div className="_uploadTextContainer">
                          <div className="_uploadText">
                            {selectedPdf && !isEmpty(selectedPdf)
                              ? selectedPdf?.name
                                ? selectedPdf?.name
                                : initVal &&
                                  !isEmpty(initVal) &&
                                  initVal?.file_meta &&
                                  !isEmpty(initVal?.file_meta)
                                ? initVal?.file_meta?.filename
                                : ''
                              : initVal?.coupon_code_pdf}
                          </div>
                        </div>
                      </Col>
                    </Row>
                  ) : (
                    uploadPdfButton
                  )}
                </Upload>
              </Form.Item>
            </Col> */}
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="common.award_text" />}
                name="award_text"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.award_text" />,
                  },
                  {
                    max: 500, // Set the maximum length here
                    message: <IntlMessages id="err.award_text_max_length" />, // Customize the error message
                  },
                ]}
              >
                <Input.TextArea rows={3} placeholder="Award Text..." />
              </Form.Item>
            </Col>
          </Row>
          <BottomViewWrapper className="bottomBtnWrapper">
            <Row gutter={12} style={{ width: '100%' }}>
              <Col xs={24}>
                <Form.Item className="btn-form">
                  <CButton
                    className="submitBtnStyle"
                    htmlType="submit"
                    loading={btnLoader}
                    disabled={btnLoader}
                    style={{ marginRight: 20 }}
                  >
                    <IntlMessages
                      id={edit ? 'common.changes' : 'common.submit'}
                    />
                  </CButton>
                </Form.Item>
              </Col>
              {/* <Form.Item className="btn-form">
                <div
                  className="cancelBtnStyle"
                  onClick={handleForm}
                  role="button"
                  onKeyPress=""
                  tabIndex="-1"
                >
                  <IntlMessages id="common.cancel" />
                </div>
              </Form.Item> */}
            </Row>
          </BottomViewWrapper>
        </Form>
      </FormWrapper>
    </Drawer>
  );
}

export default HighlightsForm;

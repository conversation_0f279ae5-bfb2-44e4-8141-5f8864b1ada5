/* eslint-disable react/jsx-no-bind */
/* eslint-disable no-nested-ternary */
/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
import React, { useState } from 'react';
import { Col, Drawer, Input, Row, Select, Form } from 'antd';
import { isEmpty, isString } from 'lodash';
import { useSelector } from 'react-redux';
import IntlMessages from '@chill/components/utility/intlMessages';
import {
  BottomViewWrapper,
  FormWrapper,
} from '@chill/assets/styles/drawerFormStyles';
import CButton from '@chill/components/uielements/CButton';
import Notification from '@chill/components/Notification';
import getApiData from '@chill/lib/helpers/apiHelper';
import useResetFormOnClose from '@chill/lib/hooks/useResetForm';

function CustomerForm(props) {
  const { initialValues, onClose, visible, view } = props;
  const edit = !isEmpty(initialValues);
  const initVal = { ...initialValues } || {};
  const [form] = Form.useForm();
  const [btnLoader, setBtnLoader] = useState(false);

  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  useResetFormOnClose({ visible, initVal, form });

  // this function reset form data when close modal
  function handleForm(type = '', data = {}) {
    form.resetFields();
    onClose(type, data);
  }

  // this function for add/edit customers
  async function addCustomer(values) {
    const url = edit ? 'updateUserTag' : 'user/add-user';

    try {
      const response = await getApiData(url, values, 'POST');
      if (response.success) {
        Notification('success', response.message);
        handleForm('success', response.data);
      } else {
        Notification('error', response.message);
      }
      setBtnLoader(false);
    } catch (error) {
      console.log(error);
      setBtnLoader(false);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  // this function for validate data
  function validate(values) {
    const obj = values;
    obj.type = 'customer';
    obj.user_tags = isString(values.user_tags)
      ? values.user_tags
      : JSON.stringify(values.user_tags);
    if (edit) obj.user_id = initVal.id;

    setBtnLoader(true);
    addCustomer(obj);
  }

  return (
    <Drawer
      title={
        <>
          <IntlMessages
            id={
              view
                ? 'customer.detail.title'
                : edit
                ? 'customer.edit'
                : 'customer.add'
            }
          />
          {edit || view ? (
            <p
              style={{ fontSize: '14px', opacity: 0.8, fontWeight: 'normal' }}
              className="subTitleStyle"
            >
              <IntlMessages id="customer.subTitle" />
            </p>
          ) : null}
        </>
      }
      width={500}
      style={{ height: window.innerHeight - 60, marginTop: 70 }}
      onClose={handleForm}
      visible={visible}
      bodyStyle={{ paddingBottom: 80 }}
      destroyOnClose
    >
      <FormWrapper>
        <Form
          form={form}
          onFinish={validate}
          layout="vertical"
          initialValues={initVal}
        >
          <Row
            gutter={[12, 20]}
            style={{
              marginBottom: 20,
            }}
          >
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="customer.name" />}
                name="full_name"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.fullname" />,
                  },
                ]}
              >
                <Input disabled={edit} />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="antTable.title.email" />}
                name="email"
                rules={[
                  {
                    type: 'email',
                    required: true,
                    message: <IntlMessages id="req.email" />,
                  },
                ]}
              >
                <Input name="email" disabled={edit} />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="input.product.tags" />}
                name="user_tags"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.tags" />,
                  },
                ]}
              >
                <Select mode="tags" />
              </Form.Item>
            </Col>
          </Row>
          <BottomViewWrapper className="bottomBtnWrapper">
            <Row gutter={12} style={{ width: '100%' }}>
              {view ? null : (
                <Col xs={24}>
                  <Form.Item className="btn-form">
                    <CButton
                      className="submitBtnStyle"
                      htmlType="submit"
                      loading={btnLoader}
                      disabled={btnLoader}
                      style={{ marginRight: 20 }}
                    >
                      <IntlMessages
                        id={edit ? 'common.changes' : 'common.submit'}
                      />
                    </CButton>
                  </Form.Item>
                </Col>
              )}
              {/* <Form.Item className="btn-form">
                <div
                  className="cancelBtnStyle"
                  onClick={handleForm}
                  role="button"
                  onKeyPress=""
                  tabIndex="-1"
                >
                  <IntlMessages id={view ? 'common.done' : 'common.cancel'} />
                </div>
              </Form.Item> */}
            </Row>
          </BottomViewWrapper>
        </Form>
      </FormWrapper>
    </Drawer>
  );
}

export default CustomerForm;

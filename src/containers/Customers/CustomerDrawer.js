/* eslint-disable react/jsx-no-bind */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
import React, { useEffect, useState } from 'react';
import { Col, Drawer, Input, Row, Collapse, Select, Checkbox } from 'antd';
import { isArray, isEmpty, isNull } from 'lodash';
import moment from 'moment';
import { useSelector } from 'react-redux';
import IntlMessages from '@chill/components/utility/intlMessages';
import Form from '@chill/components/uielements/form';
import {
  BottomViewWrapper,
  FormWrapper,
} from '@chill/assets/styles/drawerFormStyles';
import Datepicker from '@chill/components/uielements/datePicker';
import { PlusOutlined, MinusOutlined } from '@ant-design/icons';
import CButton from '@chill/components/uielements/CButton';
import InputNumber from '@chill/components/uielements/InputNumber';
import getApiData from '@chill/lib/helpers/apiHelper';
import Notification from '@chill/components/Notification';
import { Option } from 'antd/lib/mentions';
import CustomerWrapper from './Customers.Styles';

const { Panel } = Collapse;

function CustomerDrawer(props) {
  const {
    visible,
    setState = () => {},
    drawerType,
    expandIconPosition = 'right',
    languages = [],
    initialValues,
    countries = [],
    userTags = [],
    product = [],
    state,
    customerState,
    getFilterCustomer = () => {},
    getSegmentList = () => {},
    getCategoryList = () => {},
  } = props;
  console.log('visible---------------', visible, drawerType);

  const { language } = useSelector((st) => st.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);
  const [btnLoader, setBtnLoader] = useState(false);
  const [categoryList, setCategoryList] = useState();
  const [form] = Form.useForm();
  // const edit = !isEmpty(initialValues);
  const initVal = initialValues || {};
  // this function for get category
  /** this function for get category
   * @function getCategoryList
   *
   */
  async function getCampCategoryList() {
    try {
      const response = await getApiData('campaigns-category/index', {}, 'POST');
      if (response.success) {
        if (isArray(response?.data)) {
          const temp = response.data;
          temp.shift();
          setCategoryList(temp);
        }
      }
    } catch (err) {
      console.log('er=====', err);
      setCategoryList((preState) => ({
        ...preState,
      }));
    }
  }
  useEffect(() => {
    if (drawerType === 'add') {
      form.setFieldsValue({
        user_tags: undefined,
        status: '',
        country: '',
        language_id: null,
      });
    }
    if (drawerType === 'category') {
      form.setFieldsValue({
        category_name: undefined,
      });
    }
    if (drawerType === 'category-delete') {
      form.setFieldsValue({
        category_id: undefined,
      });
      getCampCategoryList();
    }
  }, [visible]);

  const onClose = () => {
    setState((p) => ({
      ...p,
      visible: false,
    }));
    if (getSegmentList) {
      getSegmentList();
    }
    if (getCategoryList) {
      getCategoryList();
    }
  };

  // this function reset form data when close modal
  function handleForm() {
    form.resetFields();
    onClose();
  }
  const cancleFilter = () => {
    setState((p) => ({
      ...p,
      visible: false,
      status: {
        active: false,
        inactive: false,
      },
      country: '',
      language_id: null,
      user_tag: [],
      filterMode: false,
      product_list: [],
    }));
    // getCustomerList();
  };
  function handleForm1() {
    form.resetFields();
    cancleFilter();
    onClose();
  }

  // this function for add segment
  async function addSegment(values) {
    const obj = { ...values };
    obj.first_session = values.first_session
      ? moment(values.first_session).format('YYYY-MM-DD')
      : '';
    obj.last_session = values.last_session
      ? moment(values.last_session).format('YYYY-MM-DD')
      : '';
    obj.user_tags = isArray(values.user_tags)
      ? JSON.stringify(values.user_tags)
      : values.user_tags;
    obj.auto_add = state.status.autoAdd ? 1 : 0;
    setBtnLoader(true);
    try {
      const response = await getApiData('segment/add-segment', obj, 'POST');
      if (response.success) {
        Notification('success', response.message);
        handleForm();
      } else {
        Notification('error', response.message);
      }
      setBtnLoader(false);
    } catch (error) {
      console.log('kkkk', error);
      setBtnLoader(false);
      Notification('error', messageArray['err.wenWrong']);
    }
  }
  // this function for add-campaigns-category
  async function deleteCategory(values) {
    const obj = { ...values };
    console.log('cate id=====', obj);
    setBtnLoader(true);
    try {
      const response = await getApiData(
        'campaigns-category/delete-campaigns-category',
        obj,
        'POST',
      );
      if (response.success) {
        Notification('success', response.message);
        handleForm1();
      } else {
        Notification('error', response.message);
      }
      setBtnLoader(false);
    } catch (error) {
      console.log(error);
      setBtnLoader(false);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  // this function for add-campaigns-category
  async function addCategory(values) {
    const obj = { ...values };
    setBtnLoader(true);
    try {
      const response = await getApiData(
        'campaigns-category/add-campaigns-category',
        obj,
        'POST',
      );
      if (response.success) {
        Notification('success', response.message);
        handleForm();
      } else {
        Notification('error', response.message);
      }
      setBtnLoader(false);
    } catch (error) {
      console.log(error);
      setBtnLoader(false);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  async function segmentCount(type, val) {
    // getfieldvalue used for geting live value
    const click_rate = form.getFieldValue('click_rate');
    const share_rate = form.getFieldValue('share_rate');
    const data = {
      country: state.countryCount,
      language_id: state.languageID,
      status: state.statusAdd,
      user_tags: state.userTag,
      no_of_products: state.noOfProduct,
      share_rate:
        type === 'shareRate' ? share_rate || val || state.shareRate : '',
      click_rate:
        type === 'clickRate' ? click_rate || val || state.clickRate : '',
      first_session: state.firstSession
        ? moment(state.firstSession).format('YYYY-MM-DD')
        : '',
      last_session: state.lastSession
        ? moment(state.lastSession).format('YYYY-MM-DD')
        : '',
    };
    try {
      const response = await getApiData('getSegmentUserCount', data, 'POST');
      if (response.success) {
        setState((p) => ({ ...p, count: response.userCount }));
      } else {
        Notification('error', response.message);
      }
    } catch (error) {
      console.log(error);
      Notification('error', messageArray['err.wenWrong']);
    }
  }
  useEffect(() => {
    segmentCount();
  }, [
    state.countryCount,
    state.languageID,
    state.userTag,
    state.noOfProduct,
    state.firstSession,
    state.lastSession,
    state.statusAdd,
  ]);

  function onDateChange(e, type) {
    const date = moment(e).format('DD-MM-YYYY');
    if (type === 'last') {
      if (state?.firstSession === '') {
        Notification('error', messageArray['err.firstSession']);
      } else {
        setState((p) => ({ ...p, lastSession: date }));
      }
    } else {
      setState((p) => ({ ...p, firstSession: date }));
    }
    console.log('state ==>', state);
  }

  function statusFilter(e, type) {
    if (type === 'active') {
      setState((p) => ({
        ...p,
        status: { ...state.status, active: !state.status.active },
      }));
    }
    if (type === 'inactive') {
      setState((p) => ({
        ...p,
        status: { ...state.status, inactive: !state.status.inactive },
      }));
    }
  }

  function autoAdd() {
    setState((p) => ({
      ...p,
      status: { ...state.status, autoAdd: !state.status.autoAdd },
    }));
  }

  function disabledDate(current) {
    // Can not select days before today and today
    return current && current > moment().endOf('day');
  }
  function onChangeCatSelect(value) {
    console.log(`selected ${value}`);
    setState((p) => ({ ...p, sendTo: value }));
    console.log('categoryList v ==>', value);
  }
  function onBlur() {
    console.log('blur');
  }

  function onFocus() {
    console.log('focus');
  }

  function onSearch(val) {
    console.log('search:', val);
  }
  return (
    <CustomerWrapper>
      <div>
        <Drawer
          title={
            <IntlMessages
              id={
                drawerType === 'category'
                  ? 'customer.newCategory'
                  : drawerType === 'add'
                  ? 'customer.newSegment'
                  : drawerType === 'category-delete'
                  ? 'Delete category'
                  : 'common.filters'
              }
            />
          }
          width={451}
          style={{ height: window.innerHeight - 60, marginTop: 70 }}
          onClose={handleForm}
          visible={visible}
          bodyStyle={{ paddingBottom: 110 }}
          destroyOnClose
          form={form}
        >
          {drawerType === 'add' ? (
            <FormWrapper>
              <Form
                form={form}
                onFinish={addSegment}
                layout="vertical"
                initialValues={initVal}
              >
                <Form.Item
                  label={<IntlMessages id="antTable.title.segmentName" />}
                  name="segment_name"
                  rules={[
                    {
                      required: true,
                      message: <IntlMessages id="err.segmentName" />,
                    },
                    {
                      max: 50,
                      message: <IntlMessages id="err.max.50char" />,
                    },
                    {
                      whitespace: true,
                      message: <IntlMessages id="err.blankSpace" />,
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
                <div style={{ paddingLeft: '10px' }}>
                  <IntlMessages id="customer.segmentCriteria" />
                </div>
                <Row
                  gutter={[12, 20]}
                  style={{
                    marginBottom: 20,
                  }}
                >
                  <Col xs={12}>
                    <Form.Item
                      label={<IntlMessages id="antTable.title.Country" />}
                      name="country"
                      // rules={[
                      //   // {
                      //   //   required: true,
                      //   //   message: <IntlMessages id="req.country" />,
                      //   // },
                      //   // {
                      //   //   whitespace: true,
                      //   //   message: <IntlMessages id="err.blankSpace" />,
                      //   // },
                      // ]}
                    >
                      <Select
                        onChange={(val) => {
                          setState((p) => ({
                            ...p,
                            countryCount: val,
                          }));
                          segmentCount();
                        }}
                      >
                        {countries.map((item) => (
                          <Select.Option
                            key={`countries_${item.apiKey}`}
                            value={item}
                          >
                            {item}
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>

                  <Col xs={12}>
                    <Form.Item
                      label={<IntlMessages id="customer.language" />}
                      name="language_id"
                    >
                      <Select
                        onChange={(val) => {
                          setState((p) => ({
                            ...p,
                            languageID: val,
                          }));
                          segmentCount();
                        }}
                      >
                        {languages.map((item) => (
                          <Select.Option
                            key={`languages${item.apiKey}`}
                            value={item.id}
                          >
                            {item.lang_name}
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col xs={12}>
                    <Form.Item
                      label={<IntlMessages id="antTable.title.firstSession" />}
                      name="first_session"
                    >
                      <Datepicker
                        style={{ minWidth: '100%' }}
                        format="DD-MM-YYYY"
                        placeholder="DD/MM/YYYY"
                        disabledDate={disabledDate}
                        onChange={(val) => {
                          onDateChange(val);
                          setState((p) => ({
                            ...p,
                            firstSession: val,
                          }));
                          segmentCount();
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={12}>
                    <Form.Item
                      label={<IntlMessages id="antTable.title.lastSession" />}
                      name="last_session"
                    >
                      <Datepicker
                        style={{ minWidth: '100%' }}
                        format="DD-MM-YYYY"
                        placeholder="DD/MM/YYYY"
                        disabledDate={(current) => {
                          const date =
                            moment(state?.firstSession, 'DD-MM-YYYY') || '';
                          return current <= date;
                        }}
                        onChange={(val) => {
                          onDateChange(val, 'last');
                          setState((p) => ({
                            ...p,
                            lastSession: val,
                          }));
                          segmentCount();
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={12}>
                    <Form.Item
                      label={<IntlMessages id="antTable.title.noProducts" />}
                      name="no_of_products"
                    >
                      <InputNumber
                        min={0}
                        onChange={(val) => {
                          setState((p) => ({
                            ...p,
                            noOfProduct: val,
                          }));
                          segmentCount();
                        }}
                      />
                    </Form.Item>
                  </Col>

                  <Col xs={12}>
                    <Form.Item
                      label={<IntlMessages id="antTable.title.status" />}
                      name="status"
                    >
                      <Select
                        onChange={(val) => {
                          setState((p) => ({
                            ...p,
                            statusAdd: val,
                          }));
                          segmentCount();
                        }}
                      >
                        <Select.Option value="active">
                          <IntlMessages id="status.option1" />
                        </Select.Option>
                        <Select.Option value="inactive">
                          <IntlMessages id="status.option2" />
                        </Select.Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col xs={12}>
                    <Form.Item
                      label={<IntlMessages id="antTable.title.clickRate" />}
                      name="click_rate"
                    >
                      <Select
                        onChange={(val) => {
                          setState((p) => ({
                            ...p,
                            clickRate: val,
                          }));
                          setTimeout(() => {
                            segmentCount('clickRate', val);
                          }, 10);
                        }}
                      >
                        <Select.Option value=">50">
                          <IntlMessages id="clickRate+50" />
                        </Select.Option>
                        <Select.Option value="<50">
                          <IntlMessages id="clickRate<50" />
                        </Select.Option>
                      </Select>
                    </Form.Item>
                  </Col>

                  <Col xs={12}>
                    <Form.Item
                      label={<IntlMessages id="antTable.title.shareRate" />}
                      name="share_rate"
                    >
                      <Select
                        onChange={(val) => {
                          setState((p) => ({
                            ...p,
                            shareRate: val,
                          }));
                          setTimeout(() => {
                            segmentCount('shareRate', val);
                          }, 10);
                        }}
                      >
                        <Select.Option value=">50">
                          <IntlMessages id="clickRate+50" />
                        </Select.Option>
                        <Select.Option value="<50">
                          <IntlMessages id="clickRate<50" />
                        </Select.Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col xs={24}>
                    <Form.Item
                      label={<IntlMessages id="antTable.title.userTags" />}
                      name="user_tags"
                    >
                      <Select
                        mode="tags"
                        onChange={(val) => {
                          setState((p) => ({
                            ...p,
                            userTag: val,
                          }));
                          segmentCount();
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24}>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                      className="checkboxStyle"
                    >
                      <Checkbox
                        checked={state?.status?.autoAdd}
                        onChange={() => {
                          autoAdd();
                        }}
                      >
                        <span style={{ fontWeight: '400' }}>
                          <IntlMessages id="autoAdd" />
                        </span>
                      </Checkbox>
                      <span style={{ fontWeight: '400', color: '#808080' }}>
                        {customerState?.statusFilter?.inactive}
                      </span>
                    </div>
                  </Col>
                </Row>
                {!isEmpty(state.countryCount) ||
                !isNull(state.languageID) ||
                !isEmpty(state.noOfProduct) ||
                !isEmpty(state.firstSession) ||
                !isEmpty(state.lastSession) ||
                !isEmpty(state.userTag) ||
                !isEmpty(state.statusAdd) ||
                !isEmpty(state.clickRate) ||
                !isEmpty(state.shareRate) ? (
                  <Row style={{ marginTop: 20 }}>
                    <Col xs={24}>
                      <span style={{ color: 'red' }}>
                        {`There are "${state.count}" people that will be added to this segment`}
                      </span>
                    </Col>
                  </Row>
                ) : null}
                <BottomViewWrapper className="bottomBtnWrapper">
                  <Row gutter={12} style={{ width: '100%' }}>
                    <Col xs={24}>
                      <Form.Item className="btn-form">
                        <CButton
                          className="submitBtnStyle"
                          htmlType="submit"
                          loading={btnLoader}
                          disabled={btnLoader}
                          style={{ marginRight: 20 }}
                        >
                          <IntlMessages id="customer.addSegment" />
                        </CButton>
                      </Form.Item>
                    </Col>
                    {/* <Form.Item>
                      <div
                        className="cancelBtnStyle"
                        onClick={handleForm}
                        role="button"
                        onKeyPress=""
                        tabIndex="-1"
                      >
                        <IntlMessages id="common.cancel" />
                      </div>
                    </Form.Item> */}
                  </Row>
                </BottomViewWrapper>
              </Form>
            </FormWrapper>
          ) : drawerType === 'category' ? (
            <FormWrapper>
              <Form
                form={form}
                onFinish={addCategory}
                layout="vertical"
                initialValues={initVal}
              >
                <Form.Item
                  label={<IntlMessages id="antTable.title.categoryName" />}
                  name="category_name"
                  rules={[
                    {
                      required: true,
                      message: <IntlMessages id="err.segmentName" />,
                    },
                    {
                      max: 50,
                      message: <IntlMessages id="err.max.50char" />,
                    },
                    {
                      whitespace: true,
                      message: <IntlMessages id="err.blankSpace" />,
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
                {/* {!isEmpty(state.countryCount) ||
                !isNull(state.languageID) ||
                !isEmpty(state.noOfProduct) ||
                !isEmpty(state.firstSession) ||
                !isEmpty(state.lastSession) ||
                !isEmpty(state.userTag) ||
                !isEmpty(state.statusAdd) ||
                !isEmpty(state.clickRate) ||
                !isEmpty(state.shareRate) ? (
                  <Row style={{ marginTop: 20 }}>
                    <Col xs={24}>
                      <span style={{ color: 'red' }}>
                        {`There are "${state.count}" people that will be added to this segment`}
                      </span>
                    </Col>
                  </Row>
                ) : null} */}
                <BottomViewWrapper className="bottomBtnWrapper">
                  <Row gutter={12}>
                    <Form.Item>
                      <CButton
                        type="primary"
                        htmlType="submit"
                        loading={btnLoader}
                        disabled={btnLoader}
                        style={{ marginRight: 20 }}
                      >
                        <IntlMessages id="customer.addCategory" />
                      </CButton>
                    </Form.Item>
                    <Form.Item>
                      <div
                        className="cancelBtnStyle"
                        onClick={handleForm}
                        role="button"
                        onKeyPress=""
                        tabIndex="-1"
                      >
                        <IntlMessages id="common.cancel" />
                      </div>
                    </Form.Item>
                  </Row>
                </BottomViewWrapper>
              </Form>
            </FormWrapper>
          ) : drawerType === 'category-delete' ? (
            <FormWrapper>
              <Form
                form={form}
                onFinish={deleteCategory}
                layout="vertical"
                initialValues={initVal}
              >
                <Form.Item
                  label={
                    <p className="titleStyle">
                      <IntlMessages id="marketing.categories" />
                    </p>
                  }
                  rules={[
                    {
                      required: true,
                      message: <IntlMessages id="req.category" />,
                    },
                  ]}
                  name="category_id"
                >
                  <Select
                    mode="multiple"
                    showSearch
                    placeholder={<IntlMessages id="marketing.chooseCategory" />}
                    optionFilterProp="children"
                    onChange={onChangeCatSelect}
                    onFocus={onFocus}
                    onBlur={onBlur}
                    onSearch={onSearch}
                    filterOption={(input, option) =>
                      option.children
                        .toLowerCase()
                        .indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {categoryList &&
                      categoryList.map((data) => (
                        <Option value={data.id}>{data.category_name}</Option>
                      ))}
                  </Select>
                </Form.Item>

                <BottomViewWrapper className="bottomBtnWrapper">
                  <Row gutter={12}>
                    <Form.Item>
                      <CButton
                        type="primary"
                        htmlType="submit"
                        loading={btnLoader}
                        disabled={btnLoader}
                        style={{ marginRight: 20 }}
                      >
                        <IntlMessages id="Delete category" />
                      </CButton>
                    </Form.Item>
                    <Form.Item>
                      <div
                        className="cancelBtnStyle"
                        onClick={handleForm1}
                        role="button"
                        onKeyPress=""
                        tabIndex="-1"
                      >
                        <IntlMessages id="common.cancel" />
                      </div>
                    </Form.Item>
                  </Row>
                </BottomViewWrapper>
              </Form>
            </FormWrapper>
          ) : (
            <>
              <Collapse
                defaultActiveKey={['1']}
                bordered={false}
                expandIconPosition={expandIconPosition}
                expandIcon={({ isActive }) =>
                  isActive ? <MinusOutlined /> : <PlusOutlined />
                }
              >
                <Panel
                  header={<IntlMessages id="tab.status" />}
                  key="1"
                  style={{ fontWeight: '600' }}
                >
                  <div
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                    className="checkboxStyle"
                  >
                    <Checkbox
                      checked={state?.status?.active}
                      onChange={(val) => {
                        statusFilter(val, 'active');
                      }}
                    >
                      <span style={{ fontWeight: '400' }}>
                        <IntlMessages id="common.active" />
                      </span>
                    </Checkbox>

                    <span style={{ fontWeight: '400', color: '#808080' }}>
                      {customerState?.statusFilter?.active}
                    </span>
                  </div>
                  <div
                    style={{
                      marginTop: '10px',
                      display: 'flex',
                      justifyContent: 'space-between',
                    }}
                    className="checkboxStyle"
                  >
                    <Checkbox
                      checked={state?.status?.inactive}
                      onChange={(val) => {
                        statusFilter(val, 'inactive');
                      }}
                    >
                      <span style={{ fontWeight: '400' }}>
                        <IntlMessages id="common.inActive" />
                      </span>
                    </Checkbox>

                    <span style={{ fontWeight: '400', color: '#808080' }}>
                      {customerState?.statusFilter?.inactive}
                    </span>
                  </div>
                </Panel>
                <Panel
                  header={<IntlMessages id="tab.country" />}
                  key="2"
                  id="area"
                  style={{ fontWeight: '600' }}
                >
                  <Select
                    showArrow
                    value={state?.country}
                    mode="multiple"
                    style={{ width: '100%' }}
                    onChange={(data) => {
                      setState((p) => ({
                        ...p,
                        country: data,
                      }));
                    }}
                  >
                    {countries.map((item) => (
                      <Select.Option
                        key={`countries_${item.apiKey}`}
                        value={item}
                      >
                        {item}
                      </Select.Option>
                    ))}
                  </Select>
                </Panel>
                <Panel
                  header={<IntlMessages id="tab.language" />}
                  key="3"
                  style={{ fontWeight: '600' }}
                >
                  <Select
                    showArrow
                    value={state?.language_id}
                    mode="multiple"
                    style={{ width: '100%' }}
                    onChange={(data) => {
                      setState((p) => ({
                        ...p,
                        language_id: data,
                      }));
                    }}
                  >
                    {languages.map((item) => (
                      <Select.Option
                        key={`languages${item.apiKey}`}
                        value={item.id}
                      >
                        {item.lang_name}
                      </Select.Option>
                    ))}
                  </Select>
                </Panel>
                <Panel
                  header={<IntlMessages id="tab.usertags" />}
                  key="4"
                  style={{ fontWeight: '600' }}
                >
                  <Select
                    showArrow
                    value={state?.user_tag}
                    mode="multiple"
                    style={{ width: '100%' }}
                    onChange={(data) => {
                      setState((p) => ({
                        ...p,
                        user_tag: data,
                      }));
                    }}
                  >
                    {userTags.map((item) => (
                      <Select.Option
                        key={`userTags${item.apiKey}`}
                        value={item}
                      >
                        {item}
                      </Select.Option>
                    ))}
                  </Select>
                </Panel>
                <Panel
                  header={<IntlMessages id="PRODUCTS" />}
                  key="5"
                  style={{ fontWeight: '600' }}
                >
                  <Select
                    showArrow
                    value={state?.product_list}
                    mode="multiple"
                    style={{ width: '100%' }}
                    onChange={(data) => {
                      setState((p) => ({
                        ...p,
                        product_list: data,
                      }));
                    }}
                  >
                    {product.map((item) => (
                      <Select.Option
                        // key={`productlist${item.apiKey}`}
                        value={item.id}
                      >
                        {item.device_name}
                      </Select.Option>
                    ))}
                  </Select>
                </Panel>
              </Collapse>
              <br />
              <div style={{ textAlign: 'right' }}>
                <span
                  style={{
                    cursor: 'pointer',
                    backgroundColor: '#09295D',
                    borderRadius: '5px',
                    color: '#fff',

                    padding: '8px 14px',
                  }}
                  onClick={() => {
                    cancleFilter();
                    setState((p) => ({
                      ...p,
                      visible: true,
                    }));
                  }}
                >
                  <IntlMessages id="filter.reset" />
                </span>
              </div>
              <BottomViewWrapper className="bottomBtnWrapper">
                <Row gutter={24} style={{ width: '100%' }}>
                  <Col xs={24}>
                    <Form.Item className="btn-form">
                      <CButton
                        className="submitBtnStyle"
                        htmlType="submit"
                        onClick={() => {
                          getFilterCustomer();
                          onClose();
                        }}
                        style={{ marginRight: 20 }}
                      >
                        <IntlMessages id="common.submit" />
                      </CButton>
                    </Form.Item>
                  </Col>
                  {/* <Form.Item className="btn-form">
                    <div
                      className="cancelBtnStyle"
                      onClick={
                        state.filterMode === true ? onClose : cancleFilter
                      }
                      role="button"
                      onKeyPress=""
                      tabIndex="-1"
                    >
                      <IntlMessages id="common.cancel" />
                    </div>
                  </Form.Item> */}
                </Row>
              </BottomViewWrapper>
            </>
          )}
        </Drawer>
      </div>
    </CustomerWrapper>
  );
}

export default CustomerDrawer;

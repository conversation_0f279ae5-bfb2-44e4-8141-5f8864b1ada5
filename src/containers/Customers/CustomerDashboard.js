/* eslint-disable new-cap */
/* eslint-disable no-undef */
/* eslint-disable consistent-return */
/* eslint-disable no-shadow */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
/* eslint-disable no-unused-expressions */
/* eslint-disable react/jsx-no-bind */
/* eslint-disable no-param-reassign */
/* eslint-disable react/no-array-index-key */
/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable no-nested-ternary */
/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
import React, { useEffect, useState, createRef } from 'react';
import {
  Row,
  Col,
  Spin,
  Button,
  Select,
  Input,
  Icon,
  TableColumnsType,
  Table,
} from 'antd';
import moment from 'moment';
import { useScreenshot } from 'use-react-screenshot';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  Tooltip,
  BarChart,
  Bar,
  Cell,
  ReferenceLine,
} from 'recharts';
import basicStyle from '@chill/assets/styles/constants';
import IntlMessages from '@chill/components/utility/intlMessages';
import { isEmpty } from 'lodash';
import getApiData from '@chill/lib/helpers/apiHelper';
import { injectIntl } from 'react-intl';
import { useSelector } from 'react-redux';
import theme from '@chill/config/theme/default';
import LinkBox from '@chill/components/LinkBox';
import {
  DownloadOutlined,
  DownOutlined,
  FilterOutlined,
  LeftCircleOutlined,
  SwapOutlined,
  LoadingOutlined,
} from '@ant-design/icons';

import Popover from '@chill/components/uielements/popover';

import TableWrapper from '@chill/assets/styles/AntTables.styles';
import DatePicker from '@chill/components/uielements/datePicker';
import lineSVG from '@chill/assets/images/LineChart.svg';
import goalSVG from '@chill/assets/images/goal.svg';
import fanSVG from '@chill/assets/images/blackfan.png';
import meterPng from '@chill/assets/images/meter.png';
import locSVG from '@chill/assets/images/location.svg';
import logoPng from '@chill/assets/images/logo.png';
import UsageCustomerProfile from '@chill/components/UsageCustomerProfile';
import CleanAirCustomerProfile from '@chill/components/CleanAirCustomerProfile';
import CanopyTempCustomerProfile from '@chill/components/CanopyTempCustomerProfile';
import NotificationCustomerProfile from '@chill/components/NotificationCustomerProfile';
import TripsCustomerProfile from '@chill/components/TripsCustomerProfile';
import {
  Document,
  Image,
  Page,
  PDFDownloadLink,
  Text,
  View,
} from '@react-pdf/renderer';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import ReactToPrint from 'react-to-print';
import MarketingWrapper from '../Marketing/Marketing.Styles';
import { WidgetWrapper } from '../Widgets/Widgets.styles';
import StickerWidget from '../Widgets/Sticker/StickerWidget';
import ReportsWidget from '../Widgets/Report/ReportWidget';
import IsoWidgetsWrapper from '../Widgets/WidgetsWrapper';

function Widgets({ visible, setDashboardVisible, initialValues }) {
  const { rowStyle, colStyle } = basicStyle;
  const ref = createRef(null);
  const ref2 = createRef(null);
  const ref3 = createRef(null);

  const [durationVisible, setDurationVisibility] = useState(false);

  const [duration, setDuration] = useState(
    <IntlMessages id="dashboard.thisweek" />,
  );
  const [pageLoad, setPageLoad] = useState(false); // true in api false =======TODO
  const { language } = useSelector((st) => st.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);
  const [cperiodType, setCPeriodType] = useState('this_week');
  const [filterMode, setFilterMode] = React.useState(false);

  const [filterByType, setFilterByType] = useState('');
  const [filterByName, setFilterByName] = useState('');
  const [sort, setSort] = useState(false);
  const [filter, setFilter] = React.useState({ pagination: {}, filters: {} });
  const [dData, setdData] = useState({ data: {}, loader: false });
  const { RangePicker } = DatePicker;
  const [dateRange, changeDateRange] = useState(null);
  const [fromDate, setFromDate] = useState();
  const [toDate, setToDate] = useState();
  const [stEndDate, setStEndDate] = useState({
    stDate: fromDate,
    endDate: toDate,
  });
  const [downloadReport, setDownloadReport] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  // const componentRef = useRef();

  const columns = [
    {
      title: <IntlMessages id="No" />,
      dataIndex: 'id',
      rowKey: 'id',
      width: 50,
      className: 'fullname-cell',
      render: (text, data, index) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="No" />
            </span>
            <span className="mobile-lbl-val">{index + 1 || '-'}</span>
          </div>
        );
      },
    },
    {
      key: 'sort_campaign_name',
      title: <IntlMessages id="customer.customerName" />,
      dataIndex: 'customer_name',
      rowKey: 'customer_name',
      width: 100,
      sortDirections: ['descend', 'ascend'],
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="customer.customerName" />
            </span>
            <div className="listNameView">
              <span className="mobile-lbl-val">{text || '-'}</span>
            </div>
          </div>
        );
      },
    },
    {
      key: 'sort_createdBy',
      title: <IntlMessages id="customer.email" />,
      dataIndex: 'customer_email',
      rowKey: 'customer_email',
      width: 170,
      className: 'fullname-cell',
      render: (text, data) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="customer.email" />
            </span>
            <div className="listNameView">
              <span className="mobile-lbl-val">{text || '-'}</span>
            </div>
          </div>
        );
      },
    },
    {
      key: 'sort_campaign_name',
      title: <IntlMessages id="customer.connectedOn" />,
      dataIndex: 'connected_on',
      rowKey: 'connected_on',
      width: 120,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="customer.connectedOn" />
            </span>
            <div className="listNameView">
              <span className="mobile-lbl-val">{text || '-'}</span>
            </div>
          </div>
        );
      },
    },
    {
      key: 'sort_campaign_name',
      title: <IntlMessages id="customer.lastDataUpload" />,
      dataIndex: 'last_data_upload',
      rowKey: 'last_data_upload',
      width: 120,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="customer.lastDataUpload" />
            </span>
            <div className="listNameView">
              <span className="mobile-lbl-val">{text || '-'}</span>
            </div>
          </div>
        );
      },
    },
    {
      key: 'sort_brand_name',
      title: <IntlMessages id="customer.ssid" />,
      dataIndex: 'ssid',
      rowKey: 'ssid',
      width: 120,
      className: 'fullname-cell',
      render: (text, data) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="customer.ssid" />
            </span>
            <div className="listNameView">
              <span className="mobile-lbl-val">{text || '-'}</span>
            </div>
          </div>
        );
      },
    },
    {
      key: 'sort_createdAt',
      title: <IntlMessages id="customer.firmwareVersion" />,
      dataIndex: 'firmware_version',
      rowKey: 'firmware_version',
      width: 120,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="customer.firmwareVersion" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      key: 'product_name',
      title: <IntlMessages id="customer.productname" />,
      dataIndex: 'product_name',
      rowKey: 'product_name',
      width: 100,
      className: 'fullname-cell',
      sorter: true,
      // fixed: 'right',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="customer.productname" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
            {/* <p>{text.split('-')[0]}-</p>
            <p>{text.split('-')[1]}-</p>
            <p>{text.split('-')[2]}</p> */}
          </div>
        );
      },
    },
  ];
  const topBoxes = [
    {
      key: 'totalNumberOfTrips',
      text: 'customer.noOfTrips',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.data) ? dData.data?.totalNumberOfTrips : 0,
      img: lineSVG,
    },
    {
      key: 'totalConnectedTime',
      text: 'customer.connectedTime',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.data) ? dData.data?.totalConnectedTime : 0,
      img: goalSVG,
    },
    {
      key: 'filteringTime',
      text: 'customer.filteringTime',
      link: '/contacts',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.data) ? dData.data?.filteringTime : 0,
      img: fanSVG,
      fan: true,
    },
    {
      key: 'averageAirQuality',
      text: 'customer.AveAQS',
      link: '/manage-tags',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.data) ? dData.data?.averageAirQuality : 0,
      img: meterPng,
    },
  ];
  const data = [
    {
      date: '1 DEC',
      aqs: 40,
      faqs: 24,
      peak: 24,
      who: 50,
    },
    {
      date: '2 DEC',
      aqs: 30,
      faqs: 13,
      peak: 22,
      who: 50,
    },
    {
      date: '3 DEC',
      aqs: 20,
      faqs: 98,
      peak: 22,
      who: 50,
    },
    {
      date: '4 DEC',
      aqs: 27,
      faqs: 39,
      peak: 20,
      who: 50,
    },
    {
      date: '5 DEC',
      aqs: 18,
      faqs: 48,
      peak: 21,
      who: 50,
    },
    {
      date: '6 DEC',
      aqs: 23,
      faqs: 38,
      peak: 25,
      who: 50,
    },
    {
      date: '7 DEC',
      aqs: 34,
      faqs: 43,
      peak: 21,
      who: 50,
    },
  ];
  function renderTopBoxes() {
    return (
      <Row style={rowStyle} className="boxRowStyle">
        {topBoxes.map((widget, index) => {
          return (
            <Col
              lg={12}
              md={12}
              sm={24}
              xs={24}
              style={{
                marginBottom: 25,
                paddingRight: index === 0 || index === 2 ? 17 : 0,
                paddingLeft: index === 1 || index === 3 ? 7 : 0,
              }}
              className="boxesStyle"
              key={widget.text}
            >
              <IsoWidgetsWrapper className="topMarketingBox">
                <LinkBox link="">
                  <StickerWidget
                    number={widget.count || 0}
                    text={<IntlMessages id={widget.text} />}
                    icon={widget.icon}
                    fontColor={widget.fontColor}
                    bgColor={widget.bgColor}
                    image={widget.img}
                    fan={widget.fan}
                    aqs
                  />
                </LinkBox>
              </IsoWidgetsWrapper>
            </Col>
          );
        })}
      </Row>
    );
  }

  function renderSearchBar() {
    return (
      <div style={{ padding: '15px' }}>
        <Row gutter={10}>
          <Col lg={8} sm={24} md={12} xs={24}>
            <Input.Search
              style={{ minWidth: '100%', marginBottom: 8 }}
              onChange={(e) => {
                const val = e && e.target ? e.target.value : '';

                setFilterByName(val);
              }}
              value={filter.campaign_name}
              allowClear
              placeholder={messageArray['marketing.campaignName']}
            />
          </Col>
          <Col lg={8} sm={24} md={12} xs={24}>
            <Select
              style={{ width: '100%' }}
              onChange={(val) => {
                setFilterByType(val);
              }}
              placeholder={messageArray['marketing.type']}
              allowClear
            >
              {['feed_post', 'push_message', 'in_app_message'].map((item) => (
                <Select.Option key={`type${item}`} value={item}>
                  {item}
                </Select.Option>
              ))}
            </Select>
          </Col>
        </Row>
      </div>
    );
  }
  const addNew = () => {
    return (
      <MarketingWrapper>
        <div className="iconStyle marketingMainDiv">
          <span>
            <p
              className="addNewUser"
              style={{
                color: theme.colors.primaryColor,
                alignItems: 'center',
                // display:'flex'
              }}
            >
              <SwapOutlined
                className="filterIcon1"
                style={{ color: sort ? theme.colors.primaryColor : '#000' }}
                onClick={() => {
                  setSort(!sort);
                }}
              />
              <FilterOutlined
                className="filterIcon"
                style={{
                  color: filterMode ? theme.colors.primaryColor : '#000',
                }}
                onClick={() => {
                  setFilterMode((pre) => !pre);
                  setFilterByName('');
                  setFilterByType('');
                }}
              />
            </p>
          </span>
        </div>
      </MarketingWrapper>
    );
  };

  function durationChange(type) {
    if (type !== 'user') {
      setDurationVisibility((visiblePre) => !visiblePre);
    }
  }

  const good = '#90D233';
  const moderate = '#E99A53';
  const bad = '#C16C8A';
  const getColor = (p) => {
    if (p <= 50) {
      return good;
    }
    if (p >= 51 && p <= 100) {
      return moderate;
    }
    if (p >= 101) {
      return bad;
    }
  };
  const aqsData = !isEmpty(dData.data) ? dData.data.userProfileData : [];
  const tripsPerDayData = !isEmpty(dData.data) ? dData.data.tripsPerDay : [];

  const renderCustomizedLabel = (props) => {
    const { x, y, width, height, value } = props;

    const fireOffset = value.toString().length < 5;
    const offset = fireOffset ? -40 : 5;
    return (
      <text
        x={x + width - offset}
        y={y + height - 5}
        fill={fireOffset ? '#285A64' : '#fff'}
        textAnchor="end"
      >
        {value}
      </text>
    );
  };

  const customerDurationContent = (
    <WidgetWrapper>
      <div className="popMainDiv">
        <div
          className="isoDropdownLink"
          onClick={() => {
            setDuration(<IntlMessages id="dashboard.thisweek" />);
            setStEndDate({ stDate: '', endDate: '' });
            changeDateRange('');
            setDurationVisibility(!durationVisible);
            setCPeriodType('this_week');
          }}
          role="button"
          onKeyPress={() => {
            setDuration(<IntlMessages id="dashboard.thisweek" />);
            setStEndDate({ stDate: '', endDate: '' });
            changeDateRange('');
            setDurationVisibility(!durationVisible);
            setCPeriodType('this_week');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.thisweek" />
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setDuration('Last Week');
            setStEndDate({ stDate: '', endDate: '' });
            changeDateRange('');
            setDurationVisibility(!durationVisible);
            setCPeriodType('week');
          }}
          role="button"
          onKeyPress={() => {
            setDuration('Last Week');
            setStEndDate({ stDate: '', endDate: '' });
            changeDateRange('');
            setDurationVisibility(!durationVisible);
            setCPeriodType('week');
          }}
          tabIndex="-1"
        >
          Last Week
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setDuration('This Month');
            setStEndDate({ stDate: '', endDate: '' });
            changeDateRange('');
            setDurationVisibility(!durationVisible);
            setCPeriodType('this_month');
          }}
          role="button"
          onKeyPress={() => {
            setDuration('This Month');
            setStEndDate({ stDate: '', endDate: '' });
            changeDateRange('');
            setDurationVisibility(!durationVisible);
            setCPeriodType('this_month');
          }}
          tabIndex="-1"
        >
          This Month
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setDuration('Last Month');
            setStEndDate({ stDate: '', endDate: '' });
            changeDateRange('');
            setDurationVisibility(!durationVisible);
            setCPeriodType('month');
          }}
          role="button"
          onKeyPress={() => {
            setDuration('Last Month');
            setStEndDate({ stDate: '', endDate: '' });
            changeDateRange('');
            setDurationVisibility(!durationVisible);
            setCPeriodType('month');
          }}
          tabIndex="-1"
        >
          Last Month
        </div>
      </div>
    </WidgetWrapper>
  );

  function disabledDate(current) {
    // Can not select days before today and today
    return current && current > moment().endOf('day');
  }
  const returnMomentDateRange = (start, finish) => {
    return [moment(start, 'DD-MM-YYYY'), moment(finish, 'DD-MM-YYYY')];
  };
  function onDateChange(e, type) {
    if (e) {
      changeDateRange(returnMomentDateRange(e[0], e[1]));
    } else {
      changeDateRange([]);
    }
    if (e != null) {
      const date1 = moment(e[0]).format('DD-MM-YYYY');
      const date2 = moment(e[1]).format('DD-MM-YYYY');
      setStEndDate({ stDate: date1, endDate: date2 });
      console.log('date range---------------------', date1, date2);
    }
  }
  const AQI_TITLE = (aqs) => {
    if (aqs < 0) return 'Unknown';
    switch (true) {
      case aqs <= 50:
        return 'Good';
      case aqs >= 51 && aqs <= 100:
        return 'Moderate';
      case aqs >= 101 && aqs <= 150:
        return 'Sensitive';
      case aqs >= 151 && aqs <= 300:
        return 'Unhealthy';
      case aqs >= 301:
        return 'Hazardous';
      default:
        return '';
    }
  };
  /** Function to fetch dashboard's data
   * @function getDashboardData
   * @param {object} data customer_overview, user_by_region
   */

  async function getDashboardData(data = {}) {
    console.log('date range====', dateRange);
    data.period_type = cperiodType;
    data.start_end_date = stEndDate;
    data.user_id = initialValues?.id;
    try {
      const res = await getApiData(
        'user-profile-platform/view-user-profile',
        data,
        'post',
      );

      if (res.success && !isEmpty(res.data)) {
        const data = {};
        data.averageAirQuality = res.data?.averageAirQuality;
        data.filteringTime = res.data?.filteringTime;
        data.totalConnectedTime = res.data?.totalConnectedTime;
        data.aqiTitle = AQI_TITLE(res.data?.aqs);
        data.totalNumberOfTrips = res.data?.totalNumberOfTrips;
        data.userProfile = res.data?.userProfile;
        data.userProfileData = res.data?.userProfileData;
        data.deviceDetails = res.data?.deviceDetails;
        data.tripsPerDay = res.data?.tripsPerDay;
        setdData((pre) => ({ ...pre, data, loader: false }));
        setTimeout(() => {
          setDownloadReport(true);
        }, 500);
      } else {
        setdData((pre) => ({ ...pre, loader: false }));
      }
    } catch (err) {
      console.log('err ===', err);
      setdData((pre) => ({ ...pre, loader: false }));
    }
  }

  useEffect(() => {
    setdData((pre) => ({ ...pre, loader: true }));
    getDashboardData();
  }, [cperiodType, stEndDate]);

  console.log('initialValues ==>', initialValues);
  function getWindowDimensions() {
    const { innerWidth: width, innerHeight: height } = window;
    return {
      width,
      height,
    };
  }
  function useWindowDimensions() {
    const [windowDimensions, setWindowDimensions] = useState(
      getWindowDimensions(),
    );

    useEffect(() => {
      function handleResize() {
        setWindowDimensions(getWindowDimensions());
      }

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, []);

    return windowDimensions;
  }
  const { height, width } = useWindowDimensions();
  const printDocument = () => {
    setIsDownloading(true);
    const input = document.getElementById('divToPrint');

    const pdf = new jsPDF('p', 'pt', 'a4', false);
    // pdf.text('Bugaboo User Profile report', 10, 50);
    html2canvas(document.querySelector('#divToPrint'), { scale: '3' }).then(
      (canvas) => {
        // document.body.appendChild(canvas); // if you want see your screenshot in body.
        const imgData = canvas.toDataURL('image/png');
        pdf.addImage(imgData, 'PNG', 10, 50, 550, 0, undefined, false);
        pdf.addPage();
        // pdf.save('download.pdf');
      },
    );
    setTimeout(() => {
      html2canvas(document.querySelector('#divToPrint2'), { scale: '3' }).then(
        (canvas) => {
          // document.body.appendChild(canvas); // if you want see your screenshot in body.
          const imgData = canvas.toDataURL('image/png');
          pdf.addImage(imgData, 'PNG', 10, 0, 550, 0, undefined, false);
          pdf.addPage();
        },
      );
    }, 300);

    setTimeout(() => {
      html2canvas(document.querySelector('#divToPrint3'), { scale: '3' }).then(
        (canvas) => {
          // document.body.appendChild(canvas); // if you want see your screenshot in body.
          const imgData = canvas.toDataURL('image/png');
          pdf.addImage(imgData, 'PNG', 10, 50, 550, 0, undefined, false);
          pdf.save(`UserProfile Report(${moment().format('DD MMM YYYY')}).pdf`);
          setIsDownloading(false);
        },
      );
    }, 500);
  };
  const expandedRowRender = () => {
    const columns = [
      {
        title: <IntlMessages id="customer.productname" />,
        dataIndex: 'product_name',
        key: 'product_name',
      },
    ];

    const data = [];
    // eslint-disable-next-line no-plusplus
    for (let i = 0; i < 1; ++i) {
      data.push({
        key: i.toString(),
        product_name: dData?.data?.deviceDetails[0]?.product_name,
      });
    }
    return <Table columns={columns} dataSource={data} pagination={false} />;
  };
  if (visible) {
    return (
      <MarketingWrapper>
        <div
          className="thisAc"
          style={{ paddingTop: 16, alignItems: 'center', display: 'flex' }}
        >
          <div className="iconStyle">
            <span style={{ cursor: 'pointer' }}>
              <div
                onClick={() => {
                  setDashboardVisible(false);
                }}
                role="button"
                tabIndex={-1}
                onKeyPress={() => {
                  setDashboardVisible(false);
                }}
                className="createSegmentP"
                style={{
                  color: theme.colors.primaryColor,
                  display: 'flex',
                }}
              >
                <p className="iconPadding" style={{ paddingRight: '10px' }}>
                  <LeftCircleOutlined />
                </p>
                <IntlMessages id="common.goBack" />
              </div>
            </span>
          </div>
          <div style={{ flexDirection: 'row', display: 'flex' }}>
            <div>
              <RangePicker
                allowclear
                style={{ minWidth: '100%' }}
                format="DD-MM-YYYY"
                disabledDate={disabledDate}
                onChange={(val) => {
                  onDateChange(val);
                }}
                value={dateRange !== '' ? dateRange : ''}
                // defaultValue={[fromDate, toDate]}
              />
            </div>
            <div style={{ marginLeft: 20, marginRight: 20 }}>
              <Popover
                content={customerDurationContent}
                trigger="click"
                visible={durationVisible}
                onVisibleChange={durationChange}
                placement="bottomRight"
              >
                <p
                  style={{
                    cursor: 'pointer',
                    fontSize: window.innerWidth <= 400 ? '12px' : '15px',
                  }}
                >
                  {duration}
                  <DownOutlined style={{ marginLeft: 5 }} />
                </p>
              </Popover>
            </div>

            {/* <img width={600} src={image} alt="Screenshot" /> */}
            {/* <Button style={{ marginBottom: '10px' }} onClick={getImage}>
              Take screenshot
            </Button> */}
            <Button
              disabled={!downloadReport}
              type="primary"
              onClick={printDocument}
              className="changeBtn"
            >
              {isDownloading ? (
                <LoadingOutlined
                  style={{
                    fontSize: window.innerWidth > 281 ? 20 : 15,
                    marginRight: window.innerWidth > 280 ? 10 : 0,
                  }}
                />
              ) : (
                <DownloadOutlined
                  style={{
                    fontSize: window.innerWidth > 281 ? 20 : 15,
                    marginRight: window.innerWidth > 280 ? 10 : 0,
                  }}
                />
              )}
              <IntlMessages id="common.report" />
            </Button>
            {/* </div> */}
            {/* </div> */}
            {/* <Button
              disabled={!allCaptured}
              type="primary"
              // htmlType="submit"
              onClick={getImage}
              className="changeBtn"
            >
              <DownloadOutlined
                style={{
                  fontSize: window.innerWidth > 281 ? 20 : 15,
                  marginRight: window.innerWidth > 280 ? 10 : 0,
                }}
              />

              <IntlMessages id="common.report" />
            </Button> */}
          </div>
        </div>

        <Spin spinning={pageLoad}>
          {/* page ----1 */}
          <div ref={ref} id="divToPrint">
            <Row gutter={24} style={{ padding: 16, margin: 0 }}>
              <Col lg={8} sm={24} md={11} xs={24} className="overViewCol">
                <Row>
                  <div className="overViewContainer">
                    <h2 className="overViewText">
                      <Row className="marginWrapper">
                        <p>
                          <IntlMessages id="customer.userprofile" />
                        </p>
                        <Col sm={12} lg={12} md={12} xl={12} xs={12}>
                          <p className="overViewTextA">
                            {!isEmpty(dData.data)
                              ? dData?.data?.userProfile?.full_name
                              : '-'}
                          </p>
                          <Row className="marginWrapper1">
                            <div className="isoIconWrapper1">
                              <img
                                src={locSVG}
                                style={{
                                  width: 16,
                                  height: 16,
                                }}
                                alt="noIcon"
                              />
                            </div>
                            <Col>
                              <p>
                                {!isEmpty(dData.data)
                                  ? dData?.data?.userProfile?.location_name
                                  : '-'}
                              </p>
                              <p>
                                {!isEmpty(dData.data)
                                  ? dData?.data?.userProfile?.time
                                  : '-'}
                              </p>
                            </Col>
                          </Row>
                        </Col>
                      </Row>
                    </h2>
                  </div>
                </Row>
                <div style={{ marginTop: '15px' }}>{renderTopBoxes()}</div>
              </Col>
              <Col lg={16} md={12} sm={24} xs={24}>
                <Row style={rowStyle} gutter={0} justify="start">
                  <Col
                    className="dataList isoTrafficList"
                    xl={24}
                    lg={24}
                    sm={24}
                    md={24}
                    xs={24}
                    style={colStyle}
                  >
                    <IsoWidgetsWrapper style={{ height: '100%' }}>
                      <Spin spinning={dData?.loader}>
                        {/* <ReportsWidget
                        widgetClassName="mb0"
                        labelClsName="mb15"
                        style={{
                          height: '100%',
                          borderRadius: 25,
                          boxShadow: '5px 5px 5px 5px #0000',
                        }}
                      >
                        <Spin spinning={accLoading}>
                          <Column
                            {...config}
                            style={{
                              height: 250,
                              width: '100%',
                              marginTop: 20,
                            }}
                          />
                        </Spin>
                      </ReportsWidget> */}
                        <ReportsWidget
                          widgetClassName="mb0"
                          labelClsName="mb15"
                          style={{
                            height: '100%',
                            borderRadius: 25,
                            boxShadow: '5px 5px 5px 5px #0000',
                          }}
                        >
                          <div
                            style={{
                              display: 'flex',
                              justifyContent: 'center',
                            }}
                          >
                            Average air quality :
                            {!isEmpty(dData.data)
                              ? ` ${dData?.data?.aqiTitle}`
                              : 'Good'}
                          </div>
                          <BarChart
                            width={width * 0.46}
                            height={300}
                            data={aqsData}
                            margin={{
                              // top: 5,
                              // right: 30,
                              // left: 20,
                              bottom: 15,
                            }}
                          >
                            <CartesianGrid strokeDasharray="1 5" />
                            <XAxis
                              dataKey="day"
                              fontSize={12}
                              tickCount={40}
                              // interval={0}
                              angle={
                                cperiodType === 'month' ||
                                cperiodType === 'this_month'
                                  ? 45
                                  : 0
                              }
                              dy={
                                cperiodType === 'month' ||
                                cperiodType === 'this_month'
                                  ? 10
                                  : 0
                              }
                            />
                            <YAxis fontSize={12} />
                            <Tooltip cursor={{ fill: '#fff' }} />

                            <Bar
                              dataKey="AQS"
                              barSize={25}
                              radius={[7, 7, 0, 0]}
                            >
                              {/* <LabelList
                              dataKey="aqs"
                              content={renderCustomizedLabel}
                              position="top"
                              style={{ fill: 'black' }}
                          /> } */}

                              {!isEmpty(dData.data)
                                ? dData.data.userProfileData
                                  ? aqsData?.map((entry, index) => (
                                      <Cell fill={getColor(entry.AQS)} />
                                    ))
                                  : null
                                : null}
                            </Bar>
                            <Bar
                              dataKey="AQS_Peak"
                              barSize={25}
                              radius={[7, 7, 0, 0]}
                              fill="#82ca9d"
                            >
                              {!isEmpty(dData.data)
                                ? dData.data.userProfileData
                                  ? aqsData?.map((entry, index) => (
                                      <Cell fill={getColor(entry.AQS_Peak)} />
                                    ))
                                  : null
                                : null}
                            </Bar>
                            <Bar
                              dataKey="Filtered_AQS"
                              barSize={25}
                              radius={[7, 7, 0, 0]}
                              fill="#E99A53"
                            >
                              {!isEmpty(dData.data)
                                ? dData.data.userProfileData
                                  ? aqsData?.map((entry, index) => (
                                      <Cell
                                        fill={getColor(entry.Filtered_AQS)}
                                      />
                                    ))
                                  : null
                                : null}
                            </Bar>
                            <ReferenceLine
                              y={50}
                              stroke="blue"
                              label="WHO Threshold"
                            />
                          </BarChart>
                        </ReportsWidget>
                      </Spin>
                    </IsoWidgetsWrapper>
                  </Col>
                </Row>
              </Col>
            </Row>

            <Row className="isoTabRow">
              <Col xl={8} lg={8} sm={8} md={4} xs={8}>
                <div className="marketingText">
                  <IntlMessages id="customer.deviceDetails" />
                </div>
              </Col>
              <Col xl={8} lg={8} md={8} className="isoAddNew">
                <div />
              </Col>
              {/* <Col xl={8} lg={8} sm={24} md={12} xs={24}>
              <Tabs
                defaultActiveKey=""
                renderTabBar={renderTabBar}
                style={{ paddingLeft: 16 }}
                onChange={(key) => setFilterTab(key)}
              >
                <TabPane tab={<IntlMessages id="tab.all" />} key="All" />
                <TabPane
                  tab={<IntlMessages id="tab.delivered" />}
                  key="Delivered"
                />
                <TabPane tab={<IntlMessages id="tab.live" />} key="Live" />
                <TabPane
                  tab={<IntlMessages id="tab.expired" />}
                  key="Expired"
                />
                <TabPane
                  tab={<IntlMessages id="tab.archive" />}
                  key="Archive"
                />
              </Tabs>
            </Col> */}
              <Col xl={8} lg={8} md={8} className="isoAddNew">
                {addNew()}
              </Col>
            </Row>
            {filterMode ? renderSearchBar() : null}
            <TableWrapper
              scroll={{ x: 0 }}
              loading={dData.loader}
              rowKey={(record) => record.id}
              dataSource={dData.data.deviceDetails}
              // expandable={{ expandedRowRender, defaultExpandedRowKeys: ['0'] }}
              columns={columns}
              //   onChange={onChange}
              className="invoiceListTable"
              pagination={false}
              showSorterTooltip={false}
            />

            {/* page ----trips */}
            <TripsCustomerProfile
              tripsPerDayData={tripsPerDayData}
              totalNumberOfTrips={
                !isEmpty(dData.data) ? dData.data?.totalNumberOfTrips : 0
              }
            />
          </div>
          <div ref={ref2} id="divToPrint2">
            {/* page ----2 */}
            <UsageCustomerProfile
              cperiodType={cperiodType}
              stEndDate={stEndDate}
              initialValues={initialValues}
              totalUseTime={
                !isEmpty(dData.data) ? dData.data?.totalConnectedTime : 0
              }
              totalFiltering={
                !isEmpty(dData.data) ? dData.data?.filteringTime : 0
              }
            />

            {/* page ----3 */}
            <CleanAirCustomerProfile
              cperiodType={cperiodType}
              stEndDate={stEndDate}
              initialValues={initialValues}
            />
          </div>
          <div ref={ref3} id="divToPrint3">
            {/* page ----4 */}
            {/* <CanopyTempCustomerProfile /> this is in page 3 */}
            {/* page ----5 */}
            <NotificationCustomerProfile
              cperiodType={cperiodType}
              stEndDate={stEndDate}
              initialValues={initialValues}
            />
          </div>
        </Spin>
      </MarketingWrapper>
    );
  }
  return true;
}

export default injectIntl(Widgets);

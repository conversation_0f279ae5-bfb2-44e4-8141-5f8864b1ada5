import styled from 'styled-components';
import withDirection from '@chill/lib/helpers/rtl';
import theme from '@chill/config/theme/default';

const CustomerWrapper = withDirection(styled.div`
  .iconStyle {
    display: flex;
    align-items: center;
    height: 100%;
  }
  .customerStyle {
    display: flex;
    align-items: center;
    height: 100%;
    padding-left: 17px;
  }

  .main-content {
    padding: 21px;
    background-color: #ffffff;
    .title-col {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .titleText {
        span {
          font-family: Inter;
          font-size: 20px;
          font-weight: 700;
          line-height: 36px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #242731;
        }
      }
      .addNewBtn {
        padding-left: 4px;
        display: flex;
        justify-content: flex-end;
        cursor: pointer;
        gap: 5.98px;

        span {
          font-family: Inter;
          font-size: 16px;
          font-weight: 500;
          line-height: 24px;
          text-align: right;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #0086ff;
        }
        @media (max-width: 1440px) {
          gap: 2.98px;
          padding-left: 0px;
        }
      }
    }
    .search-col {
      width: 100%;
      .deviceTabs {
        .site-custom-tab-bar {
          margin-bottom: 0px;
        }
        .ant-tabs-nav::before {
          border-bottom: 1px solid #d4d4d4;
        }
        .ant-tabs-ink-bar-animated {
          background-color: #007aff;
          height: 4px;
        }
        .ant-tabs-tab-active {
          .ant-tabs-tab-btn {
            span {
              font-family: Inter;
              font-size: 18px;
              font-weight: 500;
              line-height: 24px;
              text-align: left;
              text-underline-position: from-font;
              text-decoration-skip-ink: none;
              color: #242731 !important;
            }
          }
        }
        .ant-tabs-tab {
          .ant-tabs-tab-btn {
            span {
              font-family: Inter;
              font-size: 18px;
              font-weight: 500;
              line-height: 24px;
              text-align: left;
              text-underline-position: from-font;
              text-decoration-skip-ink: none;
              color: #9a9ea5;
            }
          }
        }
      }
    }
  }
  .iconPadding {
    display: flex;
    padding-right: 7px;
  }
  .addSegmentP {
    display: flex;
    align-items: center;
    flex-direction: row;
  }
  .subTitleStyle {
    font-size: 12px;
    color: black;
    opacity: 0.8;
  }
  .popMainDiv {
    display: flex;
    flex-direction: column;
    & < div < div < div {
      background-color: red;
    }
  }
  .isoDropdownLink {
    color: #000;
    padding: 8px 0px;
    cursor: pointer;
  }
  .iconStyle {
    display: flex;
    align-items: center;
    height: 100%;
    margin-bottom: 20px;
  }
  .marketingMainDiv {
    align-items: flex-start;
  }
  .addNewUser {
    display: flex;
    justify-content: flex-end;
    cursor: pointer;
  }
  .filterIcon1 {
    transform: rotate(90deg);
  }

  .filterImg {
    padding-left: 24px;
  }
  .filterCount {
    background-color: ${theme.colors.primaryColor};
    font-family: Inter;
    font-size: 11px;
    font-weight: 500;
    text-align: center;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #ffffff;
    width: 16px;
    height: 16px;
    top: -6px;
    position: absolute;
    right: -7px;
    gap: 0;
    border-radius: 50%;
  }

  .table-col {
    .table-pagnation {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px;
      background-color: #f7f7f8;
      border: 1px solid #d4d4d4;
      border-radius: 0px 0px 8px 8px;
      border-top: none;
      .removeRowBtn {
        width: 103px;
        display: flex;
        align-items: center;
        gap: 10px;
        span {
          font-family: Inter;
          font-size: 14px;
          font-weight: 400;
          line-height: 18px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #007aff;
        }
      }
      .gotoPagediv {
        display: flex;
        align-items: center;
        .goToText {
          font-family: Inter;
          padding-right: 24px;

          font-size: 14px;
          font-weight: 400;
          line-height: 18px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #9a9ea5;
        }
        .gotoInput {
          width: 32px;
          margin: 0 8px;
          border: none;
          background: transparent;
          border-bottom: 1px solid #9a9ea5;
          border-radius: 0px;
        }
        .gotoInput:focus {
          // border: none;
        }
      }
      .tabel-custom-pagination {
        .ant-pagination-disabled {
          .ant-pagination-item-link {
            border: none;
            background: none;
          }
        }
        .ant-pagination-item-active {
          background: none;
          border: none;
          a {
            border: none;
            background: none;
            font-family: Inter;
            font-size: 14px;
            font-weight: 500;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #007aff;
          }
        }

        .ant-pagination-prev,
        .ant-pagination-next {
          .ant-pagination-item-link {
            border: none;
            background: none;
          }
        }
        .ant-pagination-item {
          background: none;
          border: none;
          // a {
          //   border: none;
          //   background: none;
          //   font-family: Inter;
          //   font-size: 14px;
          //   font-weight: 400;
          //   text-align: left;
          //   text-underline-position: from-font;
          //   text-decoration-skip-ink: none;
          //   color: #242731;
          // }
        }
      }
    }
  }

  .filterIcon {
    padding-left: 12px;
    padding-right: 12px;
    color: rgb(0, 0, 0);
    margin: 0px 12px;
    border-left: 1px solid #eee;
    border-right: 1px solid #eee;
  }
  .iconPadding {
    display: flex;
    padding-right: 7px;
    @media (max-width: 281px) {
      display: none;
    }
  }
  .marketingText {
    font-weight: 400;
    text-transform: uppercase;
  }

  .hiddenCol {
    padding-bottom: 16px;
    display: flex;
    justify-content: flex-end;
    @media (min-width: 767px) {
      display: none;
    }
  }
  .isoTab {
    display: flex;
    justify-content: center;
  }
  .isoTab2 {
    justify-content: center;
  }
  .isoTab > div > div::before {
    position: unset;
  }
  .isoTab > div > div > div > div > div {
    padding: 0px;
  }
  .isoTab > div > div > div > div {
    padding-bottom: 16px;
  }
  .isoTab > div > div:nth-child(1) {
    margin: 0px;
  }
  .isoTab > div > div:nth-child(2) {
    display: none;
  }
  .isoTabs > div:nth-child(1) {
    margin: 0px;
  }
  .isoAddNew {
    display: flex;
    justify-content: flex-end;
    @media (max-width: 767px) {
      display: none;
    }
  }
  .isoTabRow {
    padding: 16px 16px 0px;
  }
`);

export default CustomerWrapper;

/* eslint-disable react/button-has-type */
/* eslint-disable eqeqeq */
/* eslint-disable array-callback-return */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-lone-blocks */
/* eslint-disable react/jsx-no-bind */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
/* eslint-disable no-param-reassign */
/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable import/extensions */
import React, { useEffect, useState } from 'react';
import {
  Button,
  Col,
  Input,
  Pagination,
  Popover,
  Row,
  Tabs,
  Typography,
} from 'antd';
import { findIndex, isArray, isEmpty, isObject, startCase } from 'lodash';
import { useSelector } from 'react-redux';
import IntlMessages from '@chill/components/utility/intlMessages';
import TableWrapper from '@chill/assets/styles/AntTables.styles';
import { DeleteOutlined, EditOutlined, SwapOutlined } from '@ant-design/icons';
import { MdArrowBackIosNew, MdArrowForwardIos } from 'react-icons/md';
import theme from '@chill/config/theme/default';
import { TabPane } from '@chill/components/uielements/tabs';
import getApiData from '@chill/lib/helpers/apiHelper';
import { getLanguageList } from '@chill/lib/helpers/utility';
import Notification from '@chill/components/Notification';
import Popconfirms from '@chill/components/Feedback/Popconfirm';
import editSVG from '@chill/assets/images/sidebarIcons/viewProduct.svg';
import addIcon from '@chill/assets/images/add-square.png';
import threeDotsDark from '@chill/assets/images/threeDots-dark.png';
import threeDotsBlue from '@chill/assets/images/three-dots-blue.png';
import filterIcon from '@chill/assets/images/page-filter-icon.png';
import rightArrow from '@chill/assets/images/right-arrow.png';
import CustomerWrapper from './Customers.Styles';
import CustomerDrawer from './CustomerDrawer';
import CustomerForm from './CustomerForm';
import CustomerDashboard from './CustomerDashboard';

const brandNames = [
  'bugaboo',
  'cbt-aqm',
  'cbtsmartpad',
  'cbtsmartcar',
  'cbthc',
];

/**
 *
 * @module Customers
 */

const { Text } = Typography;

function Customers() {
  const languages = useSelector((state) => state.Language.languages);
  const [actionMenu, setActionMenu] = useState(false);
  const [isIndex, setIsIndex] = useState(null);
  const [state, setState] = useState({
    initObj: {},
    visible: false,
    drawerType: '',
    firstSession: '',
    lastSession: '',
    status: {
      active: false,
      inactive: false,
      autoAdd: false,
    },
    statusAdd: [],
    country: '',
    language_id: null,
    user_tag: [],
    product_list: [],
    filterMode: false,
    noOfProduct: null,
    count: null,
    countryCount: [],
    languageID: null,
    userTag: [],
    shareRate: '',
    clickRate: '',
  });

  const { language } = useSelector((st) => st.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);
  const [customerState, setCustomerState] = useState({
    customers: [],
    statusFilter: {},
    customerLoad: true,
  });
  const [filter, setFilter] = useState({ pagination: {}, filters: {} });
  const [countries, setCountries] = useState([]);
  const [userTags, setUserTags] = useState([]);
  const [productList, setProduct] = useState([]);
  const [customerModal, setCustomerModal] = useState(false);
  const [sort, setSort] = useState(false);
  const [segmentList, setSegmentList] = useState();
  const [filterTab, setFilterTab] = useState('All');
  const [dasboardVisible, setDasboardVisible] = useState(false);
  const userData = useSelector((states) => states.Auth.userData);
  const uData = isObject(userData) ? { ...userData } : {};
  const [currentPage, setCurrentPage] = useState(0);
  const [gotoPage, setGotoPage] = React.useState(1);

  // this function for get segments
  /** this function for get segments
   * @function getSegmentList
   * @param {object} data {}
   */
  async function getSegmentList() {
    try {
      const response = await getApiData('getSegmentList', {}, 'POST');
      if (response.success) {
        setSegmentList(response.data);
        setState((p) => ({
          ...p,
          countryCount: [],
          userTag: [],
          languageID: null,
          statusAdd: [],
          noOfProduct: null,
          firstSession: '',
          lastSession: '',
        }));
      } else {
        setSegmentList(response.data);
      }
    } catch (err) {
      setSegmentList((preState) => ({
        ...preState,
        customerLoad: false,
      }));
    }
  }

  // this function for get country list
  /** this function for get country list
   * @function getCountryList
   * @param {object} data {}
   */
  async function getCountryList() {
    try {
      const response = await getApiData('getCountryList', {}, 'POST');
      if (response.success && isArray(response.data)) {
        setCountries(response.data);
      } else {
        Notification('error', response.message);
      }
    } catch (error) {
      console.log('error ===', error);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  /** this function for get user tags
   * @function getUserTags
   * @param {object} data {}
   */
  async function getUserTags() {
    try {
      const response = await getApiData('getUserTags', {}, 'POST');
      if (response.success && isArray(response.data)) {
        setUserTags(response.data);
      } else {
        Notification('error', response.message);
      }
    } catch (error) {
      console.log('error ===', error);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  /** this function for get product list
   * @function getProducts
   * @param {object} data {}
   */
  async function getProducts() {
    try {
      const response = await getApiData('getProductDeviceList', {}, 'GET');
      if (response.success && isArray(response.data)) {
        setProduct(response.data);
      } else {
        Notification('error', response.message);
      }
    } catch (error) {
      console.log('error ===', error);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  /** Function to fetch and filter Customer data list
   * @function getFilterCustomer
   * @param {object} data status, country, language_id, user_tags, device_id, page
   */
  async function getFilterCustomer(item) {
    let statusAr = [];
    if (state.status.active) {
      statusAr = [...statusAr, 'active'];
    }
    if (state.status.inactive) {
      statusAr = [...statusAr, 'inactive'];
    }
    const data = {
      status: statusAr,
      country: state.country,
      language_id: state.language_id,
      user_tags: state.user_tag,
      device_id: state.product_list,
      page: item?.page || 1,
    };

    if (sort) {
      data.sort = 'ASC';
    } else {
      data.sort = 'DESC';
    }

    if (filterTab !== 'All') {
      data.segment_id = Number(filterTab);
    }

    try {
      const response = await getApiData('getFilterCustomer', data, 'POST');
      if (response.success) {
        setCustomerState((preState) => ({
          ...preState,
          customers: isArray(response.data) ? response.data : [],
          customerLoad: false,
        }));
        const pagination =
          filter && filter.pagination ? { ...filter.pagination } : {};
        pagination.total = response.total_count || 0;
        pagination.pageSize = response.per_page || 10;
        pagination.current = response.page || 1;
        setFilter((f) => ({ ...f, pagination }));
        setState((p) => ({ ...p, filterMode: true }));
      } else {
        setCustomerState((preState) => ({
          ...preState,
          customerLoad: false,
        }));
      }
    } catch (error) {
      console.log('error ===', error);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  React.useEffect(() => {
    getLanguageList();
    getCountryList();
    getUserTags();
    getProducts();
    getSegmentList();
  }, []);

  React.useEffect(() => {
    getFilterCustomer();
  }, [filterTab]);

  // this function for handle pagination
  /** this function for handle pagination
   * @function onChange
   * @param {object} data page
   */
  function onChange(pagination) {
    console.log(
      `hello ~ file: index.js:278 ~ onChange ~ pagination:`,
      pagination,
      filter,
    );
    // const pager = filter && filter.pagination ? { ...filter.pagination } : {};
    // setCurrentPage((pagination.current - 1) * pagination.pageSize);
    // pager.current = pagination.current;
    // setFilter((f) => ({ ...f, pagination: pager }));
    // getFilterCustomer({
    //   page: pagination.current,
    //   ...filter.filters,
    // });

    const pager = filter && filter.pagination ? { ...filter.pagination } : {};
    setCurrentPage((pagination - 1) * filter.pageSize);
    // {(currentPage - 1) * defaultPageSize + index + 1}

    pager.current = pagination;
    setFilter((f) => ({ ...f, pagination: { ...pager } }));
    getFilterCustomer({
      page: pagination,
      ...filter.filters,
    });
    setSort(false);
  }

  // this function for update new customer data
  /** this function for update new customer data
   * @function updateData
   * @param {object} data customerName, email, tags
   */
  function updateData(data) {
    const customerAry = isArray(customerState.customers)
      ? [...customerState.customers]
      : [];
    const edit = !isEmpty(state.initObj) && data.id;
    const dataIndex = findIndex(customerAry, { id: data.id });
    if (edit && dataIndex > -1) {
      customerAry[dataIndex] = data;
    } else {
      getFilterCustomer();
    }
    setCustomerState({
      customers: customerAry,
    });
    setState({ initObj: {}, visible: false, view: false });
  }

  /** this function for delete segment
   * @function deleteSegment
   * @param {object} data segment_id
   */
  async function deleteSegment(id) {
    const data = {
      segment_id: id,
    };
    try {
      const response = await getApiData('segment/delete', data, 'POST');
      let msgTitle = 'error';
      if (response.success) {
        msgTitle = 'success';
        setFilterTab('All');
        getSegmentList();
      }
      Notification(msgTitle, response.message);
    } catch (error) {
      console.log('error ===', error);
    }
  }

  const renderTabBar = (props, DefaultTabBar) => (
    <DefaultTabBar {...props} className="site-custom-tab-bar" />
  );

  // fun. for visible PopOver
  function handleVisible() {
    setActionMenu((visiblePre) => !visiblePre);
    if (!actionMenu) {
      setIsIndex(null);
    }
  }

  const textStyle = {
    fontFamily: 'Inter',
    fontSize: '14px',
    fontWeight: 400,
    lineHeight: '16.94px',
    textAlign: 'left',
    textUnderlinePosition: 'from-font',
    textDecorationSkipInk: 'none',
    color: '#96AAC1',
    span: {
      fontFamily: 'Inter',
    },
  };

  // Popover content or list
  const content = (
    <CustomerWrapper>
      <div className="popMainDiv">
        {brandNames.includes[uData.brand_name] && (
          <div
            className="isoDropdownLink"
            onClick={() => {
              setDasboardVisible(true);
            }}
          >
            <img
              className="iconImg"
              src={editSVG}
              style={{ width: 16, height: 16, marginRight: 12 }}
              alt="noIcon"
            />
            <Text style={{ ...textStyle }}>View user profile</Text>
          </div>
        )}
        <div
          role="button"
          className="isoDropdownLink"
          onKeyPress=""
          tabIndex="-1"
          onClick={() => {
            setCustomerModal(true);
            setIsIndex(null);
          }}
        >
          <EditOutlined
            color="#96AAC1"
            style={{ paddingRight: 12, color: '#96AAC1', fontSize: 16 }}
          />
          {/* <IntlMessages id="customer.edit" /> */}
          <Text style={{ ...textStyle }}>Edit Customer</Text>
        </div>
        {/* <div className="isoDropdownLink">
          <Popconfirms
            title={<IntlMessages id="Sure to delete?" />}
            okText={<IntlMessages id="DELETE" />}
            cancelText={<IntlMessages id="No" />}
            onConfirm={() => {
              setIsIndex(null);
            }}
            onCancel={null}
          >
            <DeleteOutlined style={{ paddingRight: 12, fontSize: 16 }} />
            <IntlMessages id="customer.delete" />
          </Popconfirms>
        </div> */}
      </div>
    </CustomerWrapper>
  );

  // set item id in state
  function handleVisibleChange(item1) {
    setIsIndex(item1.id);
  }

  const columns = [
    {
      title: <IntlMessages id="customer.no" />,
      dataIndex: 'id',
      rowKey: 'id',
      width: 50,
      className: 'fullname-bold-cell',
      render: (text, data, index) => {
        console.log('first', index + currentPage + 1, index, currentPage);
        const currentPage1 = filter?.pagination?.current || 1;
        const defaultPageSize = 25;
        return (
          <div>
            <span className="label">
              <IntlMessages id="customer.no" />
            </span>
            <span
              className="mobile-lbl-val"
              style={{
                fontFamily: 'Inter',
                fontSize: 16,
                fontWeight: 400,
                lineHeight: '24px',
                textAlign: 'left',
                textUnderlinePosition: 'from-font',
                textDecorationSkipInk: 'none',
                color: '#242731',
              }}
            >
              {(currentPage1 - 1) * defaultPageSize + index + 1}
            </span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="customer.name" />,
      dataIndex: 'full_name',
      rowKey: 'full_name',
      width: 175,
      className: 'fullname-bold-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="customer.name" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="customer.emailId" />,
      dataIndex: 'email',
      rowKey: 'email',
      width: 175,
      className: 'fullname-bold-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="customer.emailId" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="customer.language" />,
      dataIndex: 'language_name',
      rowKey: 'language_name',
      width: 175,
      className: 'fullname-bold-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="customer.language" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="customer.noProduct" />,
      dataIndex: 'no_of_product',
      rowKey: 'no_of_product',
      width: 175,
      className: 'fullname-bold-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="customer.noProduct" />
            </span>
            <span className="mobile-lbl-val">{text || '0'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="customer.status" />,
      dataIndex: 'status',
      rowKey: 'status',
      width: 100,
      className: 'fullname-bold-cell',
      render: (text) => {
        // Define the styles dynamically based on status
        const statusStyles = {
          Active: { color: '#0086FF', backgroundColor: '#DDEFFF' },
          Archived: { color: '#3F3748', backgroundColor: '#E4E9EF' },
          Inactive: { color: '#D98634', backgroundColor: '#FFF2DD' },
        };

        // Get the styles for the current status
        const currentStyle = statusStyles[startCase(text)] || {
          color: '#000',
          backgroundColor: '#FFF',
        };

        return (
          <div
            style={{
              ...currentStyle,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '4px 8px',
              borderRadius: '54px',
              minHeight: '23px',
            }}
          >
            {/* Circle Indicator */}
            <span
              style={{
                width: '7px',
                height: '7px',
                borderRadius: '50%',
                backgroundColor: currentStyle.color,
                marginRight: '8px',
              }}
            />
            {/* Status Tag */}
            <span
              style={{
                ...currentStyle,
                fontFamily: 'Inter',
                fontSize: '12px',
                fontWeight: '400',
                lineHeight: '14.52px',
                textAlign: 'left',
                textUnderlinePosition: 'from-font',
                textDecorationSkipInk: 'none',
                display: 'inline-block',
              }}
            >
              {startCase(text) || '-'}
            </span>
          </div>
        );
      },
    },

    {
      title: <IntlMessages id="customer.shareRate" />,
      dataIndex: 'share_rate',
      rowKey: 'share_rate',
      width: 100,
      className: 'fullname-bold-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="customer.shareRate" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="customer.clickRate" />,
      dataIndex: 'click_rate',
      rowKey: 'click_rate',
      width: 100,
      className: 'fullname-bold-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="customer.clickRate" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="customer.action" />,
      dataIndex: 'action',
      rowKey: 'action',
      width: 100,
      className: 'fullname-bold-cell',
      render: (text, data) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Action" />
            </span>
            <span
              className="mobile-lbl-val"
              style={{
                padding: '0px 20px',
              }}
            >
              <Popover
                content={content}
                trigger="click"
                visible={isIndex === data.id ? !actionMenu : false}
                onVisibleChange={handleVisible}
                arrowPointAtCenter
                placement="bottomRight"
              >
                <button
                  onClick={() => {
                    handleVisibleChange(data);
                    setState({ ...state, initObj: data });
                  }}
                  style={{
                    backgroundColor: 'transparent',
                    border: 'none',
                    padding: 0,
                    cursor: 'pointer',
                  }}
                >
                  <img
                    src={isIndex === data.id ? threeDotsBlue : threeDotsDark}
                    alt="actionIcon"
                    style={{
                      fontSize: 16,
                    }}
                  />
                </button>
              </Popover>
            </span>
          </div>
        );
      },
    },
  ];

  const addNew = () => {
    return (
      <CustomerWrapper>
        <div
          className="iconStyle marketingMainDiv"
          style={{
            marginBottom: '0px',
          }}
        >
          <div
            className="addNewUser"
            style={{
              color: theme.colors.primaryColor,
              alignItems: 'center',
              // display:'flex'
            }}
          >
            <SwapOutlined
              className="filterIcon1"
              style={{
                fontSize: 24,
                color: sort ? theme.colors.primaryColor : '#000',
              }}
              onClick={() => {
                setSort(!sort);
                getFilterCustomer();
              }}
              size={24}
            />
            <div
              style={{
                position: 'relative',
              }}
            >
              <img
                src={filterIcon}
                alt="filter"
                onClick={() => {
                  setState((p) => ({
                    ...p,
                    visible: true,
                    drawerType: 'filter',
                  }));
                }}
                className="filterImg"
              />
              <div
                className="filterCount"
                style={{
                  display:
                    (state?.status?.active === true ||
                      state?.status?.inactive === true ||
                      !isEmpty(state.country) ||
                      !isEmpty(state.language_id) ||
                      !isEmpty(state.user_tag)) &&
                    state.filterMode === true
                      ? 'block'
                      : 'none',
                }}
              >
                {
                  [
                    state?.status?.active,
                    state?.status?.inactive,
                    !isEmpty(state.country),
                    !isEmpty(state.language_id),
                    !isEmpty(state.user_tag),
                  ].filter(Boolean).length
                }
              </div>
            </div>
          </div>
        </div>
      </CustomerWrapper>
    );
  };

  const { visible, drawerType, initObj, view } = state;
  const itemsss = [
    {
      label: (
        <span>
          <IntlMessages id="tab.all" />
        </span>
      ),
      key: 'All',
    },
  ];
  // const [items, setItem] = useState(itemsss);
  useEffect(() => {
    {
      isArray(segmentList) && segmentList.length > 0
        ? segmentList.map((sg) => {
            itemsss.push({
              label: (
                <span
                  className="tabs"
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}
                >
                  {sg.segment_name}
                  {filterTab == sg.id ? (
                    <Popconfirms
                      title={<IntlMessages id="Sure to delete?" />}
                      okText={<IntlMessages id="DELETE" />}
                      cancelText={<IntlMessages id="No" />}
                      onConfirm={() => {
                        deleteSegment(sg.id);
                      }}
                      onCancel={null}
                    >
                      <DeleteOutlined
                        style={{
                          paddingLeft: 10,
                          fontSize: 16,
                          color: 'red',
                        }}
                      />
                    </Popconfirms>
                  ) : null}
                </span>
              ),

              key: sg.id,
            });
          })
        : null;
    }
    // setItem(itemsss);
  }, [segmentList]);

  // <Tabs activeKey={type} onChange={settype} items={items}></Tabs>
  // function getWindowDimensions() {
  //   const { innerWidth: width, innerHeight: height } = window;
  //   return {
  //     width,
  //     height,
  //   };
  // }
  // function useWindowDimensions() {
  //   const [windowDimensions, setWindowDimensions] = useState(
  //     getWindowDimensions(),
  //   );

  //   useEffect(() => {
  //     function handleResize() {
  //       setWindowDimensions(getWindowDimensions());
  //     }

  //     window.addEventListener('resize', handleResize);
  //     return () => window.removeEventListener('resize', handleResize);
  //   }, []);

  //   return windowDimensions;
  // }
  return (
    <>
      {!dasboardVisible ? (
        <>
          <CustomerWrapper>
            <Row className="main-content" gutter={[0, 5]}>
              <Col xs={24} className="title-col">
                <div className="titleText">
                  <IntlMessages id="sidebar.customers" />
                </div>
                <div
                  style={{
                    color: theme.colors.primaryColor,
                  }}
                  className="addNewBtn"
                  onClick={() => {
                    setState((p) => ({
                      ...p,
                      visible: true,
                      drawerType: 'add',
                    }));
                  }}
                >
                  <IntlMessages id="customer.addSegment" />
                  <img src={addIcon} alt="addIcon" />
                </div>
              </Col>
              {/* <Col xl={4} lg={8} sm={8} md={4} xs={8}>
                <div className="marketingText">
                  <IntlMessages id="customer" />
                </div>
              </Col> */}
              {/* <Col sm={16} xs={16} className="hiddenCol">
                {addNew()}
              </Col> */}
              <Col
                xs={24}
                sm={24}
                md={18}
                lg={19}
                xl={20}
                xxl={21}
                className="search-col"
              >
                <Tabs
                  className="deviceTabs"
                  activeKey={filterTab}
                  renderTabBar={renderTabBar}
                  onChange={(key) => {
                    console.log('ssssss', key);
                    setFilterTab(key);
                  }}
                  // items={items}
                >
                  <TabPane tab={<IntlMessages id="tab.all" />} key="All" />
                  {isArray(segmentList) && segmentList.length > 0
                    ? segmentList.map((sg) => (
                        <TabPane
                          tab={
                            <span
                              className="tabs"
                              style={{
                                display: 'flex',
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                              }}
                            >
                              {sg.segment_name}
                              {filterTab == sg.id ? (
                                <Popconfirms
                                  title={<IntlMessages id="Sure to delete?" />}
                                  okText={<IntlMessages id="DELETE" />}
                                  cancelText={<IntlMessages id="No" />}
                                  onConfirm={() => {
                                    deleteSegment(sg.id);
                                  }}
                                  onCancel={null}
                                >
                                  <DeleteOutlined
                                    style={{
                                      paddingLeft: 10,
                                      fontSize: 16,
                                      color: 'red',
                                    }}
                                  />
                                </Popconfirms>
                              ) : null}
                            </span>
                          }
                          key={sg.id}
                        />
                      ))
                    : null}
                  {/* <TabPane
                tab={<IntlMessages id="tab.englishspeaking" />}
                key="2"
                />
              <TabPane tab={<IntlMessages id="tab.topshares" />} key="3" /> */}
                </Tabs>
              </Col>

              <Col
                xs={24}
                sm={24}
                md={6}
                lg={5}
                xl={4}
                xxl={3}
                style={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                  alignItems: 'center',
                  width: '100%',
                  borderBottom: '1px solid #D4D4D4',
                }}
              >
                {addNew()}
              </Col>
              <Col
                xs={24}
                className="table-col"
                style={{
                  marginTop: '20px',
                }}
              >
                <TableWrapper
                  // rowSelection={rowSelection}
                  loading={customerState.customerLoad}
                  rowKey={(record) => record.id}
                  dataSource={customerState.customers}
                  onChange={onChange}
                  columns={columns}
                  // pagination={filter.pagination || {}}
                  pagination={false}
                  className="invoiceListTable"
                  showSorterTooltip={false}
                />
                <div
                  className="table-pagnation"
                  style={{
                    justifyContent: 'flex-end',
                  }}
                >
                  <div
                    style={{
                      maxWidth: '850px',
                      width: '100%',
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    <Pagination
                      defaultCurrent={filter?.pagination?.current}
                      current={filter?.pagination?.current}
                      onChange={(pgnation) => {
                        onChange(pgnation);
                      }}
                      pageSize={filter?.pagination?.pageSize || 10}
                      total={filter?.pagination?.total}
                      showSizeChanger={false}
                      showLessItems
                      className="tabel-custom-pagination"
                      nextIcon={<MdArrowForwardIos />}
                      prevIcon={<MdArrowBackIosNew />}
                      // showLessItems={wWidth <= 425}
                    />
                    <div className="gotoPagediv">
                      <Text className="goToText">Go to page</Text>
                      <Input
                        type="number"
                        min={1}
                        max={filter?.pagination?.total}
                        defaultValue={filter?.pagination?.current}
                        onChange={(e) => {
                          const val =
                            e && e.target ? Number(e.target.value) : '';
                          setGotoPage(val);
                        }}
                        className="gotoInput"
                      />
                      <Button
                        type="text"
                        onClick={() => {
                          const pager =
                            filter && filter.pagination
                              ? { ...filter.pagination }
                              : {};
                          pager.current = gotoPage;
                          setFilter((f) => ({
                            ...f,
                            pagination: { ...pager },
                          }));
                          getFilterCustomer({
                            page: gotoPage,
                            ...filter.filters,
                          });
                        }}
                        style={{
                          paddingLeft: '12px',
                        }}
                      >
                        <img src={rightArrow} alt="arrow" />
                      </Button>
                    </div>
                  </div>
                </div>
              </Col>
            </Row>
          </CustomerWrapper>
          <CustomerDrawer
            initialValues={initObj}
            view={view}
            countries={countries}
            visible={visible}
            setState={setState}
            state={state}
            customerState={customerState}
            languages={languages}
            userTags={userTags}
            product={productList}
            drawerType={drawerType}
            getFilterCustomer={() => {
              getFilterCustomer();
            }}
            getSegmentList={getSegmentList}
            // getCustomerList={() => {
            //   getCustomerList();
            // }}
          />
          <CustomerForm
            visible={customerModal}
            onClose={(type, data) => {
              setCustomerModal(false);
              if (type === 'success') {
                updateData(data);
              }
            }}
            initialValues={initObj}
          />
        </>
      ) : (
        <CustomerDashboard
          visible={dasboardVisible}
          setDashboardVisible={setDasboardVisible}
          initialValues={initObj}
        />
      )}
    </>
  );
}

export default Customers;

/* eslint-disable import/no-dynamic-require */
/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable no-nested-ternary */
/* eslint-disable import/order */
/* eslint-disable no-unused-vars */
/* eslint-disable import/no-duplicates */
/* eslint-disable no-unused-vars */
/* eslint-disable new-cap */
/* eslint-disable react/self-closing-comp */
/* eslint-disable react/no-array-index-key */

import React, { useEffect, useState } from 'react';
import {
  Tabs,
  Card,
  Button,
  DatePicker,
  Popover,
  Drawer,
  Dropdown,
  Space,
  Typography,
  Divider,
  Menu,
  Row,
  Col,
  Tag,
} from 'antd';
import lineSVG from '@chill/assets/images/LineChart.svg';
import goalSVG from '@chill/assets/images/goal.svg';

// IMPORTING ICONS FROM REACT ICON
import {
  AiOutlineHome,
  // AiOutlineCheckCircle,
  AiOutlineDown,
} from 'react-icons/ai';

import {
  DownOutlined,
  LoadingOutlined,
  DownloadOutlined,
  ArrowUpOutlined,
  CaretRightOutlined,
  MenuOutlined,
} from '@ant-design/icons';
import { VscLayout, VscCircleFilled } from 'react-icons/vsc';
import { BsGraphUpArrow } from 'react-icons/bs';
import { FiSettings } from 'react-icons/fi';
import { GoDotFill } from 'react-icons/go';

import jsPDF from 'jspdf';
import moment from 'moment';
import html2canvas from 'html2canvas';
import IntlMessages from '@chill/components/utility/intlMessages';

// import gallery from '@chill/assets/images/gallery.svg';
import fox from '@chill/assets/images/fox.png';
import greenShareIcon from '@chill/assets/images/greenShareIcon.png';
import blueTreeIcon from '@chill/assets/images/blueTreeIcon.png';
import donkey from '@chill/assets/images/donkey.png';
import bee6 from '@chill/assets/images/bee6.png';
import morning2 from '@chill/assets/images/morning-2.png';
import morning1 from '@chill/assets/images/morning-1.png';
import morning from '@chill/assets/images/morning.png';
import Group9223 from '@chill/assets/images/Group 9223.png';
import Gausss from '@chill/containers/Analytics/Guagee';
// import theme from '@chill/config/theme/default';
import basicStyle from '@chill/assets/styles/constants';
// import { useSelector } from 'react-redux';
import GaugeChart from 'react-gauge-chart';
import close from '@chill/assets/images/close.png';
import fanSVG from '@chill/assets/images/blackfan.png';
import exchangeSVG from '@chill/assets/images/meter.png';
// import { useHistory } from 'react-router-dom';
import { ResponsiveContainer } from 'recharts';

import ReactApexChart from 'react-apexcharts';
import greenDownIcon from '@chill/assets/images/greenDownIcon.png';
import blueUpIcon from '@chill/assets/images/blueUpIcon.png';
import { WidgetWrapper } from '@chill/containers/Widgets/Widgets.styles';
import DataRangeIcon from '@chill/assets/images/DataRangeIcon.png';
import downloadIcon from '@chill/assets/images/downloadIcon.png';
import threeLineFltIcon from '@chill/assets/images/threeLineFltIcon.png';
import blueDownIcon from '@chill/assets/images/blueDownIcon.png';
import morningIcon from '@chill/assets/images/morningIcon.png';
import lunchIcon from '@chill/assets/images/lunchIcon.png';
import afternoonIcon from '@chill/assets/images/afternoonIcon.png';
import nightIcon from '@chill/assets/images/nightIcon.png';
import greenCircleUpIcon from '@chill/assets/images/greenCircleUpIcon.png';
import redCircleDownIcon from '@chill/assets/images/redCircleDownIcon.png';
import greenLine from '@chill/assets/images/greenLine.png';
import blueLine from '@chill/assets/images/blueLine.png';

// import Chartz from '@chill/containers/Analytics/PieChart';
import { PieChart, Pie, Cell } from 'recharts';
import BarGraph from '@chill/containers/Analytics/Barchart';
import Linear from '@chill/containers/Analytics/LineChart';
import Linear1 from '@chill/containers/Analytics/LineChart1';
import Linear2 from '@chill/containers/Analytics/LineChart2';
import Gauss from '@chill/containers/Analytics/GaugeChart';
import BarGraphsss from './Rebar';
import BarGraphzzz from './Rebars';
import HarChart from './Hbar';
import DailyUse from './Dailyuse';
import Horizontall from './Horizontal';
import BaarChart from './BaarChaart';
import BarGraphzzz1 from './Vbar';
import WeatherAlerts from './walerts';
import './Apps.css';
import Chartss from './DouhnutChart';
import Chartsss from './pie';
import BarrChart from './BaarChart';
import IsoWidgetsWrapper from '../Widgets/WidgetsWrapper';
import ReportsWidget from '../Widgets/Report/ReportWidget';
import getApiData from '@chill/lib/helpers/apiHelper';
import { isEmpty } from 'lodash';
import { display } from '@mui/system';
import { isArray, isEmpty, isObject } from 'lodash';
import { Column } from '@ant-design/charts';
import { useSelector } from 'react-redux';
import GaugeChart from 'react-gauge-chart';
import drangon from '@chill/assets/images/close.png';
import fanSVG from '@chill/assets/images/blackfan.png';
import exchangeSVG from '@chill/assets/images/meter.png';
import { useHistory } from 'react-router-dom';
import StickerWidget from '../Widgets/Sticker/StickerWidget';
import getApiData from '@chill/lib/helpers/apiHelper';

const { TabPane } = Tabs;

const { RangePicker } = DatePicker;

function App() {
  const { rowStyle, colStyle } = basicStyle;
  const [isRangePickerOpen, setIsRangePickerOpen] = useState(false);
  const [open, setOpen] = useState(false);
  // const userData = useSelector((state) => state.Auth.userData);
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadReport, setDownloadReport] = useState(false);
  const [cPeriodType, setCPeriodType] = useState('week');

  const [actUsrFltVis, setActUsrFltVis] = useState(false);
  const [actUsrFltTxt, setActUsrFltTxt] = useState(
    <IntlMessages id="dashboard.thisweek" />,
  );
  const [apiData, setApiData] = useState();
  const [userTabData, setUserTabData] = useState();
  const COLORS = ['#1172EC', '#F08280'];

  const handleGetAnalyticsData = async () => {
    try {
      const response = await getApiData(`analytics/overview`, {}, 'GET');
      if (response.success) {
        console.log('Analytics Data', response);
        setApiData(response);
      }
    } catch (error) {
      console.log('Error Fetching the Data', error);
    }
  };

  const handleGetUserTabData = async () => {
    try {
      const response = await getApiData(`analytics/user`, {}, 'GET');
      if (response.success) {
        console.log('UserTabData', response);
        setUserTabData(response);
      }
    } catch (error) {
      console.log('Error Fetching the Data', error);
    }
  };

  useEffect(() => {
    handleGetAnalyticsData();
    handleGetUserTabData();
  }, []);

  const childCountArray = userTabData?.childCountData;
  console.log('childcountArray', childCountArray);
  const totalCount = childCountArray?.reduce(
    (total, obj) => total + obj.child_count,
    0,
  );
  console.log('Total child count:', totalCount);

  const ProductsOwnedData = userTabData?.productsOwnedData;

  const RegisterAge = userTabData?.insightsData.avgRegisterAge;
  const ChildAge = userTabData?.insightsData.avgChildAge;
  const ActiveUsers = userTabData?.insightsData.activeUse;
  const HomeData = apiData?.pageViewData.home;
  const DashboardData = apiData?.pageViewData.dashboard;
  const SettingsData = apiData?.pageViewData.settings;
  const TimelineData = apiData?.pageViewData.timeline;
  const Background = apiData?.appActivity.background;
  const Active = apiData?.appActivity.active;

  const sliderOptions = {
    dots: true,
    infinite: false,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
  };
  const topLinkClick = [
    {
      title: 'Selling Products',
      subTitle: 'index.html',
      val: '4.32K',
    },
    {
      title: 'Landing',
      subTitle: 'index.html',
      val: '2.71K',
    },
    {
      title: 'Contact Us',
      subTitle: 'index.html',
      val: '1.89K',
    },
    {
      title: 'About Us',
      subTitle: 'index.html',
      val: '1.23K',
    },
    {
      title: 'Information Center',
      subTitle: 'index.html',
      val: '2.14K',
    },
  ];
  const [performanceChart, setPerformanceChart] = useState({
    newDailyUser: {
      data: [
        ['days', 'count'],
        ['01 Jul', 1],
        ['02 Jul', 2],
        ['03 Jul', 3],
        ['04 Jul', 2],
        ['05 Jul', 1],
        ['06 Jul', 2],
        ['07 Jul', 2],
        ['08 Jul', 3],
        ['09 Jul', 4],
        ['10 Jul', 3],
        ['11 Jul', 3],
        ['12 Jul', 2],
        ['13 Jul', 1],
        ['14 Jul', 2],
        ['15 Jul', 3],
        ['16 Jul', 4],
        ['17 Jul', 5],
        ['18 Jul', 16],
        ['19 Jul', 17],
        ['20 Jul', 18],
      ],
      total_count: 20,
      type: '',
      percentage: '',
    },
    postShares: {
      data: [
        ['days', 'count'],
        ['01 Jul', 3],
        ['02 Jul', 4],
        ['03 Jul', 5],
        ['04 Jul', 6],
        ['05 Jul', 5],
        ['06 Jul', 4],
        ['07 Jul', 3],
        ['08 Jul', 2],
        ['09 Jul', 1],
        ['10 Jul', 1],
        ['11 Jul', 2],
        ['12 Jul', 3],
        ['13 Jul', 4],
        ['14 Jul', 5],
        ['15 Jul', 6],
        ['16 Jul', 7],
        ['17 Jul', 6],
        ['18 Jul', 5],
        ['19 Jul', 4],
        ['20 Jul', 3],
      ],
      total_count: 0,
      type: '',
      percentage: '',
    },
    productUsed: {
      data: [
        ['days', 'count'],
        ['01 Jul', 1],
        ['02 Jul', 2],
        ['03 Jul', 3],
        ['04 Jul', 4],
        ['05 Jul', 5],
        ['06 Jul', 6],
        ['07 Jul', 7],
        ['08 Jul', 8],
        ['09 Jul', 9],
        ['10 Jul', 10],
        ['11 Jul', 11],
        ['12 Jul', 12],
        ['13 Jul', 13],
        ['14 Jul', 14],
        ['15 Jul', 15],
        ['16 Jul', 16],
        ['17 Jul', 17],
        ['18 Jul', 18],
        ['19 Jul', 19],
        ['20 Jul', 20],
      ],
      total_count: 0,
      type: '',
      percentage: '',
    },
  });
  const handleDateClick = () => {
    setIsRangePickerOpen(!isRangePickerOpen);
  };
  // const [period, setPeriod] = useState('' || 'week');
  // const [analyticsData, setAnalyticsData] = useState({
  //   ActiveUserChart: [
  //     { day: '16 Jul', users: 10 },
  //     { day: '17 Jul', users: 40 },
  //     { day: '18 Jul', users: 30 },
  //     { day: '19 Jul', users: 70 },
  //     { day: '20 Jul', users: 100 },
  //     { day: '21 Jul', users: 60 },
  //   ],
  //   activeUsers: [
  //     { percentage: '', changeType: '', value: 10 },
  //     { percentage: '', changeType: '', value: 20 },
  //   ],
  //   activityLogs: [
  //     {
  //       activity_name: 'Added A New Segment : All users By bugaboo',
  //       brand_id: 434,
  //       createdAt: 1679996132384,
  //       id: 595,
  //       updatedAt: 1679996132384,
  //       user_id: 434,
  //     },
  //     {
  //       activity_name: 'Added New User : mansi-Test By bugaboo',
  //       brand_id: 434,
  //       createdAt: 1678441368956,
  //       id: 594,
  //       updatedAt: 1678441368956,
  //       user_id: 434,
  //     },
  //   ],
  // });
  // const { language } = useSelector((state) => state.LanguageSwitcher);
  // eslint-disable-next-line global-require
  const handleRangePickerChange = () => {
    // Handle the selected date range here
  };

  const content = <RangePicker onChange={handleRangePickerChange} />;
  const showDrawer = () => {
    setOpen(true);
  };
  const onClose = () => {
    setOpen(false);
  };

  const dates = [
    [new Date('2024-01-01').getTime(), 1000000],
    [new Date('2024-01-02').getTime(), 1500000],
    [new Date('2024-01-03').getTime(), 2000000],
    // Add more data points as needed
  ];

  const seriesData = [
    [new Date('2024-01-01').getTime(), 120],
    [new Date('2024-01-02').getTime(), 130],
    [new Date('2024-01-03').getTime(), 125],
    // Add more data points as needed
  ];

  const pageViewBoxes = [
    {
      key: 'hPerc',
      text: 'common.home',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: `30.00%`, // !isEmpty(dData.datad) ? `${dData.datad?.hPerc}%` : 0,
      cornerColor: '#00B3FF',
      percentage: '0%',
      // count: `${HomeData}%`,
      img: (
        <AiOutlineHome
          style={{
            width: 45,
            height: 45,
            marginTop: 2,
            color: 'white',
            marginLeft: 0,
          }}
        />
      ),
    },
    {
      key: 'dPerc',
      text: 'customer.dashborad',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: `40.00%`, // !isEmpty(dData.datad) ? `${dData.datad?.dPerc}%` : 0,
      cornerColor: '#2DD683',
      percentage: '11.09%',
      // count: `${DashboardData}%`,
      img: (
        <VscLayout
          style={{
            width: 45,
            height: 45,
            marginTop: 2,
            color: 'white',
            marginLeft: 0,
          }}
        />
      ),
    },
    {
      key: 'tPerc',
      text: 'customer.timeline',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: `15.00%`, // !isEmpty(dData.datad) ? `${dData.datad?.tPerc}%` : 0,
      cornerColor: '#64AAE6',
      percentage: '11.09%',
      // count: `${TimelineData}%`,
      img: (
        <BsGraphUpArrow
          style={{
            width: 45,
            height: 45,
            marginTop: 2,
            color: 'white',
            marginLeft: 0,
          }}
        />
      ),
    },
    {
      key: 'sPerc',
      text: 'customer.settings',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      // count: `${SettingsData}%`,
      count: !isEmpty(HomeData) ? `${SettingsData}%` : 0,
      img: (
        <FiSettings
          style={{
            width: 45,
            height: 45,
            marginTop: 2,
            color: 'white',
            marginLeft: 0,
          }}
        />
      ),
    },
  ];

  const languageOptions = [
    { key: '1', value: 'English' },
    { key: '2', value: 'Spanish' },
    { key: '3', value: 'French' },
  ];

  const locationOptions = [
    { key: '1', value: 'India' },
    { key: '2', value: 'USA' },
    { key: '3', value: 'London' },
  ];

  const ageOptions = [
    { key: '1', value: '0-6 months' },
    { key: '2', value: '6-12 months' },
    { key: '3', value: '12-18 months' },
  ];

  const productsOptions = [
    { key: '1', value: 'abc' },
    { key: '2', value: 'xyz' },
    { key: '3', value: '123' },
  ];

  // const strollerBoxes = [
  //   {
  //     key: 'sentMessages',
  //     text: 'customer.fox',
  //     fontColor: '#000000',
  //     bgColor: '#ffffffff',
  //     count: 1,
  //     img: fox,
  //   },
  //   {
  //     key: 'totalClicks',
  //     text: 'customer.dragonfly',
  //     fontColor: '#000000',
  //     bgColor: '#ffffffff',
  //     count: 3,
  //     img: close,
  //   },
  //   {
  //     key: 'totalShares',
  //     text: 'customer.donkey',
  //     link: '/contacts',
  //     fontColor: '#000000',
  //     bgColor: '#ffffffff',
  //     count: 6,
  //     img: donkey,
  //   },
  //   {
  //     key: 'totalSales',
  //     text: 'customer.bee6',
  //     link: '/manage-tags',
  //     fontColor: '#000000',
  //     bgColor: '#ffffffff',
  //     count: 7,
  //     img: bee6,
  //   },
  // ];
  const ageBoxes = [
    {
      key: 'sentMessages',
      text: 'customer.userInsights',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: 0,
      //  !isEmpty(userProfileData)
      //   ? userProfileData.getSentMessages
      //   : '12 TRIPS',
      img: lineSVG,
      userInsight: true,
    },
    // {
    //   key: 'totalClicks',
    //   text: 'customer.avgChildAgeAtDropOff',
    //   fontColor: '#000000',
    //   bgColor: '#ffffffff',
    //   count: '5 YEARS 4 MONTHS',
    //   // !isEmpty(userProfileData)
    //   //   ? userProfileData.totalClicks
    //   //   : '1HR 32MINS',
    //   img: goalSVG,
    // },
    {
      key: 'totalShares',
      text: 'customer.avgChildAgeAtRegi',
      link: '/contacts',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: ChildAge,
      // !isEmpty(userProfileData)
      //   ? userProfileData.totalShare
      //   : '6HR 23MINS',
      img: fanSVG,
    },
    {
      key: 'totalSales',
      text: 'customer.avgLengthActiveUse',
      link: '/manage-tags',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: ActiveUsers, // !isEmpty(userProfileData) ? userProfileData.sale : '8HR 43MINS',
      img: exchangeSVG,
    },
    {
      key: 'totalShares',
      text: 'customer.avgChildAge',
      link: '/contacts',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: RegisterAge,
      // !isEmpty(userProfileData)
      //   ? userProfileData.totalShare
      //   : '6HR 23MINS',
      img: fanSVG,
    },
    // {
    //   key: 'totalSales',
    //   text: 'customer.childAgeRange',
    //   link: '/manage-tags',
    //   fontColor: '#000000',
    //   bgColor: '#ffffffff',
    //   count: '1 YEARS - 6 YEARS', // !isEmpty(userProfileData) ? userProfileData.sale : '8HR 43MINS',
    //   img: exchangeSVG,
    // },
  ];
  const marketingBoxes = [
    {
      key: 'sentMessages',
      text: '6am - 10am',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: '71.43%',
      img: morning,
    },
    {
      key: 'totalClicks',
      text: '10am - 2pm',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: '28.57%',
      img: morning1,
    },
    {
      key: 'totalShares',
      text: '2pm - 6pm',
      link: '/contacts',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: '0.00%',
      img: morning2,
    },
    {
      key: 'totalSales',
      text: '6pm - 10pm',
      link: '/manage-tags',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: '71.43%',
      img: Group9223,
    },
  ];

  function renderTopBoxes() {
    return (
      <Row style={{ flex: 1 }}>
        {marketingBoxes.map((widget, index) => {
          return (
            <Col
              lg={12}
              md={12}
              sm={24}
              xs={24}
              style={{
                marginBottom: 16,
                paddingRight: index === 0 || index === 2 ? 7 : 0,
                paddingLeft: index === 1 || index === 3 ? 7 : 0,
              }}
              className="boxesStyle"
              key={widget.text}
            >
              <IsoWidgetsWrapper className="topMarketingBox">
                <Card className="evng">
                  <div style={{}}>
                    <img src={widget.img} alt="txt"></img>
                    <h4>{widget.text}</h4>
                    <h1>
                      <b>{widget.count}</b>
                    </h1>
                  </div>
                </Card>
              </IsoWidgetsWrapper>
            </Col>
          );
        })}
      </Row>
    );
  }
  const printDocument = () => {
    setIsDownloading(true);

    const Pdf = new jsPDF('p', 'pt', 'a4', false);
    // pdf.text('Bugaboo User Profile report', 10, 50);
    html2canvas(document.querySelector('#divToPrint'), { scale: '3' }).then(
      (canvas) => {
        // document.body.appendChild(canvas); // if you want see your screenshot in body.
        const imgData = canvas.toDataURL('image/png');
        Pdf.addImage(imgData, 'PNG', 10, 50, 550, 0, undefined, false);
        Pdf.addPage();
        // pdf.save('download.pdf');
      },
    );
    setTimeout(() => {
      html2canvas(document.querySelector('#divToPrint2'), { scale: '3' }).then(
        (canvas) => {
          // document.body.appendChild(canvas); // if you want see your screenshot in body.
          const imgData = canvas.toDataURL('image/png');
          Pdf.addImage(imgData, 'PNG', 10, 0, 550, 0, undefined, false);
          Pdf.addPage();
          // pdf.save(`Analytics Report(${moment().format('DD MMM YYYY')}).pdf`);
          // setIsDownloading(false);
        },
      );
    }, 0);

    setTimeout(() => {
      html2canvas(document.querySelector('#divToPrint3'), { scale: '3' }).then(
        (canvas) => {
          // document.body.appendChild(canvas); // if you want see your screenshot in body.
          const imgData = canvas.toDataURL('image/png');
          Pdf.addImage(imgData, 'PNG', 10, 50, 550, 0, undefined, false);
          Pdf.save(`Analytics Report(${moment().format('DD MMM YYYY')}).pdf`);
          setIsDownloading(false);
        },
      );
    }, 900);
  };
  // const activeBGBoxes = [
  //   {
  //     key: 'bgPercentage',
  //     text: 'customer.back',
  //     fontColor: '#000000',
  //     bgColor: '#ffffffff',
  //     count: `60.00%`, // !isEmpty(dData.datad) ? `${dData.datad?.bgPercentage}%` : 0,
  //     img: <img src={gallery} alt="" />,
  //   },
  //   {
  //     key: 'activePercentage',
  //     text: 'customer.active',
  //     fontColor: '#000000',
  //     bgColor: '#ffffffff',
  //     count: `40.00%`, // !isEmpty(dData.datad) ? `${dData.datad?.activePercentage}%` : 0,
  //     img: (
  //       <AiOutlineCheckCircle
  //         style={{
  //           width: 35,
  //           height: 35,
  //           color: theme.colors.primaryColor,
  //           marginLeft: 0,
  //         }}
  //       />
  //     ),
  //   },
  // ];

  function renderPageViewBoxes() {
    return (
      <Row
        gutter={12}
        style={{
          marginBottom: 20,
          display: 'flex',
          justifyContent: 'space-between',
        }}
      >
        {pageViewBoxes.map((widget) => {
          return (
            <Col lg={6} xs={24}>
              <div
                style={{
                  background: '#FFFFFF',
                  borderRadius: 12,
                  margin: 5,
                  display: 'flex',
                  flexDirection: 'row',
                  // justifyContent: 'space-between',
                  overflow: 'hidden',
                  // borderLeft: '5px solid red',
                  // padding: '10px'
                }}
              >
                <div style={{ background: widget.cornerColor, width: '3%' }}>
                  <div />
                </div>
                <div
                  style={{
                    padding: '10px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    width: '100%',
                  }}
                >
                  <div>
                    <span style={{ color: '#AFAFAF', fontSize: '16px' }}>
                      <IntlMessages id={widget.text} />
                    </span>
                    <h4 style={{ color: '#15223C', fontSize: '20px' }}>
                      {widget.count || 0}
                    </h4>
                  </div>
                  <div
                    className="isoIconWrapper"
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                    }}
                  >
                    <Tag bordered={false} color="green">
                      <ArrowUpOutlined />
                      <IntlMessages id={widget?.percentage} />
                    </Tag>
                  </div>
                </div>
              </div>
            </Col>
          );
        })}
      </Row>
    );
  }

  function renderAgeBoxesU() {
    return (
      <Row style={rowStyle} className="boxRowStyle">
        {ageBoxes.map((widget) => {
          return (
            <Col
              lg={12}
              md={12}
              sm={24}
              xs={24}
              style={{
                marginBottom: 25,
              }}
              className="boxesStyle"
              key={widget.text}
            >
              <IsoWidgetsWrapper className="topMarketingBox">
                {widget.userInsight ? (
                  <div className="borderGradiant">
                    <div
                      style={{
                        borderRadius: 17,
                        borderWidth: 1,
                        borderColor: '#7C94B8',
                        padding: 20,
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                      }}
                    >
                      <Col>
                        <div style={{ marginTop: 20, marginLeft: 20 }}>
                          <span
                            style={{
                              color: '#FFFFFF',
                              marginBottom: 30,
                              fontSize: 17,
                            }}
                          >
                            <IntlMessages id={widget.text} />
                          </span>
                          <CaretRightOutlined
                            style={{
                              color: 'white',
                              marginLeft: 4,
                              marginTop: 5,
                            }}
                          />
                        </div>
                      </Col>
                    </div>
                  </div>
                ) : (
                  <div
                    style={{
                      backgroundColor: '#fff',
                      borderRadius: 17,
                      borderWidth: 1,
                      borderColor: '#7C94B8',
                      padding: 20,
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                    }}
                  >
                    <Col>
                      <div>
                        <span style={{ color: '#7C94B8' }}>
                          <IntlMessages id={widget.text} />
                        </span>
                        <h4 style={{ color: '#637693' }}>
                          {widget.count || 0}
                        </h4>
                      </div>
                    </Col>
                  </div>
                )}
              </IsoWidgetsWrapper>
            </Col>
          );
        })}
      </Row>
    );
  }

  const customerDurationContent = (
    <WidgetWrapper>
      <div className="popMainDiv">
        {actUsrFltVis ? (
          <div
            className="isoDropdownLink"
            onClick={() => {
              setActUsrFltTxt(<IntlMessages id="dashboard.thisweek" />);
              setActUsrFltVis(!actUsrFltVis);
              // setCPeriodType('this_week');
            }}
            role="button"
            onKeyPress={() => {
              setActUsrFltTxt(<IntlMessages id="dashboard.thisweek" />);
              setActUsrFltVis(!actUsrFltVis);
              // setCPeriodType('this_week');
            }}
            tabIndex="-1"
          >
            <IntlMessages id="dashboard.thisweek" />
          </div>
        ) : null}
        <div
          className="isoDropdownLink"
          onClick={() => {
            setActUsrFltTxt(<IntlMessages id="dashboard.lastweek" />);
            setActUsrFltVis(!actUsrFltVis);
            // setCPeriodType('week');
          }}
          role="button"
          onKeyPress={() => {
            setActUsrFltTxt(<IntlMessages id="dashboard.lastweek" />);
            setActUsrFltVis(!actUsrFltVis);
            // setCPeriodType('week');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.lastweek" />{' '}
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setActUsrFltTxt(<IntlMessages id="dashboard.thisMonth" />);
            setActUsrFltVis(!actUsrFltVis);
            // setCPeriodType('this_month');
          }}
          role="button"
          onKeyPress={() => {
            setActUsrFltTxt(<IntlMessages id="dashboard.thisMonth" />);
            setActUsrFltVis(!actUsrFltVis);
            // setCPeriodType('this_month');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.thisMonth" />
        </div>

        <div
          className="isoDropdownLink"
          onClick={() => {
            setActUsrFltTxt(<IntlMessages id="dashboard.lastMonth" />);
            setActUsrFltVis(!actUsrFltVis);
            // setCPeriodType('month');
          }}
          role="button"
          onKeyPress={() => {
            setActUsrFltTxt(<IntlMessages id="dashboard.lastMonth" />);
            setActUsrFltVis(!actUsrFltVis);
            // setCPeriodType('month');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.lastMonth" />
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setActUsrFltTxt(<IntlMessages id="dashboard.lastSixMonth" />);
            setActUsrFltVis(!actUsrFltVis);
            // setCPeriodType('six_month');
          }}
          role="button"
          onKeyPress={() => {
            setActUsrFltTxt(<IntlMessages id="dashboard.lastSixMonth" />);
            setActUsrFltVis(!actUsrFltVis);
            // setCPeriodType('six_month');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.lastSixMonth" />
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setActUsrFltTxt(<IntlMessages id="dashboard.lastYear" />);
            setActUsrFltVis(!actUsrFltVis);
            // setCPeriodType('year');
          }}
          role="button"
          onKeyPress={() => {
            setActUsrFltTxt(<IntlMessages id="dashboard.lastYear" />);
            setActUsrFltVis(!actUsrFltVis);
            // setCPeriodType('year');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.lastYear" />
        </div>
      </div>
    </WidgetWrapper>
  );

  function durationChange() {
    setActUsrFltVis((visiblePre) => !visiblePre);
  }

  // Overview tab chart data
  const apexCahrtData = {
    series: [
      {
        name: 'XYZ MOTORS',
        data: dates,
      },
    ],
    options: {
      chart: {
        type: 'area',
        stacked: false,
        height: 350,
        zoom: {
          type: 'x',
          enabled: true,
          autoScaleYaxis: true,
        },
        toolbar: {
          show: false, // Hide the toolbar
        },
      },
      dataLabels: {
        enabled: false,
      },
      markers: {
        size: 0,
      },
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          inverseColors: false,
          opacityFrom: 0.5,
          opacityTo: 0,
          stops: [0, 90, 100],
        },
      },
      yaxis: {
        show: false, // Hide the y-axis
      },
      xaxis: {
        type: 'datetime',
      },
      tooltip: {
        enabled: false, // Hide tooltips
      },
    },
  };

  const operatingSystemChartData = {
    series: [44, 20],
    options: {
      chart: {
        type: 'donut',
      },
      dataLabels: {
        enabled: true,
        formatter: (val, opts) => {
          return `${opts.w.globals.labels[opts.seriesIndex]} : ${val} %`;
        },
        style: {
          fontSize: '12px',
          colors: ['#000'],
        },
        offsetY: 10,
      },
      legend: {
        position: 'bottom', // Place the legend at the bottom
        horizontalAlign: 'center', // Center the legend horizontally
        floating: false,
        offsetY: 0,
        labels: {
          colors: ['#000'], // Optional: Set the color of legend text
        },
      },
      labels: ['Android', 'iOS'], // Specify the labels for the series
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              width: 200,
            },
            dataLabels: {
              offsetY: 30, // Adjust as needed
            },
            legend: {
              position: 'bottom', // Ensure legend is at the bottom on smaller screens as well
              horizontalAlign: 'center',
            },
          },
        },
      ],
    },
  };

  const postShareData = {
    series: [
      {
        name: 'STOCK ABC',
        data: seriesData,
      },
    ],
    options: {
      chart: {
        type: 'area',
        height: 140,
        zoom: {
          enabled: false,
        },
        background: 'transparent', // Optional: Makes the background transparent
      },
      colors: ['#07B861'], // Set the color for the series
      dataLabels: {
        enabled: false, // Hide data labels
      },
      stroke: {
        curve: 'smooth', // 'smooth' for rounded lines
        width: 2, // Optional: Set the width of the line
      },
      fill: {
        type: 'gradient', // Use 'solid' for a single color fill
        gradient: {
          shadeIntensity: 1,
          inverseColors: false,
          opacityFrom: 0.5,
          opacityTo: 0,
          stops: [0, 90, 100],
        },
      },
      xaxis: {
        labels: {
          show: false, // Hide x-axis labels
        },
        axisBorder: {
          show: false, // Hide x-axis border
        },
        axisTicks: {
          show: false, // Hide x-axis ticks
        },
      },
      yaxis: {
        labels: {
          show: false, // Hide y-axis labels
        },
        axisBorder: {
          show: false, // Hide y-axis border
        },
        axisTicks: {
          show: false, // Hide y-axis ticks
        },
      },
      title: {
        text: '', // Remove chart title
        align: 'left',
        margin: 0,
        style: {
          fontSize: '0px', // Set font size to 0
        },
      },
      subtitle: {
        text: '', // Remove chart subtitle
        align: 'left',
        margin: 0,
        style: {
          fontSize: '0px', // Set font size to 0
        },
      },
      legend: {
        show: false, // Hide legend
      },
      grid: {
        show: false, // Hide grid lines
      },
    },
  };

  const productUserAvgData = {
    series: [
      {
        name: 'STOCK ABC',
        data: seriesData,
      },
    ],
    options: {
      chart: {
        type: 'area',
        height: 140,
        zoom: {
          enabled: false,
        },
        background: 'transparent', // Optional: Makes the background transparent
      },
      colors: ['#7854DF'], // Set the color for the series
      dataLabels: {
        enabled: false, // Hide data labels
      },
      stroke: {
        curve: 'smooth', // 'smooth' for rounded lines
        width: 2, // Optional: Set the width of the line
      },
      fill: {
        type: 'gradient', // Use 'solid' for a single color fill
        gradient: {
          shadeIntensity: 1,
          inverseColors: false,
          opacityFrom: 0.5,
          opacityTo: 0,
          stops: [0, 90, 100],
        },
      },
      xaxis: {
        labels: {
          show: false, // Hide x-axis labels
        },
        axisBorder: {
          show: false, // Hide x-axis border
        },
        axisTicks: {
          show: false, // Hide x-axis ticks
        },
      },
      yaxis: {
        labels: {
          show: false, // Hide y-axis labels
        },
        axisBorder: {
          show: false, // Hide y-axis border
        },
        axisTicks: {
          show: false, // Hide y-axis ticks
        },
      },
      title: {
        text: '', // Remove chart title
        align: 'left',
        margin: 0,
        style: {
          fontSize: '0px', // Set font size to 0
        },
      },
      subtitle: {
        text: '', // Remove chart subtitle
        align: 'left',
        margin: 0,
        style: {
          fontSize: '0px', // Set font size to 0
        },
      },
      legend: {
        show: false, // Hide legend
      },
      grid: {
        show: false, // Hide grid lines
      },
    },
  };

  // User tab chart data
  const childAgesChartData = {
    series: [
      {
        name: 'Net Profit',
        data: [44, 55, 57, 56, 61, 58, 63],
      },
    ],
    options: {
      chart: {
        type: 'bar',
        height: 500,
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '55%',
          endingShape: 'rounded',
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        show: true,
        width: 2,
        colors: ['transparent'],
      },
      xaxis: {
        categories: ['0-1', '1-2', '2-3', '3-4', '4-5', '5-6', '6+'],
      },
      fill: {
        opacity: 1,
      },
      tooltip: {
        y: {
          formatter: (val) => {
            return `$ ${val} thousands`;
          },
        },
      },
    },
  };

  const dailyAvgChartData = {
    series: [
      {
        name: 'Morning 2h',
        data: [44, 55, 41, 67, 22, 43],
      },
      {
        name: 'Lunch 20m',
        data: [13, 23, 20, 8, 13, 27],
      },
      {
        name: 'Afternoon 5h',
        data: [11, 17, 15, 15, 21, 14],
      },
    ],
    options: {
      chart: {
        type: 'bar',
        height: 1050,
        stacked: true,
        toolbar: {
          show: true,
        },
        zoom: {
          enabled: true,
        },
      },
      responsive: [
        {
          breakpoint: 480,
          options: {
            legend: {
              position: 'bottom',
              offsetX: -10,
              offsetY: 0,
            },
          },
        },
      ],
      plotOptions: {
        bar: {
          horizontal: false,
          borderRadius: 10,
          borderRadiusApplication: 'end', // 'around', 'end'
          borderRadiusWhenStacked: 'last', // 'all', 'last'
          dataLabels: {
            total: {
              enabled: true,
              style: {
                fontSize: '13px',
                fontWeight: 900,
              },
            },
          },
        },
      },
      legend: {
        position: 'bottom',
        offsetX: -10,
        offsetY: 0,
      },
      fill: {
        opacity: 1,
      },
    },
  };

  const avgTimeChartData = {
    series: [
      {
        name: 'Inflation',
        data: [2.3, 3.1, 4.0, 10.1, 4.0, 3.6, 3.2],
      },
    ],
    options: {
      chart: {
        height: 350,
        type: 'bar',
      },
      plotOptions: {
        bar: {
          borderRadius: 10,
          dataLabels: {
            position: 'bottom', // Ensure data labels are positioned at the bottom of bars
          },
        },
      },
      dataLabels: {
        enabled: true,
        formatter: (val) => {
          return `${val} %`;
        },
        offsetY: -20,
        style: {
          fontSize: '12px',
          colors: ['#304758'],
        },
      },
      xaxis: {
        categories: ['M', 'T', 'W', 'T', 'F', 'S', 'Today'],
        position: 'bottom', // Move x-axis labels to the bottom
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
        crosshairs: {
          fill: {
            type: 'gradient',
            gradient: {
              colorFrom: '#D8E3F0',
              colorTo: '#BED1E6',
              stops: [0, 100],
              opacityFrom: 0.4,
              opacityTo: 0.5,
            },
          },
        },
        tooltip: {
          enabled: true,
        },
      },
      yaxis: {
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
        labels: {
          show: false,
          formatter: (val) => {
            return `${val} %`;
          },
        },
      },
      title: {
        text: 'Monthly Inflation in Argentina, 2002',
        floating: true,
        offsetY: 330,
        align: 'center',
        style: {
          color: '#444',
        },
      },
    },
  };

  const userAgeInsightChartData = {
    series: [76, 67, 61, 90],
    options: {
      chart: {
        height: 390,
        type: 'radialBar',
      },
      plotOptions: {
        radialBar: {
          offsetY: 0,
          startAngle: 0,
          endAngle: 270,
          hollow: {
            margin: 5,
            size: '30%',
            background: 'transparent',
            image: undefined,
          },
          dataLabels: {
            name: {
              show: false,
            },
            value: {
              show: false,
            },
          },
          barLabels: {
            enabled: true,
            useSeriesColors: true,
            offsetX: -8,
            fontSize: '16px',
            formatter: (seriesName, opts) => {
              return `${seriesName} : ${
                opts.w.globals.series[opts.seriesIndex]
              }`;
            },
          },
        },
      },
      colors: ['#1ab7ea', '#0084ff', '#39539E', '#0077B5'],
      labels: ['Vimeo', 'Messenger', 'Facebook', 'LinkedIn'],
      responsive: [
        {
          breakpoint: 480,
          options: {
            legend: {
              show: false,
            },
          },
        },
      ],
    },
  };

  const appPeformanceChartData = {
    series: [76],
    options: {
      chart: {
        type: 'radialBar',
        offsetY: -20,
        sparkline: {
          enabled: true,
        },
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          track: {
            background: '#e7e7e7',
            strokeWidth: '97%',
            margin: 5, // margin is in pixels
            dropShadow: {
              enabled: true,
              top: 2,
              left: 0,
              color: '#999',
              opacity: 1,
              blur: 2,
            },
          },
          dataLabels: {
            name: {
              show: false,
            },
            value: {
              offsetY: -2,
              fontSize: '22px',
            },
          },
        },
      },
      grid: {
        padding: {
          top: -10,
        },
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'light',
          shadeIntensity: 0.4,
          inverseColors: false,
          opacityFrom: 1,
          opacityTo: 1,
          stops: [0, 50, 53, 91],
        },
      },
      labels: ['Average Results'],
    },
  };

  // Environment tab chart data
  const weatherAltChartData = {
    series: [
      {
        name: 'High - 2013',
        data: [28, 29, 33, 36, 32, 32, 33],
      },
      {
        name: 'Low - 2013',
        data: [12, 11, 14, 18, 17, 13, 13],
      },
    ],
    options: {
      chart: {
        height: 350,
        type: 'line',
        dropShadow: {
          enabled: true,
          color: '#000',
          top: 18,
          left: 7,
          blur: 10,
          opacity: 0.2,
        },
        zoom: {
          enabled: false,
        },
        toolbar: {
          show: false,
        },
      },
      colors: ['#00D9C0', '#344BFD'],
      dataLabels: {
        enabled: true,
      },
      stroke: {
        curve: 'smooth',
      },
      title: {
        text: 'Weather Alerts',
        align: 'left',
      },
      grid: {
        borderColor: '#e7e7e7',
        row: {
          colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
          opacity: 0.5,
        },
      },
      markers: {
        size: 1,
      },
      xaxis: {
        categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
        title: {
          text: 'Month',
        },
      },
      yaxis: {
        title: {
          text: 'Temperature',
        },
        min: 5,
        max: 40,
      },
      legend: {
        position: 'top',
        horizontalAlign: 'right',
        floating: true,
        offsetY: -25,
        offsetX: -5,
      },
    },
  };

  const userLocationChartData = {
    series: [14, 23, 21, 17, 15, 10, 12, 17, 21],
    options: {
      chart: {
        type: 'polarArea',
      },
      stroke: {
        colors: ['#fff'],
      },
      fill: {
        opacity: 0.8,
      },
      legend: {
        position: 'bottom', // Place the legend below the chart
        horizontalAlign: 'center', // Center the legend horizontally
        floating: false,
        labels: {
          colors: ['#000'], // Optional: Set the color of legend text
        },
        itemMargin: {
          horizontal: 10,
          vertical: 5,
        },
      },
      labels: [
        'Cities',
        'Towns',
        'Country Sides',
        'Mountains',
        'Beaches',
        'Rural Area',
        'Suburbs',
        'Villages',
        'Countryside',
      ], // Customize legend names
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              width: 200,
            },
            legend: {
              position: 'bottom', // Ensure legend is at the bottom on smaller screens as well
              horizontalAlign: 'center',
            },
          },
        },
      ],
    },
  };

  const climateConditionChartData = {
    series: [44, 55, 13, 33],
    options: {
      chart: {
        width: 380,
        type: 'donut',
      },
      dataLabels: {
        enabled: false,
      },
      colors: ['#62B2FD', '#9F97F7', '#82F7EA', '#1E40AF', '#00D9C0'], // Set custom colors for each segment
      labels: ['Category 1', 'Category 2', 'Category 3', 'Category 4'], // Labels for the segments
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              width: 200,
            },
            legend: {
              show: false, // Hide legend on smaller screens
            },
          },
        },
      ],
      legend: {
        position: 'right', // Position the legend to the right
        offsetY: 0,
        height: 230, // Height of the legend
      },
    },
  };

  const lengthOfTimeChartData = {
    series: [44, 55, 13, 43, 22],
    options: {
      chart: {
        width: 380,
        type: 'pie',
      },
      colors: ['#82F7EA', '#1E40AF', '#00D9C0', '#3A3A3A'], // Custom colors for each segment
      labels: ['Warm', 'Cold', 'Moderate', 'Extreme', 'Other'], // Labels for each segment (adjust as needed)
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              width: 200,
            },
            legend: {
              position: 'bottom', // Position legend below the chart on smaller screens
            },
          },
        },
      ],
      legend: {
        position: 'bottom', // Position legend below the chart
        horizontalAlign: 'center', // Center align the legend horizontally
        floating: false,
        offsetY: 0,
        labels: {
          colors: ['#000'], // Optional: Set color for legend text
        },
      },
    },
  };

  // user tab dummy data
  const userInsightData = [
    {
      title: 'Newest',
      data: [
        { isUp: true, time: '2y 3m', percent: 15, type: 'Avg child age' },
        {
          isUp: false,
          time: '2y 3m',
          percent: 95,
          type: 'Avg child resreved age',
        },
      ],
    },
    {
      title: 'Last Quater',
      data: [
        { isUp: true, time: '2y 3m', percent: 15, type: 'Avg child age' },
        {
          isUp: false,
          time: '2y 3m',
          percent: 95,
          type: 'Avg child resreved age',
        },
        {
          isUp: false,
          time: '3m',
          percent: 65,
          type: 'Avg child resreved',
        },
      ],
    },
  ];

  async function getAnalyticsData() {
    const data = {
      period_type: 'week',
    };
    try {
      const res = await getApiData('getAnayticsData', data, 'post');
      if (res.success && !isEmpty(res.data)) {
        console.log(res.data, 'ressssssssssssssssss');
      }
    } catch (err) {
      console.log('err ===', err);
    }
  }

  useEffect(() => {
    // getAnalyticsData();
  }, []);

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'row',
        padding: '10px',
        marginLeft: '15px',
        marginRight: '15px',
      }}
    >
      {/* main div start */}
      <div id="divToPrint2" style={{ flex: 1, width: '50%' }}>
        {/* tab start */}
        <Tabs defaultActiveKey="1">
          <TabPane tab="Overview" key="1">
            {renderPageViewBoxes()}

            {/* ................... ADDING CARDS FOR THE 2nd ROW ................... */}
            {/* {renderActiveBGBoxes()} */}

            {/* ................... ADDING CARDS FOR THE 3rd ROW ................... */}
            <Row style={rowStyle} gutter={0} justify="start">
              <Col lg={14} md={24} sm={24} xs={24} style={colStyle}>
                <IsoWidgetsWrapper style={{ height: '100%' }}>
                  {/* Report Widget */}
                  <ReportsWidget
                    label={false}
                    widgetClassName="mb0"
                    labelClsName="mb15"
                    style={{
                      height: '100%',
                      borderRadius: 25,
                      boxShadow: '5px 5px 5px 5px #0000',
                      fontWeight: 700,
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'space-between',
                        fontSize: '14px',
                        fontWeight: 700,
                      }}
                    >
                      <IntlMessages id="dashboard.activeUser" />
                      <Popover
                        content={customerDurationContent}
                        trigger="click"
                        visible={actUsrFltVis}
                        onVisibleChange={() => durationChange()}
                        placement="bottomRight"
                      >
                        <p
                          style={{
                            cursor: 'pointer',
                            fontSize:
                              window.innerWidth <= 400 ? '12px' : '15px',
                            fontWeight: 'normal',
                          }}
                        >
                          {actUsrFltTxt}
                          <DownOutlined style={{ marginLeft: 5 }} />
                        </p>
                      </Popover>
                    </div>
                    <Row
                      style={{
                        height: '170px',
                        marginTop: 35,
                      }}
                    >
                      <ResponsiveContainer width="100%" height="100%">
                        <ReactApexChart
                          options={apexCahrtData.options}
                          series={apexCahrtData.series}
                          type="area"
                          height={800}
                        />{' '}
                      </ResponsiveContainer>
                    </Row>
                  </ReportsWidget>
                </IsoWidgetsWrapper>
              </Col>
              <Col lg={10} md={24} sm={24} xs={24} style={colStyle}>
                <IsoWidgetsWrapper style={{ height: '100%', width: '100%' }}>
                  <ReportsWidget
                    widgetClassName="mb0"
                    style={{
                      height: '100%',
                      borderRadius: 25,
                      boxShadow: '5px 5px 5px 5px #0000',
                    }}
                    labelClsName="mb15"
                  >
                    <div>
                      <div
                        style={{
                          fontSize: '15px',
                          fontWeight: 'bold',
                          width: '100%',
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <IntlMessages id="Goal Complition" />
                        <span
                          style={{ fontSize: '12px', fontWeight: 'normal' }}
                        >
                          <IntlMessages id="Relative Change VS Last Month" />
                        </span>
                      </div>
                      <div
                        style={{
                          fontSize: '12px',
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <IntlMessages id="New monthly app sign-up" />
                        <span style={{ fontWeight: 'bold' }}>2.14k</span>
                      </div>
                    </div>
                    <div
                      className="chartStyle"
                      style={{
                        alignSelf: 'center',
                        borderRadius: 20,
                        marginTop: 30,
                      }}
                    >
                      <GaugeChart
                        id="gauge-chart3"
                        nrOfLevels={1}
                        colors={['#00B3FF']}
                        arcWidth={0.2}
                        percent={0.37}
                        textColor='"black"'
                      />
                    </div>
                  </ReportsWidget>
                </IsoWidgetsWrapper>
              </Col>
            </Row>
            <div
              style={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: '20px',
                marginBottom: '20px',
              }}
            >
              {/* <Card
                style={{
                  display: 'flex',
                  justifyContent: 'space-evenly',
                  marginTop: '15px',
                  minHeight: '300px',
                  // width: '300px',
                  borderRadius: '20px',
                }}
              >
                <div>
                  <h4 style={{ marginTop: '-10px', textAlign: 'left' }}>
                    ACTIVE USERS
                  </h4>
                  <div style={{ marginTop: '30px' }}>
                    <div
                      style={{
                        fontSize: '12px',
                        marginTop: '10px',
                      }}
                    >
                      <p>Monthly</p>
                      <h4
                        style={{
                          marginTop: '-10px',
                          fontSize: '18px',
                        }}
                      >
                        9.2K{' '}
                        <span
                          style={{
                            marginLeft: '10px',
                            color: '#4cb083',
                            fontSize: '12px',
                          }}
                        >
                          <BsArrowUp />
                          4.63%
                        </span>
                      </h4>
                    </div>
                    <div
                      style={{
                        fontSize: '12px',
                        marginLeft: '150px',
                      }}
                    >
                      <p
                        style={{
                          marginTop: '-35px',
                        }}
                      >
                        Weekly
                      </p>
                      <h4
                        style={{
                          marginTop: '-10px',
                          fontSize: '18px',
                        }}
                      >
                        2.6K{' '}
                        <span
                          style={{
                            marginLeft: '10px',
                            color: '#ea0606',
                            fontSize: '12px',
                          }}
                        >
                          <BsArrowUp />
                          1.92%
                        </span>
                      </h4>
                    </div>
                    <div
                      style={{
                        fontSize: '12px',
                        marginLeft: '290px',
                        marginTop: '-40px',
                      }}
                    >
                      <p>Daily(Avg)</p>
                      <h4
                        style={{
                          marginTop: '-10px',
                          fontSize: '18px',
                        }}
                      >
                        0.9K{' '}
                        <span
                          style={{
                            fontSize: '12px',
                            color: '#4cb083',
                          }}
                        >
                          <BsArrowUp />
                          3.45%
                        </span>
                      </h4>
                    </div>
                  </div>
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'center',
                      marginTop: '60px',
                    }}
                  >
                    <BarGraph />
                  </div>
                </div>
              </Card> */}

              {/* ................... PERFORMANCE ANALYSIS ................... */}
              {/* <div
                style={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: '20px',
                  // position: 'static',
                  minHeight: '400px',
                  marginBottom: '20px',
                  // justifyContent: 'space-evenly',
                }}
              > */}
              {/* <Card
                  style={{
                    display: 'flex',
                    justifyContent: 'space-evenly',
                    marginTop: '15px',
                    // height: '60vh',
                    minWidth: '300px',
                    borderRadius: '20px',
                  }}
                > */}
              {/* <h4 style={{ marginTop: '-10px', textAlign: 'left' }}>
                    PERFORMANCE ANALYSIS
                  </h4> */}

              {/* First Graph NEW DAILY USERS START  */}
              {/* <div> */}
              {/* <div>
                      <p
                        style={{
                          display: 'flex',
                          marginLeft: '340px',
                          position: 'absolute',
                          justifyContent: 'space-between',
                          // width: '70%',
                          // height: '18px',
                          paddingBottom: '28px',
                          fontSize: '18px',
                          marginTop: '20px',
                        }}
                      >
                        1.56K
                      </p>
                      <p
                        style={{
                          display: 'flex',
                          marginLeft: '340px',
                          justifyContent: 'space-between',
                          position: 'absolute',
                          // width: '70%',
                          // height: '18px',
                          marginTop: '40px',
                          fontSize: '14px',
                          color: '#4CB083',
                        }}
                      >
                        <AiOutlineArrowUp style={{ height: '20px' }} />
                        4.63%
                      </p>
                      <p
                        style={{
                          display: 'flex',
                          marginLeft: '320px',
                          position: 'absolute',
                          justifyContent: 'space-between',
                          // width: '70%',
                          // height: '18px',
                          marginTop: '55px',
                          fontSize: '14px',
                        }}
                      >
                        vs lastmonth
                      </p>
                    </div> */}
              {/* <div>
                      <p
                        style={{
                          marginTop: '10px',
                          textAlign: 'left',
                          fontSize: '12px',
                        }}
                      >
                        New Daily Users
                      </p>

                      <div
                        style={{
                          marginTop: '10px',
                        }}
                      >
                        <Linear />
                      </div>
                    </div> */}

              {/* First Graph NEW DAILY USERS END */}

              {/* Second Graph POST USERS START */}

              {/* <div>
                      <p
                        style={{
                          display: 'flex',
                          marginLeft: '340px',
                          position: 'absolute',
                          width: '85px',
                          height: '18px',
                          paddingBottom: '25px',
                          fontSize: '18px',
                          marginTop: '-10px',
                        }}
                      >
                        8.39K
                      </p>
                      <p
                        style={{
                          display: 'flex',
                          marginLeft: '337px',
                          position: 'absolute',
                          width: '70%',
                          height: '18px',
                          marginTop: '10px',
                          fontSize: '14px',
                          color: '#EA0606',
                        }}
                      >
                        <AiOutlineArrowDown style={{ height: '20px' }} />
                        1.48%
                      </p>
                      <p
                        style={{
                          display: 'flex',
                          marginLeft: '320px',
                          position: 'absolute',
                          width: '70%',
                          height: '18px',
                          marginTop: '25px',
                          fontSize: '14px',
                        }}
                      >
                        vs lastmonth
                      </p>
                    </div> */}
              {/* <div>
                      <p
                        style={{
                          display: 'flex',
                          marginTop: '-40px',
                          textAlign: 'left',
                          fontSize: '12px',
                        }}
                      >
                        Post Shares
                      </p>
                      <div style={{ display: 'flex', marginTop: '15px' }}>
                        <Linear1 />
                      </div>
                    </div> */}

              {/* Second Graph POST USERS END */}

              {/* Third Graph PRODUCTS PER USERS START */}

              {/* <div>
                      <p
                        style={{
                          display: 'flex',
                          marginLeft: '340px',
                          position: 'static',
                          width: '85px',
                          height: '18px',
                          paddingBottom: '22px',
                          fontSize: '18px',
                          marginTop: '-20px',
                        }}
                      >
                        7.80K
                      </p>
                      <p
                        style={{
                          display: 'flex',
                          marginLeft: '340px',
                          position: 'absolute',
                          width: '70%',
                          height: '18px',
                          marginTop: '-5px',
                          fontSize: '14px',
                          color: '#4CB083',
                        }}
                      >
                        {' '}
                        <AiOutlineArrowUp style={{ height: '20px' }} />
                        1.4%
                      </p>
                      <p
                        style={{
                          display: 'flex',
                          marginLeft: '320px',
                          position: 'absolute',
                          width: '70%',
                          height: '18px',
                          marginTop: '10px',
                          fontSize: '14px',
                          marginBottom: '20px',
                        }}
                      >
                        vs lastmonth
                      </p>
                    </div>
                    <div>
                      <p
                        style={{
                          display: 'flex',
                          marginTop: '-40px',
                          textAlign: 'left',
                          fontSize: '12px',
                        }}
                      >
                        Products per Users(Avg)
                      </p>
                      <div style={{ display: 'flex', marginTop: '10px' }}>
                        <Linear2 />
                      </div>
                    </div> */}
              {/* </div> */}

              {/* Third Graph PRODUCTS PER USERS START */}
              {/* </Card> */}
              {/* </div> */}
            </div>
            {/* ................... ADDING CARDS FOR THE 4th ROW ................... */}
            <Row style={rowStyle} gutter={0} justify="start">
              <Col lg={8} md={24} sm={12} xs={24} style={colStyle}>
                <IsoWidgetsWrapper style={{ height: '100%' }}>
                  <ReportsWidget
                    label={false}
                    labelClsName="mb15"
                    style={{
                      height: '100%',
                      borderRadius: 25,
                      boxShadow: '5px 5px 5px 5px #0000',
                    }}
                    widgetClassName="flex1"
                  >
                    <div
                      style={{
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'space-between',
                        fontSize: '14px',
                        fontWeight: 700,
                        marginBottom: 30,
                      }}
                    >
                      <IntlMessages id="Operating System" />
                    </div>
                    <ReactApexChart
                      options={operatingSystemChartData.options}
                      series={operatingSystemChartData.series}
                      type="donut"
                    />
                  </ReportsWidget>
                </IsoWidgetsWrapper>
              </Col>
              <Col lg={16} md={20} sm={12} xs={24} style={colStyle}>
                <IsoWidgetsWrapper style={{ height: '100%' }}>
                  <ReportsWidget
                    style={{
                      height: '100%',
                      borderRadius: 25,
                      boxShadow: '5px 5px 5px 5px #0000',
                    }}
                    widgetClassName="flex1"
                  >
                    <div
                      style={{
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'space-between',
                        fontSize: '14px',
                        fontWeight: 700,
                        marginBottom: 30,
                      }}
                    >
                      <IntlMessages id="Performance Analysis" />
                    </div>
                    <Row>
                      <Col lg={12} md={24} sm={12} xs={24}>
                        <div style={{ fontSize: '18px', fontWeight: 'bold' }}>
                          <img
                            src={greenShareIcon}
                            style={{ marginRight: '10px' }}
                            alt="shareIcon"
                          ></img>
                          Post Share
                        </div>
                        <div>
                          <ReactApexChart
                            options={postShareData.options}
                            series={postShareData.series}
                            type="area"
                            height={131}
                          />
                        </div>
                        <div
                          style={{
                            padding: '25px',
                            display: 'flex',
                            justifyContent: 'space-between',
                          }}
                        >
                          <p style={{ fontSize: '24px', fontWeight: 700 }}>
                            128
                          </p>
                          <span>
                            <p style={{ fontSize: '16px', fontWeight: 500 }}>
                              -4.56%{' '}
                              <img src={greenDownIcon} alt="downIcon"></img>
                            </p>
                            <p>Vs Last Week</p>
                          </span>
                        </div>
                      </Col>
                      <Col lg={12} md={24} sm={12} xs={24}>
                        <div style={{ fontSize: '18px', fontWeight: 'bold' }}>
                          <img
                            src={blueTreeIcon}
                            style={{ marginRight: '10px' }}
                            alt="treeIcon"
                          ></img>
                          Products/User Avg
                        </div>
                        <div>
                          <ReactApexChart
                            options={productUserAvgData.options}
                            series={productUserAvgData.series}
                            type="area"
                            height={131}
                          />
                        </div>
                        <div
                          style={{
                            padding: '25px',
                            display: 'flex',
                            justifyContent: 'space-between',
                          }}
                        >
                          <p style={{ fontSize: '24px', fontWeight: 700 }}>3</p>
                          <span>
                            <p style={{ fontSize: '16px', fontWeight: 500 }}>
                              +3.75% <img src={blueUpIcon} alt="upIcon"></img>
                            </p>
                            <p>Vs Last Week</p>
                          </span>
                        </div>
                      </Col>
                    </Row>
                  </ReportsWidget>
                </IsoWidgetsWrapper>
              </Col>
              {/* <Col lg={8} md={20} sm={12} xs={24} style={colStyle}>
                {uData.brand_name === 'bugaboo' && (
                  <Col lg={10} xs={24}>
                    <div
                      style={{
                        // alignSelf: 'center',
                        // justifyContent: 'center',
                        marginLeft: 20,
                      }}
                    >
                      <IntlMessages id="customer.strollerType" />
                    </div>
                  </Col>
                )}
                {uData.brand_name === 'bugaboo' ? (
                  renderStrollerType()
                ) : (
                  <IsoWidgetsWrapper style={{ height: '100%' }}>
                    <ReportsWidget
                      style={{
                        height: '100%',
                        borderRadius: 25,
                        boxShadow: '5px 5px 5px 5px #0000',
                      }}
                      label={<IntlMessages id="analytics.topLinkClicks" />}
                      widgetClassName="flex1"
                    >
                      {!isEmpty(topLinkClick) &&
                        isArray(topLinkClick) &&
                        topLinkClick.map((data) => {
                          return (
                            <div
                              style={{
                                display: 'flex',
                                flexDirection: 'row',
                                justifyContent: 'space-between',

                                paddingBottom: '15px',
                              }}
                            >
                              <div>
                                <span
                                  style={{
                                    fontSize: '14px',
                                  }}
                                >
                                  {data.title}
                                </span>
                                <p style={{ fontSize: 12, color: '#C4C4C4' }}>
                                  {data.subTitle}
                                </p>
                              </div>
                              <div>
                                <p
                                  style={{
                                    background: '#B1D8F3',
                                    textAlign: 'center',
                                    borderRadius: '10px',
                                    padding: '7px',
                                    color: '#1172EC',
                                    fontWeight: 'bold',
                                  }}
                                >
                                  {data.val}
                                </p>
                              </div>
                            </div>
                          );
                        })}
                    </ReportsWidget>
                  </IsoWidgetsWrapper>
                )}
              </Col> */}
              {/* <Col
                lg={8}
                md={24}
                sm={12}
                xs={24}
                style={{
                  ...colStyle,
                  display: 'flex',
                }}
                className="isoGoalCompletion"
              >
                {uData.brand_name === 'bugaboo' ? (
                  <div className="borderGradiant">{renderAgeBoxes()}</div>
                ) : (
                  <IsoWidgetsWrapper
                    style={{ height: '100%', width: '95%', borderRadius: 10 }}
                  >
                    {uData.brand_name === 'bugaboo' ? (
                      renderAgeBoxes()
                    ) : !isEmpty(analyticsData) &&
                      isEmpty(analyticsData.goalCompletion) ? (
                      <ReportsWidget
                        label={<IntlMessages id="analytics.goalCOmpletion" />}
                        label2={<IntlMessages id="createnewgoal" />}
                        redirect={() => redirect()}
                        details={
                          <>
                            <p
                              style={{
                                color: '#fff',
                                fontSize: 14,
                              }}
                            >
                              <IntlMessages id="analytics.goalRelative" />

                              {item.time_period}
                            </p>
                            <div className="activeUserRow">
                              <h3
                                className="activeusercntstyle"
                                style={{ color: '#fff', fontSize: 22 }}
                              >
                                {item.current_count}
                              </h3>
                              <span
                                className="arraowPerStyle"
                                style={{
                                  color:
                                    item.growth_type === 'decrement'
                                      ? 'red'
                                      : 'green',
                                  fontSize: 14,
                                }}
                              >
                                {item.growth_type === 'decrement' ? (
                                  <ArrowDownOutlined />
                                ) : (
                                  <ArrowUpOutlined />
                                )}

                                <span className="activeUserPerStyle">
                                  {!isEmpty(item.percentage)
                                    ? item.percentage
                                    : '0%'}
                                </span>
                              </span>
                            </div>
                          </>
                        }
                        widgetClassName="flex1"
                        customStyle="borders bg-gradient"
                        labelClsName="whiteColor"
                      >
                        <h4 style={{ fontSize: '14px', color: '#fff' }}>
                          GOAL COMPLETIONS
                        </h4>
                        <h6 style={{ fontSize: '14px', color: '#fff' }}>
                          New monthly app sign up
                        </h6>

                        <div
                          className="chartStyle"
                          style={{
                            alignSelf: 'center',
                            borderRadius: 20,
                          }}
                        >
                          <GaugeChart
                            id="gauge-chart3"
                            nrOfLevels={30}
                            colors={['#1de9b6', '#fff']}
                            style={{ paddingTop: 30, width: 375 }}
                            arcWidth={0.25}
                            percent={0.75}
                            textColor="#fff"
                          />
                        </div>
                      </ReportsWidget>
                    ) : (
                      <>
                        <Slider
                          style={{
                            width: '100%',
                            borderRadius: 20,
                          }}
                          {...sliderOptions}
                        >
                          <ReportsWidget
                            label={
                              <IntlMessages id="analytics.goalCOmpletion" />
                            }
                            label2={<IntlMessages id="createnewgoal" />}
                            redirect={() => redirect()}
                            details={
                              <>
                                <p
                                  style={{
                                    color: '#fff',
                                    fontSize: 14,
                                  }}
                                >
                                  <IntlMessages id="analytics.goalRelative" />
                                  Last month
                                </p>
                                <div className="activeUserRow">
                                  <h3
                                    className="activeusercntstyle"
                                    style={{ color: '#fff', fontSize: 22 }}
                                  >
                                    1.45K
                                  </h3>
                                  <span
                                    className="arraowPerStyle"
                                    style={{
                                      color:
                                        item.growth_type === 'decrement'
                                          ? 'red'
                                          : 'green',
                                      fontSize: 14,
                                    }}
                                  >
                                    {item.growth_type === 'decrement' ? (
                                      <ArrowDownOutlined />
                                    ) : (
                                      <ArrowUpOutlined />
                                    )}

                                    <span className="activeUserPerStyle">
                                      {!isEmpty(item.percentage)
                                        ? item.percentage
                                        : '0%'}
                                    </span>
                                  </span>
                                </div>
                              </>
                            }
                            widgetClassName="flex1"
                            customStyle="border-none bg-gradient"
                            labelClsName="whiteColor"
                          >
                            <p style={{ color: '#fff' }}>{item.goal_title}</p>
                            <div
                              className="chartStyle"
                              style={{
                                alignSelf: 'center',
                              }}
                            >
                              <GaugeChart
                                id="gauge-chart3"
                                nrOfLevels={30}
                                colors={['#1de9b6', '#fff']}
                                style={{ paddingTop: 26, width: 375 }}
                                arcWidth={0.25}
                                percent={parseFloat(item.percentage) / 100}
                                textColor="#fff"
                              />
                            </div>
                          </ReportsWidget>
                        </Slider>
                      </>
                    )}
                  </IsoWidgetsWrapper>
                )}
              </Col> */}
            </Row>
            {/* -----------------------Content of Tab1 completed--------------------------- */}

            {/* -----------------------Content of Tab1 completed--------------------------- */}
          </TabPane>
          <TabPane tab="User" key="2">
            {renderPageViewBoxes()}
            <Row style={rowStyle} sm={28} gutter={0} justify="start">
              <Col xs={24} sm={7} style={colStyle}>
                <IsoWidgetsWrapper style={{ height: '100%' }}>
                  {/* Report Widget */}
                  <ReportsWidget
                    label={false}
                    widgetClassName="mb0"
                    labelClsName="mb15"
                    style={{
                      height: '100%',
                      borderRadius: 25,
                      boxShadow: '5px 5px 5px 5px #0000',
                      fontWeight: 700,
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'space-between',
                        fontSize: '14px',
                        fontWeight: 700,
                      }}
                    >
                      <IntlMessages id="Child Ages" />
                    </div>
                    <Row
                      style={{
                        height: '170px',
                        marginTop: 35,
                      }}
                    >
                      <ResponsiveContainer>
                        <ReactApexChart
                          options={childAgesChartData.options}
                          series={childAgesChartData.series}
                          type="bar"
                          height="337px"
                        />
                      </ResponsiveContainer>
                    </Row>
                  </ReportsWidget>
                </IsoWidgetsWrapper>
              </Col>
              <Col xs={24} sm={7} style={colStyle}>
                <IsoWidgetsWrapper style={{ height: '100%' }}>
                  {/* Report Widget */}
                  <ReportsWidget
                    label={false}
                    widgetClassName="mb0"
                    labelClsName="mb15"
                    style={{
                      height: '100%',
                      borderRadius: 25,
                      boxShadow: '5px 5px 5px 5px #0000',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'space-between',
                        fontSize: '12px',
                      }}
                    >
                      <IntlMessages id="Daily Average" />
                      <span>
                        <span style={{ color: '#04B8FF' }}>+30</span> this week
                      </span>
                    </div>
                    <div style={{ fontSize: '24px', fontWeight: '700' }}>
                      2h 20m
                    </div>
                    <Row
                      style={{
                        height: '170px',
                        marginTop: 35,
                      }}
                    >
                      <ResponsiveContainer width="100%" height="100%">
                        <ReactApexChart
                          options={dailyAvgChartData.options}
                          series={dailyAvgChartData.series}
                          type="bar"
                          height={350}
                        />
                      </ResponsiveContainer>
                    </Row>
                  </ReportsWidget>
                </IsoWidgetsWrapper>
              </Col>
              <Col xs={24} sm={7} style={colStyle}>
                <IsoWidgetsWrapper style={{ height: '100%' }}>
                  {/* Report Widget */}
                  <ReportsWidget
                    label={false}
                    widgetClassName="mb0"
                    labelClsName="mb15"
                    style={{
                      height: '100%',
                      borderRadius: 25,
                      boxShadow: '5px 5px 5px 5px #0000',
                      fontWeight: 700,
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'center',
                        flexDirection: 'column',
                        fontSize: '20px',
                        fontWeight: 700,
                        alignItems: 'center',
                      }}
                    >
                      <div>
                        1h 20m <img src={blueDownIcon} alt="icon"></img>
                      </div>
                      <p style={{ fontSize: '12px', fontWeight: '600' }}>
                        Average time user spent per day
                      </p>
                    </div>
                    <Row
                      style={{
                        height: '170px',
                        marginTop: 35,
                      }}
                    >
                      <ResponsiveContainer width="100%" height="100%">
                        <ReactApexChart
                          options={avgTimeChartData.options}
                          series={avgTimeChartData.series}
                          type="bar"
                          height={800}
                        />{' '}
                      </ResponsiveContainer>
                    </Row>
                  </ReportsWidget>
                </IsoWidgetsWrapper>
              </Col>
              <Col xs={24} sm={3} style={colStyle}>
                <IsoWidgetsWrapper style={{ height: '100%' }}>
                  {/* Report Widget */}
                  <ReportsWidget
                    label={false}
                    widgetClassName="mb0"
                    labelClsName="mb15"
                    style={{
                      height: '100%',
                      borderRadius: 25,
                      boxShadow: '5px 5px 5px 5px #0000',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'center',
                        padding: '5px 10px',
                      }}
                    >
                      <div>
                        <img src={morningIcon} alt="icon"></img>
                        <p style={{ fontSize: '10px', fontWeight: 'normal' }}>
                          Morning
                        </p>
                      </div>
                      <p style={{ fontSize: '20px', fontWeight: 700 }}>11%</p>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'center',
                        padding: '5px 10px',
                      }}
                    >
                      <div>
                        <img src={lunchIcon} alt="icon"></img>
                        <p style={{ fontSize: '10px', fontWeight: 'normal' }}>
                          Lunch
                        </p>
                      </div>
                      <p style={{ fontSize: '20px', fontWeight: 700 }}>28%</p>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'center',
                        padding: '5px 10px',
                      }}
                    >
                      <div>
                        <img src={afternoonIcon} alt="icon"></img>
                        <p style={{ fontSize: '10px', fontWeight: 'normal' }}>
                          Afternoon
                        </p>
                      </div>
                      <p style={{ fontSize: '20px', fontWeight: 700 }}>74%</p>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'center',
                        padding: '5px 10px',
                      }}
                    >
                      <div>
                        <img src={nightIcon} alt="icon"></img>
                        <p style={{ fontSize: '10px', fontWeight: 'normal' }}>
                          Evening
                        </p>
                      </div>
                      <p style={{ fontSize: '20px', fontWeight: 700 }}>3%</p>
                    </div>
                  </ReportsWidget>
                </IsoWidgetsWrapper>
              </Col>
            </Row>
            <Row style={rowStyle} sm={28} gutter={0} justify="start">
              <Col xs={24} sm={10} style={colStyle}>
                <IsoWidgetsWrapper style={{ height: '100%' }}>
                  {/* Report Widget */}
                  <ReportsWidget
                    label={false}
                    widgetClassName="mb0"
                    labelClsName="mb15"
                    style={{
                      height: '100%',
                      borderRadius: 25,
                      boxShadow: '5px 5px 5px 5px #0000',
                      fontWeight: 700,
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'space-between',
                        fontSize: '14px',
                        fontWeight: 700,
                      }}
                    >
                      <IntlMessages id="User Insight" />
                      <Popover
                        content={customerDurationContent}
                        trigger="click"
                        visible={actUsrFltVis}
                        onVisibleChange={() => durationChange()}
                        placement="bottomRight"
                      >
                        <p
                          style={{
                            cursor: 'pointer',
                            fontSize:
                              window.innerWidth <= 400 ? '12px' : '15px',
                            fontWeight: 'normal',
                          }}
                        >
                          {actUsrFltTxt}
                          <DownOutlined style={{ marginLeft: 5 }} />
                        </p>
                      </Popover>
                    </div>
                    {userInsightData.map((usrInst) => (
                      <div>
                        <div style={{ color: '#A0AEC0', marginTop: '15px' }}>
                          {usrInst.title}
                        </div>
                        {usrInst.data.map((nui) => (
                          <Row style={{ alignItems: 'center' }}>
                            <Col sm={20}>
                              <div
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '15px',
                                  padding: '10px 0',
                                }}
                              >
                                {nui.isUp ? (
                                  <img src={greenCircleUpIcon} alt="icon"></img>
                                ) : (
                                  <img src={redCircleDownIcon} alt="icon"></img>
                                )}
                                <span>
                                  <p>{nui.time}</p>
                                  <p style={{ fontWeight: 'normal' }}>
                                    {nui.type}
                                  </p>
                                </span>
                              </div>
                            </Col>
                            <Col sm={4}>
                              {nui.isUp ? (
                                <p style={{ color: 'green' }}>
                                  +{nui.percent}%
                                </p>
                              ) : (
                                <p style={{ color: 'red' }}>-{nui.percent}%</p>
                              )}
                            </Col>
                          </Row>
                        ))}
                      </div>
                    ))}
                  </ReportsWidget>
                </IsoWidgetsWrapper>
              </Col>
              <Col xs={24} sm={14} style={colStyle}>
                <Row style={rowStyle} sm={28} gutter={0} justify="start">
                  <Col xs={24} sm={8} style={colStyle}>
                    <IsoWidgetsWrapper style={{ height: '100%' }}>
                      {/* Report Widget */}
                      <ReportsWidget
                        label={false}
                        // widgetClassName="mb0"
                        labelClsName="mb15"
                        style={{
                          height: '100%',
                          borderRadius: 25,
                          boxShadow: '5px 5px 5px 5px #0000',
                          fontWeight: 700,
                        }}
                      >
                        <div
                          style={{
                            display: 'flex',
                            width: '100%',
                            justifyContent: 'space-between',
                            fontSize: '14px',
                            fontWeight: 700,
                          }}
                        >
                          <IntlMessages id="Child Height" />
                        </div>
                        <Row
                          style={{
                            height: '170px',
                            marginTop: 10,
                          }}
                        >
                          <ResponsiveContainer>
                            <ReactApexChart
                              options={childAgesChartData.options}
                              series={childAgesChartData.series}
                              type="bar"
                              height="337px"
                            />
                          </ResponsiveContainer>
                        </Row>
                      </ReportsWidget>
                    </IsoWidgetsWrapper>
                  </Col>
                  <Col xs={24} sm={16} style={colStyle}>
                    <IsoWidgetsWrapper style={{ height: '100%' }}>
                      <ReportsWidget
                        label={false}
                        // widgetClassName="mb0"
                        labelClsName="mb15"
                        style={{
                          height: '100%',
                          borderRadius: 25,
                          boxShadow: '5px 5px 5px 5px #0000',
                          fontWeight: 700,
                        }}
                      >
                        <div
                          style={{
                            width: '100%',
                            justifyContent: 'space-between',
                            fontSize: '14px',
                            fontWeight: 700,
                          }}
                        >
                          <IntlMessages id="User Age Insights" />
                        </div>
                        <Row
                          style={{
                            height: '200px',
                            marginTop: 10,
                          }}
                        >
                          <ResponsiveContainer>
                            <ReactApexChart
                              options={userAgeInsightChartData.options}
                              series={userAgeInsightChartData.series}
                              type="radialBar"
                              height={350}
                            />
                          </ResponsiveContainer>
                        </Row>
                      </ReportsWidget>
                    </IsoWidgetsWrapper>
                  </Col>
                </Row>
                <Row style={rowStyle} sm={28} gutter={0} justify="start">
                  <Col xs={24} sm={8} style={colStyle}>
                    <IsoWidgetsWrapper style={{ height: '100%' }}>
                      {/* Report Widget */}
                      <ReportsWidget
                        label={false}
                        // widgetClassName="mb0"
                        labelClsName="mb15"
                        style={{
                          height: '100%',
                          borderRadius: 25,
                          boxShadow: '5px 5px 5px 5px #0000',
                          fontWeight: 700,
                        }}
                      >
                        <div
                          style={{
                            display: 'flex',
                            width: '100%',
                            justifyContent: 'space-between',
                            fontSize: '14px',
                            fontWeight: 700,
                          }}
                        >
                          <IntlMessages id="Child Weight" />
                        </div>
                        <Row
                          style={{
                            height: '170px',
                            marginTop: 10,
                          }}
                        >
                          <ResponsiveContainer>
                            <ReactApexChart
                              options={childAgesChartData.options}
                              series={childAgesChartData.series}
                              type="bar"
                              height="500px"
                            />
                          </ResponsiveContainer>
                        </Row>
                      </ReportsWidget>
                    </IsoWidgetsWrapper>
                  </Col>
                  <Col xs={24} sm={16} style={colStyle}>
                    <IsoWidgetsWrapper style={{ height: '100%' }}>
                      {/* Report Widget */}
                      <ReportsWidget
                        label={false}
                        // widgetClassName="mb0"
                        labelClsName="mb15"
                        style={{
                          height: '100%',
                          borderRadius: 25,
                          boxShadow: '5px 5px 5px 5px #0000',
                          fontWeight: 700,
                        }}
                      >
                        <div
                          style={{
                            display: 'flex',
                            width: '100%',
                            justifyContent: 'center',
                            fontSize: '14px',
                            fontWeight: 700,
                          }}
                        >
                          <IntlMessages id="App Performance Score" />
                        </div>
                        <Row
                          style={{
                            height: '170px',
                            marginTop: 10,
                          }}
                        >
                          <ResponsiveContainer>
                            <ReactApexChart
                              options={appPeformanceChartData.options}
                              series={appPeformanceChartData.series}
                              type="radialBar"
                            />
                          </ResponsiveContainer>
                        </Row>
                      </ReportsWidget>
                    </IsoWidgetsWrapper>
                  </Col>
                </Row>
              </Col>
            </Row>

            {/* row2 */}
            {/* <Row style={rowStyle} gutter={20} justify="start">
              <Col lg={8} md={24} sm={12} xs={24} style={{ colStyle }}>
                <Card className="bar2">
                  <div>
                    <h4
                      style={{
                        color: '#ffff',
                      }}
                    >
                      Daily Use
                    </h4>
                    <div>
                      <DailyUse />
                    </div>
                  </div>
                </Card>

                <Card className="meter">
                  <div>
                    <h4
                      style={{
                        color: '#ffff',
                      }}
                    >
                      Weekly Use
                    </h4>
                    <div className="schart">
                      <Gausss />
                    </div>
                  </div>
                </Card>
              </Col>
              <Col lg={8} md={24} sm={12} xs={24} style={{ colStyle }}>
                <Card className="circle">
                  <div>
                    <h3>
                      <b>LENGTH OF TIME PER USE</b>
                    </h3>

                    <div className="chart">
                      <Chartss />
                    </div>
                    <div>
                      <div className="dot">
                        <i>
                          <GoDotFill />
                        </i>
                        <p>
                          <b>0 – 30 mins</b>
                        </p>
                      </div>
                      <div className="dot1">
                        <i>
                          <GoDotFill />
                        </i>
                        <p>
                          <b>31 – 60 mins</b>
                        </p>
                      </div>
                      <div className="dot2">
                        <i>
                          <GoDotFill />
                        </i>
                        <p>
                          <b>1hr – 2hrs</b>
                        </p>
                      </div>
                      <div className="dot3">
                        <i>
                          <GoDotFill />
                        </i>
                        <p>
                          <b>2Hrs+</b>
                        </p>
                      </div>
                    </div>
                  </div>
                </Card>
              </Col>
              <Col lg={8} md={24} sm={12} xs={24} style={colStyle}>
                <Card className="bar3">
                  <div>
                    <h3>
                      <b>LOCATIONS PRODUCTS USED</b>
                    </h3>
                    <div>
                      <BarGraphzzz />
                    </div>
                  </div>
                </Card>
              </Col>
            </Row> */}

            {/* row3 */}

            {/* <Row style={rowStyle} gutter={20} justify="start">
              <Col lg={8} md={24} sm={12} xs={24} style={{ colStyle }}>
                <Card className="sleepbar">
                  <h3>
                    <b>NO OF CHILD PROFILES</b>
                  </h3>
                  <div>
                    <div>
                      <Horizontall />
                    </div>
                  </div>
                  <p>
                    <b>Total : 270</b>
                  </p>
                </Card>
              </Col>
              <Col lg={5} md={24} sm={12} xs={24} style={{ colStyle }}>
                <div>
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      margin: 10,
                      alignItems: 'center',
                    }}
                  >
                    <p>STROLLER TYPE</p>
                    <AiOutlineDown />
                  </div>
                  <div
                    style={{
                      background: '#ffff',
                      height: '60vh',
                      width: '40vw',
                      overflow: 'scroll',
                      borderRadius: '20px',
                      border: '0.5px solid #ededed',
                    }}
                  >
                    {ProductsOwnedData &&
                      ProductsOwnedData.map((product, index) => (
                        <div
                          style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            padding: 10,
                            background: '#ffff',
                            borderRadius: '20px',
                            border: '0.5px solid #ededed',
                            margin: '20px',
                            alignItems: 'center',
                          }}
                          key={index}
                        >
                          <div>
                            <p>{product.product_name}</p>
                            <h2>{product.count}%</h2>
                          </div>
                          <div>
                            <img
                              style={{ height: '100px', width: '100px' }}
                              src={product.product_image}
                              alt={product.name}
                            />
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </Col>
              <Col lg={11} md={24} sm={12} xs={24} style={{ colStyle }}>
                <div className="borderGradiant">{renderAgeBoxesU()}</div>
              </Col>
            </Row> */}
            {/* row4 */}
            {/* <Row style={{ marginTop: '30px' }} gutter={20} justify="start">
              <Col xs={24} sm={12} style={colStyle}>
                <Card className="c6">
                  <div>
                    <p>
                      <b>CHILD HEIGHT</b>
                    </p>
                    <div>
                      <BaarChart />
                    </div>
                  </div>
                </Card>
              </Col>
              <Col xs={24} sm={12} style={colStyle}>
                <Card className="c7">
                  <div>
                    <p>
                      <b>CHILD WEIGHT</b>
                    </p>
                    <div>
                      <BarrChart />
                    </div>
                  </div>
                </Card>
              </Col>
            </Row> */}
          </TabPane>
          {/* // Tab 3 */}
          <TabPane tab="Environment" key="3">
            <Row style={rowStyle} gutter={20} justify="start">
              <Col lg={18} md={24} sm={12} xs={24} style={colStyle}>
                <IsoWidgetsWrapper style={{ height: '100%' }}>
                  {/* Report Widget */}
                  <ReportsWidget
                    label={false}
                    labelClsName="mb15"
                    style={{
                      height: '100%',
                      borderRadius: 25,
                      boxShadow: '5px 5px 5px 5px #0000',
                      fontWeight: 700,
                    }}
                  >
                    {/* <div
                      style={{
                        display: 'flex',
                        width: '100%',
                        gap: 45,
                        fontSize: '14px',
                        fontWeight: 700,
                      }}
                    >
                      <IntlMessages id="Weather Alerts" />
                      <span>
                        <img src={greenLine} alt="icon"></img>
                        <p style={{ fontWeight: 'normal' }}>High</p>
                      </span>
                      <span>
                        <img src={blueLine} alt="icon"></img>
                        <p style={{ fontWeight: 'normal' }}>Low</p>
                      </span>
                    </div> */}
                    <Row
                      style={{
                        height: '300px',
                        marginTop: 35,
                      }}
                    >
                      <ResponsiveContainer>
                        <ReactApexChart
                          options={weatherAltChartData.options}
                          series={weatherAltChartData.series}
                          type="line"
                          height={350}
                        />
                      </ResponsiveContainer>
                    </Row>
                  </ReportsWidget>
                </IsoWidgetsWrapper>
              </Col>
              <Col lg={6} md={24} sm={12} xs={24} style={colStyle}>
                <IsoWidgetsWrapper style={{ height: '100%' }}>
                  {/* Report Widget */}
                  <ReportsWidget
                    label={false}
                    style={{
                      height: '100%',
                      borderRadius: 25,
                      boxShadow: '5px 5px 5px 5px #0000',
                      fontWeight: 700,
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'center',
                        fontSize: '14px',
                        fontWeight: 700,
                      }}
                    >
                      <IntlMessages id="User by Location" />
                    </div>
                    <Row
                      style={{
                        height: '370px',
                        marginTop: 10,
                      }}
                    >
                      <ResponsiveContainer>
                        <ReactApexChart
                          options={userLocationChartData.options}
                          series={userLocationChartData.series}
                          type="polarArea"
                        />
                      </ResponsiveContainer>
                    </Row>
                  </ReportsWidget>
                </IsoWidgetsWrapper>
              </Col>
            </Row>
            <Row style={rowStyle} gutter={20} justify="start">
              <Col lg={9} md={24} sm={12} xs={24} style={colStyle}>
                <IsoWidgetsWrapper style={{ height: '100%' }}>
                  {/* Report Widget */}
                  <ReportsWidget
                    label={false}
                    labelClsName="mb15"
                    style={{
                      height: '100%',
                      borderRadius: 25,
                      boxShadow: '5px 5px 5px 5px #0000',
                      fontWeight: 700,
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        width: '100%',
                        gap: 45,
                        fontSize: '14px',
                        fontWeight: 700,
                      }}
                    >
                      <IntlMessages id="Climate Condition" />
                    </div>
                    <Row
                      style={{
                        height: '200px',
                        marginTop: 35,
                      }}
                    >
                      <ResponsiveContainer>
                        <ReactApexChart
                          options={climateConditionChartData.options}
                          series={climateConditionChartData.series}
                          type="donut"
                          width={380}
                        />
                      </ResponsiveContainer>
                    </Row>
                  </ReportsWidget>
                </IsoWidgetsWrapper>
              </Col>
              <Col lg={9} md={24} sm={12} xs={24} style={colStyle}>
                <IsoWidgetsWrapper style={{ height: '100%' }}>
                  {/* Report Widget */}
                  <ReportsWidget
                    label={false}
                    labelClsName="mb15"
                    style={{
                      height: '100%',
                      borderRadius: 25,
                      boxShadow: '5px 5px 5px 5px #0000',
                      fontWeight: 700,
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        width: '100%',
                        gap: 45,
                        fontSize: '14px',
                        fontWeight: 700,
                      }}
                    >
                      <IntlMessages id="Total orders" />
                    </div>
                    <Row
                      style={{
                        height: '200px',
                        marginTop: 35,
                      }}
                    >
                      <ResponsiveContainer>
                        <ReactApexChart
                          options={childAgesChartData.options}
                          series={childAgesChartData.series}
                          type="bar"
                          height="500px"
                        />
                      </ResponsiveContainer>
                    </Row>
                  </ReportsWidget>
                </IsoWidgetsWrapper>
              </Col>
              <Col lg={6} md={24} sm={12} xs={24} style={colStyle}>
                <IsoWidgetsWrapper style={{ height: '100%' }}>
                  {/* Report Widget */}
                  <ReportsWidget
                    label={false}
                    style={{
                      height: '100%',
                      borderRadius: 25,
                      boxShadow: '5px 5px 5px 5px #0000',
                      fontWeight: 700,
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'center',
                        fontSize: '14px',
                        fontWeight: 700,
                      }}
                    >
                      <IntlMessages id="Length of time/use" />
                    </div>
                    <Row
                      style={{
                        height: '270px',
                        marginTop: 10,
                      }}
                    >
                      <ResponsiveContainer>
                        <ReactApexChart
                          options={lengthOfTimeChartData.options}
                          series={lengthOfTimeChartData.series}
                          type="pie"
                        />
                      </ResponsiveContainer>
                    </Row>
                  </ReportsWidget>
                </IsoWidgetsWrapper>
              </Col>
            </Row>
            {/* <Row style={rowStyle} gutter={20} justify="start">
              <Col lg={8} md={24} sm={12} xs={24} style={colStyle}>
                <Card className="cards1">
                  <div>
                    <h3
                      style={{
                        alignItems: 'left',
                      }}
                    >
                      <b>LOCATIONS PRODUCTS USED</b>
                    </h3>
                    <div className="vbar">
                      <BarGraphzzz1 />
                    </div>
                  </div>
                </Card>
              </Col>
              <Col lg={8} md={24} sm={12} xs={24} style={colStyle}>
                <Card className="cards2">
                  <div>
                    <h3>
                      <b>LENGTH OF TIME PER USE</b>
                    </h3>
                    <div className="pie">
                      <Chartsss />
                    </div>
                    <div>
                      <div className="dots">
                        <div className="dots1">
                          <i>
                            <GoDotFill />
                          </i>
                          <p>
                            <b>Cold Climate</b>
                          </p>
                        </div>
                        <div className="dots2">
                          <i>
                            <GoDotFill />
                          </i>
                          <p>
                            <b>Moderate Climate</b>
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="dots0">
                      <div className="dots3">
                        <i>
                          <GoDotFill />
                        </i>
                        <p>
                          <b>Warm Climate</b>
                        </p>
                      </div>
                      <div className="dots4">
                        <i>
                          <GoDotFill />
                        </i>
                        <p>
                          <b>Extreme Climate</b>
                        </p>
                      </div>
                    </div>
                  </div>
                </Card>
              </Col>
              <Col lg={8} md={24} sm={12} xs={24} style={colStyle}>
                <Card className="cards3">
                  <div>
                    <h3>
                      <b>AIR QUALITY WHERE USES ARE</b>
                    </h3>
                    <div className="harchart">
                      <HarChart />
                    </div>
                  </div>
                </Card>
              </Col>
            </Row>
            <div className="rows2">
              <div>
                <Card className="box">
                  <p>
                    <b>WEATHER ALERTS</b>
                  </p>
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'center',
                      marginTop: '4%',
                      marginLeft: '-1%',
                    }}
                  >
                    <WeatherAlerts />
                  </div>
                  <div className="box1"></div>
                  <div className="box2"></div>
                </Card>
              </div>
            </div> */}
          </TabPane>
        </Tabs>
      </div>
      <div
        style={{
          position: 'absolute',
          right: 30,
          display: 'flex',
          flexDirection: 'row',
        }}
      >
        <Popover
          content={content}
          trigger="click"
          visible={isRangePickerOpen}
          onVisibleChange={handleDateClick}
        >
          <Button
            type="dashed"
            style={{
              marginRight: '10px',
              display: 'flex',
              alignItems: 'center',
              border: '0.5px solid #EDEDED',
              borderRadius: '10px',
              background: '#00B7FF 0% 0% no-repeat padding-box',
              padding: '20px',
              color: '#fff',
              fontWeight: 500,
              gap: '15px',
            }}
          >
            Data Range
            <img src={DataRangeIcon} alt='"icon"'></img>
          </Button>
        </Popover>
        <Button
          // disabled={!downloadReport}
          onClick={printDocument}
          className="changeBtn"
          style={{
            marginRight: '10px',
            borderRadius: '10px',
            padding: '20px 20px',
            display: 'flex',
            alignItems: 'center',
            gap: '15px',
          }}
        >
          {isDownloading ? (
            <LoadingOutlined
              style={{
                fontSize: window.innerWidth > 281 ? 20 : 15,
                marginRight: window.innerWidth > 280 ? 10 : 0,
              }}
            />
          ) : (
            <img src={downloadIcon} alt='"icon"'></img>
          )}
          <IntlMessages id="common.report" />
        </Button>
        <Button
          type="primary"
          style={{
            border: '0.5px solid #EDEDED',
            background: '#00B7FF 0% 0% no-repeat padding-box',
            borderRadius: '10px',
            padding: '20px',
            display: 'flex',
            alignItems: 'center',
            fontWeight: 500,
            gap: '15px',
          }}
          onClick={showDrawer}
        >
          <IntlMessages id="common.filter" />
          <img src={threeLineFltIcon} alt='"icon"'></img>
        </Button>
        <Drawer
          title="Filter"
          placement="right"
          onClose={onClose}
          visible={open}
        >
          <div>
            <Dropdown
              overlay={
                <Menu>
                  {languageOptions.map((option) => (
                    <Menu.Item key={option.key}>{option.value}</Menu.Item>
                  ))}
                </Menu>
              }
              trigger={['click']}
            >
              <Typography.Link>
                <Space style={{ width: '100%', color: '#000' }}>
                  Language
                  <DownOutlined />
                </Space>
              </Typography.Link>
            </Dropdown>
          </div>
          <Divider />
          <div>
            <Dropdown
              overlay={
                <Menu>
                  {locationOptions.map((option) => (
                    <Menu.Item key={option.key}>{option.value}</Menu.Item>
                  ))}
                </Menu>
              }
              trigger={['click']}
            >
              <Typography.Link>
                <Space style={{ width: '100%', color: '#000' }}>
                  Location
                  <DownOutlined />
                </Space>
              </Typography.Link>
            </Dropdown>
          </div>
          <Divider />
          <div>
            <Dropdown
              overlay={
                <Menu>
                  {ageOptions.map((option) => (
                    <Menu.Item key={option.key}>{option.value}</Menu.Item>
                  ))}
                </Menu>
              }
              trigger={['click']}
            >
              <Typography.Link>
                <Space style={{ width: '100%', color: '#000' }}>
                  Child Age
                  <DownOutlined />
                </Space>
              </Typography.Link>
            </Dropdown>
          </div>
          <Divider />
          <div>
            <Dropdown
              overlay={
                <Menu>
                  {productsOptions.map((option) => (
                    <Menu.Item key={option.key}>{option.value}</Menu.Item>
                  ))}
                </Menu>
              }
              trigger={['click']}
            >
              <Typography.Link>
                <Space style={{ width: '100%', color: '#000' }}>
                  Products Owned
                  <DownOutlined />
                </Space>
              </Typography.Link>
            </Dropdown>
          </div>
          <div>
            <Button
              type="primary"
              style={{
                background: '#1890FF',
                color: '#FFFFFF',
                top: '195px',
                marginLeft: '15%',
                width: '212px',
                height: '40px',
              }}
              size="large"
            >
              Apply
            </Button>
          </div>
        </Drawer>

        {/* buttons div end */}
      </div>
      {/* main div end */}
    </div>
  );
}

export default App;

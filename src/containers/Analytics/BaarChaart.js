/* eslint-disable  prefer-template */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/jsx-boolean-value  */
/* eslint-disable  no-unused-vars */

import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  LabelList,
  Tooltip,
} from 'recharts';

function BaarChart({ userTabData }) {
  console.log('#####', userTabData);
  const childAgeArray = userTabData?.childHeightData;
  console.log('childAgeArrayheight', childAgeArray);

  const formatValue = (count) => Math.round(count) + '%';

  function getWindowDimensions() {
    const { innerWidth: width, innerHeight: height } = window;
    return {
      width,
      height,
    };
  }
  function useWindowDimensions() {
    const [windowDimensions, setWindowDimensions] = useState(
      getWindowDimensions(),
    );

    useEffect(() => {
      function handleResize() {
        setWindowDimensions(getWindowDimensions());
      }

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, []);

    return windowDimensions;
  }
  const { width } = useWindowDimensions();
  return (
    <div style={{ width: '90%', height: '90%' }}>
      <BarChart
        width={width * 0.3}
        height={300}
        data={childAgeArray}
        barGap={0}
      >
        <CartesianGrid strokeDasharray="3 3" strokeOpacity={0.1} />
        <XAxis
          dataKey="height_range"
          axisLine={{ stroke: '#E6E6E6' }}
          tickLine={false}
        />
        <YAxis hide={true} domain={[0, 100]} />
        <Tooltip formatter={(count) => `${count}%`} />
        <Bar dataKey="count" barSize={30} fill="url(#colorGradient)">
          <LabelList
            dataKey="count"
            position="top"
            fill="#000"
            textAnchor="middle"
            formatter={formatValue}
          />
        </Bar>
        <defs>
          <linearGradient id="colorGradient" gradientTransform="rotate(90)">
            <stop offset="0%" stopColor="#5087FF" />
            <stop offset="100%" stopColor="#34ebd8" />
          </linearGradient>
        </defs>
      </BarChart>
    </div>
  );
}

export default BaarChart;

/* eslint-disable react/jsx-no-bind */
/* eslint-disable consistent-return */
/* eslint-disable no-param-reassign */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable no-nested-ternary */
/* eslint-disable import/order */
/* eslint-disable no-unused-vars */
/* eslint-disable import/no-duplicates */
/* eslint-disable no-unused-vars */
/* eslint-disable new-cap */
/* eslint-disable react/self-closing-comp */

import React, { useEffect, useState } from 'react';
import {
  Tabs,
  Button,
  DatePicker,
  Popover,
  Drawer,
  Dropdown,
  Space,
  Typography,
  Divider,
  Menu,
  Row,
  Col,
  Tag,
  Skeleton,
} from 'antd';
// IMPORTING ICONS FROM REACT ICON
import { AiOutlineHome } from 'react-icons/ai';
import {
  DownOutlined,
  LoadingOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
} from '@ant-design/icons';
import { VscLayout } from 'react-icons/vsc';
import { BsGraphUpArrow } from 'react-icons/bs';
import { FiSettings } from 'react-icons/fi';

import jsPDF from 'jspdf';
import moment from 'moment';
import html2canvas from 'html2canvas';
import IntlMessages from '@chill/components/utility/intlMessages';

// import gallery from '@chill/assets/images/gallery.svg';
import greenShareIcon from '@chill/assets/images/greenShareIcon.png';
import blueTreeIcon from '@chill/assets/images/blueTreeIcon.png';
// import theme from '@chill/config/theme/default';
import basicStyle from '@chill/assets/styles/constants';
// import { useSelector } from 'react-redux';
import GaugeChart from 'react-gauge-chart';
// import { useHistory } from 'react-router-dom';
import { ResponsiveContainer } from 'recharts';

import ReactApexChart from 'react-apexcharts';
import greenDownIcon from '@chill/assets/images/greenDownIcon.png';
import blueUpIcon from '@chill/assets/images/blueUpIcon.png';
import { WidgetWrapper } from '@chill/containers/Widgets/Widgets.styles';
import DataRangeIcon from '@chill/assets/images/DataRangeIcon.png';
import downloadIcon from '@chill/assets/images/downloadIcon.png';
import threeLineFltIcon from '@chill/assets/images/threeLineFltIcon.png';
import blueDownIcon from '@chill/assets/images/blueDownIcon.png';
import morningIcon from '@chill/assets/images/morningIcon.png';
import lunchIcon from '@chill/assets/images/lunchIcon.png';
import afternoonIcon from '@chill/assets/images/afternoonIcon.png';
import nightIcon from '@chill/assets/images/nightIcon.png';
import greenCircleUpIcon from '@chill/assets/images/greenCircleUpIcon.png';
import redCircleDownIcon from '@chill/assets/images/redCircleDownIcon.png';

import downArrow from '@chill/assets/images/down-arrow.png';

import './Apps.css';
import ReportsWidget from '../Widgets/Report/ReportWidget';
import getApiData from '@chill/lib/helpers/apiHelper';
import { isNumber, parseInt } from 'lodash';
import NewStyleWrapper from './new.styles';
import formatValue, {
  convertToHoursAndMinutes,
} from '@chill/components/utility/commonFunction';

const { TabPane } = Tabs;

const { RangePicker } = DatePicker;

export default function newIndex() {
  const [activeTab, setActiveTab] = useState('Overview');
  const { rowStyle, colStyle } = basicStyle;
  const pageViewBoxes = [
    {
      key: 'hPerc',
      text: <IntlMessages id="common.home" />,
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: `30`,
      cornerColor: '#00B3FF',
      percentage: '0%',
      img: (
        <AiOutlineHome
          style={{
            width: 45,
            height: 45,
            marginTop: 2,
            color: 'white',
            marginLeft: 0,
          }}
        />
      ),
    },
    {
      key: 'dPerc',
      text: <IntlMessages id="customer.dashborad" />,
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: `40`,
      cornerColor: '#2DD683',
      percentage: '11.09%',
      img: (
        <VscLayout
          style={{
            width: 45,
            height: 45,
            marginTop: 2,
            color: 'white',
            marginLeft: 0,
          }}
        />
      ),
    },
    {
      key: 'tPerc',
      text: <IntlMessages id="customer.timeline" />,
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: `15`,
      cornerColor: '#64AAE6',
      percentage: '11.09%',
      img: (
        <BsGraphUpArrow
          style={{
            width: 45,
            height: 45,
            marginTop: 2,
            color: 'white',
            marginLeft: 0,
          }}
        />
      ),
    },
    {
      key: 'sPerc',
      text: <IntlMessages id="customer.settings" />,
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: `15`,
      cornerColor: '#72E7C8',
      percentage: '0%',
      img: (
        <FiSettings
          style={{
            width: 45,
            height: 45,
            marginTop: 2,
            color: 'white',
            marginLeft: 0,
          }}
        />
      ),
    },
  ];
  const [isRangePickerOpen, setIsRangePickerOpen] = useState(false);
  const [tileData, setTileData] = useState({});
  const [goalCompletionData, setGoalCompletionData] = useState({});
  const [analyticsAvgTime, setAnalyticsAvgTime] = useState(0);
  const [analyticsChartVal, setAnalyticsChartVal] = useState({
    data: [],
    labels: [],
  });
  const [isDownloading, setIsDownloading] = useState(false);
  const languageOptions = [
    { key: '1', value: 'English' },
    { key: '2', value: 'Spanish' },
    { key: '3', value: 'French' },
  ];
  const [open, setOpen] = useState(false);
  const locationOptions = [
    { key: '1', value: 'India' },
    { key: '2', value: 'USA' },
    { key: '3', value: 'London' },
  ];
  const ageOptions = [
    { key: '1', value: '0-6 months' },
    { key: '2', value: '6-12 months' },
    { key: '3', value: '12-18 months' },
  ];
  const productsOptions = [
    { key: '1', value: 'abc' },
    { key: '2', value: 'xyz' },
    { key: '3', value: '123' },
  ];

  // Charts Data

  const [activeUserChartData, setActiveUserChartData] = useState(null);
  const [operatingSystemChartData, setOperatingSystemChartData] =
    useState(null);
  const [postShareData, setPostShareData] = useState({
    chartData: null,
    totalCount: null,
    percentage: null,
  });
  const [productUserAvgData, setProductUserAvgData] = useState({
    chartData: null,
    totalCount: null,
    percentage: null,
  });
  const [childAgesChartData, setChildAgesChartData] = useState(null);
  const [childHeightChartData, setChildHeightChartData] = useState(null);
  const [childWeightChartData, setChildWeightChartData] = useState(null);
  const [climateConditionChartData, setClimateConditionChartData] =
    useState(null);
  const [ageInsightChart, setAgeInsightChart] = useState(null);
  const [position, setPosition] = useState({ lat: 0, log: 0 });
  const [userinsightChart, setUserinsightChart] = useState([]);
  const [stEndDate, setStEndDate] = useState({ stDate: null, endDate: null });

  // Charts data end

  // Handle the selected date range here
  function onDateChange(e) {
    if (e != null) {
      const date1 = moment(e[0]).format('DD-MM-YYYY');
      const date2 = moment(e[1]).format('DD-MM-YYYY');
      setStEndDate({ stDate: date1, endDate: date2 });
    } else {
      setStEndDate({ stDate: null, endDate: null });
    }
  }

  const handleDateClick = () => {
    setIsRangePickerOpen(!isRangePickerOpen);
  };
  const content = <RangePicker onChange={onDateChange} />;

  function renderPageViewBoxes() {
    return (
      <Row gutter={[16, 16]} className="pageViewMainCont">
        {pageViewBoxes.map((widget) => {
          if (widget.key === 'hPerc') {
            widget.count = tileData?.home ?? 10;
          } else if (widget.key === 'dPerc') {
            widget.count = tileData?.dashboard ?? 0;
          } else if (widget.key === 'tPerc') {
            widget.count = tileData?.timeline ?? 0;
          } else if (widget.key === 'sPerc') {
            widget.count = tileData?.settings ?? 0;
          }
          return (
            <Col xs={24} sm={12} md={8} lg={6} xl={6}>
              <div
                className="widgetSpacing"
                style={{
                  background: '#FFFFFF',
                  borderRadius: 12,
                  display: 'flex',
                  flexDirection: 'row',
                  overflow: 'hidden',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                  border: '1px solid #E6E9EE',
                  height: '100%',
                  minHeight: '80px',
                }}
              >
                <div
                  style={{
                    background: widget.cornerColor,
                    width: '6px',
                    marginRight: '5px',
                  }}
                >
                  <div />
                </div>
                <div
                  style={{
                    padding: '10px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    width: '100%',
                  }}
                >
                  <div>
                    <span className="pageViewHeading">{widget.text}</span>
                    <h4 className="pageViewCount">{widget.count || 0}%</h4>
                  </div>
                  <div
                    className="isoIconWrapper"
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                    }}
                  >
                    {/* {widget?.percentage !== '0%' && (
                      <Tag className="pageViewTag" color="#D8FFEC">
                        <ArrowUpOutlined />
                        {widget?.percentage}
                      </Tag>
                    )} */}
                  </div>
                </div>
              </div>
            </Col>
          );
        })}
      </Row>
    );
  }

  const showDrawer = () => {
    setOpen(true);
  };

  const printDocument = () => {
    setIsDownloading(true);
    const Pdf = new jsPDF('p', 'pt', 'a4', false);
    html2canvas(document.querySelector('#divToPrint'), { scale: '3' }).then(
      (canvas) => {
        // document.body.appendChild(canvas); // if you want see your screenshot in body.
        const imgData = canvas.toDataURL('image/png');
        Pdf.addImage(imgData, 'PNG', 10, 50, 550, 0, undefined, false);
        Pdf.addPage();
        // pdf.save('download.pdf');
      },
    );
    setTimeout(() => {
      html2canvas(document.querySelector('#divToPrint2'), { scale: '3' }).then(
        (canvas) => {
          // document.body.appendChild(canvas); // if you want see your screenshot in body.
          const imgData = canvas.toDataURL('image/png');
          Pdf.addImage(imgData, 'PNG', 10, 0, 550, 0, undefined, false);
          Pdf.addPage();
          Pdf.save(`Analytics Report(${moment().format('DD MMM YYYY')}).pdf`);
          setIsDownloading(false);
        },
      );
    }, 500);

    setTimeout(() => {
      html2canvas(document.querySelector('#divToPrint3'), { scale: '3' }).then(
        (canvas) => {
          // document.body.appendChild(canvas); // if you want see your screenshot in body.
          const imgData = canvas.toDataURL('image/png');
          Pdf.addImage(imgData, 'PNG', 10, 50, 550, 0, undefined, false);
          Pdf.save(`Analytics Report(${moment().format('DD MMM YYYY')}).pdf`);
          setIsDownloading(false);
        },
      );
    }, 900);
  };

  const onClose = () => {
    setOpen(false);
  };

  useEffect(() => {
    if (!navigator.geolocation) {
      console.log('Geolocation is not supported by your browser');
      return;
    }

    let isMounted = true;

    navigator.geolocation.getCurrentPosition(
      (location) => {
        if (isMounted) {
          setPosition({
            lat: location.coords.latitude,
            log: location.coords.longitude,
          });
        }
      },
      (err) => {
        console.error('Geolocation error:', err.code, err.message);
        if (isMounted) {
          console.log(`Error (${err.code}): ${err.message}`);
        }
      },
      {
        enableHighAccuracy: false,
        timeout: 15000, // 15 seconds
        maximumAge: 0, // No cached data
      },
    );

    return () => {
      isMounted = false;
    };
  }, [activeTab]);

  // ********** Start Overview Tab ***************
  const [cPeriodType, setCPeriodType] = useState('week');
  const [actUsrFltVis, setActUsrFltVis] = useState(false);
  const [actUsrFltTxt, setActUsrFltTxt] = useState('This Week');
  const [dailyAverageData, setDailyAverageData] = useState({});
  const [datilyAvgChartData, setDailyAvgChartData] = useState({
    data: [],
    labels: [],
  });
  const [userbyLocation, setUserByLocation] = useState({
    data: [],
    labels: [],
  });
  const [totalOrdersChart, setTotalOrdersChart] = useState({
    data: [],
    labels: [],
  });
  const [weatherChartData, setWeatherChartData] = useState({});
  const [dailyAvgPercentage, setDailyAvgPercentage] = useState({});
  const customerDurationContent = (
    <WidgetWrapper>
      <div className="popMainDiv">
        {actUsrFltVis ? (
          <div
            className="isoDropdownLink"
            onClick={() => {
              setActUsrFltTxt('This Week');
              setActUsrFltVis(!actUsrFltVis);
              setCPeriodType('this_week');
            }}
            role="button"
            onKeyPress={() => {
              setActUsrFltTxt('This Week');
              setActUsrFltVis(!actUsrFltVis);
              setCPeriodType('this_week');
            }}
            tabIndex="-1"
          >
            <IntlMessages id="dashboard.thisweek" />
          </div>
        ) : null}
        <div
          className="isoDropdownLink"
          onClick={() => {
            setActUsrFltTxt('Last Week');
            setActUsrFltVis(!actUsrFltVis);
            setCPeriodType('week');
          }}
          role="button"
          onKeyPress={() => {
            setActUsrFltTxt('Last Week');
            setActUsrFltVis(!actUsrFltVis);
            setCPeriodType('week');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.lastweek" />{' '}
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setActUsrFltTxt('This Month');
            setActUsrFltVis(!actUsrFltVis);
            setCPeriodType('this_month');
          }}
          role="button"
          onKeyPress={() => {
            setActUsrFltTxt('This Month');
            setActUsrFltVis(!actUsrFltVis);
            setCPeriodType('this_month');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.thisMonth" />
        </div>

        <div
          className="isoDropdownLink"
          onClick={() => {
            setActUsrFltTxt('Last Month');
            setActUsrFltVis(!actUsrFltVis);
            setCPeriodType('month');
          }}
          role="button"
          onKeyPress={() => {
            setActUsrFltTxt('Last Month');
            setActUsrFltVis(!actUsrFltVis);
            setCPeriodType('month');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.lastMonth" />
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setActUsrFltTxt('Last Six Month');
            setActUsrFltVis(!actUsrFltVis);
            setCPeriodType('six_month');
          }}
          role="button"
          onKeyPress={() => {
            setActUsrFltTxt('Last Six Month');
            setActUsrFltVis(!actUsrFltVis);
            setCPeriodType('six_month');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.lastSixMonth" />
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setActUsrFltTxt('Last Year');
            setActUsrFltVis(!actUsrFltVis);
            setCPeriodType('year');
          }}
          role="button"
          onKeyPress={() => {
            setActUsrFltTxt('Last Year');
            setActUsrFltVis(!actUsrFltVis);
            setCPeriodType('year');
          }}
          tabIndex="-1"
        >
          <IntlMessages id="dashboard.lastYear" />
        </div>
      </div>
    </WidgetWrapper>
  );

  function durationChange() {
    setActUsrFltVis((visiblePre) => !visiblePre);
  }

  // User dummy data

  const dailyAvgChartData = {
    series: datilyAvgChartData?.data || [],
    options: {
      chart: {
        type: 'bar',
        height: 1050,
        stacked: true,
        toolbar: {
          show: false,
        },
        zoom: {
          enabled: true,
        },
      },
      responsive: [
        {
          breakpoint: 480,
          options: {
            legend: {
              position: 'bottom',
              offsetX: -10,
              offsetY: 0,
            },
          },
        },
      ],
      legend: {
        position: 'bottom',
        labels: {
          style: {
            fontSize: '10px', // Set legend font size to 10px
          },
        },
      },
      fill: {
        opacity: 1,
      },
      colors: ['#344BFD', '#00D9C0', '#04B8FF'], // Set the bar colors
      yaxis: {
        labels: {
          show: false, // Remove y-axis labels
        },
      },
      plotOptions: {
        bar: {
          columnWidth: '12px', // Set the bar width to 12px
        },
      },
      dataLabels: {
        enabled: false, // Remove labels from bars
      },
      xaxis: {
        categories: datilyAvgChartData?.labels || [], // Set x-axis labels
        axisTicks: {
          show: false, // Optionally hide the ticks on the x-axis
        },
      },
      grid: {
        show: false, // Hide grid lines
      },
    },
  };

  const avgTimeChartData = {
    series: [
      {
        name: 'Avg Time',
        data: analyticsChartVal?.data, // M to Today
      },
    ],
    options: {
      chart: {
        type: 'bar',
        height: 250,
        toolbar: { show: false },
      },
      plotOptions: {
        bar: {
          borderRadius: 6,
          columnWidth: '40%',
        },
      },
      dataLabels: {
        enabled: false,
      },
      xaxis: {
        categories: analyticsChartVal?.labels,
        labels: {
          style: {
            fontSize: '12px',
            colors: '#888',
          },
        },
        axisTicks: { show: false },
        axisBorder: { show: false },
      },
      grid: {
        strokeDashArray: 4,
        borderColor: '#eee',
      },
      fill: {
        colors: [
          '#00D9C0', // Today (highlighted)
        ],
      },
      tooltip: {
        y: {
          formatter: (val) => {
            const h = Math.floor(val);
            const m = Math.round((val - h) * 60);
            return `${h}h ${m}m`;
          },
        },
      },
    },
  };

  const userAgeInsightChartData = {
    series: userinsightChart || [],
    options: {
      chart: {
        height: 440,
        type: 'radialBar',
      },
      plotOptions: {
        radialBar: {
          offsetY: 0,
          startAngle: 0,
          endAngle: 270,
          hollow: {
            margin: 5,
            size: '25%',
            background: 'transparent',
            image: undefined,
          },
          dataLabels: {
            name: {
              show: false,
            },
            value: {
              show: false,
            },
          },
          barLabels: {
            enabled: true,
            useSeriesColors: false,
            offsetX: -8,
            fontSize: '10px',
            formatter: (seriesName, opts) => {
              // Show shortened labels in bar
              const shortLabels = [
                '0-18',
                '19-24',
                '25-29',
                '30-34',
                '35-39',
                '40-50',
                '50+',
              ];
              return `${shortLabels[opts.seriesIndex]}: ${
                opts.w.globals.series[opts.seriesIndex]
              }%`;
            },
            style: {
              colors: ['#667085'], // Set the color of bar labels
            },
          },
          track: {
            background: '#FFFFFF', // Set the remaining part color to white
            strokeWidth: '60%', // Adjust the track width if needed
          },
        },
      },
      colors: [
        '#4FC3F7',
        '#00D9C0',
        '#3B82F6',
        '#6366F1',
        '#ACD4FF',
        '#82F7EA',
        '#8D6E63',
      ], // Updated colors
      labels: [
        '0-18 Growing Up',
        '19-24 Early Adults',
        '25-29 Young Pros',
        '30-34 Mid Career',
        '35-39 Family Builders',
        '40-50 Established',
        '50+ Prime Achievers',
      ],
      legend: {
        show: true,
        position: 'bottom', // Place the legend at the bottom
        horizontalAlign: 'left', // Align legend to the left
        floating: false, // Ensure the legend is not floating
        fontSize: '10px',
        labels: {
          colors: ['#667085'], // Set the color of legend text
          useSeriesColors: false, // Use series colors for the legend
        },
      },
      responsive: [
        {
          breakpoint: 480,
          options: {
            legend: {
              show: true, // Show legend on smaller screens
              position: 'bottom',
              horizontalAlign: 'left',
              offsetY: -10,
              offsetX: -10,
            },
          },
        },
      ],
    },
  };

  const appPeformanceChartData = {
    series: [42],
    options: {
      chart: {
        type: 'radialBar',
        offsetY: -20,
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          track: {
            background: '#E9EDF0', // Set track color
            strokeWidth: '35px', // Set the width of the empty track
            margin: 5, // Margin is in pixels
          },
          dataLabels: {
            name: {
              show: false,
            },
            value: {
              offsetY: -2,
              fontSize: '22px',
            },
          },
        },
      },
      grid: {
        padding: {
          top: -10,
        },
      },
      fill: {
        type: 'solid', // Use solid fill for a single color
        colors: ['#00B3FF'], // Set the fill color
      },
      labels: ['Average Results'],
    },
  };

  const userInsightData = [
    {
      title: '',
      data: [
        { isUp: true, time: '2y 3m', percent: 15, type: 'Average child age' },
        {
          isUp: false,
          time: '2y 3m',
          percent: 95,
          type: 'Average age of child when registered',
        },
        {
          isUp: false,
          time: '2y 3m',
          percent: 95,
          type: 'Average age of first use',
        },
        {
          isUp: false,
          time: '3m',
          percent: 65,
          type: 'Average age of last use',
        },
        {
          isUp: true,
          time: '2y 3m',
          percent: 15,
          type: 'Average length of use',
        },
      ],
    },
  ];

  // ******* Start Enviornment Tab ********

  const [orderFlt, setOrderFlt] = useState({
    type: 'last_week',
    vis: false,
    text: 'Last Week',
  });

  const customerOrderFltContent = (
    <WidgetWrapper>
      <div className="popMainDiv">
        {orderFlt.vis ? (
          <div
            className="isoDropdownLink"
            onClick={() => {
              setOrderFlt({
                type: 'last_week',
                vis: !orderFlt.vis,
                text: 'Last Week',
              });
            }}
            role="button"
            onKeyPress={() => {
              setOrderFlt({
                type: 'last_week',
                vis: !orderFlt.vis,
                text: 'Last Week',
              });
            }}
            tabIndex="-1"
          >
            <IntlMessages id="Last Week" />
          </div>
        ) : null}
        <div
          className="isoDropdownLink"
          onClick={() => {
            setOrderFlt({
              type: 'this_month',
              vis: !orderFlt.vis,
              text: 'This Month',
            });
          }}
          role="button"
          onKeyPress={() => {
            setOrderFlt({
              type: 'this_month',
              vis: !orderFlt.vis,
              text: 'This Month',
            });
          }}
          tabIndex="-1"
        >
          <IntlMessages id="This Month" />{' '}
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setOrderFlt({
              type: 'last_month',
              vis: !orderFlt.vis,
              text: 'Last Month',
            });
          }}
          role="button"
          onKeyPress={() => {
            setOrderFlt({
              type: 'last_month',
              vis: !orderFlt.vis,
              text: 'Last Month',
            });
          }}
          tabIndex="-1"
        >
          <IntlMessages id="Last Month" />
        </div>

        <div
          className="isoDropdownLink"
          onClick={() => {
            setOrderFlt({
              type: 'last_6_month',
              vis: !orderFlt.vis,
              text: 'Last 6 Months',
            });
          }}
          role="button"
          onKeyPress={() => {
            setOrderFlt({
              type: 'last_6_month',
              vis: !orderFlt.vis,
              text: 'Last 6 Months',
            });
          }}
          tabIndex="-1"
        >
          <IntlMessages id="Last 6 Months" />
        </div>
        <div
          className="isoDropdownLink"
          onClick={() => {
            setOrderFlt({
              type: 'last_year',
              vis: !orderFlt.vis,
              text: 'Last Year',
            });
          }}
          role="button"
          onKeyPress={() => {
            setOrderFlt({
              type: 'last_year',
              vis: !orderFlt.vis,
              text: 'Last Year',
            });
          }}
          tabIndex="-1"
        >
          <IntlMessages id="Last Year" />
        </div>
      </div>
    </WidgetWrapper>
  );

  function orderFltChange() {
    setOrderFlt((visiblePre) => {
      return { ...visiblePre, vis: !visiblePre.vis };
    });
  }

  // Enviornment Dummy Data
  const weatherAltChartData = {
    series: [
      {
        name: 'High',
        data: weatherChartData?.highs || [],
      },
      {
        name: 'Low',
        data: weatherChartData?.lows || [],
      },
    ],
    options: {
      chart: {
        height: 350,
        type: 'line',
        dropShadow: {
          enabled: true,
          color: '#000',
          top: 18,
          left: 7,
          blur: 10,
          opacity: 0.2,
        },
        zoom: {
          enabled: false,
        },
        toolbar: {
          show: false,
        },
      },
      colors: ['#00D9C0', '#344BFD'],
      dataLabels: {
        enabled: true,
      },
      stroke: {
        curve: 'smooth',
        width: 2, // Set line width to 2px
      },
      title: {
        text: 'Weather Alerts',
        align: 'left',
        style: {
          fontSize: '14px',
          fontWeight: '700',
          lineHeight: '19.6px',
          color: '#2D3748',
        },
      },
      grid: {
        borderColor: '#e7e7e7',
      },
      markers: {
        size: 1,
      },
      xaxis: {
        categories: weatherChartData?.labels || [],
      },
      yaxis: {
        min: 5,
        max: 40,
      },
      legend: {
        position: 'top',
        horizontalAlign: 'right',
        floating: true,
        offsetY: -25,
        offsetX: -5,
      },
      fill: {
        colors: ['#00D9C0', '#344BFD'], // Solid fill colors
      },
    },
  };

  const userLocationChartData = {
    series: userbyLocation?.data || [],
    options: {
      chart: {
        type: 'polarArea',
      },
      colors: [
        '#1E40AF',
        '#ACD4FF',
        '#00D9C0',
        '#82F7EA',
        '#60A5FA',
        '#93C5FD',
      ],
      stroke: {
        colors: ['#fff'],
      },
      fill: {
        opacity: 0.8,
      },
      legend: {
        position: 'bottom', // Place the legend below the chart
        horizontalAlign: 'left', // Center the legend horizontally
        offsetY: -10,
        offsetX: 7,
        floating: false,
        labels: {
          colors: ['#000'], // Optional: Set the color of legend text
        },
      },
      labels: userbyLocation?.labels || [], // Customize legend names
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              width: 200,
            },
            legend: {
              position: 'bottom', // Ensure legend is at the bottom on smaller screens as well
              horizontalAlign: 'center',
            },
          },
        },
      ],
    },
  };

  const lengthOfTimeChartData = {
    series: [57.6, 28.5, 9, 5],
    options: {
      chart: {
        width: 380,
        type: 'pie',
      },
      dataLabels: {
        enabled: false,
      },
      colors: ['#404746', '#02B5FF', '#00D9C0', '#1E40AF'], // Custom colors for each segment
      labels: ['Warm', 'Cold', 'Moderate', 'Extreme'], // Labels for each segment (adjust as needed)
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              width: 200,
            },
            legend: {
              position: 'bottom', // Position legend below the chart on smaller screens
            },
          },
        },
      ],
      legend: {
        position: 'bottom', // Position legend below the chart
        horizontalAlign: 'left', // Center align the legend horizontally
        floating: false,
        offsetY: -10,
        offsetX: 10,
        labels: {
          colors: ['#000'], // Optional: Set color for legend text
        },
      },
    },
  };

  const totalOrderChartData = {
    series: [
      {
        name: 'Total Order',
        data: totalOrdersChart?.data || [],
      },
    ],
    options: {
      chart: {
        type: 'bar',
        height: 500,
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '8px', // Set the bar width
          endingShape: 'rounded', // Set ending shape to rounded
          colors: {
            ranges: [
              {
                from: 0,
                to: 100,
                color: '#3A3A3A', // Bar color
              },
            ],
          },
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        show: true,
        width: 2,
        colors: ['transparent'],
      },
      xaxis: {
        categories: totalOrdersChart?.labels || [],
        labels: {
          show: true, // Show x-axis labels
          style: {
            fontSize: '10px', // Set x-axis label size to 10px
          },
        },
        axisBorder: {
          show: false, // Hide the x-axis line
        },
        axisTicks: {
          show: false, // Hide ticks on the x-axis
        },
      },
      yaxis: {
        labels: {
          show: false,
        },
      },
      fill: {
        opacity: 1,
        colors: ['#3A3A3A'], // Bar color
      },
      tooltip: {
        y: {
          formatter: (val) => {
            return `$ ${val} thousands`;
          },
        },
      },
      colors: ['#3A3A3A'], // Bar color
      hover: {
        // Set hover colors
        colors: ['#344BFD'],
      },
      grid: {
        show: true, // Show grid lines
        borderColor: '#e0e0e0', // Color of grid lines
        strokeDashArray: 4, // Set to 4 for dotted lines
        xaxis: {
          lines: {
            show: false, // Hide vertical grid lines
          },
        },
        yaxis: {
          lines: {
            show: true, // Show horizontal grid lines
          },
        },
      },
    },
  };

  // ******* End Enviornment Tab *********

  const getAnayticsOverviewData = async () => {
    const data = {
      period_type: 'week',
      brand_id: '',
      start_end_date: stEndDate,
    };
    try {
      const response = await getApiData('getAnayticsData', data, 'post');
      const response2 = await getApiData('analytics/overview');

      if (response.success) {
        // Active user chart data
        const actuserData = [];
        const label = response.data.ActiveUserChart.map((usr) => {
          return usr.day;
        });
        await response.data.ActiveUserChart.map((usr) => {
          return actuserData.push([
            new Date(usr.day).getTime(),
            Number(usr.users),
          ]);
        });
        const newactuserData = {
          series: [
            {
              name: 'Active Users',
              data: actuserData,
            },
          ],
          options: {
            chart: {
              color: '#04B8FF',
              type: 'area',
              stacked: false,
              height: 350,
              zoom: {
                type: 'x',
                enabled: true,
                autoScaleYaxis: true,
              },
              toolbar: {
                show: false, // Hide the toolbar
              },
            },
            dataLabels: {
              enabled: false,
            },
            markers: {
              size: 0,
            },
            fill: {
              type: 'gradient',
              gradient: {
                shadeIntensity: 1,
                inverseColors: false,
                opacityFrom: 0.5,
                opacityTo: 0,
                stops: [0, 90, 100],
              },
            },
            yaxis: {
              show: false, // Hide the y-axis
            },
            xaxis: {
              type: 'datetime',
              labels: {
                formatter: (value) => {
                  return new Date(value).toLocaleString('default', {
                    day: '2-digit',
                    month: 'short',
                  }); // Use 'short' for abbreviated month names
                },
                rotate: 0, // Keep labels horizontal
                show: true,
              },
              tickAmount: label.lenght, // Show 12 ticks for each month
              axisBorder: {
                show: false, // Hide the x-axis line
              },
              axisTicks: {
                show: false, // Hide ticks on the x-axis
              },
            },
            tooltip: {
              enabled: false, // Hide tooltips
            },
            grid: {
              show: false, // Hide grid lines
            },
          },
        };
        setActiveUserChartData(newactuserData);

        // operating System Chart
        let optSrs = [];
        let optLbl = [];
        if (response2.success && response2.platformData) {
          optSrs = [
            Number(response2.platformData.ANDROID),
            Number(response2.platformData.IOS),
          ];
          optLbl = ['ANDROID', 'IOS'];
        } else {
          await response.data.operatingSystemChart.map((opt) => {
            if (isNumber(opt[1])) {
              optSrs.push(Number(opt[1]));
              optLbl.push(opt[0]);
            }
            return opt;
          });
        }

        const newOptSysData = {
          series: optSrs,
          options: {
            chart: {
              type: 'donut',
              width: '100%',
            },
            colors: ['#344BFD', '#72E7C8'], // Specify colors for each series
            dataLabels: {
              enabled: true,
              formatter: (val, opts) => {
                return `${opts.w.globals.labels[opts.seriesIndex]} : ${val} %`;
              },
              style: {
                fontSize: '12px',
                colors: ['#000'],
              },
              offsetY: 10,
              dropShadow: {
                enabled: false, // Disable text shadow
              },
            },
            legend: {
              position: 'bottom', // Place the legend at the bottom
              horizontalAlign: 'center', // Center the legend horizontally
              floating: false,
              offsetY: 0,
              labels: {
                colors: ['#000'], // Optional: Set the color of legend text
              },
            },
            labels: optLbl, // Specify the labels for the series
            responsive: [
              {
                breakpoint: 480,
                options: {
                  chart: {
                    width: 200,
                  },
                  dataLabels: {
                    offsetY: 30, // Adjust as needed
                  },
                  legend: {
                    position: 'bottom', // Ensure legend is at the bottom on smaller screens as well
                    horizontalAlign: 'center',
                  },
                },
              },
            ],
          },
        };
        setOperatingSystemChartData(newOptSysData);

        // performance Analysis Data ********************
        // postShares **********

        const pstShrData = [];

        await response.data.performanceAnalysisData.postShares.data.map(
          (pfa) => {
            if (isNumber(pfa[1])) {
              // Parse "16 Jun" as a date (assuming current year)
              const dateStr = `${pfa[0]} ${new Date().getFullYear()}`; // e.g., "16 Jun 2025"
              const date = new Date(dateStr);
              const formattedDate = date.toISOString().split('T')[0]; // "YYYY-MM-DD"
              pstShrData.push([formattedDate, Number(pfa[1])]);
            }
            return pfa;
          },
        );

        const newpstShr = {
          series: [
            {
              name: 'Post Share',
              data: pstShrData,
            },
          ],
          options: {
            chart: {
              type: 'area',
              height: 140,
              zoom: {
                enabled: false,
              },
              background: 'transparent', // Optional: Makes the background transparent
            },
            colors: ['#07B861'], // Set the color for the series
            dataLabels: {
              enabled: false, // Hide data labels
            },
            stroke: {
              curve: 'smooth', // 'smooth' for rounded lines
              width: 2, // Optional: Set the width of the line
            },
            fill: {
              type: 'gradient', // Use 'solid' for a single color fill
              gradient: {
                shadeIntensity: 1,
                inverseColors: false,
                opacityFrom: 0.5,
                opacityTo: 0,
                stops: [0, 90, 100],
              },
            },
            xaxis: {
              labels: {
                show: false, // Hide x-axis labels
              },
              axisBorder: {
                show: false, // Hide x-axis border
              },
              axisTicks: {
                show: false, // Hide x-axis ticks
              },
            },
            yaxis: {
              labels: {
                show: false, // Hide y-axis labels
              },
              axisBorder: {
                show: false, // Hide y-axis border
              },
              axisTicks: {
                show: false, // Hide y-axis ticks
              },
            },
            title: {
              text: '', // Remove chart title
              align: 'left',
              margin: 0,
              style: {
                fontSize: '0px', // Set font size to 0
              },
            },
            subtitle: {
              text: '', // Remove chart subtitle
              align: 'left',
              margin: 0,
              style: {
                fontSize: '0px', // Set font size to 0
              },
            },
            legend: {
              show: false, // Hide legend
            },
            grid: {
              show: false, // Hide grid lines
            },
          },
        };
        setTileData(response2?.pageViewData);
        setPostShareData({
          chartData: newpstShr,
          totalCount:
            response.data.performanceAnalysisData.postShares.total_count,
          percentage:
            response.data.performanceAnalysisData.postShares.percentage,
        });

        // porduct/user average

        const PdtUsrAvg = [];
        await response.data.performanceAnalysisData.productUsed.data.map(
          (pfa) => {
            if (isNumber(pfa[1])) {
              PdtUsrAvg.push([new Date(pfa[0]).getTime(), Number(pfa[1])]);
            }
            return pfa;
          },
        );

        const newPdtUsrAvg = {
          series: [
            {
              name: 'STOCK ABC',
              data: pstShrData,
            },
          ],
          options: {
            chart: {
              type: 'area',
              height: 140,
              zoom: {
                enabled: false,
              },
              background: 'transparent', // Optional: Makes the background transparent
            },
            colors: ['#7854DF'], // Set the color for the series
            dataLabels: {
              enabled: false, // Hide data labels
            },
            stroke: {
              curve: 'smooth', // 'smooth' for rounded lines
              width: 2, // Optional: Set the width of the line
            },
            fill: {
              type: 'gradient', // Use 'solid' for a single color fill
              gradient: {
                shadeIntensity: 1,
                inverseColors: false,
                opacityFrom: 0.5,
                opacityTo: 0,
                stops: [0, 90, 100],
              },
            },
            xaxis: {
              labels: {
                show: false, // Hide x-axis labels
              },
              axisBorder: {
                show: false, // Hide x-axis border
              },
              axisTicks: {
                show: false, // Hide x-axis ticks
              },
            },
            yaxis: {
              labels: {
                show: false, // Hide y-axis labels
              },
              axisBorder: {
                show: false, // Hide y-axis border
              },
              axisTicks: {
                show: false, // Hide y-axis ticks
              },
            },
            title: {
              text: '', // Remove chart title
              align: 'left',
              margin: 0,
              style: {
                fontSize: '0px', // Set font size to 0
              },
            },
            subtitle: {
              text: '', // Remove chart subtitle
              align: 'left',
              margin: 0,
              style: {
                fontSize: '0px', // Set font size to 0
              },
            },
            legend: {
              show: false, // Hide legend
            },
            grid: {
              show: false, // Hide grid lines
            },
          },
        };

        setProductUserAvgData({
          chartData: newPdtUsrAvg,
          percentage:
            response.data.performanceAnalysisData.productUsed.percentage,
          totalCount:
            response.data.performanceAnalysisData.productUsed.total_count,
        });
        if (response?.data?.goalCompletion) {
          setGoalCompletionData(response.data.goalCompletion[0]);
        }
      }
    } catch (error) {
      console.log('error ===', error);
    }
  };

  const getAnayticsUserData = async () => {
    try {
      const userResp = await getApiData('analytics/user');

      // Child Ages chart data
      if (userResp.success) {
        // Child Ages Chart Data
        const chldAgData = [];
        const chldAgCat = [];

        await userResp.childAgeData.map((chlAg) => {
          chldAgData.push(Number(chlAg.count));
          return chldAgCat.push(chlAg.age_range || '0');
        });

        const newChldAgesChartData = {
          series: [
            {
              name: 'Child Ages',
              data: chldAgData,
            },
          ],
          options: {
            chart: {
              type: 'bar',
              height: 500,
              width: '14px',
            },
            plotOptions: {
              bar: {
                horizontal: false,
                columnWidth: '14px',
                endingShape: 'rounded',
                colors: {
                  ranges: [
                    {
                      from: 0,
                      to: 100,
                      color: '#E9ECF1', // Default bar color
                    },
                  ],
                },
                dataLabels: {
                  enabled: false,
                },
                hover: {
                  colors: ['#0000FF'], // Change hover color to blue
                },
              },
            },
            dataLabels: {
              enabled: false,
            },
            stroke: {
              show: true,
              width: 2,
              colors: ['transparent'],
            },
            xaxis: {
              categories: chldAgCat,
              axisTicks: {
                show: false, // Hide ticks on the x-axis
              },
            },
            yaxis: {
              categories: [5, 50, 100], // Only show 5%, 50%, and 100%
              labels: {
                formatter: (val) => {
                  return `${val}%`;
                },
              },
              tickAmount: 2,
              max: 100,
              min: 0,
            },
            fill: {
              opacity: 1,
              colors: ['#E9ECF1'], // Default bar color
            },
            tooltip: {
              y: {
                formatter: (val) => {
                  return `$ ${val} thousands`;
                },
              },
            },
            colors: ['#E9ECF1'], // Default bar color
          },
        };
        setChildAgesChartData(newChldAgesChartData);

        // Child Height Data
        const chldHgtSrs = [];
        const chldHgtCat = [];

        await userResp.childHeightData.map((chlHgt) => {
          chldHgtSrs.push(Number(chlHgt.count));
          return chldHgtCat.push(chlHgt.height_range || '0');
        });

        const newChldHgtData = {
          series: [
            {
              name: 'Child Height',
              data: chldHgtSrs, // Updated data
            },
          ],
          options: {
            chart: {
              type: 'bar',
              height: 500,
            },
            plotOptions: {
              bar: {
                horizontal: false,
                columnWidth: '5px', // Approximate bar width
                endingShape: 'rounded', // Rounded edges
                borderRadius: 50, // High value for rounded corners
              },
            },
            dataLabels: {
              enabled: false, // Enable data labels
            },
            stroke: {
              show: true,
              width: 5, // Set stroke width to 5px
              colors: ['#000'], // Set border color to black
            },
            xaxis: {
              categories: chldHgtCat, // Updated categories
              axisBorder: {
                show: false, // Hide the x-axis line
              },
              axisTicks: {
                show: false, // Optionally hide the ticks on the x-axis
              },
            },
            yaxis: { labels: { show: false } },
            grid: {
              show: false, // Hide grid lines
            },
            fill: {
              colors: ['#000'], // Set bar color to black
              opacity: 1,
            },
            tooltip: {
              y: {
                formatter: (val) => {
                  return `$ ${val} thousands`;
                },
              },
            },
            responsive: [
              {
                breakpoint: 480,
                options: {
                  chart: {
                    width: '100%',
                  },
                  plotOptions: {
                    bar: {
                      columnWidth: '60%', // Adjust for smaller screens
                    },
                  },
                },
              },
            ],
          },
        };
        setChildHeightChartData(newChldHgtData);

        // Child Weight Data
        const chldWgtSrs = [];
        const chldWgtCat = [];

        await userResp.childWeightData.map((chlWgt) => {
          chldWgtSrs.push(Number(chlWgt.count));
          return chldWgtCat.push(chlWgt.weight_range || '0');
        });

        const newChldWgtData = {
          series: [
            {
              name: 'Child Height',
              data: chldWgtSrs, // Updated data
            },
          ],
          options: {
            chart: {
              type: 'bar',
              height: 500,
            },
            plotOptions: {
              bar: {
                horizontal: false,
                columnWidth: '5px', // Approximate bar width
                endingShape: 'rounded', // Rounded edges
                borderRadius: 50, // High value for rounded corners
              },
            },
            dataLabels: {
              enabled: false, // Enable data labels
            },
            stroke: {
              show: true,
              width: 5, // Set stroke width to 5px
              colors: ['#000'], // Set border color to black
            },
            xaxis: {
              categories: chldWgtCat, // Updated categories
              axisBorder: {
                show: false, // Hide the x-axis line
              },
              axisTicks: {
                show: false, // Optionally hide the ticks on the x-axis
              },
            },
            yaxis: { labels: { show: false } },
            grid: {
              show: false, // Hide grid lines
            },
            fill: {
              colors: ['#000'], // Set bar color to black
              opacity: 1,
            },
            tooltip: {
              y: {
                formatter: (val) => {
                  return `$ ${val} thousands`;
                },
              },
            },
            responsive: [
              {
                breakpoint: 480,
                options: {
                  chart: {
                    width: '100%',
                  },
                  plotOptions: {
                    bar: {
                      columnWidth: '60%', // Adjust for smaller screens
                    },
                  },
                },
              },
            ],
          },
        };
        setChildWeightChartData(newChldWgtData);

        // Create separate arrays for category and count
        const categories = await userResp?.temperatureCatData.map(
          (item) => item?.temperature_category || 'Unknown',
        );

        const counts = await userResp?.temperatureCatData.map(
          (item) => parseFloat(item?.count) || 0,
        );

        const climateConditionData = {
          series: counts,
          options: {
            chart: {
              width: 380,
              type: 'donut',
            },
            dataLabels: {
              enabled: false,
            },
            colors: ['#62B2FD', '#00D9C0', '#1E40AF', '#94F0E6', '#9F97F7'], // Set custom colors for each segment
            labels: categories, // Labels for the segments
            responsive: [
              {
                breakpoint: 480,
                options: {
                  chart: {
                    width: 200,
                  },
                  legend: {
                    show: false, // Hide legend on smaller screens
                  },
                },
              },
            ],
            legend: {
              position: 'right', // Position the legend to the right
              offsetY: 0,
              height: 230, // Height of the legend
            },
          },
        };
        setClimateConditionChartData(climateConditionData);
        setAnalyticsAvgTime(userResp?.avgUserTimePerData);
        const data = userResp?.avgUserTimePerData?.average_user_time.map(
          (item) => parseFloat(item.avg_time_hours.toFixed(2)),
        );
        const labels = userResp?.avgUserTimePerData?.average_user_time.map(
          (item) => item.day_label,
        );
        setAnalyticsChartVal({ data, labels });
        setDailyAverageData(userResp?.getDailyAverageTime);
        if (userResp?.getDailyAverageTime?.average_user_time) {
          const dailyAvgLabel =
            userResp?.getDailyAverageTime?.average_user_time.map(
              (item) => item.day_label,
            );

          const morningData = [];
          const lunchData = [];
          const afternoonData = [];

          userResp?.getDailyAverageTime?.average_user_time.forEach((item) => {
            morningData.push(item.morning_avg);
            lunchData.push(item.lunch_avg);
            afternoonData.push(item.afternoon_avg);
          });
          setDailyAvgChartData({
            data: [
              { name: 'Morning Avg', data: morningData },
              { name: 'Lunch Avg', data: lunchData },
              { name: 'Afternoon Avg', data: afternoonData },
            ],
            labels: dailyAvgLabel,
          });
        }

        setDailyAvgPercentage(userResp?.getDailyAverageTimePercentage);

        // Set User Insights data
        setAgeInsightChart(userResp.ageInsightsData);
        const userInsightVal = userResp?.userAgeInsightsChart.map((val) =>
          Number(val.percentage),
        );
        setUserinsightChart(userInsightVal);
      }
    } catch (err) {
      console.log('errr');
    }
  };

  const getEnvironmentalData = async () => {
    try {
      const resp = await getApiData(
        `analytics/environment?latitude=${position.lat}&longitude=${position.log}`,
      );

      // Child Ages chart data
      if (resp.success) {
        setWeatherChartData(resp?.weatherDetail);
        const labels = await resp?.userByLocationDetail.map(
          (item) => item?.country || 'Unknown',
        );

        const data = await resp?.userByLocationDetail.map(
          (item) => parseFloat(item?.user_count) || 0,
        );
        setUserByLocation({ labels, data });
        const totalOrderLabel = await resp?.totalOrderData.map(
          (item) => item?.location || 'Unknown',
        );

        const rotalOderData = await resp?.totalOrderData.map(
          (item) => parseFloat(item?.order_count) || 0,
        );
        setTotalOrdersChart({ labels: totalOrderLabel, data: rotalOderData });
      }
    } catch (err) {
      console.log('errr');
    }
  };

  const rednerGoalCompletion = () => {
    return (
      <ReportsWidget
        style={{
          height: '100%',
          borderRadius: 16,
          boxShadow: '0px 4px 8px 0px #0000000D',
        }}
      >
        <div>
          <div className="chartHeading">
            <IntlMessages id="title.goal completions" />
            <span
              style={{
                fontSize: '12px',
                fontWeight: 400,
                fontStyle: 'italic',
                color: '#202225',
                fontFamily: 'Inter',
                lineHeight: '14.52px',
              }}
            >
              Relative Change VS Last Month
            </span>
          </div>
          <div
            style={{
              fontSize: '11px',
              display: 'flex',
              fontWeight: 400,
              justifyContent: 'space-between',
              color: '#2E2E30',
              lineHeight: '13.31px',
              fontFamily: 'Inter',
              marginTop: '5px',
            }}
          >
            {goalCompletionData?.goal_title ?? ''}
            <span
              style={{
                fontWeight: 500,
                fontSize: '16px',
                fontFamily: 'Roboto',
                color: '#15134B',
                lineHeight: '18.75px',
              }}
            >
              {formatValue(goalCompletionData?.current_count) ?? 0}
              <span
                style={{
                  fontSize: '10px',
                  fontWeight: 500,
                  color:
                    goalCompletionData?.growth_type === 'increase'
                      ? '#2DD683'
                      : 'red',
                  marginLeft: '10px',
                  fontFamily: 'Inter',
                  lineHeight: '12.1px',
                }}
              >
                {goalCompletionData?.growth_type === 'increase' ? (
                  <ArrowUpOutlined />
                ) : (
                  <ArrowDownOutlined />
                )}
                {goalCompletionData?.percentage || ''}
              </span>
            </span>
          </div>
        </div>
        <div
          className="chartStyle"
          style={{
            alignSelf: 'center',
            borderRadius: 20,
            marginTop: 30,
          }}
        >
          <GaugeChart
            id="gauge-chart3"
            nrOfLevels={1}
            colors={['#00B3FF']}
            arcWidth={0.2}
            percent={goalCompletionData?.goal_complete_percentage || 0}
            textColor='"black"'
          />
        </div>
      </ReportsWidget>
    );
  };

  useEffect(() => {
    if (activeTab === 'User') {
      getAnayticsUserData();
    } else if (activeTab === 'Environment') {
      getEnvironmentalData();
    } else {
      getAnayticsOverviewData();
    }
  }, [activeTab, stEndDate]);

  return (
    <NewStyleWrapper>
      <div className="mainContainer">
        {/* Responsive Header Controls */}
        <div className="headerControls">
          <Popover
            content={content}
            trigger="click"
            open={isRangePickerOpen}
            onOpenChange={handleDateClick}
          >
            <Button
              type="dashed"
              className="headerButton dateRange filterBtnTxt"
            >
              <IntlMessages id="analytics.dataRange" />
              <img src={DataRangeIcon} alt="icon" />
            </Button>
          </Popover>
          <Button
            onClick={printDocument}
            className="headerButton download filterBtnTxt"
          >
            {isDownloading ? (
              <LoadingOutlined
                style={{
                  fontSize: 16,
                }}
              />
            ) : (
              <img src={downloadIcon} alt="icon" />
            )}
            <IntlMessages id="common.report" />
          </Button>
          <Button
            type="primary"
            className="headerButton filter filterBtnTxt"
            onClick={showDrawer}
          >
            <IntlMessages id="common.filter" />
            <img src={threeLineFltIcon} alt="icon" />
          </Button>
        </div>

        <div className="contentArea" id="divToPrint2">
          <Tabs
            defaultActiveKey="Overview"
            className="custom-tabs"
            onChange={(e) => setActiveTab(e)}
          >
            {/* *** Overview Tab ***** */}
            <TabPane tab={<IntlMessages id="overviewTitle" />} key="Overview">
              {renderPageViewBoxes()}
              <Row style={rowStyle} gutter={[16, 16]} justify="start">
                <Col lg={14} md={24} sm={24} xs={24} style={colStyle}>
                  <ReportsWidget
                    style={{
                      height: '100%',
                      borderRadius: 10,
                      boxShadow: '5px 5px 5px 5px #0000',
                      fontWeight: 700,
                    }}
                  >
                    <div className="chartHeading">
                      <IntlMessages id="dashboard.activeUser" />
                      <Popover
                        content={customerDurationContent}
                        trigger="click"
                        open={actUsrFltVis}
                        onOpenChange={() => durationChange()}
                        placement="bottomRight"
                      >
                        <p
                          style={{
                            cursor: 'pointer',
                            fontSize: '14px',
                            fontWeight: 400,
                            color: '#4B5157',
                            fontFamily: 'Inter',
                            lineHeight: '20px',
                          }}
                        >
                          {actUsrFltTxt}
                          <DownOutlined style={{ marginLeft: 5 }} />
                        </p>
                      </Popover>
                    </div>
                    <Row
                      style={{
                        height: '200px',
                        marginTop: 35,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}
                    >
                      {activeUserChartData ? (
                        <ResponsiveContainer width="100%" height="100%">
                          <ReactApexChart
                            options={activeUserChartData.options}
                            series={activeUserChartData.series}
                            type="area"
                            height={800}
                          />
                        </ResponsiveContainer>
                      ) : (
                        <Skeleton />
                      )}
                    </Row>
                  </ReportsWidget>
                </Col>
                <Col lg={10} md={24} sm={24} xs={24} style={colStyle}>
                  {rednerGoalCompletion()}
                </Col>
              </Row>
              <Row style={rowStyle} gutter={[16, 16]} justify="start">
                <Col lg={8} md={24} sm={12} xs={24} style={colStyle}>
                  <ReportsWidget
                    style={{
                      height: '100%',
                      borderRadius: 10,
                      boxShadow: '5px 5px 5px 5px #0000',
                    }}
                  >
                    <div
                      className="chartHeading"
                      style={{ marginBottom: '15px' }}
                    >
                      <IntlMessages id="analytics.operatingSystem" />
                    </div>
                    <Row
                      style={{
                        height: '200px',
                        // marginTop: 35,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}
                    >
                      {operatingSystemChartData ? (
                        <ReactApexChart
                          options={operatingSystemChartData.options}
                          series={operatingSystemChartData.series}
                          type="donut"
                        />
                      ) : (
                        <Skeleton />
                      )}
                    </Row>
                  </ReportsWidget>
                </Col>
                <Col lg={16} md={20} sm={12} xs={24} style={colStyle}>
                  <ReportsWidget
                    style={{
                      height: '100%',
                      borderRadius: 10,
                      boxShadow: '5px 5px 5px 5px #0000',
                    }}
                  >
                    <div className="chartHeading" style={{ marginBottom: 20 }}>
                      <IntlMessages id="analytics.performanceAnalysis" />
                    </div>
                    <Row style={{ justifyContent: 'space-between' }}>
                      <Col lg={11} md={24} sm={12} xs={24}>
                        <div
                          className="performaceAnaTitle"
                          style={{
                            gap: '17px',
                            display: 'flex',
                            alignItems: 'center',
                          }}
                        >
                          <img src={greenShareIcon} alt="shareIcon"></img>
                          <IntlMessages id="chart.postshares" />
                        </div>
                        <Row
                          style={{
                            height: '200px',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}
                        >
                          {postShareData.chartData ? (
                            <div className="chartContainer">
                              <ReactApexChart
                                options={postShareData.chartData.options}
                                series={postShareData.chartData.series}
                                type="area"
                                height={131}
                              />

                              <div
                                style={{
                                  padding: '25px',
                                  display: 'flex',
                                  justifyContent: 'space-between',
                                }}
                              >
                                <p className="perAna2">
                                  {postShareData.totalCount || 0}
                                </p>
                                <span>
                                  <p
                                    style={{
                                      fontSize: '16px',
                                      fontWeight: 500,
                                      fontFamily: 'UniNeue',
                                      color: '#111111',
                                      lineHeight: '21.6px',
                                      letterSpacing: '-0.01em',
                                      gap: '10px',
                                      display: 'flex',
                                      alignItems: 'center',
                                    }}
                                  >
                                    {postShareData.percentage || '0%'}{' '}
                                    <img
                                      src={greenDownIcon}
                                      alt="downIcon"
                                    ></img>
                                  </p>
                                  <p
                                    style={{
                                      fontWeight: 100,
                                      fontSize: 10,
                                      color: '#111111',
                                      fontFamily: 'Inter',
                                      lineHeight: '12.1px',
                                      letterSpacing: '-0.01em',
                                    }}
                                  >
                                    Vs last week
                                  </p>
                                </span>
                              </div>
                            </div>
                          ) : (
                            <Skeleton />
                          )}
                        </Row>
                      </Col>
                      <Col lg={11} md={24} sm={12} xs={24}>
                        <div
                          className="performaceAnaTitle"
                          style={{
                            gap: '18px',
                            display: 'flex',
                            alignItems: 'center',
                          }}
                        >
                          <img src={blueTreeIcon} alt="treeIcon"></img>
                          <IntlMessages id="title.productsUserAvg" />
                        </div>
                        <Row
                          style={{
                            height: '200px',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}
                        >
                          {productUserAvgData.chartData ? (
                            <div>
                              <ReactApexChart
                                options={productUserAvgData.chartData.options}
                                series={productUserAvgData.chartData.series}
                                type="area"
                                height={131}
                              />

                              <div
                                style={{
                                  padding: '25px',
                                  display: 'flex',
                                  justifyContent: 'space-between',
                                }}
                              >
                                <p className="perAna2">
                                  {productUserAvgData.totalCount || 0}
                                </p>
                                <span>
                                  <p
                                    style={{
                                      fontSize: '16px',
                                      fontWeight: 500,
                                      fontFamily: 'UniNeue',
                                      color: '#111111',
                                      lineHeight: '21.6px',
                                      letterSpacing: '-0.01em',
                                      gap: '10px',
                                      display: 'flex',
                                      alignItems: 'center',
                                    }}
                                  >
                                    {productUserAvgData.percentage || '0%'}{' '}
                                    <img src={blueUpIcon} alt="upIcon"></img>
                                  </p>
                                  <p
                                    style={{
                                      fontWeight: 100,
                                      fontSize: 10,
                                      color: '#111111',
                                      fontFamily: 'Inter',
                                      lineHeight: '12.1px',
                                      letterSpacing: '-0.01em',
                                    }}
                                  >
                                    Vs last week
                                  </p>
                                </span>
                              </div>
                            </div>
                          ) : (
                            <Skeleton />
                          )}
                        </Row>
                      </Col>
                    </Row>
                  </ReportsWidget>
                </Col>
              </Row>
            </TabPane>
            {/* ***** User Tab ***** */}
            <TabPane tab={<IntlMessages id="dashboard.User" />} key="User">
              {renderPageViewBoxes()}
              <Row style={rowStyle} gutter={[16, 16]} justify="start">
                <Col xl={6} md={12} sm={24} xs={24} style={colStyle}>
                  <ReportsWidget
                    className="widgetSpacing"
                    style={{
                      height: '100%',
                      borderRadius: 10,
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                      fontWeight: 700,
                    }}
                  >
                    <div className="chartHeading">
                      <IntlMessages id="chart.childAges" />
                    </div>

                    <Row
                      style={{
                        height: '270px',
                        marginTop: 35,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      {childAgesChartData ? (
                        <ResponsiveContainer>
                          <ReactApexChart
                            options={childAgesChartData.options}
                            series={childAgesChartData.series}
                            type="bar"
                            height="337px"
                          />
                        </ResponsiveContainer>
                      ) : (
                        <Skeleton />
                      )}
                    </Row>
                  </ReportsWidget>
                </Col>
                <Col xl={6} md={12} sm={24} xs={24} style={colStyle}>
                  <ReportsWidget
                    className="widgetSpacing"
                    style={{
                      height: '100%',
                      borderRadius: 10,
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'space-between',
                        fontSize: '12px',
                        fontWeight: 500,
                        fontFamily: 'Inter',
                        lineHeight: '14.52px',
                        color: '#2E2E30',
                      }}
                    >
                      <IntlMessages id="chart.dailyAvg" />
                      <span
                        style={{
                          fontFamily: 'Inter',
                          lineHeight: '14.52px',
                          fontWeight: 500,
                          color: '#2E2E30',
                        }}
                      >
                        <span
                          style={{
                            color:
                              dailyAverageData?.growth_type === 'increase'
                                ? '#04B8FF'
                                : 'red',
                            fontFamily: 'Inter',
                          }}
                        >
                          {dailyAverageData?.growth_type === 'increase'
                            ? '+'
                            : '-'}
                          {`${
                            convertToHoursAndMinutes(
                              dailyAverageData?.average_time,
                            ).hours || 0
                          }h ${
                            convertToHoursAndMinutes(
                              dailyAverageData?.average_time,
                            ).minutes || 0
                          }m`}
                        </span>{' '}
                        this week
                      </span>
                    </div>
                    <div
                      style={{
                        fontSize: '24px',
                        fontWeight: '700',
                        fontFamily: 'Inter',
                        lineHeight: '29.05px',
                        color: '#2E2E30',
                      }}
                    >
                      {`${
                        convertToHoursAndMinutes(
                          dailyAverageData?.daily_average,
                        ).hours || 0
                      }h ${
                        convertToHoursAndMinutes(
                          dailyAverageData?.daily_average,
                        ).minutes || 0
                      }m`}
                    </div>
                    <Row
                      style={{
                        height: '270px',
                        marginTop: 15,
                      }}
                    >
                      <ResponsiveContainer width="100%" height="100%">
                        <ReactApexChart
                          options={dailyAvgChartData.options}
                          series={dailyAvgChartData.series}
                          type="bar"
                          height={350}
                        />
                      </ResponsiveContainer>
                    </Row>
                  </ReportsWidget>
                </Col>
                <Col xl={6} md={12} sm={24} xs={24} style={colStyle}>
                  <ReportsWidget
                    className="widgetSpacing"
                    style={{
                      height: '100%',
                      borderRadius: 10,
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                      fontWeight: 700,
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'center',
                        flexDirection: 'column',
                        fontSize: '20px',
                        fontWeight: 700,
                        alignItems: 'center',
                      }}
                    >
                      <div
                        style={{
                          fontFamily: 'Inter',
                          lineHeight: '24.2px',
                          color: '#2E2E30',
                        }}
                      >
                        {`${
                          convertToHoursAndMinutes(
                            analyticsAvgTime?.average_time,
                          ).hours || 0
                        }h ${
                          convertToHoursAndMinutes(
                            analyticsAvgTime?.average_time,
                          ).minutes || 0
                        }m`}{' '}
                        <span
                          style={{
                            fontSize: '15px',
                            fontWeight: 500,
                            color:
                              analyticsAvgTime?.growth_type === 'increase'
                                ? '#2DD683'
                                : 'red',
                            fontFamily: 'Inter',
                            lineHeight: '12.1px',
                          }}
                        >
                          {analyticsAvgTime?.growth_type === 'decrese' ? (
                            <ArrowUpOutlined color="green" />
                          ) : (
                            <ArrowDownOutlined color="red" />
                          )}
                        </span>
                      </div>
                      <p
                        style={{
                          fontSize: '12px',
                          fontWeight: '600',
                          fontFamily: 'Inter',
                          lineHeight: '14.52px',
                          color: '#2E2E30',
                        }}
                      >
                        <IntlMessages id="analytics.avgTime" />
                      </p>
                    </div>
                    <Row
                      style={{
                        height: '270px',
                        marginTop: 15,
                      }}
                    >
                      <ResponsiveContainer width="100%" height="100%">
                        <ReactApexChart
                          options={avgTimeChartData.options}
                          series={avgTimeChartData.series}
                          type="bar"
                          width="500"
                          // height={800}
                        />{' '}
                      </ResponsiveContainer>
                    </Row>
                  </ReportsWidget>
                </Col>
                <Col xl={6} md={24} sm={24} xs={24} style={colStyle}>
                  <ReportsWidget
                    className="widgetSpacing"
                    style={{
                      height: '100%',
                      borderRadius: 10,
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        marginBottom: '25px',
                        marginTop: '20px',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                      }}
                    >
                      <div className="weatherText">
                        <img src={morningIcon} alt="icon"></img>
                        <p
                          style={{
                            fontSize: '10px',
                            fontWeight: 500,
                            fontFamily: 'Inter',
                            color: '#94979B',
                            lineHeight: '12.1px',
                          }}
                        >
                          Morning
                        </p>
                      </div>
                      <p className="weatherPercentage">
                        {dailyAvgPercentage?.morning_percent || 0}%
                      </p>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        marginBottom: '25px',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                      }}
                    >
                      <div className="weatherText">
                        <img src={lunchIcon} alt="icon"></img>
                        <p
                          style={{
                            fontSize: '10px',
                            fontWeight: 500,
                            fontFamily: 'Inter',
                            color: '#94979B',
                            lineHeight: '12.1px',
                          }}
                        >
                          Lunch
                        </p>
                      </div>
                      <p className="weatherPercentage">
                        {dailyAvgPercentage?.lunch_percent || 0}%
                      </p>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        marginBottom: '25px',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                      }}
                    >
                      <div className="weatherText">
                        <img src={afternoonIcon} alt="icon"></img>
                        <p
                          style={{
                            fontSize: '10px',
                            fontWeight: 500,
                            fontFamily: 'Inter',
                            color: '#94979B',
                            lineHeight: '12.1px',
                          }}
                        >
                          Afternoon
                        </p>
                      </div>
                      <p className="weatherPercentage">
                        {dailyAvgPercentage?.afternoon_percent || 0}%
                      </p>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        marginBottom: '25px',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                      }}
                    >
                      <div className="weatherText">
                        <img src={nightIcon} alt="icon"></img>
                        <p
                          style={{
                            fontSize: '10px',
                            fontWeight: 500,
                            fontFamily: 'Inter',
                            color: '#94979B',
                            lineHeight: '12.1px',
                          }}
                        >
                          Evening
                        </p>
                      </div>
                      <p className="weatherPercentage">
                        {dailyAvgPercentage?.night_percent || 0}%
                      </p>
                    </div>
                  </ReportsWidget>
                </Col>
              </Row>
              <Row style={rowStyle} gutter={[16, 16]} justify="start">
                <Col xl={12} md={12} sm={24} xs={24} style={colStyle}>
                  <ReportsWidget
                    style={{
                      height: '100%',
                      borderRadius: 20,
                      boxShadow: '0px 5px 14px 0px #0000000D',
                      fontWeight: 700,
                    }}
                  >
                    <div
                      className="chartHeading"
                      style={{
                        color: '#2D3748',
                        fontWeight: 600,
                        lineHeight: '19.6px',
                      }}
                    >
                      <IntlMessages id="customer.userInsights" />
                      <Popover
                        content={customerDurationContent}
                        trigger="click"
                        open={actUsrFltVis}
                        onOpenChange={() => durationChange()}
                        placement="bottomRight"
                      >
                        <p
                          style={{
                            cursor: 'pointer',
                            fontSize: '14px',
                            color: '#4B5157',
                            fontWeight: 400,
                            fontFamily: 'Inter',
                            lineHeight: '20px',
                          }}
                        >
                          Year 2023-24
                          <DownOutlined style={{ marginLeft: 5 }} />
                        </p>
                      </Popover>
                    </div>
                    {ageInsightChart ? (
                      ageInsightChart.map((usrInst) => (
                        <div>
                          {usrInst.data.map((nui) => (
                            <Row style={{ alignItems: 'center' }}>
                              <Col sm={20}>
                                <div
                                  style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '15px',
                                    padding: '10px 0',
                                  }}
                                >
                                  {nui.isUp ? (
                                    <img
                                      src={greenCircleUpIcon}
                                      alt="icon"
                                    ></img>
                                  ) : (
                                    <img
                                      src={redCircleDownIcon}
                                      alt="icon"
                                    ></img>
                                  )}
                                  <span>
                                    <p
                                      style={{
                                        fontFamily: 'Inter',
                                        lineHeight: '19.6px',
                                        fontWeight: 700,
                                        fontSize: '14px',
                                        color: '#2D3748',
                                      }}
                                    >
                                      {nui.time}
                                    </p>
                                    <p
                                      style={{
                                        fontWeight: 700,
                                        lineHeight: '18px',
                                        fontSize: '12px',
                                        color: '#A0AEC0',
                                        fontFamily: 'Helvetica',
                                        marginTop: '3px',
                                      }}
                                    >
                                      {nui.type}
                                    </p>
                                  </span>
                                </div>
                              </Col>
                              {/* <Col sm={4}>
                              {nui.isUp ? (
                                <p
                                  style={{
                                    color: '#2DD683',
                                    fontFamily: 'Inter',
                                    fontWeight: 700,
                                    fontSize: '16px',
                                    lineHeight: '22.4px',
                                  }}
                                >
                                  +{nui.percent}%
                                </p>
                              ) : (
                                <p
                                  style={{
                                    color: '#E53E3E',
                                    fontFamily: 'Inter',
                                    fontWeight: 700,
                                    fontSize: '16px',
                                    lineHeight: '22.4px',
                                  }}
                                >
                                  -{nui.percent}%
                                </p>
                              )}
                            </Col> */}
                            </Row>
                          ))}
                        </div>
                      ))
                    ) : (
                      <Skeleton />
                    )}
                  </ReportsWidget>
                </Col>
                <Col xl={12} md={12} sm={24} xs={24} style={colStyle}>
                  <Row style={rowStyle} gutter={[16, 16]} justify="start">
                    <Col xs={24} sm={10} md={24} xl={12} style={colStyle}>
                      <ReportsWidget
                        style={{
                          height: '100%',
                          borderRadius: 10,

                          boxShadow: '0px 0px 4px 0px #0000000D',
                          fontWeight: 700,
                        }}
                      >
                        <div className="chartHeading">
                          <IntlMessages id="customer.childHeight" />
                        </div>
                        <Row
                          style={{
                            marginTop: 30,
                            height: '200px',
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}
                        >
                          {childHeightChartData ? (
                            <ReactApexChart
                              options={childHeightChartData.options}
                              series={childHeightChartData.series}
                              type="bar"
                              height="200px"
                            />
                          ) : (
                            <Skeleton />
                          )}
                        </Row>
                      </ReportsWidget>
                    </Col>
                    <Col xs={24} sm={24} md={24} xl={12} style={colStyle}>
                      <ReportsWidget
                        className="widgetSpacing"
                        style={{
                          height: '100%',
                          borderRadius: 12,
                          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                          fontWeight: 700,
                        }}
                      >
                        <div
                          className="chartHeading"
                          style={{ fontWeight: 600, color: '#15223C' }}
                        >
                          <IntlMessages id="chart.userAgeinsights" />
                        </div>
                        <Row
                          style={{
                            marginTop: 5,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <ReactApexChart
                            options={userAgeInsightChartData.options}
                            series={userAgeInsightChartData.series}
                            type="radialBar"
                            height="380px"
                          />
                        </Row>
                      </ReportsWidget>
                    </Col>
                  </Row>
                  <Row style={rowStyle} gutter={[16, 16]} justify="start">
                    <Col xs={24} sm={10} md={24} xl={12} style={colStyle}>
                      <ReportsWidget
                        style={{
                          height: '100%',
                          borderRadius: 10,
                          boxShadow: '0px 0px 4px 0px #0000000D',
                          fontWeight: 700,
                        }}
                      >
                        <div className="chartHeading">
                          <IntlMessages id="chart.childWeight" />
                        </div>
                        <Row
                          style={{
                            marginTop: 10,
                            height: '200px',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          {childWeightChartData ? (
                            <ReactApexChart
                              options={childWeightChartData.options}
                              series={childWeightChartData.series}
                              type="bar"
                              height="200px"
                            />
                          ) : (
                            <Skeleton />
                          )}
                        </Row>
                      </ReportsWidget>
                    </Col>
                    <Col xs={24} sm={24} md={24} xl={12} style={colStyle}>
                      <ReportsWidget
                        style={{
                          height: '100%',
                          display: 'flex',
                          alignItem: 'center',
                          borderRadius: 12,
                          boxShadow: '0px 4px 4px 0px #8299B90F',
                          fontWeight: 700,
                        }}
                      >
                        <div className="chartHeading">
                          <IntlMessages id="chart.appPerformance" />
                        </div>
                        <Row
                          style={{
                            marginTop: 30,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <ReactApexChart
                            options={appPeformanceChartData.options}
                            series={appPeformanceChartData.series}
                            type="radialBar"
                            height="300px"
                          />
                        </Row>
                      </ReportsWidget>
                    </Col>
                  </Row>
                </Col>
              </Row>
            </TabPane>
            {/* ***** Environment Tab */}
            <TabPane
              tab={<IntlMessages id="dashboard.environment" />}
              key="Environment"
            >
              <Row style={rowStyle} gutter={[16, 16]} justify="start">
                <Col lg={16} md={24} sm={12} xs={24} style={colStyle}>
                  <ReportsWidget
                    style={{
                      height: '100%',
                      borderRadius: 10,
                      boxShadow: '5px 5px 5px 5px #0000',
                      fontWeight: 700,
                    }}
                  >
                    <Row style={{ height: '400px' }}>
                      <ResponsiveContainer>
                        <ReactApexChart
                          options={weatherAltChartData.options}
                          series={weatherAltChartData.series}
                          type="line"
                        />
                      </ResponsiveContainer>
                    </Row>
                  </ReportsWidget>
                </Col>
                <Col lg={8} md={24} sm={12} xs={24} style={colStyle}>
                  <ReportsWidget
                    label={false}
                    style={{
                      height: '100%',
                      borderRadius: 16,
                      boxShadow: '0px 4px 10px 4px #3A2B7D0A',
                      fontWeight: 700,
                    }}
                  >
                    <div
                      className="chartHeading"
                      style={{
                        fontWeight: 600,
                        lineHeight: '20px',
                        color: '#1C1C1C',
                      }}
                    >
                      <IntlMessages id="chart.usersbyLocation" />
                      <Popover
                        content={customerDurationContent}
                        trigger="click"
                        open={actUsrFltVis}
                        onOpenChange={() => durationChange()}
                        placement="bottomRight"
                      >
                        <p
                          style={{
                            cursor: 'pointer',
                          }}
                        >
                          <DatePicker
                            picker="year"
                            placeholder="Year"
                            style={{ width: '75px' }}
                            defaultValue={moment('2024-06-06', 'YYYY')}
                            bordered={false}
                            size="small"
                            suffixIcon={
                              <DownOutlined style={{ marginLeft: 3 }} />
                            }
                          />
                        </p>
                      </Popover>
                    </div>
                    <Row
                      style={{
                        height: '370px',
                        marginTop: 10,
                      }}
                    >
                      <ResponsiveContainer>
                        <ReactApexChart
                          options={userLocationChartData.options}
                          series={userLocationChartData.series}
                          type="polarArea"
                        />
                      </ResponsiveContainer>
                    </Row>
                  </ReportsWidget>
                </Col>
              </Row>
              <Row style={rowStyle} gutter={[16, 16]} justify="start">
                <Col lg={12} md={24} sm={12} xs={24} style={colStyle}>
                  <ReportsWidget
                    label={false}
                    labelClsName="mb15"
                    style={{
                      height: '100%',
                      borderRadius: 8,
                      boxShadow: '5px 5px 5px 5px #0000',
                      fontWeight: 700,
                    }}
                  >
                    <div
                      className="chartHeading"
                      style={{
                        color: '#222529',
                        lineHeight: '22px',
                        fontWeight: 700,
                      }}
                    >
                      <IntlMessages id="chart.climateCondition" />
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginTop: '10px',
                      }}
                    >
                      <Popover
                        content={customerDurationContent}
                        trigger="click"
                        open={actUsrFltVis}
                        onOpenChange={() => durationChange()}
                        placement="bottomRight"
                      >
                        <p
                          style={{
                            cursor: 'pointer',
                            fontSize: '14px',
                            fontWeight: 500,
                            border: '1px solid #E1E7EC',
                            borderRadius: '4px',
                            padding: '5px 10px',
                            fontFamily: 'DM Sans',
                            lineHeight: '16px',
                            color: '#2A2E33',
                          }}
                        >
                          Air Quality
                          <DownOutlined style={{ marginLeft: 5 }} />
                        </p>
                      </Popover>
                      <Popover
                        content={customerDurationContent}
                        trigger="click"
                        open={actUsrFltVis}
                        onOpenChange={() => durationChange()}
                        placement="bottomRight"
                      >
                        <p
                          style={{
                            cursor: 'pointer',
                            fontSize: '14px',
                            fontWeight: 500,
                            border: '1px solid #E1E7EC',
                            borderRadius: '4px',
                            padding: '5px 10px',
                            fontFamily: 'DM Sans',
                            lineHeight: '16px',
                            color: '#2A2E33',
                          }}
                        >
                          Location
                          <DownOutlined style={{ marginLeft: 5 }} />
                        </p>
                      </Popover>
                      <Popover
                        content={customerDurationContent}
                        trigger="click"
                        open={actUsrFltVis}
                        onOpenChange={() => durationChange()}
                        placement="bottomRight"
                      >
                        <p
                          style={{
                            cursor: 'pointer',
                            fontSize: '14px',
                            fontWeight: 500,
                            border: '1px solid #E1E7EC',
                            borderRadius: '4px',
                            padding: '5px 10px',
                            fontFamily: 'DM Sans',
                            lineHeight: '16px',
                            color: '#2A2E33',
                          }}
                        >
                          Today
                          <DownOutlined style={{ marginLeft: 5 }} />
                        </p>
                      </Popover>
                    </div>
                    <Row
                      style={{
                        height: '200px',
                        marginTop: 35,
                      }}
                    >
                      <ResponsiveContainer>
                        {climateConditionChartData ? (
                          <ReactApexChart
                            options={climateConditionChartData.options}
                            series={climateConditionChartData.series}
                            type="donut"
                            width={350}
                          />
                        ) : (
                          <Skeleton />
                        )}
                      </ResponsiveContainer>
                    </Row>
                  </ReportsWidget>
                </Col>
                <Col lg={12} md={24} sm={12} xs={24} style={colStyle}>
                  <ReportsWidget
                    label={false}
                    labelClsName="mb15"
                    style={{
                      height: '100%',
                      borderRadius: 8,
                      boxShadow: '0px 5px 14px 0px #0000000D',
                      fontWeight: 700,
                    }}
                  >
                    <div className="chartHeading" style={{ color: '#2D3748' }}>
                      <IntlMessages id="chart.totalorders" />
                      <Popover
                        content={customerOrderFltContent}
                        trigger="click"
                        open={orderFlt.vis}
                        onOpenChange={() => orderFltChange()}
                        placement="bottomRight"
                      >
                        <p
                          style={{
                            cursor: 'pointer',
                            fontSize: '14px',
                            fontWeight: 400,
                            lineHeight: '20px',
                            fontFamily: 'Inter',
                            color: '#4B5157',
                          }}
                        >
                          {orderFlt.text}
                          <DownOutlined style={{ marginLeft: 5 }} />
                        </p>
                      </Popover>
                    </div>
                    <Row
                      style={{
                        height: '286px',
                        marginTop: 15,
                      }}
                    >
                      <ResponsiveContainer>
                        <ReactApexChart
                          options={totalOrderChartData.options}
                          series={totalOrderChartData.series}
                          type="bar"
                          height="500px"
                        />
                      </ResponsiveContainer>
                    </Row>
                  </ReportsWidget>
                </Col>
                {/* <Col lg={6} md={24} sm={12} xs={24} style={colStyle}>
                  <ReportsWidget
                    label={false}
                    style={{
                      height: '100%',
                      borderRadius: 16,
                      boxShadow: '0px 4px 10px 4px #3A2B7D0A',
                      fontWeight: 700,
                    }}
                  >
                    <div
                      className="chartHeading"
                      style={{
                        fontWeight: 600,
                        color: '#1C1C1C',
                        lineHeight: '20px',
                      }}
                    >
                      Length of time/use
                      <Popover
                        content={customerDurationContent}
                        trigger="click"
                        open={actUsrFltVis}
                        onOpenChange={() => durationChange()}
                        placement="bottomRight"
                      >
                        <p
                          style={{
                            cursor: 'pointer',
                            fontSize: '14px',
                            fontWeight: 400,
                            color: '#2A3548',
                            fontFamily: 'Inter',
                            lineHeight: '20px',
                          }}
                        >
                          <DatePicker
                            picker="year"
                            placeholder="Year"
                            style={{ width: '75px' }}
                            defaultValue={moment('2024-06-06', 'YYYY')}
                            bordered={false}
                            size="small"
                            suffixIcon={
                              <DownOutlined style={{ marginLeft: 3 }} />
                            }
                          />
                        </p>
                      </Popover>
                    </div>
                    <Row
                      style={{
                        height: '270px',
                        marginTop: 10,
                      }}
                    >
                      <ResponsiveContainer>
                        <ReactApexChart
                          options={lengthOfTimeChartData.options}
                          series={lengthOfTimeChartData.series}
                          type="pie"
                        />
                      </ResponsiveContainer>
                    </Row>
                  </ReportsWidget>
                </Col> */}
              </Row>
            </TabPane>
          </Tabs>
        </div>

        {/* Filter Drawer */}
        <Drawer
          title={<IntlMessages id="common.filter" />}
          placement="right"
          onClose={onClose}
          open={open}
        >
          <div>
            <Dropdown
              menu={{
                items: languageOptions.map((option) => ({
                  key: option.key,
                  label: option.value,
                })),
              }}
              trigger={['click']}
            >
              <Typography.Link>
                <Space style={{ width: '100%', color: '#000' }}>
                  <IntlMessages id="customer.language" />
                  <img src={downArrow} alt='"icon"' />
                </Space>
              </Typography.Link>
            </Dropdown>
          </div>
          <Divider />
          <div>
            <Dropdown
              menu={{
                items: locationOptions.map((option) => ({
                  key: option.key,
                  label: option.value,
                })),
              }}
              trigger={['click']}
            >
              <Typography.Link>
                <Space style={{ width: '100%', color: '#000' }}>
                  <IntlMessages id="analytics.location" />
                  <img src={downArrow} alt='"icon"' />
                </Space>
              </Typography.Link>
            </Dropdown>
          </div>
          <Divider />
          <div>
            <Dropdown
              menu={{
                items: ageOptions.map((option) => ({
                  key: option.key,
                  label: option.value,
                })),
              }}
              trigger={['click']}
            >
              <Typography.Link>
                <Space style={{ width: '100%', color: '#000' }}>
                  <IntlMessages id="chart.childAges" />
                  <img src={downArrow} alt='"icon"' />
                </Space>
              </Typography.Link>
            </Dropdown>
          </div>
          <Divider />
          <div>
            <Dropdown
              menu={{
                items: productsOptions.map((option) => ({
                  key: option.key,
                  label: option.value,
                })),
              }}
              trigger={['click']}
            >
              <Typography.Link>
                <Space style={{ width: '100%', color: '#000' }}>
                  <IntlMessages id="analytics.productsOwned" />
                  <img src={downArrow} alt='"icon"' />
                </Space>
              </Typography.Link>
            </Dropdown>
          </div>
          <div>
            <Button
              type="primary"
              style={{
                background: '#09295D',
                color: '#FFFFFF',
                borderRadius: '5px',
                top: '195px',
                marginLeft: '15%',
                width: '212px',
                height: '40px',
              }}
              size="large"
            >
              <IntlMessages id="common.apply" />
            </Button>
          </div>
        </Drawer>
      </div>
    </NewStyleWrapper>
  );
}

// import React from 'react';
// import Gauge<PERSON>hart from 'react-gauge-chart';

// export default function Gauss() {
//   return (
//     <div>
//       <GaugeChart
//         id="gauge-chart3"
//         nrOfLevels={30}
//         colors={['#1de9b6', '#fff']}
//         style={{ paddingTop: 30, width: 300 }}
//         arcWidth={0.25}
//         percent={0}
//         textColor="#fff"
//       />
//     </div>
//   );
// }

import React from 'react';
import GaugeChart from 'react-gauge-chart';

export default function Gausss() {
  const levelsFilled = 18; // Example value, replace with your logic to determine the filled levels

  const percentage = Math.floor((levelsFilled / 30) * 100); // Calculate the percentage based on the filled levels

  return (
    <div>
      <GaugeChart
        id="gauge-chart3"
        nrOfLevels={30}
        colors={['#29EFC4', '#ffff']}
        style={{ paddingTop: 30, width: 250 }}
        arcWidth={0.25}
        percent={percentage}
        hideText // Hide the text inside the gauge
        needleBaseColor="transparent"
        needleColor="transparent" // Set the needle color to transparent
      />
      <div
        style={{
          textAlign: 'center',
          marginTop: '-25%',
          fontSize: '30px',
          color: '#ffff',
        }}
      >
        {percentage}%
      </div>{' '}
      {/* Display the percentage */}
    </div>
  );
}

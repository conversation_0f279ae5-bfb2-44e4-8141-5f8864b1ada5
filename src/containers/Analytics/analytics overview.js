import React, { useState } from 'react';
import { Tabs, Card, Button, DatePicker } from 'antd';
// import dayjs from 'dayjs';
// import './App.css';
import Chartz from '@chill/containers/Analytics/PieChart';
import BarGraph from '@chill/containers/Analytics/Barchart';
import Gauss from '@chill/containers/Analytics/GaugeChart';
import Linear from '@chill/containers/Analytics/LineChart';
import Linear1 from '@chill/containers/Analytics/LineChart1';
import Linear2 from '@chill/containers/Analytics/LineChart2';
// import dropdownbtn from './components/Dropdownfilter';
// import MyComponent from '.components/calender'

// IMPORTING ICONS FROM REACT ICON
import {
  AiOutlineDownload,
  AiOutlineCalendar,
  AiOutlineHome,
  AiOutlineCheckCircle,
  AiOutlineDown,
} from 'react-icons/ai';

import { BiFilterAlt } from 'react-icons/bi';
import { VscLayout, VscCircleFilled } from 'react-icons/vsc';
import { BsGraphUpArrow, BsArrowUp } from 'react-icons/bs';
import { FiSettings } from 'react-icons/fi';
import gallery from '@chill/assets/images/gallery.svg';
// import dashboard from '@chill/assets/images/dashboard-interface.svg';

// CONSTANT FUNCTION PARAMETERS FOR ALL

const { TabPane } = Tabs;

const { RangePicker } = DatePicker;

// const onRangeChange = (dates, dateStrings) => {
//   if (dates) {
//     console.log('From: ', dates[0], ', to: ', dates[1]);
//     console.log('From: ', dateStrings[0], ', to: ', dateStrings[1]);
//   } else {
//     console.log('Clear');
//   }
// };
// const rangePresets = [
//   {
//     label: 'Last 7 Days',
//     value: [dayjs().add(-7, 'd'), dayjs()],
//   },
//   {
//     label: 'Last 14 Days',
//     value: [dayjs().add(-14, 'd'), dayjs()],
//   },
//   {
//     label: 'This Month',
//     value: [dayjs().add(-30, 'd'), dayjs()],
//   },
//   {
//     label: 'Last 90 Days',
//     value: [dayjs().add(-90, 'd'), dayjs()],
//   },
// ];

function App() {
  const [isRangePickerOpen, setIsRangePickerOpen] = useState(false);
  const handleButtonClick = () => {
    setIsRangePickerOpen(!isRangePickerOpen);
  };
  // const handleRangePickerChange = (dates, dateStrings) => {
  //   // Handle range picker change here
  // };

  // const rangePresets = [
  //   // Define your range presets here
  // ];

  return (
    <div
      style={{
        display: 'flex',
        // position: 'fixed',
        flexDirection: 'row',
        padding: '10px',
        marginLeft: '15px',
        marginRight: '15px',
      }}
    >
      {/* main div start */}
      <div style={{ flex: 1 }}>
        {/* tab start */}
        <Tabs defaultActiveKey="1">
          <TabPane tab="Overview" key="1">
            {/* ................... ADDING CONTENT FOR THE 1st TAB ................... */}
            {/* ................... ADDING CONTENT FOR THE 1st TAB ................... */}

            {/* ................... ADDING CARDS FOR THE 1st ROW ................... */}

            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                marginTop: '10px',
              }}
            >
              <Card
                style={{
                  display: 'flex',
                  height: '40%',
                  width: '23%',
                  background:
                    'transparent linear-gradient(112deg, #5087FF 0%, #29EFC4 100%) 0% 0% no-repeat padding-box',
                  fontSize: '18px',
                  borderRadius: '20px',
                  // justifyContent: 'spaceBetween',
                  letterSpacing: '0px',
                  opacity: 1,
                }}
              >
                <p
                  style={{
                    margin: '0px',
                    textAlign: 'left',
                    textDecoration: 'solid',
                    color: '#fff',
                  }}
                >
                  Home
                </p>
                <p>
                  <span
                    style={{
                      color: '#fff',
                      fontSize: '24px',
                    }}
                  >
                    0.00%
                  </span>
                </p>
                <div
                  style={{
                    marginTop: '-60px',
                    marginLeft: '130px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    height: '40px',
                    width: '40px',
                    fontSize: '40px',
                    color: '#fff',
                  }}
                >
                  <AiOutlineHome />
                </div>
              </Card>
              <Card
                style={{
                  display: 'flex',
                  height: '40%',
                  width: '23%',
                  background:
                    'transparent linear-gradient(112deg, #5087FF 0%, #29EFC4 100%) 0% 0% no-repeat padding-box',
                  fontSize: '18px',
                  borderRadius: '20px',
                  // justifyContent: 'spaceBetween',
                  letterSpacing: '0px',
                  opacity: 1,
                }}
              >
                <p
                  style={{
                    margin: '0px',
                    textAlign: 'left',
                    textDecoration: 'solid',
                    color: '#fff',
                  }}
                >
                  Dashboard
                </p>
                <p>
                  <span
                    style={{
                      color: '#fff',
                      fontSize: '24px',
                    }}
                  >
                    0.00%
                  </span>
                </p>
                <div
                  style={{
                    marginTop: '-60px',
                    marginLeft: '130px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    height: '40px',
                    width: '40px',
                    fontSize: '40px',
                    color: '#fff',
                  }}
                >
                  <VscLayout />
                </div>
              </Card>
              <Card
                style={{
                  display: 'flex',
                  height: '40%',
                  width: '23%',
                  background:
                    'transparent linear-gradient(112deg, #5087FF 0%, #29EFC4 100%) 0% 0% no-repeat padding-box',
                  fontSize: '18px',
                  borderRadius: '20px',
                  // justifyContent: 'spaceBetween',
                  letterSpacing: '0px',
                  opacity: 1,
                }}
              >
                <p
                  style={{
                    margin: '0px',
                    textAlign: 'left',
                    textDecoration: 'solid',
                    color: '#fff',
                  }}
                >
                  Timeline
                </p>
                <p>
                  <span
                    style={{
                      color: '#fff',
                      fontSize: '24px',
                    }}
                  >
                    0.00%
                  </span>
                </p>
                <div
                  style={{
                    marginTop: '-60px',
                    marginLeft: '130px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    height: '40px',
                    width: '40px',
                    fontSize: '40px',
                    color: '#fff',
                  }}
                >
                  <BsGraphUpArrow />
                </div>
              </Card>
              <Card
                style={{
                  display: 'flex',
                  height: '40%',
                  width: '23%',
                  background:
                    'transparent linear-gradient(112deg, #5087FF 0%, #29EFC4 100%) 0% 0% no-repeat padding-box',
                  fontSize: '18px',
                  borderRadius: '20px',
                  // justifyContent: 'spaceBetween',
                  letterSpacing: '0px',
                  opacity: 1,
                }}
              >
                <p
                  style={{
                    margin: '0px',
                    textAlign: 'left',
                    textDecoration: 'solid',
                    color: '#fff',
                  }}
                >
                  Settings
                </p>
                <p>
                  <span
                    style={{
                      color: '#fff',
                      fontSize: '24px',
                    }}
                  >
                    0.00%
                  </span>
                </p>
                <div
                  style={{
                    marginTop: '-60px',
                    marginLeft: '130px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    height: '40px',
                    width: '40px',
                    fontSize: '40px',
                    color: '#fff',
                  }}
                >
                  <FiSettings />
                </div>
              </Card>
            </div>

            {/* ................... ADDING CARDS FOR THE 2nd ROW ................... */}

            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                marginTop: '15px',
                gap: '10px',
              }}
            >
              <Card
                style={{
                  width: '50%',
                  height: '30%',
                  background: '#fff',
                  borderRadius: '10px',
                }}
              >
                <p
                  style={{
                    color: '#000',
                    fontSize: '18px',
                    marginTop: '-10px',
                    fontWeight: 'bold',
                  }}
                >
                  Background
                </p>
                <p>
                  <span
                    style={{
                      color: '#a9abbc',
                      fontSize: '24px',
                    }}
                  >
                    0.00%
                  </span>
                </p>
                <img
                  style={{
                    display: 'flex',
                    marginTop: '-50px',
                    marginLeft: '380px',
                  }}
                  src={gallery}
                  alt=""
                />
              </Card>
              <Card
                style={{
                  width: '50%',
                  height: '30%',
                  background: '#fff',
                  borderRadius: '10px',
                }}
              >
                <p
                  style={{
                    color: '#000',
                    fontSize: '18px',
                    marginTop: '-10px',
                    fontWeight: 'bold',
                  }}
                >
                  Active
                </p>
                <p>
                  <span
                    style={{
                      color: '#a9abbc',
                      fontSize: '24px',
                    }}
                  >
                    0.00%
                  </span>
                </p>
                <div
                  style={{
                    display: 'flex',
                    marginLeft: '380px',
                    height: '40px',
                    marginTop: '-50px',
                    width: '40px',
                    color: '#2a77f4',
                    fontSize: '40px',
                  }}
                >
                  <AiOutlineCheckCircle />
                </div>
              </Card>
            </div>

            {/* ................... ADDING CARDS FOR THE 3rd ROW ................... */}

            <div
              style={{
                display: 'flex',
                justifySelf: 'center',
                gap: '20px',
              }}
            >
              <Card
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginTop: '15px',
                  height: '60vh',
                  width: '50%',
                  borderRadius: '20px',
                }}
              >
                <h4 style={{ marginTop: '-10px', textAlign: 'left' }}>
                  ACTIVE USERS
                </h4>
                <div
                  style={{
                    fontSize: '12px',
                    marginTop: '10px',
                  }}
                >
                  <p>Monthly</p>
                  <h4
                    style={{
                      marginTop: '-10px',
                      fontSize: '18px',
                    }}
                  >
                    9.2K{' '}
                    <span
                      style={{
                        marginLeft: '10px',
                        color: '#4cb083',
                        fontSize: '12px',
                      }}
                    >
                      <BsArrowUp />
                      4.63%
                    </span>
                  </h4>
                </div>
                <div
                  style={{
                    fontSize: '12px',
                    marginLeft: '150px',
                  }}
                >
                  <p
                    style={{
                      marginTop: '-35px',
                    }}
                  >
                    Weekly
                  </p>
                  <h4
                    style={{
                      marginTop: '-10px',
                      fontSize: '18px',
                    }}
                  >
                    2.6K{' '}
                    <span
                      style={{
                        marginLeft: '10px',
                        color: '#ea0606',
                        fontSize: '12px',
                      }}
                    >
                      <BsArrowUp />
                      1.92%
                    </span>
                  </h4>
                </div>
                <div
                  style={{
                    fontSize: '12px',
                    marginLeft: '290px',
                    marginTop: '-40px',
                  }}
                >
                  <p>Daily(Avg)</p>
                  <h4
                    style={{
                      marginTop: '-10px',
                      fontSize: '18px',
                    }}
                  >
                    0.9K{' '}
                    <span
                      style={{
                        fontSize: '12px',
                        color: '#4cb083',
                      }}
                    >
                      <BsArrowUp />
                      3.45%
                    </span>
                  </h4>
                </div>
                <div
                  style={{
                    // marginTop: '-10px',
                    // top: '550px',
                    // left: '373px',
                    // height: '140px',
                    // width: '434px',
                    // padding: '10px',
                    marginLeft: '0px',
                    marginTop: '30px',
                    height: '140px',
                    width: '50%',
                  }}
                >
                  <BarGraph />
                </div>
              </Card>

              <Card
                style={{
                  display: 'flex',
                  justifyContent: 'spaceBetween',
                  marginTop: '15px',
                  height: '60vh',
                  width: '50%',
                  borderRadius: '20px',
                }}
              >
                <h4 style={{ marginTop: '-10px', textAlign: 'left' }}>
                  PERFORMANCE ANALYSIS
                </h4>
                <div>
                  <p
                    style={{
                      marginTop: '5px',
                      textAlign: 'left',
                      fontSize: '12px',
                    }}
                  >
                    New Daily Users
                  </p>
                  <div
                    style={{
                      marginTop: '10px',
                    }}
                  >
                    <Linear />
                  </div>
                </div>

                <div>
                  <p1
                    style={{
                      display: 'flex',
                      marginTop: '-30px',
                      textAlign: 'left',
                      fontSize: '12px',
                    }}
                  >
                    Post Shares
                  </p1>
                  <div style={{ display: 'flex', marginTop: '15px' }}>
                    <Linear1 />
                  </div>
                </div>

                <div>
                  <p2
                    style={{
                      display: 'flex',
                      marginTop: '-40px',
                      textAlign: 'left',
                      fontSize: '12px',
                    }}
                  >
                    Products per Users(Avg)
                  </p2>
                  <div style={{ display: 'flex', marginTop: '20px' }}>
                    <Linear2 />
                  </div>
                </div>
              </Card>
            </div>

            {/* ................... ADDING CARDS FOR THE 4th ROW ................... */}

            <div
              style={{
                display: 'flex',
                justifySelf: 'center',
                gap: '20px',
              }}
            >
              <Card
                style={{
                  display: 'flex',
                  justifyContent: 'spaceBetween',
                  marginTop: '15px',
                  height: '60vh',
                  width: '33.3%',
                  borderRadius: '20px',
                }}
              >
                <div
                  style={{
                    fontSize: '14px',
                    textAlign: 'left',
                    marginTop: '-10px',
                  }}
                >
                  <h4>OPERATING SYSTEMS</h4>
                </div>
                <div
                  style={{
                    height: '189.57px',
                    width: '186.16px',
                    marginLeft: '15px',
                    marginTop: '25px',
                  }}
                >
                  <Chartz />
                </div>
                <div
                  style={{
                    display: 'flex',
                    height: '8px',
                    width: '8px',
                    gap: '20px',
                    marginTop: '30px',
                    marginLeft: '70px',
                  }}
                >
                  <p1
                    style={{
                      display: 'flex',
                      color: '#f08280',
                    }}
                  >
                    <VscCircleFilled />
                    <span
                      style={{
                        color: '#000',
                        height: '14px',
                        width: '18px',
                        fontSize: '12px',
                        fontWeight: 'bold',
                      }}
                    >
                      iOS
                    </span>
                  </p1>
                  <p2
                    style={{
                      display: 'flex',
                      color: '#1172ec',
                    }}
                  >
                    <VscCircleFilled />
                    <span
                      style={{
                        color: '#000',
                        height: '14px',
                        width: '18px',
                        fontSize: '12px',
                        fontWeight: 'bold',
                      }}
                    >
                      Android
                    </span>
                  </p2>
                </div>
              </Card>
              <Card
                style={{
                  display: 'flex',
                  justifyContent: 'spaceBetween',
                  marginTop: '15px',
                  height: '60vh',
                  width: '33.3%',
                  borderRadius: '20px',
                }}
              >
                <h4 style={{ marginTop: '-10px', marginLeft: '5px' }}>
                  TOP LINK CLICKS
                </h4>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                  }}
                >
                  <div>
                    <h6
                      style={{
                        fontSize: '10px',
                        marginTop: '10px',
                        marginLeft: '5px',
                        fontWeight: 'bold',
                      }}
                    >
                      Selling Products
                    </h6>
                    <p
                      style={{
                        marginTop: '0px',
                        marginLeft: '5px',
                        fontSize: '9px',
                      }}
                    >
                      analytic-index.html
                    </p>
                  </div>
                  <div>
                    <p
                      style={{
                        marginLeft: '90px',
                        marginTop: '10px',
                        width: '58px',
                        height: '33px',
                        background: '#B1D8F3',
                        textAlign: 'center',
                        borderRadius: '10px',
                        marginRight: '-110px',
                        color: '#1172EC',
                      }}
                    >
                      <b>4.32k</b>
                    </p>
                  </div>
                </div>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                  }}
                >
                  <div>
                    <h6
                      style={{
                        fontSize: '10px',
                        marginTop: '10px',
                        marginLeft: '5px',
                        fontWeight: 'bold',
                      }}
                    >
                      Landing
                    </h6>
                    <p
                      style={{
                        marginTop: '0px',
                        marginLeft: '5px',
                        fontSize: '9px',
                      }}
                    >
                      analytic-index.html
                    </p>
                  </div>
                  <div>
                    <p
                      style={{
                        marginLeft: '90px',
                        marginTop: '10px',
                        width: '58px',
                        height: '33px',
                        background: '#B1D8F3',
                        textAlign: 'center',
                        borderRadius: '10px',
                        marginRight: '-110px',
                        color: '#1172EC',
                      }}
                    >
                      <b>2.71k</b>
                    </p>
                  </div>
                </div>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                  }}
                >
                  <div>
                    <h6
                      style={{
                        fontSize: '10px',
                        marginTop: '10px',
                        marginLeft: '5px',
                      }}
                    >
                      Contact Us
                    </h6>
                    <p
                      style={{
                        marginTop: '0px',
                        marginLeft: '5px',
                        fontSize: '9px',
                      }}
                    >
                      analytic-index.html
                    </p>
                  </div>
                  <div>
                    <p
                      style={{
                        marginLeft: '90px',
                        marginTop: '10px',
                        width: '58px',
                        height: '33px',
                        background: '#B1D8F3',
                        textAlign: 'center',
                        borderRadius: '10px',
                        marginRight: '-110px',
                        color: '#1172EC',
                      }}
                    >
                      <b>1.89k</b>
                    </p>
                  </div>
                </div>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                  }}
                >
                  <div>
                    <h6
                      style={{
                        fontSize: '10px',
                        marginTop: '10px',
                        marginLeft: '5px',
                        fontWeight: 'bold',
                      }}
                    >
                      About Us
                    </h6>
                    <p
                      style={{
                        marginTop: '0px',
                        marginLeft: '5px',
                        fontSize: '9px',
                      }}
                    >
                      analytic-index.html
                    </p>
                  </div>
                  <div>
                    <p
                      style={{
                        marginLeft: '90px',
                        marginTop: '10px',
                        width: '58px',
                        height: '33px',
                        background: '#B1D8F3',
                        textAlign: 'center',
                        borderRadius: '10px',
                        marginRight: '-110px',
                        color: '#1172EC',
                      }}
                    >
                      <b>1.23k</b>
                    </p>
                  </div>
                </div>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                  }}
                >
                  <div>
                    <h6
                      style={{
                        fontSize: '10px',
                        marginTop: '10px',
                        marginLeft: '5px',
                        fontWeight: 'bold',
                      }}
                    >
                      Information Center
                    </h6>
                    <p
                      style={{
                        marginTop: '0px',
                        marginLeft: '5px',
                        fontSize: '9px',
                      }}
                    >
                      analytic-index.html
                    </p>
                  </div>
                  <div>
                    <p
                      style={{
                        marginLeft: '90px',
                        marginTop: '10px',
                        width: '58px',
                        height: '33px',
                        background: '#B1D8F3',
                        textAlign: 'center',
                        borderRadius: '10px',
                        marginRight: '-110px',
                        color: '#1172EC',
                      }}
                    >
                      <b>2.14k</b>
                    </p>
                  </div>
                </div>
              </Card>
              <Card
                style={{
                  display: 'flex',
                  justifyContent: 'spaceBetween',
                  marginTop: '15px',
                  height: '60vh',
                  width: '33.3%',
                  background:
                    'transparent linear-gradient(136deg, #5087FF 0%, #29EFC4 100%) 0% 0% no-repeat padding-box',
                  borderRadius: '20px',
                }}
              >
                <div
                  style={{
                    color: '#fff',
                    textAlign: 'left',
                    marginTop: '-10px',
                  }}
                >
                  <h4 style={{ fontSize: '14px', color: '#fff' }}>
                    {' '}
                    GOAL COMPLETIONS{' '}
                  </h4>
                  <p
                    style={{
                      display: 'flex',
                      padding: '5px',
                      fontSize: '12px',
                      marginTop: '10px',
                      justifyContent: 'space-between',
                    }}
                  >
                    Lorem Ipsum is simply dummy text of the printing <br />
                    and typesetting industry.
                  </p>
                </div>
                <div
                // style={{
                //   // top: '863px',
                //   // left: '1108px',
                //   width: '200px',
                //   height: '122px',
                //   justifyContent: 'center',
                //   alignItem: 'center',
                //   marginLeft: '10px',
                //   // marginTop: '25px',
                // }}
                >
                  <Gauss />
                  {/* <p
                      style={{
                        fontSize: '20px',
                        color: '#fff',
                        marginTop: '-110px',
                        marginLeft: '95px',
                      }}
                    >
                      75%
                    </p> */}
                </div>
                <div
                  style={{
                    color: '#fff',
                    textAlign: 'left',
                    marginBottom: '0px',
                  }}
                >
                  <h4 style={{ fontSize: '12px', color: '#fff' }}>
                    Relative Change vs last month
                  </h4>
                  <div
                    style={{
                      marginTop: '0px',
                    }}
                  >
                    <p
                      style={{
                        color: '#fff',
                        fontSize: '18px',
                      }}
                    >
                      1.45k
                      <span
                        style={{
                          color: '#076038',
                          fontSize: '12px',
                          marginLeft: '18px',
                          fontWeight: 'bold',
                        }}
                      >
                        {' '}
                        <BsArrowUp />
                        34.63%
                      </span>
                    </p>
                  </div>
                </div>
              </Card>
            </div>
            {/* -----------------------Content of Tab1 completed--------------------------- */}

            {/* -----------------------Content of Tab1 completed--------------------------- */}
          </TabPane>
          <TabPane tab="User" key="2">
            Content of Tab Pane 2
          </TabPane>
          <TabPane tab="Environment" key="3">
            Content of Tab Pane 3
          </TabPane>
          <TabPane tab="Engagement" key="4">
            Content of Tab Pane 4
          </TabPane>
          <TabPane tab="Ecommerce" key="5">
            {/* Content of Ecommerce Tab */}
          </TabPane>
        </Tabs>
      </div>
      {/* tab div  end */}
      <div
        style={{
          // marginTop: '10px',
          // display: 'flex',
          // justifyContent: 'flex-end',
          position: 'absolute',
          // marginLeft: '50%',
          display: 'flex',
          right: 30,
          flexDirection: 'row',
        }}
      >
        {/* buttons div start */}
        <Button
          type="dashed"
          icon={<BiFilterAlt />}
          style={{
            marginRight: '10px',
            width: '128px',
            border: '0.5px solid #EDEDED',
            borderRadius: '10px',
            background: '#FFFFFF 0% 0% no-repeat padding-box',
          }}
        >
          Filter <AiOutlineDown />
        </Button>
        <Button
          type="dashed"
          icon={<AiOutlineCalendar />}
          style={{
            marginRight: '10px',
            width: '165px',
            border: '0.5px solid #EDEDED',
            borderRadius: '10px',
            background: '#FFFFFF 0% 0% no-repeat padding-box',
          }}
          onClick={handleButtonClick}
        >
          {' '}
          Date Range <AiOutlineDown />
        </Button>
        {isRangePickerOpen && (
          <RangePicker
            // onChange={handleRangePickerChange}
            onOpenChange={() => setIsRangePickerOpen(true)}
          />
        )}
        <Button
          style={{
            background:
              'transparent linear-gradient(105deg, #2F95F7 0%, #46B6F8 100%) 0% 0% no-repeat padding-box',
            width: '130px',
            color: '#fff',
          }}
          type="dashed"
          icon={<AiOutlineDownload />}
          // onClick={handleButtonClick}
        >
          Report
        </Button>
        {/* buttons div end */}
      </div>
      {/* main div end */}
    </div>
  );
}

export default App;

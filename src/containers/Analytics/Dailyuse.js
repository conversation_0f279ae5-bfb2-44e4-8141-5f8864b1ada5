/* eslint-disable react/no-array-index-key */

// import React, { useEffect, useState } from "react";
// import {
//   Bar<PERSON>hart,
//   Bar,
//   XAxis,
//   YAxis,
//   CartesianGrid,
//   // Tooltip,
//   // Legend,
//   Label,
// } from "recharts";
// import axios from "axios";

// const DailyUse = () => {
//   const [data, setData] = useState([]);

//   useEffect(() => {
//     // Fetch data from the API
//     const fetchData = async () => {
//       try {
//         const response = await axios.get(
//           "https://hub.dummyapis.com/vj/NpB8WXk"
//         );
//         setData(Object.values(response.data[0])); // Extract the data array from the response
//       } catch (error) {
//         console.log(error);
//       }
//     };

//     fetchData();
//   }, []);

//   const getBarFill = (value) => {
//     // Define the transparent linear gradient as the fill color for each bar
//     return `linear-gradient(171deg, rgba(80, 135, 255, 0) 0%, rgba(41, 239, 196, 0) 100%), ${value}`;
//   };

//   return (
//     <BarChart width={360} height={160} data={data} barGap={17} categoryGap={20}>
//       <CartesianGrid strokeDasharray="3 3" vertical={false} horizontal={false} />
//       {/* Set vertical and horizontal to true to show both vertical and horizontal grid lines */}
//       <XAxis
//         dataKey="name"
//         tickLine={false}
//         tick={{ fillOpacity: 0 }} // Set fillOpacity to 0 to make X-axis values transparent
//       />
//       <YAxis axisLine={true} tickLine={false} tick={{ fillOpacity: 0 }} />
//       {/* <Tooltip />
//       <Legend /> */}
//       <Bar dataKey="value" background={getBarFill} barSize={15}>
//         <Label
//           content={({ value }) => `${value}`}
//           position="top"
//           fill="#000000"
//           fontSize={14}
//           fontWeight="bold"
//         />
//       </Bar>
//     </BarChart>
//   );
// };

// export default DailyUse;
import React from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip } from 'recharts';

function DailyUse() {
  const data = [
    { name: '', value: 27 },
    { name: '', value: 45 },
    { name: '', value: 18 },
    { name: '', value: 36 },
    { name: '', value: 10 },
    { name: '', value: 16 },
    { name: '', value: 6 },
    { name: '', value: 26 },
    { name: '', value: 30 },
    { name: '', value: 32 },
    { name: '', value: 29 },
  ];

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <BarChart width={250} height={150} data={data} barGap={27}>
        <CartesianGrid
          strokeDasharray="3 3"
          strokeOpacity={0.2}
          vertical={false}
          horizontal={false}
        />
        <XAxis dataKey="name" axisLine={{ stroke: '#ffff' }} tickLine={false} />
        <YAxis axisLine={{ stroke: '#ffff' }} tick={false} tickLine={false} />
        <Tooltip />
        {/* <Legend /> */}
        <Bar dataKey="value" fill="#ffff" barSize={10}>
          {data.map((entry, index) => (
            <text
              key={index}
              x={entry.value > 0 ? entry.value + 5 : -20}
              y={-10}
              dy={-2}
              fill="#000"
              textAnchor={entry.value > 0 ? 'start' : 'end'}
            >
              {entry.value}
            </text>
          ))}
        </Bar>
      </BarChart>
    </div>
  );
}

export default DailyUse;

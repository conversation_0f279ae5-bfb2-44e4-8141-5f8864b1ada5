/* eslint-disable react/jsx-boolean-value */

import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Sc<PERSON><PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'recharts';

function WeatherAlerts() {
  const series = [
    {
      name: 'High',
      data: [
        { category: 'California', value: 20 },
        { category: 'Texas', value: 19 },
        { category: 'Florida', value: 22 },
        { category: 'New Jersey', value: 24 },
        { category: 'Georgia', value: 23 },
        { category: 'Washington', value: 20 },
      ],
      lineColor: 'white',
      dotColor: '#2A77F4',
      dotwidth: 8,
    },
    {
      name: 'Low',
      data: [
        { category: 'California', value: 6 },
        { category: 'Texas', value: 8 },
        { category: 'Florida', value: 9 },
        { category: 'New Jersey', value: 11 },
        { category: 'Georgia', value: 10 },
        { category: 'Washington', value: 6 },
      ],
      lineColor: 'white',
      dotColor: '#29EFC4',
      dotwidth: 8,
    },
  ];

  return (
    <LineChart
      width={900}
      height={400}
      margin={{ top: 50, right: 30, bottom: 70, left: 20 }}
    >
      <CartesianGrid
        strokeDasharray="3 3"
        vertical={true}
        stroke="white"
        strokeWidth={2}
        horizontal={false}
      />
      <XAxis
        dataKey="category"
        type="category"
        allowDuplicatedCategory={false}
      />
      <YAxis hide={true} />
      <Tooltip />
      <Legend />
      {series.map((s) => (
        <React.Fragment key={s.name}>
          <Line
            dataKey="value"
            data={s.data}
            name={s.name}
            key={s.name}
            stroke={s.lineColor}
            strokeWidth={2}
            dot={{ fill: s.dotColor, r: s.dotwidth }}
            label={{
              position: 'top',
              fill: s.lineColor,
              fontSize: 12,
              fontWeight: 'bold',
              formatter: (value) => `${value}°`,
              offset: 15,
            }}
          />
          {s.data.map((entry) => (
            <Scatter
              key={`${s.name}-${entry.category}`}
              data={[entry]}
              fill={s.dotColor}
              shape="dot"
              line
              lineType="vertical"
              lineStrokeWidth={2}
              lineStroke={s.lineColor}
              lineDot
              lineDotFill={s.dotColor}
              lineDotRadius={1}
            />
          ))}
        </React.Fragment>
      ))}
    </LineChart>
  );
}

export default WeatherAlerts;
// import React, { useEffect, useState } from "react";
// import {
//   LineChart,
//   Line,
//   XAxis,
//   YAxis,
//   CartesianGrid,
//   Tooltip,
//   Legend,
// } from "recharts";

// import axios from "axios";
// function WeatherAlerts() {
//   const [series, setSeries] = useState([]);

//   useEffect(() => {
//     // Fetch data from the API
//     const fetchData = async () => {
//       try {
//         const response = await axios.get(
//           "https://hub.dummyapis.com/vj/stzUFwH"
//         );
//         setSeries(Object.values(response.data[0])); // Extract the data array from the response
//       } catch (error) {
//         console.log(error);
//       }
//     };

//     fetchData();
//   }, []);
// function WeatherAlerts() {
//   const [series, setSeries] = useState([]);

//   useEffect(() => {
//     // Fetch data from the API
//     fetch('https://hub.dummyapis.com/vj/stzUFwH')
//       .then(response => response.json())
//       .then(data => {
//         const apiData = data; // Adjust this line based on your API response structure

//         const newSeries = [
//           {
//             name: apiData[0]['[0]'].name,
//             data: Object.values(apiData[0]).map(obj => ({ category: obj.category, value: obj.Value }))
//           },
//           {
//             name: apiData[1]['[0]'].name,
//             data: Object.values(apiData[1]).map(obj => ({ category: obj.category, value: obj.Value }))
//           }
//         ];

//         setSeries(newSeries);
//       })
//       .catch(error => {
//         console.error('Error fetching data from the API:', error);
//       });
//   }, []);

//   return (
//     <LineChart width={900} height={300}>
//       <CartesianGrid strokeDasharray="3 3" />
//       <XAxis
//         dataKey="category"
//         type="category"
//         allowDuplicatedCategory={false}
//       />
//       <YAxis dataKey="value" />
//       <Tooltip />
//       <Legend />
//       {series.map((s, index) => (
//         <Line key={index} dataKey="value" data={s.data} name={s.name} />
//       ))}
//     </LineChart>
//   );
// }

// export default WeatherAlerts;

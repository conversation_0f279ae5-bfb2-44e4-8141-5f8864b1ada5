/* eslint-disable react/jsx-boolean-value */
/* eslint-disable react/jsx-curly-brace-presence */

import React from 'react';
import { Pie<PERSON><PERSON>, Pie } from 'recharts';

const data02 = [
  { name: 'Cold Climates', value: 25, fill: '#5A96F8' },
  { name: 'Moderate Climate', value: 45, fill: '#2A77F4' },
  { name: 'Warm Climate', value: 20, fill: '#30CDAB' },
  { name: 'Extreme Climate', value: 15, fill: '#29EFC4' },
];

function Chartss() {
  return (
    <PieChart width={250} height={270}>
      <Pie
        data={data02}
        dataKey="value"
        cx={90}
        cy={90}
        innerRadius={35}
        outerRadius={90}
        fill={'#82ca9d'}
        paddingAngle={1}
        stroke="hide"
        // dataKey="value"
        labelLine={false}
        label={({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
          const RADIAN = Math.PI / 180;
          const radius = innerRadius + (outerRadius - innerRadius) * 0.3;
          const x = cx + radius * Math.cos(-midAngle * RADIAN);
          const y = cy + radius * Math.sin(-midAngle * RADIAN);

          return (
            <text
              x={x}
              y={y}
              fill="#fff"
              textAnchor={x > cx ? 'start' : 'end'}
              dominantBaseline="central"
            >
              {`${(percent * 100).toFixed(0)}%`}
            </text>
          );
        }}
      >
        {/* {data.map((entry, index) => (
          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
        ))} */}
        {/* <Label value="Total" position="center" /> */}
      </Pie>
    </PieChart>
  );
}
export default Chartss;

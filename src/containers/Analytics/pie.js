/* eslint-disable no-irregular-whitespace */

// import { Chart as ChartJS, ArcElement, <PERSON><PERSON><PERSON>, Legend} from "chart.js";
// import { Doughnut } from "react-chartjs-2";
// //import { GoPrimitiveDot } from "react-icons/go";

// ChartJS.register(
//   ArcElement,
//   Tooltip,
//   Legend

// );

// function Charts() {
//   const data ={
//     datasets: [{
//       label:"poll",
//       data: [ 20, 15, 20, 45],
//       backgroundColor: ['#2A77F4', '#30CDAB', '#29EFC4', '#5A96F8' ],
//       bordercolor: ['#2A77F4', '#30CDAB', '#29EFC4', '#5A96F8']
//     }]
//   }

//   const options = {

//   }

//     return(
//       <div >
//         <Doughnut
//           data={data} options={options}>
//         </Doughnut>
//       </div>
//     )
// }

// export default Charts;

import React from 'react';
import { <PERSON><PERSON><PERSON>, Pie } from 'recharts';

const data02 = [
  { name: 'Cold Climates', value: 25, fill: '#5A96F8' },
  { name: 'Moderate Climate', value: 45, fill: '#2A77F4' },
  { name: 'Warm Climate', value: 20, fill: '#30CDAB' },
  { name: 'Extreme Climate', value: 15, fill: '#29EFC4' },
];

function Chartsss() {
  return (
    <PieChart width={300} height={300}>
      <Pie
        data={data02}
        dataKey="value"
        cx={85}
        cy={100}
        innerRadius={40}
        outerRadius={90}
        labelLine={false}
        label={({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
          const RADIAN = Math.PI / 180;
          const radius = innerRadius + (outerRadius - innerRadius) * 0.3;
          const x = cx + radius * Math.cos(-midAngle * RADIAN);
          const y = cy + radius * Math.sin(-midAngle * RADIAN);

          return (
            <text
              x={x}
              y={y}
              fill="#fff"
              textAnchor={x > cx ? 'start' : 'end'}
              dominantBaseline="central"
            >
              {`${(percent * 100).toFixed(0)}%`}
            </text>
          );
        }}
      />
    </PieChart>
  );
}
export default Chartsss;

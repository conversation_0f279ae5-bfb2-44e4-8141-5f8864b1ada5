import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  LabelList,
} from 'recharts';

function HarChart() {
  const data = [
    { name: '0-50 AQM -50%', value: 50 },
    { name: '51-100 -12%', value: 12 },
    { name: '101-150 -20%', value: 20 },
    { name: '150+ -27%', value: 27 },
  ];

  const formatValue = (value) => `${value}%`;

  return (
    <div style={{ width: '90%', height: '90%' }}>
      <BarChart width={270} height={300} data={data} layout="vertical">
        <CartesianGrid strokeDasharray="3 3" strokeOpacity={0.1} />
        <YAxis
          dataKey="hide"
          type="category"
          width={100}
          tick={false}
          axisLine={{ stroke: '#E6E6E6' }}
        />
        <XAxis
          type="number"
          domain={[0, 100]}
          axisLine={{ strokeOpacity: 0 }}
          tick={false}
        />
        <Bar dataKey="value" fill="url(#colorGradient)" barSize={25}>
          <LabelList
            dataKey="value"
            position="right"
            fill="#000"
            textAnchor="middle"
            formatter={formatValue}
          />
        </Bar>
        <defs>
          <linearGradient id="colorGradient" gradientTransform="rotate(90)">
            <stop offset="0%" stopColor="#5087FF" />
            <stop offset="100%" stopColor="#34ebd8" />
          </linearGradient>
        </defs>
      </BarChart>
    </div>
  );
}

export default HarChart;

// import React, { useEffect, useState } from "react";
// import { Chart } from "react-google-charts";

// const options = {
//   chart: {
//     title: "AIR QUALITY WHERE USES ARE",
//     chartArea: { width: "50%" },
//   },
//   hAxis: {
//     title: "Total Population",
//     minValue: 0,
//   },
//   vAxis: {
//     title: "City",
//   },
//   bars: "horizontal",
//   axes: {
//     y: {
//       0: { side: "right" },
//     },
//   },
// };

// function HarChart() {
//   const [chartData, setChartData] = useState([]);

//   useEffect(() => {
//     // Fetch data from API
//     fetch("https://hub.dummyapis.com/vj/wzGUSCB")
//       .then((response) => response.json())
//       .then((apiData) => {
//         const formattedData = [
//           ["City", "2010 Population"],
//           ...apiData.map((item) => [item.city, item.population]),
//         ];
//         setChartData(formattedData);
//       })
//       .catch((error) => {
//         console.log("Error fetching data from API:", error);
//       });
//   }, []);

//   return (
//     <Chart
//       chartType="Bar"
//       width="100%"
//       height="400px"
//       data={chartData}
//       options={options}
//     />
//   );
// }

// export default HarChart;

/* eslint-disable react/jsx-boolean-value */
// import React, { useEffect, useState } from "react";
// import {
//   BarChart,
//   Bar,
//   XAxis,
//   YAxis,
//   CartesianGrid,
//   // Tooltip,
//   // Legend,
//   Label,
// } from "recharts";
// import axios from "axios";

// const BarGraphzzz = () => {
//   const [data, setData] = useState([]);

//   useEffect(() => {
//     // Fetch data from the API
//     const fetchData = async () => {
//       try {
//         const response = await axios.get(
//           "https://hub.dummyapis.com/vj/qqw0Btp"
//         );
//         setData(Object.values(response.data[0])); // Extract the data array from the response
//       } catch (error) {
//         console.log(error);
//       }
//     };

//     fetchData();
//   }, []);

//   const getBarFill = (value) => {
//     // Define the transparent linear gradient as the fill color for each bar
//     return `linear-gradient(171deg, rgba(80, 135, 255, 0) 0%, rgba(41, 239, 196, 0) 100%), ${value}`;
//   };

//   return (
//     <BarChart width={400} height={300} data={data} barGap={52} categoryGap={20}>
//       <CartesianGrid strokeDasharray="3 3" vertical={false} horizontal={false}/> {/* Set vertical to false to hide vertical grid lines */}
//       <XAxis dataKey="name" tickLine={false} />
//       <YAxis
//         axisLine={false}
//         tickLine={false}
//         tick={{ fillOpacity: 0 }} // Set fillOpacity to 0 to make Y-axis values transparent
//       />
//       {/* <Tooltip />
//       <Legend /> */}
//       <Bar dataKey="value" background={getBarFill} barSize={30}>
//         <Label
//           content={({ value }) => `${value}`} // Remove the "%" symbol from the content function
//           position="top"
//           fill="#000000"
//           fontSize={14}
//           fontWeight="bold"
//         />
//       </Bar>
//     </BarChart>
//   );
// };

// export default BarGraphzzz;

import React, { useEffect, useState } from 'react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  LabelList,
} from 'recharts';

function BarGraphzzz() {
  const data = [
    { name: 'Cities', value: 27 },
    { name: 'Towns', value: 45 },
    { name: 'Countryside', value: 18 },
    { name: 'Mountains', value: 36 },
  ];
  function getWindowDimensions() {
    const { innerWidth: width, innerHeight: height } = window;
    return {
      width,
      height,
    };
  }
  function useWindowDimensions() {
    const [windowDimensions, setWindowDimensions] = useState(
      getWindowDimensions(),
    );

    useEffect(() => {
      function handleResize() {
        setWindowDimensions(getWindowDimensions());
      }

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, []);

    return windowDimensions;
  }
  const { width } = useWindowDimensions();

  return (
    <div style={{ width: '90%', height: '90%' }}>
      <BarChart width={width * 0.2} height={290} data={data} barGap={0}>
        <CartesianGrid strokeDasharray="3 3" strokeOpacity={0.1} />
        <XAxis
          dataKey="name"
          axisLine={{ stroke: '#E6E6E6' }}
          tickLine={false}
        />
        <YAxis hide={true} />
        {/* <Tooltip /> */}
        <Bar dataKey="value" barSize={20} fill="url(#colorGradient)">
          <LabelList
            dataKey="value"
            position="top"
            fill="#000"
            textAnchor="middle"
          />
        </Bar>
        <defs>
          <linearGradient id="colorGradient" gradientTransform="rotate(90)">
            <stop offset="0%" stopColor="#5087FF" />
            <stop offset="100%" stopColor="#34ebd8" />
          </linearGradient>
        </defs>
      </BarChart>
    </div>
  );
}

export default BarGraphzzz;

import styled from 'styled-components';
import withDirection from '@chill/lib/helpers/rtl';

const NewStyleWrapper = withDirection(styled.div`
  /* Mobile-first responsive breakpoints */
  /* Mobile: 320px - 767px */
  /* Tablet: 768px - 1023px */
  /* Desktop: 1024px+ */

  .mainContainer {
    display: flex;
    flex-direction: column;
    padding: 8px;
    margin: 8px;
    position: relative;

    /* Tablet and up */
    @media (min-width: 768px) {
      flex-direction: row;
      padding: 10px;
      margin: 15px;
    }

    /* Desktop */
    @media (min-width: 1024px) {
      margin-left: 15px;
      margin-right: 15px;
      margin-top: 15px;
    }
  }

  /* Header Controls - Mobile First */
  .headerControls {
    position: static;
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
    width: 100%;

    /* Tablet */
    @media (min-width: 768px) {
      flex-direction: row;
      justify-content: flex-end;
      position: absolute;
      right: 20px;
      top: 0;
      width: auto;
      margin-bottom: 0;
      z-index: 10;
    }

    /* Desktop */
    @media (min-width: 1024px) {
      right: 38px;
      gap: 10px;
    }
  }

  .headerButton {
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    border-radius: 8px;
    background: #00b7ff;
    color: #ffffff;
    font-weight: 600;
    font-size: 14px;
    height: 44px;
    min-height: 44px; /* Touch-friendly minimum */
    padding: 8px 12px;
    gap: 8px;
    width: 100%;

    /* Tablet */
    @media (min-width: 768px) {
      width: auto;
      height: 40px;
      min-width: 120px;
    }

    /* Desktop */
    @media (min-width: 1024px) {
      padding: 8px 16px;
      gap: 15px;
    }
  }

  .headerButton.dateRange {
    @media (min-width: 768px) {
      width: 158px;
    }
  }

  .headerButton.download {
    @media (min-width: 768px) {
      width: 131px;
    }
  }

  .headerButton.filter {
    @media (min-width: 768px) {
      width: 128px;
    }
  }

  /* Tabs - Responsive */
  .ant-tabs-tab-btn {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    color: #5f6d7e;

    @media (min-width: 768px) {
      font-size: 15px;
      line-height: 22px;
    }
  }

  .custom-tabs .ant-tabs-tab-active > .ant-tabs-tab-btn {
    color: #04b8ff !important;
  }

  .ant-tabs-ink-bar {
    background: #04b8ff !important;
  }

  /* Content Area */
  .contentArea {
    flex: 1;
    width: 100%;

    @media (min-width: 768px) {
      margin-top: 60px; /* Account for absolute positioned header */
    }
  }

  /* Page View Cards Container */
  .pageViewMainCont {
    width: 100%;
    margin-bottom: 16px;

    @media (min-width: 768px) {
      margin-bottom: 20px;
    }
  }

  /* Typography - Responsive */
  .pageViewHeading {
    color: #afafaf;
    font-size: 14px;
    font-weight: 500;
    font-family: 'Poppins', system-ui;
    line-height: 20px;

    @media (min-width: 768px) {
      font-size: 16px;
      line-height: 24px;
    }
  }

  .pageViewCount {
    color: #15223c;
    font-weight: 600;
    font-size: 18px;
    font-family: 'Poppins', system-ui;
    line-height: 26px;
    margin-top: 4px;

    @media (min-width: 768px) {
      font-size: 20px;
      line-height: 30px;
      margin-top: 5px;
    }
  }

  .pageViewTag {
    padding: 4px 6px;
    border-radius: 6px;
    color: #2dd683;
    font-weight: 400;
    font-size: 12px;
    font-family: 'Poppins', system-ui;

    @media (min-width: 768px) {
      padding: 6px 8px;
      font-size: 14px;
    }
  }

  .chartHeading {
    font-size: 12px;
    font-weight: 700;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    color: #2e2e30;
    font-family: 'Inter', sans-serif;
    line-height: 16px;
    flex-wrap: wrap;
    gap: 8px;

    @media (min-width: 768px) {
      font-size: 14px;
      line-height: 16.94px;
      flex-wrap: nowrap;
    }
  }

  .weatherPercentage {
    font-size: 18px;
    font-weight: 700;
    font-family: 'Inter', sans-serif;
    color: #262e3d;
    width: auto;
    text-align: center;
    line-height: 22px;

    @media (min-width: 768px) {
      font-size: 20px;
      width: 65px;
      line-height: 24.2px;
    }
  }

  .weatherText {
    width: auto;
    text-align: center;

    @media (min-width: 768px) {
      width: 49px;
    }
  }

  .filterBtnTxt > span {
    font-size: 12px;
    font-family: 'Inter', sans-serif;
    line-height: 16px;

    @media (min-width: 768px) {
      font-size: 14px;
      line-height: 16.94px;
    }
  }

  .performaceAnaTitle {
    font-family: Inter;
    font-size: 16px;
    font-weight: 600;
    line-height: 20px;
    letter-spacing: -0.01em;
    text-align: left;
    color: #111111;

    @media (min-width: 768px) {
      font-size: 18px;
      line-height: 21.78px;
    }
  }

  .perAna2 {
    font-family: Inter;
    font-size: 20px;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: -0.01em;
    text-align: left;

    @media (min-width: 768px) {
      font-size: 24px;
      line-height: 29.05px;
    }
  }

  .apexcharts-title-text {
    font-family: Helvetica;
    font-size: 12px;
    font-weight: 700;
    line-height: 16px;
    text-align: left;
    color: #2d3748;

    @media (min-width: 768px) {
      font-size: 14px;
      line-height: 19.6px;
    }
  }

  .ant-picker-input > input {
    font-family: Inter;
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    text-align: center;
    color: #2a3548;

    @media (min-width: 768px) {
      font-size: 14px;
      line-height: 20px;
    }
  }

  /* Chart Container Responsive */
  .chartContainer {
    width: 100%;
    height: auto;
    min-height: 200px;

    @media (min-width: 768px) {
      min-height: 250px;
    }

    @media (min-width: 1024px) {
      min-height: 300px;
    }
  }

  /* Widget Responsive Spacing */
  .widgetSpacing {
    margin-bottom: 16px;

    @media (min-width: 768px) {
      margin-bottom: 20px;
    }

    @media (min-width: 1024px) {
      margin-bottom: 24px;
    }
  }

  /* Touch-friendly interactive elements */
  .touchTarget {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Responsive grid adjustments */
  .responsiveGrid {
    display: grid;
    gap: 16px;
    grid-template-columns: 1fr;

    @media (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
    }

    @media (min-width: 1024px) {
      grid-template-columns: repeat(3, 1fr);
      gap: 24px;
    }
  }

  /* Drawer responsive adjustments */
  .ant-drawer-content-wrapper {
    width: 100% !important;
    max-width: 400px;

    @media (min-width: 768px) {
      width: 400px !important;
    }
  }

  /* Performance analysis responsive layout */
  .performanceAnalysisContainer {
    display: flex;
    flex-direction: column;
    gap: 16px;

    @media (min-width: 768px) {
      flex-direction: row;
      gap: 20px;
    }
  }

  .performanceAnalysisItem {
    flex: 1;
    min-width: 0; /* Prevent flex item overflow */
  }
`);

export default NewStyleWrapper;

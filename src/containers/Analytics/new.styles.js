import styled from 'styled-components';
import withDirection from '@chill/lib/helpers/rtl';

const NewStyleWrapper = withDirection(styled.div`
  .mainContainer {
    display: flex;
    flex-direction: row;
    padding: 10px;
    margin-left: 15px;
    margin-right: 15px;
    margin-top: 15px;
  }

  .ant-tabs-tab-btn {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    font-size: 15px;
    line-height: 22px;
    color: #5f6d7e;
  }

  .custom-tabs .ant-tabs-tab-active > .ant-tabs-tab-btn {
    color: #04b8ff !important;
  }

  .ant-tabs-ink-bar {
    background: #04b8ff !important;
  }

  .pageViewMainCont {
    width: 100%;
    margin-bottom: 20px;
  }

  .pageViewHeading {
    color: #afafaf;
    font-size: 16px;
    font-weight: 500;
    font-family: 'Poppins', system-ui;
    line-height: 24px;
  }

  .pageViewCount {
    color: #15223c;
    font-weight: 600;
    font-size: 20px;
    font-family: 'Poppins', system-ui;
    line-height: 30px;
    margin-top: 5px;
  }

  .pageViewTag {
    padding: 6px 8px;
    border-radius: 6px;
    color: #2dd683;
    font-weight: 400;
    font-size: 14px;
    font-family: 'Poppins', system-ui;
  }

  .chartHeading {
    font-size: 14px;
    font-weight: 700;
    width: 100%;
    display: flex;
    justify-content: space-between;
    color: #2e2e30;
    font-family: 'Inter', sans-serif;
    line-height: 16.94px;
  }

  .weatherPercentage {
    font-size: 20px;
    font-weight: 700;
    font-family: 'Inter', sans-serif;
    color: #262e3d;
    width: 65px;
    text-align: center;
    line-height: 24.2px;
  }

  .weatherText {
    width: 49px;
    text-align: center;
  }

  .filterBtnTxt > span {
    font-size: 14px;
    font-family: 'Inter', sans-serif;
    line-height: 16.94px;
  }

  .performaceAnaTitle {
    font-family: Inter;
    font-size: 18px;
    font-weight: 600;
    line-height: 21.78px;
    letter-spacing: -0.01em;
    text-align: left;
    color: #111111;
  }

  .perAna2 {
    font-family: Inter;
    font-size: 24px;
    font-weight: 700;
    line-height: 29.05px;
    letter-spacing: -0.01em;
    text-align: left;
  }

  .apexcharts-title-text {
    font-family: Helvetica;
    font-size: 14px;
    font-weight: 700;
    line-height: 19.6px;
    text-align: left;
    color: #2d3748;
  }

  .ant-picker-input > input {
    font-family: Inter;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    text-align: center;
    color: #2a3548;
  }
`);

export default NewStyleWrapper;

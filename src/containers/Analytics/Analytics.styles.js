import styled from 'styled-components';
import withDirection from '@chill/lib/helpers/rtl';

const AnalyticsWrapper = withDirection(styled.div`
  // .activeUserRow {
  //   display: flex !important;
  //   flex-direction: row;
  //   align-items: center;
  // }
  // & .activeMonthlyText {
  //   font-size: 15px;
  //   color: #000;
  //   font-weight: 400;
  //   opacity: 0.7;
  // }
  // .arraowPerStyle {
  //   margin-left: 10px;
  // }
  // .activeUserPerStyle {
  //   margin-left: 5px;
  // }
  // .activeusercntstyle {
  //   font-weight: 400;
  // }
  // .nameWithImg {
  //   width: 15px;
  //   height: 15px;
  //   background-color: #ec8e8e;
  //   margin-right: 12px;
  //   border-radius: 15px;
  //   margin-left: 20px;
  // }
  // .listNameView {
  //   display: flex;
  //   flex-direction: row;
  //   align-items: center;
  //   justify-content: center;
  // }
  // .listNameViewColum {
  //   display: flex;
  //   flex-direction: row;
  // }
  // .marginTop {
  //   margin-top: 20px
  // }
  // .topLinkWrapper {
  //   display: flex;
  //   flex-direction: row;
  //   align-items: center;
  //   justify-content: space-between;
  // }
  // .topLinkCountWrapper {
  //   padding: 5px 12px;
  //   background-color: #d0d9ef;
  //   color: #1172ec;
  //   border-radius: 20px;
  // }
  // .topLinktitle {
  //   font-size: 15px;
  //   color: #000;
  //   font-weight: 400;
  // }
  // .topLinkSubTitle {
  //   font-size: 13px;
  //   color: #000;
  //   opacity: 0.6;
  // }
  // .activityTextStyle {
  //   font-weight: 400;
  //   color: #000;
  // }
  // .activityDateTextStyle {
  //   font-weight: 400;
  //   color: #000;
  //   opacity: 0.6;
  // }
  // .periodDiv{
  //   padding-left: 20px;
  //   margin-top: 2px;
  //   @media (max-width: 767px){
  //     padding-left: 0px;
  //     margin: 10px 0px;
  //   }
  // }
  // .thisAc {
  //   flex-direction: row;
  //   display: flex;
  //   width: 100%;
  //   justify-content: flex-end;
  //   margin:10px;
  //   @media (max-width: 767px){
  //     flex-direction: column;
  //     margin-bottom: 16px;
  //   }
  // }
  // .changeBtn {
  //   border-radius: 8px;
  //   font-weight: 500;
  //   border: 0px;
  //   color: #fff
  //   background: linear-gradient(90deg, #039be5 38%, #03a9f4 90%);
  //   &:hover {
  //     background: linear-gradient(90deg, #039be5 38%, #03a9f4 90%);
  //   }
  //   margin-left: 20px;
  //   padding: 5px 40px 6px 40px;
  //   @media (min-width: 300px) and (max-width: 600px) {
  //     padding: 7px 20px 6px 20px;
  //     margin-left: 0px;
  //   }
  //   @media (max-width: 280px) {
  //     padding: 0px 12px;
  //     margin-left: 0px;
  //   }
  // }
  // .goalCompletion{
  //   display: flex;
  //   flex-direction:row;
  // }
  // .isoGoalCompletion>div > div > div > div > h4 > span{
  //   color: #fff;
  // }
  // .isoTrafficList > div > div > div > h4 > span{
  //   padding: 0px 16px;
  // }
  // .trafficList{
  //   padding: 25px 0px;
  // }
  // .traffic{
  //   padding: 0px 16px;
  // }
  // .whiteColor {
  //   color: #FFF;
  // }
  // .chartStyle > div > svg > g > g:nth-child(2){
  //   display: none;
  // }
  // .chartStyle > div > svg > g > g:nth-child(3) > text{
  //   dominant-baseline: text-before-edge;
  // }
  // .overViewContainer {
  //   height: 100%;
  //   width: 100%;
  //   padding: 16px;
  //   background: linear-gradient(
  //     112deg,
  //     rgba(80, 135, 255, 1) 14%,
  //     rgba(41, 239, 196, 1) 86%
  //   );
  //   border-radius: 10px;
  //   margin: 0px 0px 20px;
  //   box-shadow: 2px 1px 10px 1px #ccc;
  //   color: white;
  // }
  // & .overViewText {
  //   font-size: 20px;
  //   color: white;
  //   font-weight: 400;
  //   margin-bottom: 5px;
  // }

  .tabs {
    margin: 0;
    padding: 0;
    margin-top: 10px;
    margin-left: 10px;
  }

  .custom-tabs .ant-tabs-nav .ant-tabs-tab {
    margin-right: 10px; /* Adjust the value as per your preference */
  }

  .custom-tabs .ant-tabs-ink-bar {
    display: none; /* Hide the horizontal line below the tabs */
  }

  .custom-tabs .ant-tabs-nav .ant-tabs-tab:nth-child(1) .ant-tabs-tab-btn,
  .custom-tabs .ant-tabs-nav .ant-tabs-tab:nth-child(2) .ant-tabs-tab-btn,
  .custom-tabs .ant-tabs-nav .ant-tabs-tab:nth-child(3) .ant-tabs-tab-btn,
  .custom-tabs .ant-tabs-nav .ant-tabs-tab:nth-child(4) .ant-tabs-tab-btn,
  .custom-tabs .ant-tabs-nav .ant-tabs-tab:nth-child(5) .ant-tabs-tab-btn {
    color: #000000;
  }
  .custom-tabs .ant-tabs-nav {
    border-bottom: none; /* Remove the default bottom border */
    border-radius: 0%; /* Add border-radius for rounded corners */
  }

  .custom-tabs .ant-tabs-nav::before {
    display: none; /* Hide the pseudo-element that creates the line */
  }
  .custom-tabs .ant-tabs-nav {
    border: none; /* Remove the default border */
    background-color: #f1f1f1; /* Set background color */
    padding: 8px 16px; /* Adjust padding as needed */
    display: flex; /* Display the tabs in a row */
    justify-content: flex-start; /* Align the tabs to the left */
  }

  .custom-tabs .ant-tabs-tab {
    margin-right: 10px; /* Adjust the spacing between tabs */
    border-radius: 0; /* Remove the circular border radius */
    border-top-left-radius: 8px; /* Apply border radius to the first tab */
    border-top-right-radius: 8px; /* Apply border radius to the last tab */
    border: none; /* Remove the default border */
  }

  .custom-tabs .ant-tabs-tab:first-child {
    border-top-left-radius: 0; /* Remove the border radius from the first tab */
  }

  .custom-tabs .ant-tabs-tab:last-child {
    border-top-right-radius: 0; /* Remove the border radius from the last tab */
  }

  .custom-tabs .ant-tabs-nav {
    background-color: white; /* Set tab background color */
  }

  .custom-tabs .ant-tabs-tab {
    background-color: white; /* Set tab background color */
  }

  .custom-tabs .ant-tabs-tab-active {
    background-color: white; /* Set active tab background color */
  }
  .custom-tabs .ant-tabs-nav .ant-tabs-tab:hover {
    border-bottom: 2px solid #1890ff;
  }

  .custom-tabs .ant-tabs-nav .ant-tabs-tab {
    padding: 8px 16px; /* Adjust the padding to decrease box size */
    margin-right: 10px; /* Adjust the margin as needed */
  }

  .custom-tabs .ant-tabs-tab-btn {
    font-size: 12px; /* Adjust the font size as needed */
  }

  /* CSS FOR CARD Container */

  .row1 {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    gap: 5px;
  }

  .card {
    display: flex;
    height: 102px;
    width: 230px;
    background: transparent linear-gradient(112deg, #5087ff 0%, #29efc4 100%) 0%
      0% no-repeat padding-box;
    color: white;
    font-size: 18px;
    /* text-align: justify; */
    border-radius: 20px;
    justify-content: space-between;
    /* text-align: left; */
    letter-spacing: 0px;
    opacity: 1;
    gap: 5px;
  }
  .card p {
    margin: 0; /* Remove default margin */
    text-align: left;
    text-decoration: solid;
  }
  .card p span {
    color: #ffffff;
    font-size: 24px;
  }
  .icon {
    margin-top: -60px;
    margin-left: 150px;
    height: 40px;
    width: 40px;
    font-size: 40px;
  }
  /* CSS for 2nd row cards */
  .row2 {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    gap: 10px;
  }
  .cardmain {
    width: 480px;
    height: 102px;
    background: #ffffff;
  }
  .cardmain p {
    color: black;
    font-size: 18px;
    margin-top: -10px;
  }
  .cardmain p span {
    color: #a9abbc;
    font-size: 24px;
  }
  .cardmain img {
    display: flex;
    margin-top: -80px;
    margin-left: 380px;
  }

  .cardmain .icon {
    display: flex;
    margin-left: 400px;
    margin-top: -80px;
    height: 40px;
    width: 40px;
    color: #2a77f4;
  }

  /* CSS for 3rd row cards */

  .row3 {
    display: flex;
    justify-self: center;
    gap: 20px;
  }

  .bars {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    height: 293px;
    width: 480px;
    border-radius: 20px;
  }
  .text1 {
    font-size: 12px;
    margin-top: 10px;
  }
  .text1 h4 {
    margin-top: -10px;
    font-size: 18px;
  }
  .text1 h4 span {
    margin-left: 10px;
    color: #4cb083;
    font-size: 12px;
  }
  .text2 {
    font-size: 12px;
    margin-top: -10px;
    margin-left: 150px;
  }
  .text2 h4 {
    margin-top: -10px;
    font-size: 18px;
  }
  .text2 h4 span {
    margin-left: 10px;
    color: #ea0606;
    font-size: 12px;
  }
  .text3 {
    font-size: 12px;
    margin-top: -73px;
    margin-left: 290px;
  }
  .text3 h4 {
    margin-top: -10px;
    font-size: 18px;
  }
  .text3 h4 span {
    margin-left: 10px;
    color: #4cb083;
    font-size: 12px;
  }

  .graph1 {
    /* margin-left: 60px;
    margin-top: -10px;
    top: 550px;
    left: 373px;
    height: 140px;
    width: 434px;
    padding: 10px; */
    margin-left: 50px;
    margin-top: 20px;
    height: 140px;
    width: 434px;
  }
  .waves {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    height: 293px;
    width: 480px;
    border-radius: 20px;
  }

  /* CSS for 4th row cards */

  .row4 {
    display: flex;
    justify-self: center;
    gap: 20px;
  }
  .circle {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    height: 350px;
    width: 320px;
    border-radius: 20px;
  }
  .head {
    font-size: 14px;
    text-align: left;
    margin-top: -30px;
  }
  .piechart {
    height: 189.57px;
    width: 186.16px;
    margin-left: 40px;
  }
  .dots {
    display: flex;
    height: 8px;
    width: 8px;
    gap: 20px;
    margin-top: 30px;
    margin-left: 70px;
  }
  .dots p1 {
    display: flex;
    color: #f08280;
  }
  .dots p2 {
    display: flex;
    color: #1172ec;
  }
  .dots p1 span {
    color: black;
    height: 14px;
    width: 18px;
    font-size: 12px;
  }
  .dots p2 span {
    color: black;
    height: 14px;
    width: 18px;
    font-size: 12px;
  }
  .content {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    height: 350px;
    width: 320px;
    border-radius: 20px;
  }
  .meter {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    height: 350px;
    width: 330px;
    background: transparent linear-gradient(112deg, #5087ff 0%, #29efc4 100%) 0%
      0% no-repeat padding-box;
    border-radius: 20px;
  }
  .topinfo {
    color: white;
    text-align: left;
    margin-top: -30px;
    font-family: 'Rubik', sans-serif;
  }

  .gauss {
    top: 863px;
    left: 1108px;
    width: 226px;
    height: 122px;
    margin-left: 40px;
    margin-top: -25px;
  }
  .gauss p {
    font-size: 20px;
    color: white;
    margin-top: -110px;
    margin-left: 95px;
  }
  .info {
    color: white;
    text-align: left;
    margin-top: 100px;
  }
  .style {
    margin-top: -20px;
  }
  .style p {
    color: #ffffff;
    font-size: 18px;
  }
  .style p span {
    color: #076038;
    font-size: 12px;
    margin-left: 18px;
    font-weight: bold;
  }
  .buttons {
    margin-left: 500px;
    margin-top: -960px;
    display: flex;
    gap: 20px;
    border-radius: 20px;
    border: none;
    height: 35px;
  }
`);
export default AnalyticsWrapper;

/* eslint-disable react/jsx-boolean-value */

// import { Chart as ChartJS, BarElement, LinearScale } from "chart.js/auto";
// import { Bar } from "react-chartjs-2";

// ChartJS.register(BarElement, LinearScale);
// function BarChart() {
//   const data = {
//     labels: ['Cities', 'Towns', 'Countryside', 'Mountains', 'Beaches'],
//     datasets: [
//       { X
//         label: 'Product Sales',
//         data: [27, 45, 35, 18, 27],
//         backgroundColor: '#2A77F4',
//       },
//     ],
//   };

//   const options = {
//     indexAxis: 'x',
//     responsive: true,
//     maintainAspectRatio: false,
//     plugins: {
//       legend: {
//         display: false,
//       },
//     },
//     scales: {
//       x: {
//         grid: {
//           display: false,
//         },
//       },
//       y: {
//         grid: {
//           display: false,
//         },
//       },
//     },
//   };

//   return (
//     <div className="chart-container">
//      <Bar data={data} options={options} />
//     </div>
//   );
// }

// export default BarChart;

import React from 'react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  LabelList,
} from 'recharts';

function BarGraphzzz1() {
  const data = [
    { name: 'Cities', value: 27 },
    { name: 'Towns', value: 45 },
    { name: 'Countryside', value: 18 },
    { name: 'Mountains', value: 36 },
    { name: 'beaches', value: 27 },
  ];

  return (
    <div style={{ width: '90%', height: '90%' }}>
      <BarChart width={200} height={270} data={data} barGap={0}>
        <CartesianGrid strokeDasharray="3 3" strokeOpacity={0.1} />
        <XAxis dataKey="name" axisLine={true} tickLine={false} />
        <YAxis hide={true} />
        {/* <Tooltip /> */}
        <Bar dataKey="value" barSize={20} fill="url(#colorGradient)">
          <LabelList
            dataKey="value"
            position="top"
            fill="#000"
            textAnchor="middle"
          />
        </Bar>
        <defs>
          <linearGradient id="colorGradient" gradientTransform="rotate(90)">
            <stop offset="0%" stopColor="#5087FF" />
            <stop offset="100%" stopColor="#34ebd8" />
          </linearGradient>
        </defs>
      </BarChart>
    </div>
  );
}

export default BarGraphzzz1;

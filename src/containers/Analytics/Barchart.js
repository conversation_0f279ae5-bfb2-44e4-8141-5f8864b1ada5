import React, { PureComponent } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  //   Cell,
  //   XAxis,
  //   YAxis,
  //   CartesianGrid,
  //   Tooltip,
  //   Legend,
  ResponsiveContainer,
} from 'recharts';

const data = [
  {
    name: '<PERSON> A',
    uv: 130,
  },
  {
    name: '<PERSON> B',
    uv: 96,
  },
  {
    name: '<PERSON> C',
    uv: 116,
  },
  {
    name: '<PERSON> D',
    uv: 66,
  },
  {
    name: '<PERSON> E',
    uv: 140,
  },
  {
    name: 'Page F',
    uv: 106,
  },
  {
    name: '<PERSON> G',
    uv: 73,
  },
  {
    name: '<PERSON> H',
    uv: 96,
  },
  {
    name: '<PERSON> <PERSON>',
    uv: 116,
  },
  {
    name: '<PERSON> J',
    uv: 116,
  },
  {
    name: '<PERSON> K',
    uv: 73,
  },
  {
    name: '<PERSON> L',
    uv: 96,
  },
  {
    name: '<PERSON> M',
    uv: 106,
  },
  {
    name: 'Page N',
    uv: 140,
  },
  {
    name: '<PERSON> O',
    uv: 140,
  },
  {
    name: '<PERSON> <PERSON>',
    uv: 60,
  },
  {
    name: '<PERSON> <PERSON>',
    uv: 113,
  },
  {
    name: '<PERSON> <PERSON>',
    uv: 96,
  },
  {
    name: '<PERSON> S',
    uv: 73,
  },
  {
    name: '<PERSON> T',
    uv: 96,
  },
  {
    name: 'Page U',
    uv: 122,
  },
  {
    name: 'Page V',
    uv: 96,
  },
  {
    name: 'Page W',
    uv: 113,
  },
  {
    name: 'Page X',
    uv: 96,
  },
  {
    name: 'Page Y',
    uv: 60,
  },
  {
    name: 'Page Z',
    uv: 96,
  },
  {
    name: 'Page a',
    uv: 128,
  },
  {
    name: 'Page b',
    uv: 60,
  },
  {
    name: 'Page c',
    uv: 79,
  },
  {
    name: 'Page d',
    uv: 113,
  },
  {
    name: 'Page c',
    uv: 96,
  },
  {
    name: 'Page d',
    uv: 61,
  },
];

export default class BarGraph extends PureComponent {
  static demoUrl = 'https://codesandbox.io/s/tiny-bar-chart-35meb';

  render() {
    return (
      <ResponsiveContainer width={300} height={180}>
        <BarChart data={data}>
          <Bar isAnimationActive={false} dataKey="uv" fill="#1172EC" />
        </BarChart>
      </ResponsiveContainer>
    );
  }
}

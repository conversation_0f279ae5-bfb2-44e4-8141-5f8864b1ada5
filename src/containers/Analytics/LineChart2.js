import React, { PureComponent } from 'react';
import {
  LineC<PERSON>,
  Line,
  // XAxis,
  // YAxis,
  // CartesianGrid,
  // Tooltip,
  // Legend,
  ResponsiveContainer,
} from 'recharts';

const data = [
  {
    name: 'Page A',
    wave3: 2,
  },
  {
    name: 'Page B',
    wave3: 1,
  },
  {
    name: 'Page C',
    wave3: 2,
  },
  {
    name: 'Page D',
    wave3: 1,
  },
  {
    name: 'Page E',
    wave3: 1,
  },
  {
    name: 'Page F',
    wave3: 2,
  },
];

export default class Linear extends PureComponent {
  render() {
    return (
      <ResponsiveContainer width={300} height={100}>
        <LineChart data={data}>
          {/* <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          <Legend /> */}
          <Line
            type="monotone"
            dataKey="wave3"
            stroke="#1172EC"
            strokeWidth={2}
            name="Wave 3"
            dot={false} // Remove the pointers for values
          />
        </LineChart>
      </ResponsiveContainer>
    );
  }
}

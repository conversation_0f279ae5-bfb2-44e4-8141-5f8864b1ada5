/* eslint-disable  prefer-template */
/* eslint-disable  no-unused-vars */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/jsx-boolean-value  */

// import React, { useEffect, useState } from "react";
// import {
//   Bar<PERSON>hart,
//   Bar,
//   XAxis,
//   YAxis,
//   CartesianGrid,
//   // Tooltip,
//   // Legend,
//   Label,
// } from "recharts";
// import axios from "axios";

// const BarGraphsss = () => {
//   const [data, setData] = useState([]);

//   useEffect(() => {
//     // Fetch data from the API
//     const fetchData = async () => {
//       try {
//         const response = await axios.get(
//           "https://hub.dummyapis.com/vj/OqC8W2W"
//         );
//         setData(Object.values(response.data[0])); // Extract the data array from the response
//       } catch (error) {
//         console.log(error);
//       }
//     };

//     fetchData();
//   }, []);

//   const getBarFill = (count) => {
//     // Define the transparent linear gradient as the fill color for each bar
//     return `linear-gradient(171deg, rgba(80, 135, 255, 0) 0%, rgba(41, 239, 196, 0) 100%), ${count}`;
//   };

//   return (
//     <BarChart width={400} height={300} data={data} barGap={65} categoryGap={20}>
//       <CartesianGrid strokeDasharray="3 3" vertical={false} horizontal={false}/> {/* Set vertical to false to hide vertical grid lines */}
//       <XAxis dataKey="age_range" tickLine={false}/>
//       <YAxis
//         axisLine={false}
//         tickLine={false}
//         tick={{ fillOpacity: 0 }} // Set fillOpacity to 0 to make Y-axis values transparent
//       />
//       {/* <Tooltip />
//       <Legend /> */}
//       <Bar dataKey="count" background={getBarFill} barSize={30}>
//         <Label
//           content={({ count }) => `${count}%`}
//           position="top"
//           fill="#000000"
//           fontSize={14}
//           fontWeight="bold"
//         />
//       </Bar>
//     </BarChart>
//   );
// };

// export default BarGraphsss;
import React, { useEffect, useState } from 'react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  LabelList,
  Tooltip,
} from 'recharts';

function BarGraphsss({ userTabData }) {
  console.log('@@@@@@@@@@', userTabData);
  const childAgeArray = userTabData?.childAgeData;
  console.log('childAgeArray', childAgeArray);

  const formatValue = (count) => Math.round(count) + '%';
  function getWindowDimensions() {
    const { innerWidth: width, innerHeight: height } = window;
    return {
      width,
      height,
    };
  }
  function useWindowDimensions() {
    const [windowDimensions, setWindowDimensions] = useState(
      getWindowDimensions(),
    );

    useEffect(() => {
      function handleResize() {
        setWindowDimensions(getWindowDimensions());
      }

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, []);

    return windowDimensions;
  }
  const { width } = useWindowDimensions();
  return (
    <div style={{ width: '100%', height: '90%' }}>
      <BarChart
        width={width * 0.36}
        height={300}
        data={childAgeArray}
        barGap={25}
      >
        <CartesianGrid strokeDasharray="3 3" strokeOpacity={0.1} />
        <XAxis
          dataKey="age_range"
          axisLine={{ stroke: '#E6E6E6' }}
          tickLine={false}
        />
        <YAxis hide={true} domain={[0, 100]} />
        <Tooltip formatter={(count) => `${count}%`} />
        <Bar dataKey="count" barSize={40} fill="url(#colorGradient)">
          <LabelList
            dataKey="count"
            position="top"
            fill="#000"
            textAnchor="middle"
            formatter={formatValue}
          />
        </Bar>
        <defs>
          <linearGradient id="colorGradient" gradientTransform="rotate(90)">
            <stop offset="0%" stopColor="#5087FF" />
            <stop offset="100%" stopColor="#34ebd8" />
          </linearGradient>
        </defs>
      </BarChart>
    </div>
  );
}

export default BarGraphsss;

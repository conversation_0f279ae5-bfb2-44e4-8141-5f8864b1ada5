import React, { PureComponent } from 'react';
import {
  LineC<PERSON>,
  Line,
  // XAxis,
  // YAxis,
  // CartesianGrid,
  // Tooltip,
  // Legend,
  ResponsiveContainer,
} from 'recharts';

const data = [
  {
    name: 'Page A',
    wave2: 2,
  },
  {
    name: 'Page B',
    wave2: 1,
  },
  {
    name: 'Page C',
    wave2: 2,
  },
  {
    name: 'Page D',
    wave2: 1,
  },
  {
    name: 'Page E',
    wave2: 1,
  },
  {
    name: 'Page F',
    wave2: 2,
  },
];

export default class Linear extends PureComponent {
  render() {
    return (
      <ResponsiveContainer width={300} height={100}>
        <LineChart data={data}>
          {/* <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          <Legend /> */}
          <Line
            type="monotone"
            dataKey="wave2"
            stroke="#1172EC"
            strokeWidth={2}
            name="Wave 2"
            dot={false} // Remove the pointers for values
          />
        </LineChart>
      </ResponsiveContainer>
    );
  }
}

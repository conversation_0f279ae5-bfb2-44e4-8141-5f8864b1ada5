/* eslint-disable no-unused-vars */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable  no-unused-vars */

import React, { useEffect, useState } from 'react';
import {
  Bar<PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  LabelList,
  Tooltip,
} from 'recharts';

function Horizontall({ userTabData }) {
  console.log('childCountData', userTabData);
  const childCountArray = userTabData?.childCountData;
  console.log('childcountArray', childCountArray);

  const formatValue = (child_count) => `${child_count}`;
  function getWindowDimensions() {
    const { innerWidth: width, innerHeight: height } = window;
    return {
      width,
      height,
    };
  }
  function useWindowDimensions() {
    const [windowDimensions, setWindowDimensions] = useState(
      getWindowDimensions(),
    );

    useEffect(() => {
      function handleResize() {
        setWindowDimensions(getWindowDimensions());
      }

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, []);

    return windowDimensions;
  }
  const { width } = useWindowDimensions();
  return (
    <div style={{ width: '90%', height: '90%' }}>
      <BarChart
        width={width * 0.22}
        height={300}
        data={childCountArray}
        layout="vertical"
      >
        <CartesianGrid strokeDasharray="3 3" strokeOpacity={0.1} />
        <YAxis
          dataKey="no_of_children"
          type="category"
          width={100}
          domain={[0, 100]}
          axisLine={{ stroke: '#E6E6E6' }}
          tick={{ fill: '#000' }} // Add this line to style the ticks
          tickLine={false} // Add this line to hide the tick lines
        />
        <XAxis
          type="number"
          domain={[0, 100]}
          axisLine={{ strokeOpacity: 0 }}
          tick={{ fill: '#000' }}
          tickLine={false}
          tickFormatter={(child_count) => `${child_count}`}
        />
        <Tooltip formatter={(child_count) => `${child_count}`} />
        <Bar dataKey="child_count" fill="url(#colorGradient)">
          <LabelList
            dataKey="child_count"
            position="right"
            fill="#000"
            textAnchor="middle"
            formatter={formatValue}
          />
        </Bar>
        <defs>
          <linearGradient id="colorGradient" gradientTransform="rotate(90)">
            <stop offset="0%" stopColor="#5087FF" />
            <stop offset="100%" stopColor="#34ebd8" />
          </linearGradient>
        </defs>
      </BarChart>
    </div>
  );
}

export default Horizontall;

// import './styles.css';
import React from 'react';
import { Pie<PERSON><PERSON>, Pie } from 'recharts';

const data01 = [
  { name: 'A1', value: 25, fill: '#1172EC' },
  { name: 'A2', value: 75, fill: '#F08280' },
];

// const COLORS = ['#82ca9d', '#8884d8']; // Custom colors for the two values

export default function Chartz() {
  return (
    <PieChart width={400} height={400}>
      <Pie
        data={data01}
        dataKey="value"
        cx={100}
        cy={100}
        innerRadius={70}
        outerRadius={90}
        // fill="#82ca9d"
        // label
      />
      {/* {data01.map((entry) => (
        <Cell
          key={entry.name}
          fill={COLORS[data01.indexOf(entry) % COLORS.length]}
        />
      ))} */}
    </PieChart>
  );
}

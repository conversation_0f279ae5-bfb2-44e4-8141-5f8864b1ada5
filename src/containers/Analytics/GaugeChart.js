import React from 'react';
import Gauge<PERSON>hart from 'react-gauge-chart';

export default function Gauss() {
  return (
    <div>
      <GaugeChart
        id="gauge-chart3"
        nrOfLevels={30}
        colors={['#1de9b6', '#fff']}
        style={{ paddingTop: 30, width: 300 }}
        arcWidth={0.25}
        percent={0}
        textColor="#fff"
        needleBaseColor="transparent"
        needleColor="transparent" // Set the needle color to transparent
      />
    </div>
  );
}

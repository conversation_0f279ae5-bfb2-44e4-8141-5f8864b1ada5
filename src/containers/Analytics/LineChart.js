import React, { PureComponent } from 'react';
import {
  LineChart,
  Line,
  // XAxis,
  // YAxis,
  // CartesianGrid,
  // Tooltip,
  // Legend,
  ResponsiveContainer,
} from 'recharts';

const data = [
  {
    name: 'Page A',
    wave1: 2,
  },
  {
    name: 'Page B',
    wave1: 1,
  },
  {
    name: 'Page C',
    wave1: 2,
  },
  {
    name: 'Page D',
    wave1: 1,
  },
  {
    name: 'Page E',
    wave1: 1,
  },
  {
    name: 'Page F',
    wave1: 2,
  },
];

export default class Linear extends PureComponent {
  render() {
    return (
      <ResponsiveContainer width={300} height={100}>
        <LineChart data={data}>
          {/* <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          <Legend /> */}
          <Line
            type="monotone"
            dataKey="wave1"
            stroke="#1172EC"
            strokeWidth={2}
            name="Wave 1"
            dot={false} // Remove the pointers for values
          />
          {/* <Line
            type="monotone"
            dataKey="wave2"
            stroke="#FF6600"
            strokeWidth={2}
            name="Wave 2"
          />
          <Line
            type="monotone"
            dataKey="wave3"
            stroke="#00CC66"
            strokeWidth={2}
            name="Wave 3"
          /> */}
        </LineChart>
      </ResponsiveContainer>
    );
  }
}

import { Chart as ChartJ<PERSON>, ArcElement, Legend } from 'chart.js';
import { Doughn<PERSON> } from 'react-chartjs-2';

ChartJS.register(ArcElement, Legend);

function Charts() {
  const data = {
    // labels: ['Label 1', 'Label 2', 'Label 3', 'Label 4'],
    datasets: [
      {
        label: 'Time',
        data: [20, 15, 20, 45],
        backgroundColor: ['#29EFC4', '#5A96F8', '#30CDAB', '#2A77F4'],
        bordercolor: ['#29EFC4', '#5A96F8', '#30CDAB', '#2A77F4'],
      },
    ],
  };

  const options = {};

  return (
    <div className="App">
      <div style={{ width: '100%', height: '75%' }}>
        <Doughnut data={data} options={options}></Doughnut>
      </div>
    </div>
  );
}

export default Charts;

// import React, { useEffect, useState } from 'react';
// import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>A<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Cartesian<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, LabelList } from 'recharts';

// const BarGraph = () => {
//   const [data, setData] = useState([]);

//   useEffect(() => {
//     // Fetch data from API
//     fetch('https://hub.dummyapis.com/vj/qqw0Btp')
//       .then(response => response.json())
//       .then(data => setData(data));
//   }, []);

//   return (
//     <BarChart width={600} height={300} data={data}>
//       <CartesianGrid strokeDasharray="3 3" />
//       <XAxis dataKey="name" />
//       <YAxis />
//       <Tooltip />
//       <Legend />
//       <Bar dataKey="value" fill="#8884d8">
//         <LabelList dataKey="value" position="top" formatter={(value) => `${value}%`} />
//       </Bar>
//     </BarChart>
//   );
// };

// export default BarGraph;

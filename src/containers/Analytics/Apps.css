/* CSS FOR CARDS IN CONTENT SECTION */
.row1 {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  /* background: #fd3a84; */
  padding: 10px;
  min-height: 400px;
  justify-content: space-evenly;
}
.bar {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 0.5px solid #ededed;
  border-radius: 20px;
  opacity: 1;
  margin-bottom: 15px;
}

.mng {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 0.5px solid #ededed;
  border-radius: 20px;
  opacity: 1;
  margin-bottom: 15px;
}
.mng img {
  display: flex;
  float: left;
  font-size: 55px;
  margin-top: 10px;
  color: #ffde00;
}
.mng h4 {
  display: flex;
  margin-left: 120px;
  margin-top: 15px;
  font: normal normal medium 15px/18px Rubik;
  color: #000000;
  opacity: 100%;
}
.mng h1 {
  display: flex;
  margin-left: 110px;
  margin-top: 10px;
  font: normal normal medium 26px/31px Rubik;
  letter-spacing: 0px;
  color: #2a77f4;
  opacity: 1;
  font-size: 26px;
}
.aft {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 0.5px solid #ededed;
  border-radius: 20px;
  opacity: 1;
  margin-bottom: 15px;
}
.aft img {
  display: flex;
  float: left;
  font-size: 55px;
  margin-top: 10px;
  color: #f60000;
}
.aft h4 {
  display: flex;
  margin-left: 120px;
  margin-top: 15px;
  font: normal normal medium 15px/18px Rubik;
  color: #000000;
  opacity: 100%;
}
.aft h1 {
  display: flex;
  margin-left: 110px;
  margin-top: 10px;
  font: normal normal medium 26px/31px Rubik;
  letter-spacing: 0px;
  color: #2a77f4;
  opacity: 1;
  font-size: 26px;
}
.evng {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 0.5px solid #ededed;
  border-radius: 20px;
  opacity: 1;
  margin-bottom: 15px;
  padding-bottom: 30px;
}
.evng img {
  display: flex;
  float: left;
  font-size: 55px;
  margin-top: 10px;
  color: #fd5900;
}
.evng h4 {
  display: flex;
  margin-left: 120px;
  margin-top: 15px;
  font: normal normal medium 15px/18px Rubik;
  color: #000000;
  opacity: 100%;
}
.evng h1 {
  display: flex;
  margin-left: 120px;
  margin-top: 10px;
  font: normal normal medium 26px/31px Rubik;
  letter-spacing: 0px;
  color: #2a77f4;
  opacity: 1;
  font-size: 26px;
}
.nyt {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 0.5px solid #ededed;
  border-radius: 20px;
  opacity: 1;
  margin-bottom: 15px;
}
.nyt img {
  display: flex;
  float: left;
  font-size: 55px;
  color: #fd3a84;
}
.nyt h4 {
  display: flex;
  margin-left: 120px;
  margin-top: 15px;
  font: normal normal medium 15px/18px Rubik;
  color: #000000;
  opacity: 100%;
}
.nyt h1 {
  display: flex;
  margin-left: 110px;
  margin-top: 10px;
  font: normal normal medium 26px/31px Rubik;
  letter-spacing: 0px;
  color: #2a77f4;
  opacity: 1;
  font-size: 26px;
}
/* CSS FOR ROW2 */
.row2 {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 10px;
  min-height: 400px;
  justify-content: space-evenly;
  /* background: yellow; */
}
.bar2 {
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent linear-gradient(129deg, #5087ff 0%, #29efc4 100%) 0%
    0% no-repeat padding-box;
  border-radius: 20px;
  margin-bottom: 10px;
}

.meter {
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent linear-gradient(129deg, #5087ff 0%, #29efc4 100%) 0%
    0% no-repeat padding-box;
  border-radius: 20px;
  margin-bottom: 15px;
}

.circle {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  padding-bottom: 35px;
}

.dot {
  display: flex;
}
.dot i {
  display: flex;
  font-size: 20px;
  color: #5087ff;
}
.dot p {
  display: flex;
  font: Rubik, Regular;
  font-size: 14px;
  color: #0a0606;
  opacity: 100%;
  gap: 1px;
}
.dot1 i {
  display: flex;
  font-size: 20px;
  margin-top: -19px;
  margin-left: 50%;
  color: #5a96f8;
}
.dot1 p {
  display: flex;
  font: Rubik, Regular;
  font-size: 14px;
  color: #0a0606;
  margin-top: -22px;
  margin-left: 60%;
  opacity: 100%;
  gap: 1px;
}
.dot2 i {
  display: flex;
  font-size: 20px;
  margin-top: 15px;
  margin-left: -1%;
  color: #29efc4;
}
.dot2 p {
  display: flex;
  font: Rubik, Regular;
  margin-top: -22px;
  margin-left: 8%;
  gap: 1px;
}
.dot3 i {
  display: flex;
  font-size: 20px;
  margin-top: -19px;
  margin-left: 50%;
  color: #30cdab;
}
.dot3 p {
  display: flex;
  font: Rubik, Regular;
  margin-top: -22px;
  margin-left: 60%;
  gap: 1px;
}

.bar3 {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 20px;
  margin-bottom: 15px;
  padding: 35px;
}

/* CSS FOR ROW3 */
.row3 {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  /* background: rgb(20, 187, 233); */

  min-height: 400px;
  justify-content: space-evenly;
}
.sleepbar {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 0.5px solid #ededed;
  border-radius: 20px;
  opacity: 1;
  margin-bottom: 15px;
  padding: 10px 0px 90px 0px;
}

.stollers {
  display: flex;
}
.txt p {
  display: flex;
  font: Rubik, Regular;
  font-size: 14px;
  color: #0a0606;
  font-size: 14px;
  margin-bottom: 10px;
}
.ico {
  display: flex;
  float: right;
  margin-top: -30px;
  margin-left: -50px;
  margin-bottom: 10px;
}
.strollers1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 0.5px solid #ededed;
  border-radius: 20px;
  opacity: 1;
  margin-bottom: 15px;
  padding: 0px;
}
.strollers1 p {
  font: normal normal normal 12px/14px Rubik;
  color: #000000;
  opacity: 50%;
}
.strollers1 h3 {
  font: normal normal normal 18px/22px Rubik;
  color: #000000;
}
.strollers1 img {
  display: grid;
  width: 70px;
  height: 70px;
  margin-top: -50px;
  margin-left: 90px;
}
.strollers2 {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 0.5px solid #ededed;
  border-radius: 20px;
  opacity: 1;
  margin-bottom: 15px;
}
.strollers2 p {
  font: normal normal normal 12px/14px Rubik;
  color: #000000;
  opacity: 50%;
}
.strollers2 h3 {
  font: normal normal normal 18px/22px Rubik;
  color: #000000;
}
.strollers2 img {
  display: grid;
  width: 70px;
  height: 70px;
  margin-top: -50px;
  margin-left: 90px;
}
.strollers3 {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 0.5px solid #ededed;
  border-radius: 20px;
  opacity: 1;
  margin-bottom: 15px;
}
.strollers3 p {
  font: normal normal normal 12px/14px Rubik;
  color: #000000;
  opacity: 50%;
}

.strollers3 h3 {
  font: normal normal normal 18px/22px Rubik;
  color: #000000;
}
.strollers3 img {
  display: grid;
  width: 70px;
  height: 70px;
  margin-top: -50px;
  margin-left: 90px;
}
.strollers4 {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 0.5px solid #ededed;
  border-radius: 20px;
  opacity: 1;
  margin-bottom: 15px;
}
.strollers4 p {
  font: normal normal normal 12px/14px Rubik;
  color: #000000;
  opacity: 50%;
}
.strollers4 h3 {
  font: normal normal normal 18px/22px Rubik;
  color: #000000;
}
.strollers4 img {
  display: grid;
  width: 70px;
  height: 70px;
  margin-top: -50px;
  margin-left: 90px;
}
.main {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 10px;
  min-height: 400px;
  justify-content: space-evenly;
  background: transparent linear-gradient(129deg, #5087ff 0%, #29efc4 100%);
  border-radius: 20px;
}
.main p {
  display: flex;
  margin-top: -50px;
  color: #ffffff;
  margin-left: 30px;
}
.c1 {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 0.5px solid #ededed;
  border-radius: 20px;
  opacity: 1;
  margin-top: 15px;
  margin-bottom: 15px;
}
.c1 p {
  display: flex;
  font: Rubik, Regular;
  font-size: 12px;
  color: #000000;
  opacity: 50%;
  margin-top: -5px;
}
.c1 h4 {
  font: Rubik, Regular;
  font-size: 14px;
  color: #000000;
  opacity: 100%;
  margin-top: 5px;
}
.c2 {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 0.5px solid #ededed;
  border-radius: 20px;
  opacity: 1;
  margin-bottom: 15px;
}
.c2 p {
  display: flex;
  font: Rubik, Regular;
  color: #000000;
  font-size: 12px;
  opacity: 50%;
  margin-top: -5px;
}

.c3 {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 0.5px solid #ededed;
  border-radius: 20px;
  opacity: 1;
  margin-bottom: 15px;
}
.c3 p {
  display: flex;
  font: Rubik, Regular;
  font-size: 12px;
  color: #000000;
  opacity: 50%;
  margin-top: -10px;
}
.c4 {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 0.5px solid #ededed;
  border-radius: 20px;
  opacity: 1;
  margin-bottom: 15px;
}
.c4 p {
  display: flex;
  font: Rubik, Regular;
  color: #000000;
  font-size: 12px;
  opacity: 50%;
  margin-top: -10px;
  margin-left: 35px;
}
.c5 {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 0.5px solid #ededed;
  border-radius: 20px;
  opacity: 1;
  margin-bottom: 15px;
}
.c5 p {
  display: flex;
  font: Rubik, Regular;
  font-size: 12px;
  color: #000000;
  opacity: 50%;
  margin-top: -10px;
  margin-left: 30px;
}
.row4 {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  /* background: blue; */
  padding: 10px;
  min-height: 400px;
  margin-top: 15px;
  justify-content: space-evenly;
}
.c6 {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 20px;
  margin-bottom: 15px;
}
.c6 p {
  color: #000000;
}

.c7 {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 20px;
}

/* tab 3 */

.rows1 {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  /* background: blue; */
  padding: 10px;
  min-height: 400px;
  justify-content: space-evenly;
}

.cards1 {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 0.5px solid #ededed;
  border-radius: 20px;
  opacity: 1;
  margin-bottom: 15px;
  padding: 20px;
}

.cards2 {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 0.5px solid #ededed;
  border-radius: 20px;
  opacity: 1;
  margin-bottom: 15px;
}

.cards3 {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff 0% 0% no-repeat padding-box;
  border: 0.5px solid #ededed;
  border-radius: 20px;
  opacity: 1;
}

.rows2 {
  display: flex;
  justify-content: space-between;
}

.box {
  margin-top: 1%;
  left: 4%;
  width: 100%;
  height: 65%;
  background: transparent linear-gradient(110deg, #5087ff 0%, #29efc4 100%) 0%
    0% no-repeat padding-box;
  border: 0.5px solid #ededed;
  border-radius: 20px;
  opacity: 1;
}
.box p {
  top: 450px;
  left: 300px;
  height: 5px;
  font-size: 6px;
  text-align: left;
  font: normal normal normal 14px/17px Rubik;
  letter-spacing: 0.7px;
  color: #ffffff;
  text-transform: uppercase;
  opacity: 1;
}

.box1 {
  top: 601px;
  left: 390px;
  width: 926px;
  height: 93px;
  opacity: 1;
}

.box2 {
  top: 701px;
  left: 390px;
  width: 926px;
  height: 103px;
  opacity: 1;
}

.pie {
  margin-top: 5px;
  opacity: 1;
  margin-left: 55px;
  width: 186px;
  height: 190px;
  display: flex;
}

.dots {
  display: flex;
  flex-direction: column;
  text-decoration: left;
  margin-top: 9px;
}

.dots1 {
  display: flex;

  margin-top: 8px;
  margin-left: 6px;
}

.dots2 {
  display: flex;
  /* align-items: right; */
  margin-top: 6px;
  margin-left: 6px;
  margin-bottom: 10px;
}

.dots3 {
  display: flex;
  float: right;
  align-items: center;
  margin-top: -70px;
  margin-right: 8px;
  margin-bottom: 40px;
}

.dots4 {
  display: flex;
  float: right;
  align-items: center;
  margin-top: -40px;
  margin-right: -4px;
  margin-bottom: 40px;
}

.dots1 i {
  display: flex;
  margin-top: 3%;
  margin-left: -17px;
  color: #2a77f4;
}

.dots2 i {
  display: flex;
  float: left;
  margin-left: -17px;
  margin-top: 5px;
  color: #29efc4;
}

.dots3 i {
  margin-top: 8px;
  margin-left: 18px;
  color: #5a96f8;
}

.dots4 i {
  margin-top: 10px;
  margin-left: 17px;
  color: #29efc4;
}

.dots1 p {
  display: flex;
  margin-top: 5px;
  margin-left: 5px;
}

.dots2 p {
  display: flex;
  margin-top: 1px;
  margin-left: 5px;
}

.dots3 p {
  display: flex;
  margin-right: 15px;
  margin-left: 5px;
  margin-top: 7px;
}
.dots4 p {
  display: flex;
  margin-right: 5px;
  margin-left: 4px;
  margin-top: 7px;
}

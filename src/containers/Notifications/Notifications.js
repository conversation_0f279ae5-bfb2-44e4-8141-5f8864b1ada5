import React, { useEffect } from 'react';
import { isArray, isObject, isEmpty, map } from 'lodash';
import moment from 'moment';
import { useSelector, useDispatch } from 'react-redux';
import Notification from '@chill/components/Notification';
import LayoutWrapper from '@chill/components/utility/layoutWrapper';
import PageHeader from '@chill/components/utility/pageHeader';
import Button from '@chill/components/uielements/button';
import IntlMessages from '@chill/components/utility/intlMessages';
import PopDeleteConfirm from '@chill/components/PopDeleteConfirm/PopDeleteConfirm';
import { CardWrapper, Box } from '@chill/assets/styles/drawerFormStyles';
import getApiData from '@chill/lib/helpers/apiHelper';
import notiActions from '@chill/redux/notification/actions';
import { getSortOrder } from '@chill/lib/helpers/utility';
import TableWrapper from '@chill/assets/styles/AntTables.styles';
import siteConfig from '@chill/config/site.config';
import { EyeOutlined } from '@ant-design/icons';
import LinkBox from '@chill/components/LinkBox';
import Tooltip from '@chill/components/uielements/tooltip';
import getRedirectUrl, { hideNotiTypes } from './config';

const { updateTopNotification } = notiActions;

export default function Notifications() {
  const dispatch = useDispatch();

  const noti = useSelector((state) => state.Notification.notification);
  const notiVar = isObject(noti) ? noti : {};
  const [notiState, setnotiState] = React.useState({
    notifications: [],
    notificationLoad: true,
  });
  const [selected, setSelected] = React.useState([]);
  const [filters, setFilters] = React.useState({});

  const [pagination, setPagination] = React.useState({});

  async function fetchData(flt = {}, type = '') {
    setnotiState((pre) => ({
      ...pre,
      notificationLoad: true,
    }));
    try {
      const res = await getApiData('notification/get-notification', flt);
      if (res.success && isArray(res.data)) {
        setnotiState({
          notifications: isArray(res.data) ? res.data : [],
          notificationLoad: false,
        });
        const paginationVar =
          filters && filters.pagination ? { ...filters.pagination } : {};
        paginationVar.total = res.total_count || 0;
        paginationVar.pageSize = res.per_page || 10;
        paginationVar.current = res.page || 1;
        setFilters((f) => ({ ...f, page: res.page || 1 }));
        setPagination(paginationVar);

        // Clear notification count
        if (type === 'init') {
          dispatch(updateTopNotification({}, 'clear', 0));
        }
      } else {
        setnotiState({
          notifications: [],
          notificationLoad: false,
        });
      }
    } catch (err) {
      setnotiState({
        notifications: [],
        notificationLoad: false,
      });
      Notification('error');
    }
  }

  useEffect(() => {
    fetchData({}, 'init');
  }, []);

  useEffect(() => {
    // Refresh list when new a notification is arrived
    if (
      isObject(notiVar) &&
      !isEmpty(notiVar) &&
      hideNotiTypes.indexOf(notiVar.type) < 0
    )
      fetchData();
  }, [notiVar]);

  async function deleteNoti(id) {
    setnotiState((pre) => ({
      ...pre,
      notificationLoad: true,
    }));
    try {
      const res = await getApiData(
        'notification/delete',
        { notification_id: id },
        'POST',
      );
      let msgTitle = 'error';
      if (res.success) {
        msgTitle = 'success';
        fetchData(filters);
      } else {
        setnotiState((pre) => ({
          ...pre,
          notificationLoad: false,
        }));
      }
      Notification(msgTitle, res.message);
    } catch (err) {
      setnotiState((pre) => ({
        ...pre,
        notificationLoad: false,
      }));
      Notification('error');
    }
  }

  function renderViewBtn(data = {}) {
    const rUrl = getRedirectUrl(data);
    return (
      <LinkBox link={rUrl ? `/${rUrl}` : null}>
        <Button
          disabled={!rUrl}
          shape="circle"
          icon={<EyeOutlined />}
          className="invoiceViewBtn"
        />
      </LinkBox>
    );
  }

  const columns = [
    {
      key: 'sort_created',
      title: <IntlMessages id="common.date" />,
      dataIndex: 'createdAt',
      rowKey: 'createdAt',
      width: 175,
      sorter: true,
      className: 'fullname-cell',
      render: (text) => {
        const date = text
          ? moment(text).format(`${siteConfig.dateFormat} hh:mm a`)
          : '';
        return (
          <div>
            <span className="label">
              <IntlMessages id="common.date" />
            </span>
            <span className="mobile-lbl-val">{date || '-'}</span>
          </div>
        );
      },
    },
    {
      key: 'message',
      title: <IntlMessages id="common.message" />,
      dataIndex: 'message',
      rowKey: 'message',
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="common.message" />
            </span>
            <span
              className="wSpaceWrap"
              // eslint-disable-next-line react/no-danger
              dangerouslySetInnerHTML={{ __html: text || '-' }}
            />
          </div>
        );
      },
    },
    {
      key: 'document_name',
      title: <IntlMessages id="documents.doc" />,
      dataIndex: 'document_name',
      rowKey: 'document_name',
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="documents.doc" />
            </span>
            <span className="mobile-lbl-val">
              <div className="nameEmailMain">
                <Tooltip
                  className="iNum normal"
                  placement="topLeft"
                  title={text}
                >
                  {text || '-'}
                </Tooltip>
              </div>
            </span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="common.action" />,
      dataIndex: 'edit',
      rowKey: 'edit',
      width: 120,
      fixed: 'right',
      className: 'action-cell',
      render: (_, data) => (
        <div className="isoInvoiceBtnView">
          {renderViewBtn(data)}
          <PopDeleteConfirm
            apiUrl="notification/delete"
            apiData={{ notification_id: data.id }}
            fetchData={fetchData}
            onConfirm={() => deleteNoti([data.id])}
          />
        </div>
      ),
    },
  ];

  function onChange(pgnation, flts, sorter) {
    const flt = { ...filters };
    if (sorter && sorter.columnKey) {
      flt[sorter.columnKey] = getSortOrder(sorter.order);
    }
    if (pgnation) {
      flt.page = pgnation.current;
      setPagination(pgnation);
    }
    setFilters(flt);
    fetchData(flt);
  }

  const rowSelection = {
    width: 50,
    hideDefaultSelections: true,
    selectedRowKeys: selected,
    onChange: (sel) => setSelected(sel),
    selections: [
      {
        key: 'all-data',
        text: <IntlMessages id="common.selectAll" />,
        onSelect: () => setSelected(map(notiState.notifications, 'id')),
      },
      {
        key: 'no-data',
        text: <IntlMessages id="common.unselectAll" />,
        onSelect: () => setSelected([]),
      },
      {
        key: 'delete-selected',
        text: <IntlMessages id="common.deleteSelected" />,
        onSelect: () => {
          if (selected.length > 0) {
            deleteNoti(selected);
          }
        },
      },
    ],
    onSelection: (sel) => setSelected(sel),
  };

  return (
    <LayoutWrapper>
      <PageHeader>
        <IntlMessages id="sidebar.notification" />
      </PageHeader>
      <Box>
        <CardWrapper>
          <div className="isoInvoiceTable">
            <TableWrapper
              scroll={{ x: 1400 }}
              rowSelection={rowSelection}
              loading={notiState.notificationLoad}
              rowKey={(record) => record.id}
              dataSource={notiState.notifications}
              onChange={onChange}
              columns={columns}
              pagination={{ ...pagination, showSizeChanger: false }}
              className="invoiceListTable"
              showSorterTooltip={false}
            />
          </div>
        </CardWrapper>
      </Box>
    </LayoutWrapper>
  );
}

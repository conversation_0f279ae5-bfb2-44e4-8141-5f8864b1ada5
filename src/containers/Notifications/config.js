import React, { useEffect } from 'react';
import { isEmpty, isObject } from 'lodash';
import { notification } from 'antd';
import trans from '@chill/config/translation/translate';
import Button from '@chill/components/uielements/button';
import { useHistory } from 'react-router';
import { useSelector } from 'react-redux';

// TODO: Add notification types here If you dont want to display that notification
const hideNotiTypes = [
  'update_documents',
  'update_sign_request',
  'update_document_details',
  'update_shared_document',
  'update_security_settings',
  'update_workflow_list',
  'update_workflow_details',
  'update_workflow_shared_list',
  'update_security_settings_app',
];

// TODO: Add notification types here If you dont want to append counts in Topbar Notfication Badge
const staticNotifications = ['notification'];

function getRedirectUrl(data = {}) {
  const nObj = isObject(data) ? data : {};

  const signReq = ['signed', 'sent', 'declined'];

  if (nObj.type === 'notification') return '';
  if (nObj.type === 'share') return `dashboard/shared-documents`;
  if (nObj.type === 'workflow') {
    if (nObj.link) return `dashboard/create-project/${nObj.link}`;
    return `dashboard/my-projects`;
  }
  if (nObj.type === 'share-workflow') {
    if (nObj.link) return `dashboard/project/${nObj.link}`;
    return `dashboard/shared-projects`;
  }
  if (signReq.indexOf(nObj.type) > -1) return 'dashboard/signing-requests';
  if (nObj.type === 'sign-received') {
    if (nObj.link) return `document/${nObj.link}/sign`;
    return 'dashboard/signing-requests';
  }
  if (nObj.type === 'digital-payment') {
    return 'dashboard/account-settings?active=billing&bactive=digital';
  }
  if (nObj.type === 'plan-detail') {
    return 'dashboard/account-settings?active=billing';
  }
  return 'dashboard/notifications';
}

function useNotification() {
  const history = useHistory();
  const noti = useSelector((state) => state.Notification.notification);

  useEffect(() => {
    const notiVar = isObject(noti) ? noti : {};
    const type = notiVar.type || '';
    const redUrl = getRedirectUrl(notiVar);

    if (!isEmpty(notiVar) && hideNotiTypes.indexOf(type) < 0) {
      const key = `open_${Date.now()}`;
      try {
        notification.open({
          type: notiVar.status || null,
          message: trans(notiVar.title) || trans('notification'),
          description: (
            <p
              // eslint-disable-next-line react/no-danger
              dangerouslySetInnerHTML={{
                __html: trans(notiVar.description) || '',
              }}
            />
          ),
          key,
          btn: !redUrl ? null : (
            <Button
              type="primary"
              size="small"
              onClick={(e) => {
                e.preventDefault();
                notification.close(key);

                setTimeout(() => {
                  history.push({
                    pathname: `/${redUrl}`,
                    query: { notiVar },
                  });
                }, 200);
              }}
            >
              {trans('common.view')}
            </Button>
          ),
        });
      } catch (err) {
        console.log(err);
      }
    }
  }, [noti]);
}

export { hideNotiTypes, useNotification, staticNotifications };
export default getRedirectUrl;

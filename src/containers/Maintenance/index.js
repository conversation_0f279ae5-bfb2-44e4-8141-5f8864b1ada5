/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
import React, { useEffect, useState } from 'react';
import { Col, Row, Divider, Select } from 'antd';
import { isArray, isEmpty, isObject } from 'lodash';
import { useSelector } from 'react-redux';
import {
  FilterOutlined,
  PlusCircleOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import theme from '@chill/config/theme/default';
import TableWrapper from '@chill/assets/styles/AntTables.styles';
import IntlMessages from '@chill/components/utility/intlMessages';
import Popover from '@chill/components/uielements/popover';
import getApiData from '@chill/lib/helpers/apiHelper';
import Popconfirms from '@chill/components/Feedback/Popconfirm';
import Notification from '@chill/components/Notification';
import { metricTypes, ruleTypes } from '@chill/lib/helpers/utilityData';
import MaintenanceManagementForm from './MaintenanceForm';
import MaintenanceStyles from './Maintenance.styles';

/**
 *
 * @module Maintenance
 */

const Maintenance = () => {
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const [actionMenu, setActionMenu] = React.useState(false);
  const [isIndex, setIsIndex] = React.useState(null);
  const [state, setState] = React.useState({
    initObj: {},
    visible: false,
  });
  const [maintenanceState, setMaintenanceState] = React.useState({
    maintenances: [],
    maintenanceLoad: false,
  });
  const [filter, setFilter] = React.useState({ pagination: {}, filters: {} });
  const [filterMode, setFilterMode] = React.useState(false);
  const [sort, setSort] = React.useState(false);
  const [groupList, setGroupList] = useState([]);
  const [currentPage, setCurrentPage] = React.useState(0);

  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  const getGroupList = async () => {
    const response = await getApiData('manage-group/index?allData=1');
    if (response.success) {
      setGroupList(response.data);
      return response.data;
    }
    return [];
  };

  // this function for get maintenance's
  /** this function for get maintenance's list
   * @function getMaintenanceList
   * @param {object} data sort
   */
  async function getMaintenanceList(data = {}) {
    setMaintenanceState((p) => ({
      ...p,
      maintenanceLoad: true,
    }));

    const url = 'manage-maintenance/index';

    try {
      const response = await getApiData(url, data);
      if (response.success && isArray(response.data)) {
        const groupData = await getGroupList();
        response.data.forEach((mData) => {
          const _Mdata = mData;
          _Mdata.metric =
            metricTypes.find((metric) => metric.value === _Mdata.metric_type)
              ?.text || '';
          _Mdata.rule =
            ruleTypes.find((rule) => rule.value === _Mdata.rule_type)?.text ||
            '';
          _Mdata.groupName =
            groupData.find((group) => group.id === _Mdata.buggy_group_id)
              ?.group_name || '';
          return _Mdata;
        });
        setMaintenanceState((preState) => ({
          ...preState,
          maintenances: isArray(response.data) ? response.data : [],
          maintenanceLoad: false,
        }));
        const pagination =
          filter && filter.pagination ? { ...filter.pagination } : {};
        pagination.total = response.total_count || 0;
        pagination.pageSize = response.per_page || 10;
        pagination.current = response.page || 1;
        setFilter((f) => ({ ...f, pagination }));
      } else {
        setMaintenanceState((preState) => ({
          ...preState,
          maintenanceLoad: false,
        }));
      }
    } catch (err) {
      setMaintenanceState((preState) => ({
        ...preState,
        maintenanceLoad: false,
      }));
    }
  }

  // this function filters data and get updated list of maintenance
  /** this function filters data and get updated list of maintenance
   * @function fetchDataFilters
   * @param {object} data page, title
   */
  function fetchDataFilters() {
    const page =
      filter && filter.pagination && filter.pagination.current
        ? filter.pagination.current
        : 1;
    const filters = filter && filter.filters ? filter.filters : {};
    getMaintenanceList({ page, ...filters });
  }

  // this function for delete maintenance
  /** this function for delete maintenance
   * @function deleteMaintenance
   * @param {object} data maintenance_id
   */
  async function deleteMaintenance() {
    const data = {
      maintenance_id:
        isObject(state) &&
        isObject(state.initObj) &&
        !isEmpty(state.initObj) &&
        state.initObj.id,
    };
    const maintenanceAry = isArray(maintenanceState.maintenances)
      ? maintenanceState.maintenances
      : [];
    setMaintenanceState({
      maintenanceLoad: true,
    });
    try {
      const response = await getApiData(
        'manage-maintenance/delete-maintenance',
        data,
        'POST',
      );
      let msgTitle = 'error';
      if (response.success) {
        msgTitle = 'success';
        fetchDataFilters();
      } else {
        setMaintenanceState((pre) => ({
          ...pre,
          maintenances: maintenanceAry,
          maintenanceLoad: false,
        }));
      }
      Notification(msgTitle, response.message);
    } catch (error) {
      console.log('error ===', error);
      setMaintenanceState((pre) => ({
        ...pre,
        maintenances: maintenanceAry,
        maintenanceLoad: false,
      }));
    }
  }

  // fun. for visible PopOver
  function handleVisible() {
    setActionMenu((visiblePre) => !visiblePre);
  }

  // this function for handle pagination
  /** this function for handle pagination
   * @function onChange
   * @param {object} data page
   */
  const onChange = (pagination) => {
    const pager = filter && filter.pagination ? { ...filter.pagination } : {};
    setCurrentPage((pagination.current - 1) * pagination.pageSize);
    pager.current = pagination.current;
    setFilter((f) => ({ ...f, pagination: pager }));
    getMaintenanceList({
      page: pagination.current,
      ...filter.filters,
    });
    setSort(false);
  };

  // Popover content or list
  const content = (
    <MaintenanceStyles>
      <div className="popMainDiv">
        <div
          role="button"
          className="isoDropdownLink"
          onKeyPress=""
          tabIndex="-1"
          onClick={() => {
            setState({ ...state, visible: true });
            setIsIndex(null);
          }}
        >
          <EditOutlined style={{ paddingRight: 12, fontSize: 16 }} />
          <IntlMessages id="maintenance.edit" />
        </div>
        <div className="isoDropdownLink">
          <Popconfirms
            title={<IntlMessages id="Sure to delete?" />}
            okText={<IntlMessages id="DELETE" />}
            cancelText={<IntlMessages id="No" />}
            onConfirm={() => {
              deleteMaintenance();
              setIsIndex(null);
            }}
            onCancel={null}
          >
            <DeleteOutlined style={{ paddingRight: 12, fontSize: 16 }} />
            <IntlMessages id="maintenance.delete" />
          </Popconfirms>
        </div>
      </div>
    </MaintenanceStyles>
  );

  // set item id in state
  function handleVisibleChange(item1) {
    setIsIndex(item1.id);
  }

  const columns = [
    {
      title: <IntlMessages id="ID" />,
      dataIndex: 'id',
      rowKey: 'id',
      width: 10,
      className: 'fullname-cell',
      render: (text, data, index) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="ID" />
            </span>
            <span className="mobile-lbl-val">
              {index + currentPage + 1 || '-'}
            </span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="antTable.title" />,
      dataIndex: 'title',
      rowKey: 'title',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="antTable.title" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="common.buggyGroup" />,
      dataIndex: 'groupName',
      rowKey: 'groupName',
      width: 180,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="common.buggyGroup" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="antTable.metric" />,
      dataIndex: 'metric',
      rowKey: 'metric',
      width: 180,
      className: 'fullname-cell',
      render: (text, data) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="antTable.metric" />
            </span>
            <span className="mobile-lbl-val">
              {data.metric_value || '-'} {data.metric || '-'}
            </span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="antTable.rule" />,
      dataIndex: 'rule',
      rowKey: 'rule',
      width: 180,
      className: 'fullname-cell',
      render: (text, data) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="antTable.rule" />
            </span>
            <span className="mobile-lbl-val">
              {data.rule || '-'} {data.rule_value || '-'}
            </span>
          </div>
        );
      },
    },
    {
      title: <IntlMessages id="Action" />,
      dataIndex: 'action',
      rowKey: 'action',
      className: 'fullname-cell',
      width: 50,
      fixed: 'right',
      render: (text, item) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Action" />
            </span>
            <span className="mobile-lbl-val">
              <Popover
                content={content}
                trigger="click"
                visible={isIndex === item.id ? !actionMenu : false}
                onVisibleChange={() => handleVisible()}
                arrowPointAtCenter
                placement="bottomRight"
              >
                <MoreOutlined
                  onClick={() => {
                    handleVisibleChange(item);
                    setState({ ...state, initObj: item });
                  }}
                  style={{ color: '#000', fontSize: 24 }}
                />
              </Popover>
            </span>
          </div>
        );
      },
    },
  ];

  // Add Maintenance View
  const addNew = () => {
    return (
      <MaintenanceStyles>
        <div className="iconStyle marketingMainDiv">
          <span>
            <p
              className="addNewUser"
              style={{
                color: theme.colors.primaryColor,
                alignItems: 'center',
              }}
            >
              <FilterOutlined
                className="filterIcon"
                style={{
                  color: filterMode ? theme.colors.primaryColor : '#000',
                }}
                onClick={() => {
                  setFilterMode((pre) => !pre);
                  setFilter({ filters: {} });
                }}
              />
              <div
                role="button"
                tabIndex="0"
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                }}
                onClick={() => setState({ visible: true })}
                onKeyDown={() => setState({ visible: true })}
              >
                <p className="iconPadding" style={{ paddingRight: 8 }}>
                  <PlusCircleOutlined />
                </p>
                <IntlMessages id="maintenance.add" />
              </div>
            </p>
          </span>
        </div>
      </MaintenanceStyles>
    );
  };

  // this function renders search view
  function renderSearchBar() {
    return (
      <div style={{ padding: '15px' }}>
        <Row gutter={10}>
          <Col lg={8} sm={24} md={12} xs={24} style={{ marginBottom: 8 }}>
            <Select
              style={{ width: '100%' }}
              showSearch
              onChange={(e) => {
                const val = e;
                const flt = filter ? { ...filter } : {};
                flt.filters.buggy_group_id = val;
                setFilter(flt);
                getMaintenanceList(flt.filters);
              }}
              value={filter.buggy_group_id}
              allowClear
              options={groupList}
              placeholder={messageArray['common.gpName']}
              fieldNames={{ label: 'group_name', value: 'id' }}
            />
          </Col>
        </Row>
      </div>
    );
  }

  useEffect(() => {
    fetchDataFilters();
  }, [sort]);

  const { initObj, visible } = state;
  return (
    <MaintenanceStyles>
      <Row className="isoTabRow">
        <Col xl={8} lg={8} sm={8} md={4} xs={8}>
          <div className="marketingText">
            <IntlMessages id="sidebar.maintenance" />
          </div>
        </Col>
        <Col sm={16} xs={16} className="hiddenCol">
          {addNew()}
        </Col>
        <Col xl={16} lg={16} md={20} xs={16} className="isoAddNew">
          {addNew()}
        </Col>
      </Row>
      <Divider className="horizontalDevider" />
      {filterMode ? renderSearchBar() : null}
      <TableWrapper
        loading={maintenanceState.maintenanceLoad}
        rowKey={(record) => record.id}
        dataSource={maintenanceState.maintenances}
        onChange={onChange}
        columns={columns}
        className="invoiceListTable"
        pagination={filter.pagination || {}}
        showSorterTooltip={false}
      />
      <MaintenanceManagementForm
        initialValues={initObj}
        visible={visible}
        onClose={(type) => {
          if (type === 'success') {
            fetchDataFilters();
            setState({ initObj: {}, visible: false });
          } else {
            setState({ initObj: {}, visible: false });
          }
        }}
      />
    </MaintenanceStyles>
  );
};

export default Maintenance;

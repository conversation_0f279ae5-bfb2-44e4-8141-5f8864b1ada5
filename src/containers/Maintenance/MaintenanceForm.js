/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
/* eslint-disable no-nested-ternary */
import React, { useEffect, useState } from 'react';
import { Col, Drawer, Input, Row, Form, Select, Upload } from 'antd';
import { isEmpty } from 'lodash';
import { useSelector } from 'react-redux';
import IntlMessages from '@chill/components/utility/intlMessages';
import {
  BottomViewWrapper,
  FormWrapper,
} from '@chill/assets/styles/drawerFormStyles';
import CButton from '@chill/components/uielements/CButton';
import Notification from '@chill/components/Notification';
import getApiData from '@chill/lib/helpers/apiHelper';
import useResetFormOnClose from '@chill/lib/hooks/useResetForm';
import { ruleTypes, metricTypes } from '@chill/lib/helpers/utilityData';
import Icon, { PaperClipOutlined, UploadOutlined } from '@ant-design/icons';

function MaintenanaceForm(props) {
  const { initialValues, onClose, visible, view } = props;
  const edit = !isEmpty(initialValues);
  const initVal = initialValues || {};
  const [form] = Form.useForm();
  const [btnLoader, setBtnLoader] = useState(false);
  const [selectedPdf, setSelectedPdf] = useState(null);
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);
  const [groupList, setGroupList] = useState([]);

  useResetFormOnClose({ visible, initVal, form });

  const getGroupList = async () => {
    const response = await getApiData('manage-group/index?allData=1');
    if (response.success) {
      setGroupList(response.data);
    }
  };

  useEffect(() => {
    if (!edit) {
      form.resetFields();
    }
    if (visible) {
      getGroupList();
    }
  }, [visible]);

  // this function reset form data when close modal
  function handleForm(type = '', data = {}) {
    form.resetFields();
    onClose(type, data);
    setSelectedPdf({});
  }

  // this function for add new faqs
  async function addMaintenance(values) {
    const formData = new FormData();
    const url = edit
      ? 'manage-maintenance/update-maintenance'
      : 'manage-maintenance/create-maintenance';

    Object.keys(values).map((k) => formData.append(k, values[k]));
    if (!isEmpty(selectedPdf)) {
      formData.append('attachment', selectedPdf);
    }

    try {
      const response = await getApiData(url, formData, 'POST', {}, true);
      if (response.success) {
        Notification('success', response.message);
        handleForm('success', response.data);
      } else {
        Notification('error', response.message);
      }
      setBtnLoader(false);
    } catch (error) {
      console.log(error);
      setBtnLoader(false);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  // this function for validate data
  function validate(values) {
    const obj = values;
    if (edit) obj.maintenance_id = initVal.id;
    setBtnLoader(true);
    addMaintenance(obj);
  }

  const beforePdfUpload = (file) => {
    const validType =
      file.type === 'application/pdf' || file.type === 'video/mp4';
    if (!validType) {
      Notification('error', messageArray['common.pdf&video.upload']);
      return false;
    }
    setSelectedPdf(file);
    return false;
  };

  const uploadPdfButton = (
    <div>
      <Icon type="plus" />
      <div className="ant-upload-text">
        <UploadOutlined /> <IntlMessages id="common.uploadFile" />
      </div>
    </div>
  );

  return (
    <Drawer
      title={
        <IntlMessages id={edit ? 'maintenance.edit' : 'maintenance.add'} />
      }
      width={500}
      style={{ height: window.innerHeight - 60, marginTop: 70 }}
      onClose={() => handleForm()}
      visible={visible}
      bodyStyle={{ paddingBottom: 80 }}
      destroyOnClose
    >
      <FormWrapper>
        <Form
          form={form}
          onFinish={(e) => validate(e)}
          layout="vertical"
          initialValues={initVal}
        >
          <Row
            gutter={[12, 20]}
            style={{
              marginBottom: 20,
            }}
          >
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="common.title" />}
                name="title"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.enterTitle" />,
                  },
                ]}
              >
                <Input placeholder="Title" />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="common.buggyGroup" />}
                name="buggy_group_id"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.buggyGroup" />,
                  },
                ]}
              >
                <Select
                  placeholder={<IntlMessages id="common.search.buggyGroup" />}
                >
                  {groupList.map((status) => {
                    if (status.disabled) return null;
                    return (
                      <Select.Option key={status.id} value={status.id}>
                        {status.group_name}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="common.ruleType" />}
                name="rule_type"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.ruleType" />,
                  },
                ]}
              >
                <Select
                  placeholder={<IntlMessages id="common.search.ruleType" />}
                >
                  {ruleTypes.map((status) => {
                    if (status.disabled) return null;
                    return (
                      <Select.Option key={status.value} value={status.value}>
                        {status.text}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="common.ruleValue" />}
                name="rule_value"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.ruleValue" />,
                  },
                ]}
              >
                <Input type="number" placeholder="Rule Value" />
              </Form.Item>
            </Col>
            <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="common.metricType" />}
                name="metric_type"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.metricType" />,
                  },
                ]}
              >
                <Select
                  placeholder={<IntlMessages id="common.search.metricType" />}
                >
                  {metricTypes.map((status) => {
                    if (status.disabled) return null;
                    return (
                      <Select.Option key={status.value} value={status.value}>
                        {status.text}
                      </Select.Option>
                    );
                  })}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="common.metricValue" />}
                name="metric_value"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.metricValue" />,
                  },
                ]}
              >
                <Input type="number" placeholder="Metric Value" />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="input.desc.label" />}
                name="description"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.description" />,
                  },
                ]}
              >
                <Input.TextArea rows={3} placeholder="Description..." />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item label={<IntlMessages id="notification.attachment" />}>
                <Upload
                  className="usermanualstyle"
                  showUploadList={false}
                  listType="picture-card"
                  beforeUpload={beforePdfUpload}
                  disabled={!!view}
                  style={{ width: '100%' }}
                >
                  {/* {uploadButton} */}
                  {(selectedPdf && !isEmpty(selectedPdf)) ||
                  (initVal && !isEmpty(initVal?.attachment)) ? (
                    <Row>
                      <Col xs={24}>
                        <PaperClipOutlined style={{ fontSize: 50 }} />
                      </Col>
                      <Col xs={24}>
                        <div className="_uploadTextContainer">
                          <div className="_uploadText">
                            {selectedPdf && !isEmpty(selectedPdf)
                              ? selectedPdf?.name
                                ? selectedPdf?.name
                                : initVal &&
                                  !isEmpty(initVal) &&
                                  initVal?.file_meta &&
                                  !isEmpty(initVal?.file_meta)
                                ? initVal?.file_meta?.filename
                                : ''
                              : initVal?.attachment}
                          </div>
                        </div>
                      </Col>
                    </Row>
                  ) : (
                    uploadPdfButton
                  )}
                </Upload>
              </Form.Item>
            </Col>
          </Row>
          <BottomViewWrapper className="bottomBtnWrapper">
            <Row gutter={12} style={{ width: '100%' }}>
              <Col xs={24}>
                <Form.Item className="btn-form">
                  <CButton
                    className="submitBtnStyle"
                    htmlType="submit"
                    loading={btnLoader}
                    disabled={btnLoader}
                    style={{ marginRight: 20 }}
                  >
                    <IntlMessages
                      id={edit ? 'common.changes' : 'common.submit'}
                    />
                  </CButton>
                </Form.Item>
              </Col>
              {/* <Form.Item className="btn-form">
                <div
                  className="cancelBtnStyle"
                  onClick={handleForm}
                  role="button"
                  onKeyPress=""
                  tabIndex="-1"
                >
                  <IntlMessages id="common.cancel" />
                </div>
              </Form.Item> */}
            </Row>
          </BottomViewWrapper>
        </Form>
      </FormWrapper>
    </Drawer>
  );
}

export default MaintenanaceForm;

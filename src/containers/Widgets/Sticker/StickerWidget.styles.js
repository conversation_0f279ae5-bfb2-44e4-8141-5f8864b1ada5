/* eslint-disable import/prefer-default-export */
import styled from 'styled-components';
import { borderRadius } from '@chill/lib/helpers/style_utils';

const StickerWidgetWrapper = styled.div`
  width: 100%;
  display: flex;
  align-items: stretch;
  overflow: hidden;
  box-shadow: 20px 10px 30px 10px #000000;
  // margin-top: 20px;
  ${borderRadius('5px')};
  box-shadow: 20px 10px 30px 10px #000000;
  .isoIconWrapper {
    // display: flex;
    padding-top: 20px;
    padding-left: 15px;
    align-items: flex-start;
    justify-content: center;
    width: 30px;
    // flex-shrink: 0;
    // background-color: rgba(0, 0, 0, 0.1);

    & span.anticon {
      font-size: 20px;
      color: #123456;
    }
  }

  .isoContentWrapper {
    width: 100%;
    padding: 20px 0px 0px 0px;
    display: flex;
    flex-direction: column;

    .isoStatNumber {
      font-family: Roboto;
      font-size: 24px;
      font-weight: 500 !important;
      line-height: 28.13px;
      color: rgba(19, 27, 75, 1) !important;
    }

    .isoStatNumberSecond {
      font-size: 14px;
      font-weight: 500 !important;
      color: #01c49a !important;
    }

    .isoLabel {
      font-family: Inter;
      font-size: 14px;
      font-weight: 700;
      margin: 0;
      line-height: 16.41px;
      color: rgba(42, 53, 72, 1) !important;
      text-wrap: nowrap !important;
    }
  }
`;

export { StickerWidgetWrapper };

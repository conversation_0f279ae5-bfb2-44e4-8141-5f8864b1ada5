/* eslint-disable no-nested-ternary */
import React from 'react';
import { StickerWidgetWrapper } from './StickerWidget.styles';

export default function ({
  fontColor,
  bgColor,
  icon,
  number,
  text,
  image,
  fan,
  aqs,
  imgName,
  textPT = 'inherit',
  textBottomPT = 'inherit',
  percentage,
  isUp,
}) {
  const textColor = {
    color: fontColor,
    fontFamily: 'Inter',
  };
  const widgetStyle = {
    backgroundColor: bgColor,
    padding: aqs ? '5px' : '0px',
    // width: '105%',
  };

  return (
    <StickerWidgetWrapper
      className="isoStickerWidget"
      style={{
        ...widgetStyle,
        boxShadow: '0px 20px 45px 0px rgba(240, 237, 246, 1)',
        borderRadius: 10,
        height: 200,
        display: 'flex',
        flexDirection: 'column',
        padding: '15px',
      }}
    >
      {image ? (
        <div className="isoIconWrapper">
          <img
            src={image}
            alt="chat"
            style={{
              width: fan
                ? 50
                : imgName === 'bell'
                ? 24
                : imgName === 'temp'
                ? 30
                : imgName === 'drop'
                ? 25
                : imgName === 'meter'
                ? 20
                : 22,
              height: fan ? 50 : imgName === 'drop' ? 95 : 22,
              marginTop: fan ? -12 : 2,
              marginLeft: fan ? -10 : 0,
            }}
          />
        </div>
      ) : (
        <div
          style={{
            backgroundColor: '#EAEBED',
            padding: '15px',
            borderRadius: '10px',
            width: 50,
            height: 50,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: 15,
          }}
        >
          {icon}
        </div>
      )}

      <div className="isoContentWrapper" style={{ paddingTop: textPT }}>
        <span className="isoLabel" style={textColor}>
          {text}
        </span>
      </div>
      <div className="isoContentWrapper" style={{ paddingTop: textBottomPT }}>
        <h4 className="isoStatNumber" style={textColor}>
          {number || 0}
        </h4>
      </div>
      <div className="isoContentWrapper" style={{ paddingTop: textBottomPT }}>
        <h4
          style={{
            color: isUp ? '#01C49A' : '#E6920A',
            fontSize: '14px',
            fontWeight: 500,
            lineHeight: '16.41px',
            fontFamily: 'Inter',
          }}
        >
          {isUp ? '+' : '-'} {percentage ? Math.abs(percentage) : 0}%
        </h4>
      </div>
    </StickerWidgetWrapper>
  );
}

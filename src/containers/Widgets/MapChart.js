import React, { useState, useEffect } from 'react';
import { isArray, isEmpty } from 'lodash';
import {
  ComposableMap,
  Geographies,
  Geography,
  Marker,
} from 'react-simple-maps';

// const geoUrl = 'https://raw.githubusercontent.com/zcreativelabs/react-simple-maps/master/topojson-maps/world-110m.json';
const geoUrl = 'https://unpkg.com/world-atlas@1/world/110m.json';

const MapChart = (prop) => {
  const { array, setToolTipContent } = prop;
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const [windowHeight, setWindowHeight] = useState(window.innerHeight);

  // Handle window resize for responsiveness
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
      setWindowHeight(window.innerHeight);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Adjust projection scale based on window size
  const getScale = () => {
    if (windowWidth > 1200) return 200;
    if (windowWidth > 800) return 180;
    return 120;
  };

  const getTranslateY = () => {
    if (windowWidth > 1200) return 100;
    if (windowWidth > 800) return 80;
    return 70;
  };

  return (
    <div style={{ width: '100%', height: '100%', marginBottom: '20px' }}>
      <ComposableMap
        data-tip=""
        projection="geoMercator"
        projectionConfig={{
          scale: getScale(),
          translateY: getTranslateY(),
          clipAngle: 90,
          center: [0, 20],
        }}
        width={windowWidth} // Dynamically set width
        height={windowHeight / 2} // Dynamically set height to a portion of the window height
        viewBox={`0 0 ${windowWidth} ${windowHeight / 2}`} // Set responsive viewBox
        style={{ width: '100%', height: '63%', marginTop: 15 }} // Set SVG responsive dimensions
      >
        <Geographies geography={geoUrl}>
          {({ geographies }) =>
            geographies.map((geo) => (
              <Geography
                key={geo.rsmKey}
                geography={geo}
                fill="#04B8FF"
                stroke="#D6D6DA"
              />
            ))
          }
        </Geographies>
        {!isEmpty(array) &&
          isArray(array) &&
          array.map(({ coordinates, key, country, totalCount }) => {
            const color = '#1C1C1C';
            return (
              <Marker
                key={key}
                coordinates={coordinates}
                onMouseEnter={() => {
                  setToolTipContent(`${country}: ${totalCount}`);
                }}
                onMouseLeave={() => {
                  setToolTipContent('');
                }}
              >
                <circle r={5} fill={color} stroke={color} strokeWidth={30} />
              </Marker>
            );
          })}
      </ComposableMap>
    </div>
  );
};

export default MapChart;

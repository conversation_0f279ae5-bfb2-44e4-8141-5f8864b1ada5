import Popover from '@chill/components/uielements/popover';
import { Button } from 'antd';
import React from 'react';

export default function PopOver({
  buttonBackColor,
  colorPicker,
  button,
  onClick = () => {},
}) {
  return (
    <Popover placement="bottom" content={colorPicker} trigger="click">
      {button && (
        <Button
          onClick={() => {
            onClick();
          }}
          className="buttonMargin"
          style={{ backgroundColor: buttonBackColor }}
        >
          {' '}
        </Button>
      )}
    </Popover>
  );
}

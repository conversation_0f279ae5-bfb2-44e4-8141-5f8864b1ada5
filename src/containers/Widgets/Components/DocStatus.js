import React from 'react';
import { List, Progress, Tooltip } from 'antd';
import IntlMessages from '@chill/components/utility/intlMessages';
import { map } from 'lodash';
import { ArrowDownOutlined, ArrowUpOutlined } from '@ant-design/icons';
import { getObjVal } from '@chill/lib/helpers/utility';
import theme from '@chill/config/theme/default';
import LinkBox from '@chill/components/LinkBox';
import IsoWidgetsWrapper from '../WidgetsWrapper';
import ReportsWidget from '../Report/ReportWidget';
import { ListWrapper } from '../Widgets.styles';
import { SIGN_REQS } from '../config';

function DocStatus(props) {
  const { dashData, isTeamUser } = props;

  function renderNumCircle({ type = '', value }) {
    const sent = type === 'sent';
    const totalSent = dashData?.sentRequest?.totalRequest || 0;
    const totalReveived = dashData?.receivedRequest?.totalRequest || 0;
    const totalSignReq = totalSent + totalReveived;
    const per = (100 * value) / totalSignReq;
    return (
      <Tooltip
        placement={sent ? 'left' : 'right'}
        title={<IntlMessages id={`common.${sent ? 'sent' : 'received'}`} />}
      >
        <div className="numContent">
          {sent ? <ArrowUpOutlined /> : <ArrowDownOutlined />}

          <Progress
            type="circle"
            width={80}
            strokeColor={
              sent ? theme.colors.primaryColor : theme.palette.color[3]
            }
            percent={per}
            format={() => value}
          />
        </div>
      </Tooltip>
    );
  }

  /**
   * Renders doc's status
   */
  function renderDocStatus() {
    return map(SIGN_REQS, (widget) => {
      const lnk = widget.link ? `/dashboard${widget.link}` : '';
      const receivedVal = getObjVal(dashData, widget.receivedKey) || 0;
      const sentVal = getObjVal(dashData, widget.sentKey) || 0;
      return (
        <LinkBox key={widget.key} link={isTeamUser ? '' : lnk}>
          <List.Item>
            <List.Item.Meta
              title={<IntlMessages id={widget.label} />}
              description={
                <div className="numMain">
                  {renderNumCircle({
                    type: 'sent',
                    value: sentVal,
                  })}
                  {renderNumCircle({
                    type: 'received',
                    value: receivedVal,
                  })}
                </div>
              }
            />
          </List.Item>
        </LinkBox>
      );
    });
  }

  return (
    <IsoWidgetsWrapper style={{ height: '100%' }}>
      <ReportsWidget
        label={<IntlMessages id="sidebar.signReq" />}
        style={{ height: '100%' }}
        widgetClassName="flex1"
      >
        <ListWrapper>
          <List>{renderDocStatus()}</List>
        </ListWrapper>
      </ReportsWidget>
    </IsoWidgetsWrapper>
  );
}

export default DocStatus;

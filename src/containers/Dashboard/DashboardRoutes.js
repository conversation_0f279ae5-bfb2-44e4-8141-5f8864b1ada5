import React, { lazy, Suspense } from 'react';
import { useSelector } from 'react-redux';
import { includes, isEmpty } from 'lodash';
import { useRouteMatch, Switch, Route } from 'react-router-dom';
import Loader from '@chill/components/utility/Loader';
import accessMenus, { upgradeScreens } from '../Sidebar/config';

const UpgradePlanMsg = lazy(() => import('@chill/components/UpgradePlanMsg'));

// TODO: Add new routes here with pathname
const routes = [
  {
    path: '',
    component: lazy(() => import('@chill/containers/Widgets')),
    userType: [
      'chillbaby_admin',
      'brand_admin',
      'marketing_manager',
      'chillbaby_user',
      'manager',
    ],
    exact: true,
  },
  {
    path: 'customers',
    component: lazy(() => import('@chill/containers/Customers/index')),
    userType: [
      'brand_admin',
      'marketing_manager',
      'customer_service',
      'manager',
    ],
    exact: true,
  },
  {
    path: 'ecommerce',
    component: lazy(() => import('@chill/containers/ECommerce/EcomWidgets')),
    userType: ['brand_admin'],
    exact: true,
  },
  {
    path: 'admin-accounting',
    component: lazy(() => import('@chill/containers/Accounting/index')),
    userType: ['chillbaby_admin'],
    exact: true,
  },
  {
    path: 'marketing-new',
    component: lazy(() => import('@chill/containers/Marketing/index')),
    userType: ['brand_admin', 'marketing_manager', 'manager'],
    exact: true,
  },
  {
    path: 'marketingNew',
    component: lazy(() => import('@chill/containers/Marketing/marketingNew')),
    userType: ['brand_admin', 'marketing_manager', 'manager'],
    exact: true,
  },
  {
    path: 'marketingnew1',
    component: lazy(() => import('@chill/containers/Marketing/marketingnew1')),
    userType: ['brand_admin', 'marketing_manager', 'manager'],
    exact: true,
  },
  {
    path: 'marketingOld',
    component: lazy(() => import('@chill/containers/Marketing/marketingOld')),
    userType: ['brand_admin', 'marketing_manager', 'manager'],
    exact: true,
  },
  {
    path: 'dummyAd',
    component: lazy(() => import('@chill/containers/Marketing/dummyAd')),
    userType: ['brand_admin', 'marketing_manager', 'manager'],
    exact: true,
  },
  {
    path: 'dummyAdCamp',
    component: lazy(() => import('@chill/containers/Marketing/dummyAdCamp')),
    userType: ['brand_admin', 'marketing_manager', 'manager'],
    exact: true,
  },
  {
    path: 'card',
    component: lazy(() => import('@chill/containers/Marketing/CardDetails')),
    userType: [
      'chillbaby_admin',
      'brand_admin',
      'marketing_manager',
      'chillbaby_user',
      'manager',
    ],
    exact: true,
  },
  {
    path: 'marketing/audience',
    component: lazy(() => import('@chill/containers/Marketing/Audience')),
    userType: ['brand_admin', 'marketing_manager', 'manager'],
    exact: true,
  },

  {
    path: 'FeedCampaigns',
    component: lazy(() => import('@chill/containers/Marketing/FeedCampaigns')),
    userType: ['brand_admin', 'marketing_manager', 'manager'],
    exact: true,
  },
  {
    path: 'TMPlace',
    component: lazy(() => import('@chill/containers/Marketing/TMPlace')),
    userType: ['brand_admin', 'marketing_manager', 'manager'],
    exact: true,
  },

  // {
  //   path: 'marketing/in-app',
  //   component: lazy(() => import('@chill/containers/Marketing/Audience')),
  //   userType: ['brand_admin', 'marketing_manager', 'manager'],
  //   exact: true,
  // },
  // {
  //   path: 'marketing/sms',
  //   component: lazy(() => import('@chill/containers/Marketing/Audience')),
  //   userType: ['brand_admin', 'marketing_manager', 'manager'],
  //   exact: true,
  // },
  // {
  //   path: 'marketing/email',
  //   component: lazy(() => import('@chill/containers/Marketing/Audience')),
  //   userType: ['brand_admin', 'marketing_manager', 'manager'],
  //   exact: true,
  // },
  {
    path: 'setting',
    component: lazy(() => import('@chill/containers/Settings/index')),
    userType: [
      'chillbaby_admin',
      'brand_admin',
      'marketing_manager',
      'chillbaby_user',
      'manager',
    ],
    exact: true,
  },
  {
    path: 'products',
    component: lazy(() => import('@chill/containers/Products/index')),
    userType: [
      'brand_admin',
      'marketing_manager',
      'customer_service',
      'manager',
    ],
    exact: true,
  },
  {
    path: 'product-category',
    component: lazy(() => import('@chill/containers/ProductCategory/index')),
    userType: [
      'brand_admin',
      'marketing_manager',
      'customer_service',
      'manager',
    ],
    exact: true,
  },
  {
    path: 'account',
    component: lazy(() => import('@chill/containers/Account/index')),
    userType: [
      'chillbaby_admin',
      'brand_admin',
      'marketing_manager',
      'chillbaby_user',
      'manager',
    ],
    exact: true,
  },
  {
    path: 'settings',
    component: lazy(() => import('@chill/containers/Account/index')),
    userType: [
      'chillbaby_admin',
      'brand_admin',
      'marketing_manager',
      'chillbaby_user',
      'manager',
    ],
    exact: true,
  },
  {
    path: 'changePassword',
    component: lazy(() => import('@chill/containers/Account/index')),
    userType: [
      'chillbaby_admin',
      'brand_admin',
      'marketing_manager',
      'chillbaby_user',
      'manager',
    ],
    exact: true,
  },
  {
    path: 'brands',
    component: lazy(() => import('@chill/containers/Brands/index')),
    userType: ['chillbaby_admin', 'chillbaby_user'],
    exact: true,
  },
  {
    path: 'brand-integration',
    component: lazy(() => import('@chill/containers/BrandIntegration/index')),
    userType: ['chillbaby_admin', 'chillbaby_user'],
    exact: true,
  },
  {
    path: 'analytics',
    component: lazy(() => import('@chill/containers/Analytics/newIndex')),
    userType: [
      'chillbaby_admin',
      'brand_admin',
      'marketing_manager',
      'chillbaby_user',
      'manager',
    ],
    exact: true,
  },
  {
    path: 'support',
    component: lazy(() => import('@chill/containers/Services/index')),
    userType: ['brand_admin', 'customer_service', 'manager', 'chillbaby_admin'],
    exact: true,
  },
  {
    path: 'mainDevices',
    component: lazy(() => import('@chill/containers/DeviceManagement/index')),
    userType: ['chillbaby_admin', 'brand_admin', 'manager'],
    exact: true,
  },
  {
    path: 'devices-category',
    component: lazy(() => import('@chill/containers/DeviceData/index')),
    userType: ['chillbaby_admin', 'brand_admin', 'manager'],
    exact: true,
  },
  {
    path: 'goal-completion',
    component: lazy(() => import('@chill/containers/GoalCompletion/index')),
    userType: [
      'chillbaby_admin',
      'brand_admin',
      'chillbaby_user',
      'manager',
      'marketing_manager',
    ],
    exact: true,
  },
  {
    path: 'group-management',
    component: lazy(() => import('@chill/containers/GroupManagement/index')),
    userType: ['chillbaby_admin', 'brand_admin', 'manager'],
    exact: true,
  },
  {
    path: 'trailer-management',
    component: lazy(() => import('@chill/containers/TrailerManagement/index')),
    userType: ['chillbaby_admin', 'brand_admin', 'manager'],
    exact: true,
  },
  {
    path: 'gpx-management',
    component: lazy(() => import('@chill/containers/GpxManagement/index')),
    userType: ['chillbaby_admin', 'brand_admin', 'manager'],
    exact: true,
  },
  {
    path: 'maintenance',
    component: lazy(() => import('@chill/containers/Maintenance/index')),
    userType: ['chillbaby_admin', 'brand_admin', 'manager'],
    exact: true,
  },
  {
    path: 'highlights',
    component: lazy(() => import('@chill/containers/Highlights/index')),
    userType: ['chillbaby_admin', 'brand_admin', 'manager'],
    exact: true,
  },
  {
    path: 'faq',
    component: lazy(() => import('@chill/containers/FAQ/index')),
    userType: ['chillbaby_admin', 'brand_admin', 'manager'],
    exact: true,
  },
  {
    path: 'strollers',
    component: lazy(() => import('@chill/containers/Strollers/index')),
    userType: ['chillbaby_admin', 'brand_admin', 'manager'],
    exact: true,
  },
];

export default function AppRouter() {
  const { url } = useRouteMatch();
  const access = useSelector((state) => state.Auth.access);
  const userData = useSelector((state) => state.Auth.userData);
  const userType = (!isEmpty(userData) && userData.user_type) || '';

  return (
    <Suspense fallback={<Loader />}>
      <Switch>
        {routes.map((route) => {
          const accessKey = accessMenus[route.path] || '';
          const showRoute = access[accessKey] || !accessKey;
          const upScreen = upgradeScreens.indexOf(route.path) > -1;

          // Hide screens which are not accessible by user
          if (!upScreen && !showRoute) return null;

          const findUserType = route.userType
            ? includes(route.userType, userType)
            : '';
          if (!findUserType) return null;

          // Show loader for paid screens while getting user's access data
          if (upScreen && access.loading)
            return <Loader key={`loader_${route.path}`} />;

          return (
            <Route
              exact={route.exact}
              key={`route_${route.path}`}
              path={`${url}/${route.path}`}
            >
              {showRoute ? <route.component /> : <UpgradePlanMsg />}
            </Route>
          );
        })}
      </Switch>
    </Suspense>
  );
}

/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
import React, { useEffect, Suspense, lazy, useState } from 'react';
import { isArray, isEmpty, isEqual } from 'lodash';
import { useSelector, useDispatch } from 'react-redux';
import { Layout, notification } from 'antd';
import getDefaultPath from '@chill/lib/helpers/url_sync';
import useWindowSize from '@chill/lib/hooks/useWindowSize';
import IntlMessages from '@chill/components/utility/intlMessages';
import appActions from '@chill/redux/app/actions';
import authActions from '@chill/redux/auth/actions';
import chatActions from '@chill/redux/chat/actions';
import { useHistory, useLocation } from 'react-router-dom';
import Loader from '@chill/components/utility/Loader';
import { DashboardContainer, DashboardGlobalStyles } from './Dashboard.styles';

const Sidebar = lazy(() => import('../Sidebar/Sidebar'));
const Topbar = lazy(() => import('../Topbar/Topbar'));
const DashboardRoutes = lazy(() => import('./DashboardRoutes'));

const { Content, Footer } = Layout;
const { changeCurrent } = appActions;
const { updateAccess } = authActions;

const styles = {
  layout: { flexDirection: 'row', overflowX: 'hidden' },
  content: {
    padding: '70px 0 0px',
    flexShrink: '0',
    background: 'rgba(250, 251, 252, 1)',
    position: 'relative',
  },
  footer: {
    background: 'rgba(250, 251, 252, 1)',
    textAlign: 'center',
    // borderTop: '1px solid #e0e0e0',
    color: '#A7ACB0',
    fontSize: '15px',
    fontWeight: 400,
    letterSpacing: '-0.20454545319080353px',
    fontFamily: 'Inter !important',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  logo: {
    maxWidth: '100px',
    marginRight: '15px',
  },
};

export default function Dashboard() {
  const dispatch = useDispatch();
  const token = useSelector((state) => state.Auth.idToken);
  const appHeight = useSelector((state) => state.App.height);
  const history = useHistory();
  const { latestMsg } = useSelector((state) => state.Chat);
  const [lstMsg, setLstMsg] = useState({});
  const { height } = useWindowSize();
  const preKeys = getDefaultPath();
  const location = useLocation();

  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);
  const currentPage = isArray(preKeys) && !isEmpty(preKeys) ? preKeys[0] : '';

  useEffect(() => {
    if (token) {
      // Get user access
      dispatch(updateAccess('init'));
    }
  }, [token]);

  useEffect(() => {
    dispatch(changeCurrent(preKeys));
  }, [location]);

  useEffect(() => {
    const btn = (
      <a
        onClick={(e) => {
          e.preventDefault();
          history.push({
            pathname: '/dashboard/support',
            query: { latestMsg },
          });
          notification.close('msg_notification');
        }}
        href={`# `}
        className="viewChatLink"
      >
        {messageArray['notification.view.btn']}
      </a>
    );
    if (
      !isEmpty(latestMsg) &&
      !isEqual(lstMsg, latestMsg) &&
      currentPage !== 'support'
    ) {
      notification.open({
        message: latestMsg.name || 'User',
        description:
          latestMsg.type === 'file'
            ? messageArray['notification.attachment']
            : latestMsg.message,
        key: 'msg_notification',
        btn,
      });
      setLstMsg(latestMsg);
      dispatch(chatActions.setChatNotification());
    }
  }, [latestMsg]);

  return (
    <Suspense fallback={<Loader />}>
      <DashboardContainer>
        <DashboardGlobalStyles />
        <Layout style={{ height }}>
          <Topbar />
          <Layout style={styles.layout}>
            <Sidebar />
            <Layout
              className="isoContentMainLayout"
              style={{
                height: appHeight,
              }}
            >
              <Content className="isomorphicContent" style={styles.content}>
                <DashboardRoutes />
              </Content>
              <Footer style={styles.footer}>
                <div>
                  <img src="/logo.png" alt="Logo" style={styles.logo} />
                </div>
                <IntlMessages
                  id="common.copyRightTxt"
                  values={{ year: new Date().getFullYear() }}
                />
              </Footer>
            </Layout>
          </Layout>
        </Layout>
      </DashboardContainer>
    </Suspense>
  );
}

import styled from 'styled-components';
import { palette } from 'styled-theme';
import Buttons from '@chill/components/uielements/button';
import Inputs, {
  Textarea as Textareas,
} from '@chill/components/uielements/input';

const Input = styled(Inputs)``;
const Textarea = styled(Textareas)``;

const MessageSingle = styled.div`
  .messageUser {
    max-width: 100%;
    display: flex;
    flex-wrap: wrap;
    padding: 12px 0px;
    margin: 0px 15px;
    align-items: flex-end;
    flex-shrink: 0;
    justify-content: flex-end;

    .messageLeftRight {
      display: flex;
      align-items: flex-start;
      flex-direction: row-reverse;
      max-width: 70%;
      width: 100%;
    }

    .mDetailsWithDate {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      width: 100%;
      gap: 8px;
      .timeStampStyle {
        display: flex;
        // flex-direction: column;
        align-items: center;

        justify-content: flex-end;
        gap: 5px;
        .dateTextStyle {
          color: #04b8ff;
          font-family: Nunito;
          font-size: 12px;
          font-weight: 400;
          line-height: 20px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
        }
      }
    }
    .messageGravatar {
      width: 45px;
      height: 45px;
      flex-shrink: 0;
      overflow: hidden;
      margin: 0px 9px 0px 16px;

      img {
        border-radius: 50%;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .messageContent {
      width: 100%;
      display: flex;
      flex-direction: column;
      max-width: calc(100% - 110px);
      flex-shrink: 0;

      .messageContentText {
        position: relative;
        font-size: 14px;
        vertical-align: top;
        display: inline-block;
        // padding: 10px 15px;
        ${'' /* overflow: hidden; */} word-break: break-word;

        p {
          margin: 0;
        }

        & .fileInfoContent {
          display: flex;
          // padding: 0px;
          align-items: center;
          & .fileInfo {
            margin: 0px 10px;
            white-space: nowrap;
            width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            @media (max-width: 375px) {
              white-space: nowrap;
              width: 140px;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            & > b,
            & > p {
              white-space: nowrap;
              width: 150px;
              overflow: hidden;
              text-overflow: ellipsis;
              font-weight: 500;
              font-size: 13px;
              @media (max-width: 375px) {
                white-space: nowrap;
                width: 140px;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
          }
        }
      }
      .msgUserDetail {
        display: flex;
        // align-items: center;
        justify-content: space-between;
        width: 100%;
      }

      .messageTime {
        font-size: 12px;
        color: ${palette('text', 3)};
        margin-top: 5px;
      }
      &.isUser {
        align-items: flex-end;
        .messageContentText {
          background: ${palette('primary', 1)};
          // color: #ffffff;
          border-radius: 3px 0 3px 3px;

          &:after {
            content: '';
            position: absolute;
            border-style: solid;
            display: block;
            width: 0;
            top: 0;
            bottom: auto;
            left: auto;
            right: -9px;
            border-width: 0px 0 10px 10px;
            border-color: transparent ${palette('primary', 1)};
            margin-top: 0;
          }
        }
        .messageTime {
          margin-left: auto;
        }
      }
      &.notUser {
        align-items: flex-start;
        background: #f3f4f6;
        padding: 12px 12px 12px 10px;
        border: 1px solid #dee1e6;
        border-radius: 4px;
        .messageContentText {
          .message-txt {
            font-family: Nunito;
            font-size: 14px;
            font-weight: 400;
            line-height: 22px;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #171a1f;
          }
          // background: ${palette('grayscale', 4)};
          color: ${palette('text', 0)};
          border-radius: 0 3px 3px 3px;

          // &:after {
          //   content: '';
          //   position: absolute;
          //   border-style: solid;
          //   display: block;
          //   width: 0;
          //   top: 0;
          //   bottom: auto;
          //   left: -9px;
          //   border-width: 0px 10px 10px 0;
          //   border-color: transparent ${palette('grayscale', 4)};
          //   margin-top: 0;
          // }
        }
        .messageTime {
          margin-right: auto;
        }
      }
    }
  }

  .messageNotUser {
    max-width: 100%;
    display: flex;
    flex-wrap: wrap;
    padding: 12px 0px;
    margin: 0px 15px;
    align-items: flex-start;
    justify-content: flex-start;
    flex-shrink: 0;

    .messageLeftRight {
      display: flex;
      align-items: flex-start;
      max-width: 70%;
      width: 100%;
    }

    .timeStampStyle {
      display: flex;
      // flex-direction: column;
      align-items: center;

      justify-content: flex-start;
      gap: 5px;
      .dateTextStyle {
        color: #04b8ff;
        font-family: Nunito;
        font-size: 12px;
        font-weight: 400;
        line-height: 20px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
      }
    }

    .mDetailsWithDate {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      width: 100%;
      gap: 8px;
    }
    .messageGravatar {
      width: 45px;
      height: 45px;
      flex-shrink: 0;
      overflow: hidden;
      margin: 0px 16px 0px 9px;

      img {
        border-radius: 50%;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .messageContent {
      width: 100%;
      display: flex;
      flex-direction: column;
      max-width: calc(100% - 110px);
      flex-shrink: 0;

      .messageContentText {
        position: relative;
        font-size: 14px;
        vertical-align: top;
        display: inline-block;
        // padding: 10px 15px;
        ${'' /* overflow: hidden; */} word-break: break-word;

        p {
          margin: 0;
        }

        & .fileInfoContent {
          display: flex;
          // padding: 0px;
          align-items: center;
          & .fileInfo {
            margin: 0px 10px;
            white-space: nowrap;
            width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            @media (max-width: 375px) {
              white-space: nowrap;
              width: 140px;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            & > b,
            & > p {
              white-space: nowrap;
              width: 150px;
              overflow: hidden;
              text-overflow: ellipsis;
              font-weight: 500;
              font-size: 13px;
              @media (max-width: 375px) {
                white-space: nowrap;
                width: 140px;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
          }
        }
      }
      .msgUserDetail {
        display: flex;
        // align-items: center;
        justify-content: space-between;
        width: 100%;
      }

      .messageTime {
        font-size: 12px;
        color: ${palette('text', 3)};
        margin-top: 5px;
      }
      &.isUser {
        align-items: flex-end;
        .messageContentText {
          background: ${palette('primary', 1)};
          // color: #ffffff;
          border-radius: 3px 0 3px 3px;

          &:after {
            content: '';
            position: absolute;
            border-style: solid;
            display: block;
            width: 0;
            top: 0;
            bottom: auto;
            left: auto;
            right: -9px;
            border-width: 0px 0 10px 10px;
            border-color: transparent ${palette('primary', 1)};
            margin-top: 0;
          }
        }
        .messageTime {
          margin-left: auto;
        }
      }
      &.notUser {
        align-items: flex-start;
        background: #ffffff;
        border: 1px solid #dee1e6;
        border-radius: 4px;
        padding: 12px;

        .messageContentText {
          .message-txt {
            font-family: Nunito;
            font-size: 14px;
            font-weight: 400;
            line-height: 22px;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #171a1f;
          }
          // background: ${palette('grayscale', 4)};
          // color: ${palette('text', 0)};
          // border-radius: 0 3px 3px 3px;

          // &:after {
          //   content: '';
          //   position: absolute;
          //   border-style: solid;
          //   display: block;
          //   width: 0;
          //   top: 0;
          //   bottom: auto;
          //   left: -9px;
          //   border-width: 0px 10px 10px 0;
          //   border-color: transparent ${palette('grayscale', 4)};
          //   margin-top: 0;
          // }
        }
        .messageTime {
          margin-right: auto;
        }
      }
    }
  }

  @media only screen and (max-width: 767px) {
    margin: 10px 0;
  }

  &.loggedUser {
    justify-content: flex-end;
  }

  .attchmentDivStyle {
    border: 1px solid #eee;
    border-radius: 15px !important;
    padding: 10px 15px;
    margin-top: 15px;

    .downloadIconStyle {
      font-size: 20px;
      color: #1172ec;
    }
  }
`;
const ChatWindow = styled.div`
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;

  .ant-tabs-nav {
    margin: 0px !important;
  }

  @media only screen and (max-width: 767px) {
    > div {
      width: 100%;
      max-width: 100%;
    }
  }
`;
const ChatBox = styled.div`
  width: calc(100% - 350px);
  background-color: #ffffff;
  border: 1px solid ${palette('border', 0)};
  border-left-width: 0;
  display: flex;
  flex-direction: column;

  @media only screen and (max-width: 767px) {
    border-left-width: 1px;
  }

  @media only screen and (min-width: 768px) and (max-width: 991px) {
    width: calc(100% - 280px);
  }
`;
const ChatSidebar = styled.div`
  flex-shrink: 0;
  border: 1px solid ${palette('border', 0)};
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;

  .UserListsWrapper {
    .messageHelperText {
      background: #ffffff;
      height: 100%;
      padding: 0 !important;
    }
  }

  .chatTabStyle {
    .ant-tabs-ink-bar {
      display: none;
    }
    .ant-tabs-tab {
      margin: 0px;
      height: 83px;
    }
    .ant-tabs-tab-active {
      background: #323842;
      span {
        font-family: Inter;
        font-size: 12px;
        font-weight: 700 !important;
        line-height: 30px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #ffffff !important;
      }
    }
    .ant-tabs-tab-btn {
      span {
        font-family: Inter;
        font-size: 12px;
        font-weight: 600;
        line-height: 30px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #6a707b;
      }
    }
  }

  .ComposeMessageButton {
    margin-top: auto;
    padding: 20px;
    flex-shrink: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    text-align: center;
    background: #ffffff;

    button {
      width: 100%;
      margin: 0;
    }
  }

  .ant-tabs-nav-list {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ant-tabs-tab {
    margin: 0px;
    width: 100%;
    align-items: center;
    justify-content: center;
    padding: 22px 0px;
  }

  .tabBadgeStyle {
    background-color: #ff0000;
    height: 20px;
    width: 20px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
    color: #fff;
    font-weight: 400;
    font-size: 13px;
  }

  .flexStyle {
    display: flex;
  }

  @media only screen and (min-width: 768px) and (max-width: 991px) {
    width: 280px;
  }
  @media only screen and (min-width: 992px) {
    width: 360px;
  }
`;

const Button = styled(Buttons)`
  width: calc(100% - 60px);
  margin-left: 30px;
  margin-top: 30px;
  span {
    &:last-child {
      span {
        background-color: #ffffff;
      }
    }
  }
`;

const ComposeMessageWrapper = styled.div`
  .mainDiv {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0px 10px 0px 0px;
    background: #ffffff;
    margin: 0px 25px 21px 23px;

    @media (min-width: 359px) and (max-width: 1025px) {
      padding: 0px 10px 0px 10px;
      margin: 0px 20px 16px;
    }
    @media (max-width: 280px) {
      padding: 0px 10px 0px 10px;
    }
    .sendMessageButton {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 10px 0px;
    }
  }

  .changeBtn {
    width: 40px;
    height: 40px;
    font-weight: 500;
    color: #fff;
    border-radius: 15px;
    border: 0px;
    padding: 0px;
    text-align: center;
    background: linear-gradient(90deg, #039be5 38%, #03a9f4 90%);
    &:hover {
      background: linear-gradient(90deg, #039be5 38%, #03a9f4 90%);
      color: #fff;
    }
    @media (min-width: 359px) and (max-width: 1025px) {
      width: 50px;
      height: 40px;
    }
    @media (max-width: 280px) {
      width: 60px;
      height: 40px;
    }
  }

  .textInputStyle {
    // padding: 0px 0px 0px 14px !important;
    border-radius: 0px !important;
    margin: 0px 10px !important;
    font-family: Nunito;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #171a1f;
    &:disabled {
      background-color: transparent;
    }
  }
  .textInputStyle::-webkit-scrollbar {
    display: none;
  }

  ${Textarea} {
    border-radius: 20px;
    padding: 20px;
    border: 0;
    background-color: transparent;
    resize: none;

    &:focus {
      box-shadow: none;
    }
  }
`;
const ComposeInputWrapper = styled.div`
  background: #ffffff;
  height: 65vh;
  padding: 30px 30px 0;
  > div {
    margin-top: -8px;
    width: 100%;
    margin-left: 20px;
  }
`;

const UserListsWrapper = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  height: inherit;
  .search {
    width: 100%;
    border: none;
  }
  .filterSelect {
    width: 100%;
  }
  .ant-select-selector {
    border: none !important;
  }
`;

const UserLists = styled.div`
  width: 100%;
  margin: 0;
  padding: 10px 20px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  text-align: left;
  position: relative;
  margin: 0;
  align-items: center;
  cursor: pointer;
  box-sizing: border-box;
  transition: all 0.25s ease;
  background-color: #fff;
  border-bottom: 1px solid rgba(224, 224, 224, 0.6);

  .chatTitle {
    font-family: Nunito;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #424955;
  }

  .pMessage {
    font-family: Nunito;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #424955;
  }

  .previous-message-txt {
    font-family: Nunito;
    font-size: 16px;
    font-weight: 700;
    line-height: 26px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #171a1f;
  }

  * {
    box-sizing: border-box;
  }

  &:hover {
    background-color: ${palette('grayscale', 3)};
  }

  .userListsGravatar {
    width: 50px;
    margin: 0 7px 0 0;
    flex-shrink: 0;
    img {
      border-radius: 50%;
    }
  }
  .userListsContent {
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    h4 {
      font-size: 14px;
      margin: 0;
      font-weight: 500;
    }
    & .userListTitleMain {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  .chatExcerpt {
    width: 100%;
    display: flex;
    // align-items: center;
    justify-content: space-between;
    align-items: flex-start;

    & .p {
      color: ${palette('text', 1)};
      margin: 0;
      font-size: 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 80%;
      display: inline-block;

      @supports (-webkit-line-clamp: 2) {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: initial;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        word-break: break-word;
        // width: auto;
      }
    }
  }
  & .userListsTime {
    color: #424955;
    flex-shrink: 0;
    font-family: Nunito;
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
    text-align: right;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
  }
`;

const ToggleViewProfile = styled.div`
  background: #ffffff;
  height: 65px;
  flex-shrink: 0;
  // padding-left: 30px;
  margin: 0px 20px;
  display: flex;
  align-items: center;
  // justify-content: space-between;

  .userInfoMain {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
    height: 100%;
    & * {
      transition: all 0.3s;
    }
  }
  .title {
    font-size: 17px;
    color: ${palette('text', 0)};
    cursor: pointer;
  }
  .dateTextStyle {
    color: #c4c4c4;
    font-size: 13px;
  }

  ${Button} {
    width: auto;
    padding: 0;
    border: 0;
    margin: 0;
    margin-right: 15px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:focus {
      box-shadow: none;
    }

    i {
      font-size: 12px;
      color: ${palette('text', 0)};
    }
  }
`;

const SidebarSearchBox = styled.div`
  padding: 15px 20px;
  background: #ffffff;
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid ${palette('border', 2)};

  ${Input} {
    padding: 0;
    border: 0;

    &:focus {
      box-shadow: none;
    }
  }
`;

const MessageChatWrapper = styled.div`
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  overflow: hidden;
  overflow-y: auto;
  background: #fafafb;
  border-top: 1px solid ${palette('border', 0)};

  & .loaderMain {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0px;
  }
  & .scrollToBtn {
    position: absolute;
    right: 30px;
    bottom: 110px;
  }
  & > div {
    ::-webkit-scrollbar {
      display: none;
    }
  }
`;

const ChatViewWrapper = styled.div`
  height: 100%;

  @media only screen and (max-width: 767px) {
    padding: 0;
  }
`;

const MessageDialog = styled.div`
  h5 {
    font-size: 13px;
    color: ${palette('text', 0)};
    margin-bottom: 10px;
  }

  ${ComposeMessageWrapper} {
    background: #ffffff;
    flex-shrink: 0;
    border: 0;

    ${Textarea} {
      padding: 4px 10px;
      border: 1px solid ${palette('border', 0)};
      margin-bottom: 10px;

      &:focus {
        border-color: #4482ff;
        box-shadow: 0 0 0 2px rgba(68, 130, 255, 0.2);
      }
    }
  }
`;

const AddUserBtn = styled(Buttons)`
  && {
    padding: 0 12px;
    i {
      font-size: 17px;
      color: ${palette('text', 1)};
    }

    &:hover {
      i {
        color: inherit;
      }
    }
  }
`;

const Fieldset = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
`;

const Label = styled.label`
  font-size: 12px;
  color: ${palette('text', 0)};
  line-height: 1.5;
  font-weight: 400;
  padding: 0;
  margin: 0 0 5px;
`;

export {
  MessageSingle,
  ChatWindow,
  ChatBox,
  ChatSidebar,
  Button,
  Input,
  Textarea,
  ComposeMessageWrapper,
  UserLists,
  UserListsWrapper,
  ToggleViewProfile,
  ComposeInputWrapper,
  SidebarSearchBox,
  MessageChatWrapper,
  ChatViewWrapper,
  MessageDialog,
  AddUserBtn,
  Fieldset,
  Label,
};

/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable consistent-return */
/* eslint-disable jsx-a11y/mouse-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Spin } from 'antd';
import ViewProfile from '@chill/components/Chat/ViewProfile';
import { LeftOutlined } from '@ant-design/icons';
import chatActions from '@chill/redux/chat/actions';
import { Popover } from 'react-tiny-popover';
import horizonalDots from '@chill/assets/images/more-horizontal.png';
import submitToTechBlueImg from '@chill/assets/images/submit-to-tech-team-blue.svg';
import submitToTechGreyImg from '@chill/assets/images/submit-to-tech-team-grey.svg';
import pendingBlueImg from '@chill/assets/images/pending-blue.svg';
import pendingGreyImg from '@chill/assets/images/pending-grey.svg';
import solveCloseBlueImg from '@chill/assets/images/check-double-blue.svg';
import solveCloseGreyImg from '@chill/assets/images/check-double-grey.svg';
import pinBlueImg from '@chill/assets/images/Pin-blue.svg';
import styled from 'styled-components';
import pinGreyImg from '@chill/assets/images/Pin-grey.svg';
import getApiData from '@chill/lib/helpers/apiHelper';
import IntlMessages from '@chill/components/utility/intlMessages';
import { isObject } from 'lodash';
import ReturnToBrandForm from '@chill/components/Chat/ReturnToBrandForm';
import Notification from '@chill/components/Notification';
import ComposeMessage from './ComposeMessage';
import Messages from './Messages';
import ChatRooms from './ChatRooms';
import {
  ChatWindow,
  ChatBox,
  Button,
  ToggleViewProfile,
} from './Messages.styles';

const {
  toggleViewProfile,
  chatInit,
  mobileActiveProfile,
  getChatList,
  toggleMobileList,
  toggleMobileProfile,
  setMessages,
  setChatTab,
} = chatActions;

export default function MobileView({ allChats }) {
  const dispatch = useDispatch();
  const {
    loading,
    users,
    userId,
    mobileActiveList,
    selectedChatRoom,
    chatTab,
    messages,
    viewProfile,
  } = useSelector((state) => state.Chat);
  React.useEffect(() => {
    if (!users) {
      dispatch(chatInit(userId));
    }
  });
  const { language } = useSelector((state) => state.LanguageSwitcher);

  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  const userData = useSelector((state) => state.Auth.userData);

  const uData = isObject(userData) ? { ...userData } : {};
  console.log(
    `hello ~ file: MobileView.js:73 ~ MobileView ~ selectedChatRoom:`,
    selectedChatRoom,
  );

  const [isDropDownOpen, setDropDownOpen] = useState(false);
  console.log(
    `hello ~ file: MobileView.js:79 ~ MobileView ~ isDropDownOpen:`,
    isDropDownOpen,
  );
  const [pinChatLoader, setPinChatLoader] = useState(false);
  const [ticketLoader, setTicketLoader] = useState(false);
  const [escalateLoader, setescalateLoader] = useState(false);
  const [returnLoader, setReturnLoader] = useState(false);
  const [state, setState] = useState({
    visible: false,
    initObj: {},
  });
  const { visible, initObj } = state;
  const [isHover, setIsHover] = useState(false);
  const [isPinChat, setIsPinChat] = useState(
    selectedChatRoom?.is_pin_chat === 1,
  );
  console.log(
    `hello ~ file: MobileView.js:92 ~ MobileView ~ isPinChat:`,
    isPinChat,
    selectedChatRoom?.is_pin_chat === 1,
    selectedChatRoom,
  );

  // Pending Chat api call
  async function pendingChat() {
    // setTicketLoader(true);
    const obj = {
      lead_id: selectedChatRoom.lead_id,
    };
    try {
      const response = await getApiData('chat/pending-chat', obj, 'POST');
      if (response.success) {
        Notification('success', response.message);
        // if (response.data) {
        //   messages.push(response.data);
        //   dispatch(setMessages(messages));
        // }
      } else {
        Notification('error', response.message);
      }
    } catch (error) {
      console.log(error);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  // Pin Chat api call
  async function handlePinChat() {
    // setTicketLoader(true);
    const obj = {
      chat_id: selectedChatRoom?.id,
    };
    try {
      setPinChatLoader(true);
      const response = await getApiData('chat/pin-chat', obj, 'POST');
      if (response.success) {
        Notification('success', response.message);
        // setescalateLoader(true);
        setIsPinChat(!isPinChat);
        dispatch(getChatList('', chatTab));

        // if (response.data) {
        //   messages.push(response.data);
        //   dispatch(setMessages(messages));
        // }
      } else {
        Notification('error', response.message);
      }
    } catch (error) {
      console.log(error);
      Notification('error', messageArray['err.wenWrong']);
    } finally {
      setPinChatLoader(false);
    }
  }

  React.useEffect(() => {
    if (selectedChatRoom?.is_pin_chat === 1) {
      setIsPinChat(true);
    } else {
      setIsPinChat(false);
    }
  }, [selectedChatRoom]);

  const ActionWrapper = styled.div`
    display: flex;
    align-items: center;
    justify-content: center;
    .popMainDiv {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2px;
      justify-content: center;
      .isoDropdownLink {
        width: 100%;
        height: 36px;
        padding: 8px 10px 8px 16px;
        cursor: pointer;
        gap: 10px;
        border-radius: 4px;
        background: #ffffff;
        span {
          font-family: Inter;
          font-size: 12px;
          font-weight: 400;
          line-height: 20px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;

          color: #565e6c;
        }
      }
      .isoDropdownLink:hover {
        background: #e9f9ff;

        span {
          font-family: Inter;
          font-size: 12px;
          font-weight: 700;
          line-height: 20px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #04b8ff;
        }
      }
    }
  `;

  async function escalateTochillbaby() {
    setTicketLoader(true);
    const obj = {
      lead_id: selectedChatRoom.lead_id,
    };
    try {
      const response = await getApiData('chat/chat-to-techteam', obj, 'POST');
      if (response.success) {
        Notification('success', response.message);
        setescalateLoader(true);
        if (response.data) {
          messages.push(response.data);
          dispatch(setMessages(messages));
        }
      } else {
        Notification('error', response.message);
        setescalateLoader(false);
      }
      setTicketLoader(false);
    } catch (error) {
      console.log(error);
      Notification('error', messageArray['err.wenWrong']);
      setescalateLoader(false);
      setTicketLoader(false);
    }
  }

  const returnToBrand = () => {
    setState((pre) => ({ ...pre, initObj: {}, visible: true }));
  };

  // this function for close ticket
  /** this function for close ticket
   * @function closeTicket
   * @param {object} data lead_id
   */
  async function closeTicket() {
    setTicketLoader(true);
    const obj = {
      lead_id: selectedChatRoom.lead_id,
    };
    try {
      const response = await getApiData('chat/close-chat', obj, 'POST');
      if (response.success) {
        Notification('success', response.message);
        // dispatch(setSelectedChatroom({}));
        dispatch(setMessages([]));
        dispatch(setChatTab('Closed'));
        dispatch(getChatList('', 'Closed'));
      } else {
        Notification('error', response.message);
      }
      setTicketLoader(false);
    } catch (error) {
      console.log(error);
      Notification('error', messageArray['err.wenWrong']);
      setTicketLoader(false);
    }
  }

  const content = () => {
    console.log('first');
    return (
      <ActionWrapper>
        <div className="popMainDiv">
          {[
            {
              id: 'Submit to Tech Team',
              blueImg: submitToTechBlueImg,
              greyImg: submitToTechGreyImg,
              onClick: () => {
                if (!escalateLoader) {
                  escalateTochillbaby();
                }
                setDropDownOpen(false);
                // setDasboardVisible(!dasboardVisible);
              },
            },
            {
              id: 'Return to brand',
              blueImg: submitToTechBlueImg,
              greyImg: submitToTechGreyImg,
              onClick: () => {
                if (!returnLoader) {
                  returnToBrand();
                }
                setDropDownOpen(false);
                // setDasboardVisible(!dasboardVisible);
              },
            },
            {
              id: 'Pending',
              blueImg: pendingBlueImg,
              greyImg: pendingGreyImg,
              onClick: () => {
                pendingChat();
                setDropDownOpen(false);
                // history.push({
                //   pathname: '/dashboard/marketing/audience',
                //   state: { data: item },
                // });
                // setDasboardVisible(!dasboardVisible);
              },
            },
            {
              id: 'Solved and Close',
              blueImg: solveCloseBlueImg,
              greyImg: solveCloseGreyImg,
              onClick: () => {
                if (ticketLoader) return null;
                setDropDownOpen(false);
                closeTicket();
              },
            },
          ].map((item, index) => {
            console.log(
              'chatTab===>',
              selectedChatRoom.status,
              uData.user_type,
              chatTab,
              selectedChatRoom.tech_team_close,
              (selectedChatRoom.tech_team_close || chatTab === 'Closed') &&
                item.id === 'Return to brand' &&
                uData.user_type !== 'chillbaby_admin',
            );
            if (
              item.id === 'Solved and Close' &&
              (uData.user_type === 'chillbaby_admin' ||
                selectedChatRoom.status === 'Closed')
            ) {
              return null;
            }
            if (
              uData.user_type !== 'chillbaby_admin' &&
              item.id === 'Return to brand'
            ) {
              return null;
              // eslint-disable-next-line no-else-return
            } else if (
              (selectedChatRoom.tech_team_close || chatTab === 'Closed') &&
              item.id === 'Return to brand' &&
              uData.user_type === 'chillbaby_admin'
            ) {
              return null;
            }

            if (
              (selectedChatRoom.status === 'Closed' ||
                uData.user_type === 'chillbaby_admin') &&
              item.id === 'Submit to Tech Team'
            ) {
              return null;
            }

            return (
              <div
                key={item?.id}
                className="isoDropdownLink"
                onMouseOver={() => setIsHover(index)}
                onMouseOut={() => setIsHover(null)}
                onClick={item.onClick}
              >
                <img
                  className="iconImg"
                  src={isHover === index ? item.blueImg : item.greyImg}
                  style={{ width: 16, height: 16, marginRight: 12 }}
                  alt="noIcon"
                />
                <IntlMessages id={item.id} />
              </div>
            );
          })}
        </div>
      </ActionWrapper>
    );
  };

  if (loading) {
    return (
      <div className="loaderMain">
        <Spin />
      </div>
    );
  }

  function submit(data = {}) {
    setReturnLoader(true);
    setTicketLoader(false);
    messages.push(data);
    dispatch(setMessages(messages));
    if (chatTab === 'Active') {
      dispatch(setChatTab('Closed'));
      dispatch(getChatList('', 'Closed'));
    }
    // set on refresh  return btn, update msg on side list
    setState((pre) => ({ ...pre, visible: false }));
  }

  let CurrentView = <Spin />;

  if (mobileActiveList) {
    CurrentView = (
      <div>
        {/* <Modal
					visible={openCompose}
					onCancel={() => dispatch(toggleCompose())}
					title={<IntlMessages id="Compose Message" />}
					footer={null}
				>
					<MessageDialog>
						<h5>
							<IntlMessages id="common.startChat" />
						</h5>
						<InputName
							users={users}
							setComposedId={() => dispatch(setComposedId())}
							className={className}
						/>
						<ComposeMessage autosize={{ minRows: 5, maxRows: 9 }} showButton rows={8} />
					</MessageDialog>
				</Modal> */}
        <ChatRooms
          allChats={allChats}
          toggleMobileList={() => dispatch(toggleMobileList())}
        />
      </div>
    );
  } else if (mobileActiveProfile) {
    CurrentView = (
      <ViewProfile
        viewProfile={viewProfile}
        toggleViewProfile={() => dispatch(toggleViewProfile())}
        toggleMobileProfile={() => dispatch(toggleMobileProfile())}
      />
    );
  } else {
    CurrentView = (
      <ChatBox className="ChatBox">
        {selectedChatRoom && (
          <ToggleViewProfile
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <Button onClick={() => dispatch(toggleMobileList(true))}>
                <LeftOutlined />
              </Button>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                }}
                onClick={() => {
                  dispatch(toggleViewProfile(selectedChatRoom));
                  dispatch(toggleMobileProfile(true));
                }}
              >
                <p
                  style={{
                    fontFamily: 'Inter',
                    fontSize: '16px',
                    fontWeight: 700,
                    lineHeight: '26px',
                    textAlign: 'left',
                    textUnderlinePosition: 'from-font',
                    textDecorationSkipInk: 'none',
                    color: '#171A1F',
                  }}
                >
                  {selectedChatRoom.ticket}
                </p>
                <p
                  style={{
                    width: '3px',
                    height: '3px',
                    borderRadius: '50%',
                    backgroundColor: '#171A1F',
                  }}
                />

                <p
                  style={{
                    fontFamily: 'Inter',
                    fontSize: '16px',
                    fontWeight: 600,
                    lineHeight: '26px',
                    textAlign: 'left',
                    textUnderlinePosition: 'from-font',
                    textDecorationSkipInk: 'none',
                    color: '#171A1F',
                  }}
                >
                  {selectedChatRoom.name}
                </p>
              </div>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
              {/* {uData.user_type ===
                'chillbaby_admin' ? null : selectedChatRoom.status ===
                  'Closed' ? null : (
                  <CButton
                    type="primary"
                    style={{ marginRight: 10 }}
                    onClick={closeTicket}
                    loading={ticketLoader}
                    disabled={ticketLoader}
                  >
                    <IntlMessages id="chat.close.ticket" />
                  </CButton>
                )} */}
              <Button
                onClick={() => {
                  handlePinChat();
                }}
                loading={pinChatLoader}
                style={{
                  padding: 0,
                  border: 'none',
                  background: 'none',
                  // marginBottom: 5,
                }}
              >
                <img src={isPinChat ? pinBlueImg : pinGreyImg} alt="pin-chat" />{' '}
              </Button>
              <Popover
                content={
                  <div
                    style={{
                      margin: '10px',
                      top: '20%',
                      backgroundColor: '#FFFFFF',
                      width: 220,
                      position: 'fixed',
                      right: '30px',
                      padding: '12px 14px 12px 12px',
                      gap: 0,
                      borderRadius: '4px',
                      boxShadow:
                        '0px 4px 9px 0px #171A1F1C, 0px 0px 2px 0px #171A1F1F',
                    }}
                  >
                    {content()}
                  </div>
                }
                isOpen={isDropDownOpen}
                onClickOutside={() => setDropDownOpen(!isDropDownOpen)}
                arrowPointAtCenter
                className="action-popover"
                positions="left"
              >
                <Button
                  type="text"
                  onClick={() => {
                    setDropDownOpen(!isDropDownOpen);
                  }}
                  style={{
                    padding: 0,
                    border: 'none',
                    background: 'none',
                  }}
                >
                  <img
                    src={isDropDownOpen ? horizonalDots : horizonalDots}
                    alt="action"
                    style={{
                      transform: isDropDownOpen
                        ? 'rotate(0deg)'
                        : 'rotate(90deg)',
                    }}
                  />
                </Button>
              </Popover>
            </div>
          </ToggleViewProfile>
        )}

        <Messages toggleMobileProfile={() => dispatch(toggleMobileProfile())} />
        <ComposeMessage
          InputProps={{
            disableUnderline: true,
          }}
        />
        {visible ? (
          <ReturnToBrandForm
            // type={type}
            leadId={selectedChatRoom?.lead_id}
            onlyId={selectedChatRoom?.id}
            initVal={initObj}
            // eslint-disable-next-line react/jsx-no-bind
            handleSubmit={submit}
            visible={visible}
            onClose={() =>
              setState((pre) => ({ ...pre, initObj: {}, visible: false }))
            }
          />
        ) : null}
      </ChatBox>
    );
  }
  return <ChatWindow className="ChatWindow">{CurrentView}</ChatWindow>;
}

/* eslint-disable consistent-return */
/* eslint-disable jsx-a11y/mouse-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable react/jsx-no-bind */
/* eslint-disable no-nested-ternary */
/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
import React, { useState, useEffect } from 'react';
import { isObject, isEmpty } from 'lodash';
import { useDispatch, useSelector } from 'react-redux';
import { Button, message } from 'antd';
import IntlMessages from '@chill/components/utility/intlMessages';
import ViewProfile from '@chill/components/Chat/ViewProfile';
import InputName from '@chill/components/Chat/InputName';
// import Loader from '@chill/components/utility/loader';
import Modal from '@chill/components/Feedback/Modal';
import chatActions from '@chill/redux/chat/actions';
import CButton from '@chill/components/uielements/CButton';
import getApiData from '@chill/lib/helpers/apiHelper';
import Notification from '@chill/components/Notification';
import { Popover as ReactPopOver } from 'react-tiny-popover';
import horizonalDots from '@chill/assets/images/more-horizontal.png';
import submitToTechBlueImg from '@chill/assets/images/submit-to-tech-team-blue.svg';
import submitToTechGreyImg from '@chill/assets/images/submit-to-tech-team-grey.svg';
import pendingBlueImg from '@chill/assets/images/pending-blue.svg';
import pendingGreyImg from '@chill/assets/images/pending-grey.svg';
import solveCloseBlueImg from '@chill/assets/images/check-double-blue.svg';
import solveCloseGreyImg from '@chill/assets/images/check-double-grey.svg';
import pinBlueImg from '@chill/assets/images/Pin-blue.svg';
import pinGreyImg from '@chill/assets/images/Pin-grey.svg';

// import { colorArray } from '@chill/lib/helpers/utility';
import ReturnToBrandForm from '@chill/components/Chat/ReturnToBrandForm';
import styled from 'styled-components';
import {
  ChatWindow,
  ChatBox,
  ToggleViewProfile,
  MessageDialog,
} from './Messages.styles';
import ChatRooms from './ChatRooms';
import Messages from './Messages';
import ComposeMessage from './ComposeMessage';

const {
  toggleCompose,
  setComposedId,
  toggleViewProfile,
  getChatList,
  setChatTab,
  setSelectedChatroom,
  setMessages,
} = chatActions;

/**
 *
 *@module CloseTicket
 *
 */
export default function DesktopView({ className, allChats }) {
  const dispatch = useDispatch();
  const [typingDataState, setTypingData] = useState({});
  const {
    loading,
    users,
    openCompose,
    selectedChatRoom,
    viewProfile,
    typingData,
    messages,
    chatTab,
  } = useSelector((state) => state.Chat);
  const [ticketLoader, setTicketLoader] = useState(false);
  const [escalateLoader, setescalateLoader] = useState(false);
  const [returnLoader, setReturnLoader] = useState(false);
  let selectedRoom = isObject(selectedChatRoom) ? selectedChatRoom : {};
  console.log(
    `hello ~ file: DesktopView.js:79 ~ DesktopView ~ selectedRoom:`,
    selectedRoom,
  );
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);
  const userData = useSelector((state) => state.Auth.userData);
  const [isDropDownOpen, setDropDownOpen] = useState(false);
  const [pinChatLoader, setPinChatLoader] = useState(false);
  console.log(
    `hello ~ file: DesktopView.js:73 ~ DesktopView ~ isDropDownOpen:`,
    isDropDownOpen,
  );
  const [isHover, setIsHover] = useState(false);
  console.log(
    `hello ~ file: DesktopView.js:81 ~ DesktopView ~ isHover:`,
    isHover,
  );
  const uData = isObject(userData) ? { ...userData } : {};
  const [state, setState] = useState({
    visible: false,
    initObj: {},
  });
  const [isPinChat, setIsPinChat] = useState(selectedRoom?.is_pin_chat === 1);
  console.log(
    `hello ~ file: DesktopView.js:105 ~ DesktopView ~ isPinChat:`,
    isPinChat,
    selectedRoom?.is_pin_chat,
  );
  const { visible, initObj } = state;
  const [lastReturn, setLastReturn] = useState({});
  useEffect(() => {
    setescalateLoader(selectedRoom.is_tech_team);
  }, [selectedRoom]);
  useEffect(() => {
    console.log('-----in useeffect --desktopview-', message);
    // console.log(
    //   '------last element of messages',
    //   messages[messages.length - 1],
    // );
    if (messages[messages.length - 1]?.tech_team_close) {
      console.log('-----last msg tech team close---');
      if (chatTab === 'Active') {
        dispatch(setChatTab('Closed'));
        dispatch(getChatList('', 'Closed'));
      }
    }
  }, [messages]);

  // this function for close ticket
  /** this function for close ticket
   * @function closeTicket
   * @param {object} data lead_id
   */
  async function closeTicket() {
    setTicketLoader(true);
    const obj = {
      lead_id: selectedRoom.lead_id,
    };
    try {
      const response = await getApiData('chat/close-chat', obj, 'POST');
      if (response.success) {
        Notification('success', response.message);
        dispatch(setSelectedChatroom({}));
        dispatch(setMessages([]));
        dispatch(setChatTab('Closed'));
        dispatch(getChatList('', 'Closed'));
      } else {
        Notification('error', response.message);
      }
      setTicketLoader(false);
    } catch (error) {
      console.log(error);
      Notification('error', messageArray['err.wenWrong']);
      setTicketLoader(false);
    }
  }
  // this function for escalateTochillbaby
  /** this function for escalateTochillbaby
   * @function escalateTochillbaby
   * @param {object} data lead_id
   */
  async function escalateTochillbaby() {
    setTicketLoader(true);
    const obj = {
      lead_id: selectedRoom.lead_id,
    };
    try {
      const response = await getApiData('chat/chat-to-techteam', obj, 'POST');
      if (response.success) {
        Notification('success', response.message);
        setescalateLoader(true);
        if (response.data) {
          messages.push(response.data);
          dispatch(setMessages(messages));
        }
      } else {
        Notification('error', response.message);
        setescalateLoader(false);
      }
      setTicketLoader(false);
    } catch (error) {
      console.log(error);
      Notification('error', messageArray['err.wenWrong']);
      setescalateLoader(false);
      setTicketLoader(false);
    }
  }

  // Pending Chat api call
  async function pendingChat() {
    // setTicketLoader(true);
    const obj = {
      lead_id: selectedRoom.lead_id,
    };
    try {
      const response = await getApiData('chat/pending-chat', obj, 'POST');
      if (response.success) {
        Notification('success', response.message);
        setescalateLoader(true);
        // if (response.data) {
        //   messages.push(response.data);
        //   dispatch(setMessages(messages));
        // }
      } else {
        Notification('error', response.message);
      }
    } catch (error) {
      console.log(error);
      Notification('error', messageArray['err.wenWrong']);
    }
  }

  // Pin Chat api call
  async function handlePinChat() {
    // setTicketLoader(true);
    const obj = {
      chat_id: selectedRoom?.id,
    };
    try {
      setPinChatLoader(true);
      const response = await getApiData('chat/pin-chat', obj, 'POST');
      if (response.success) {
        Notification('success', response.message);
        // setescalateLoader(true);
        setIsPinChat(!isPinChat);
        dispatch(getChatList('', chatTab));

        // if (response.data) {
        //   messages.push(response.data);
        //   dispatch(setMessages(messages));
        // }
      } else {
        Notification('error', response.message);
      }
    } catch (error) {
      console.log(error);
      Notification('error', messageArray['err.wenWrong']);
    } finally {
      setPinChatLoader(false);
    }
  }

  function submit(data = {}) {
    setReturnLoader(true);
    setTicketLoader(false);
    messages.push(data);
    setLastReturn(data);
    dispatch(setMessages(messages));
    if (chatTab === 'Active') {
      dispatch(setChatTab('Closed'));
      dispatch(getChatList('', 'Closed'));
    }
    // set on refresh  return btn, update msg on side list
    setState((pre) => ({ ...pre, visible: false }));
  }
  const returnToBrand = () => {
    setState((pre) => ({ ...pre, initObj: {}, visible: true }));
  };

  const getReceiverUser = (obj) => {
    return {
      ...obj,
      avtar_url: obj.ravtar_url,
      name: obj.rname,
    };
  };
  if (allChats) {
    selectedRoom = getReceiverUser(selectedRoom);
  }

  // this function for set bg color for user avatar
  // function getColor(name = '') {
  //   const obj = {};
  //   const trimmedName = name.trim(); // Trim any leading or trailing whitespace

  //   const bgObj = colorArray().find(
  //     (item) => item.name === trimmedName.charAt(0).toUpperCase(),
  //   );
  //   const secondLetter = colorArray().find(
  //     (item) => item.name === trimmedName.charAt(1).toUpperCase(),
  //   );

  //   if (trimmedName !== '') {
  //     obj.name = bgObj && bgObj.name ? bgObj.name : '';
  //     if (secondLetter && secondLetter.name) {
  //       obj.name += secondLetter.name;
  //     }
  //   }
  //   obj.color = bgObj && bgObj.Color ? bgObj.Color : '#3498db';

  //   return obj;
  // }

  useEffect(() => {
    setTypingData(typingData);
  }, [typingData]);

  if (loading) {
    // return <Loader />;
  }

  const isTyping =
    isObject(typingDataState) &&
    !isEmpty(typingDataState) &&
    selectedRoom.lead_id === typingDataState.lead_id &&
    !typingDataState.stop;

  // const colorObj = getColor(selectedRoom.name);

  const ActionWrapper = styled.div`
    display: flex;
    align-items: center;
    justify-content: center;
    .popMainDiv {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2px;
      justify-content: center;
      .isoDropdownLink {
        width: 100%;
        height: 36px;
        padding: 8px 10px 8px 16px;
        cursor: pointer;
        gap: 10px;
        border-radius: 4px;
        background: #ffffff;
        span {
          font-family: Inter;
          font-size: 12px;
          font-weight: 400;
          line-height: 20px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;

          color: #565e6c;
        }
      }
      .isoDropdownLink:hover {
        background: #e9f9ff;

        span {
          font-family: Inter;
          font-size: 12px;
          font-weight: 700;
          line-height: 20px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #04b8ff;
        }
      }
    }
  `;

  const content = () => {
    return (
      <ActionWrapper>
        <div className="popMainDiv">
          {[
            {
              id: 'Submit to Tech Team',
              blueImg: submitToTechBlueImg,
              greyImg: submitToTechGreyImg,
              onClick: () => {
                if (!escalateLoader) {
                  escalateTochillbaby();
                }
                setDropDownOpen(false);
                // setDasboardVisible(!dasboardVisible);
              },
            },
            {
              id: 'Return to brand',
              blueImg: submitToTechBlueImg,
              greyImg: submitToTechGreyImg,
              onClick: () => {
                if (!returnLoader) {
                  returnToBrand();
                }
                setDropDownOpen(false);
                // setDasboardVisible(!dasboardVisible);
              },
            },
            {
              id: 'Pending',
              blueImg: pendingBlueImg,
              greyImg: pendingGreyImg,
              onClick: () => {
                pendingChat();
                setDropDownOpen(false);
                // history.push({
                //   pathname: '/dashboard/marketing/audience',
                //   state: { data: item },
                // });
                // setDasboardVisible(!dasboardVisible);
              },
            },
            {
              id: 'Solved and Close',
              blueImg: solveCloseBlueImg,
              greyImg: solveCloseGreyImg,
              onClick: () => {
                if (ticketLoader) return null;
                setDropDownOpen(false);
                closeTicket();
              },
            },
          ].map((item, index) => {
            console.log(
              'chatTab===>',
              selectedRoom.status,
              uData.user_type,
              chatTab,
              selectedRoom.tech_team_close,
              (selectedRoom.tech_team_close || chatTab === 'Closed') &&
                item.id === 'Return to brand' &&
                uData.user_type !== 'chillbaby_admin',
            );
            if (
              item.id === 'Solved and Close' &&
              (uData.user_type === 'chillbaby_admin' ||
                selectedRoom.status === 'Closed')
            ) {
              return null;
            }
            if (
              uData.user_type !== 'chillbaby_admin' &&
              item.id === 'Return to brand'
            ) {
              return null;
              // eslint-disable-next-line no-else-return
            } else if (
              (selectedRoom.tech_team_close || chatTab === 'Closed') &&
              item.id === 'Return to brand' &&
              uData.user_type === 'chillbaby_admin'
            ) {
              return null;
            }

            if (
              (selectedRoom.status === 'Closed' ||
                uData.user_type === 'chillbaby_admin') &&
              item.id === 'Submit to Tech Team'
            ) {
              return null;
            }

            return (
              <div
                key={item?.id}
                className="isoDropdownLink"
                onMouseOver={() => setIsHover(index)}
                onMouseOut={() => setIsHover(null)}
                onClick={item.onClick}
              >
                <img
                  className="iconImg"
                  src={isHover === index ? item.blueImg : item.greyImg}
                  style={{ width: 20, height: 20, marginRight: 12 }}
                  alt="noIcon"
                />
                <IntlMessages id={item.id} />
              </div>
            );
          })}
        </div>
      </ActionWrapper>
    );
  };

  return (
    <ChatWindow className="ChatWindow">
      <ChatRooms lastReturn={lastReturn} />
      <ChatBox style={{ height: '100%' }}>
        <Modal
          visible={openCompose}
          onCancel={() => dispatch(toggleCompose())}
          title={<IntlMessages id="Compose Message" />}
          footer={null}
        >
          <MessageDialog>
            <h5>Starting your chat with...</h5>
            <InputName
              users={users}
              setComposedId={(id) => dispatch(setComposedId(id))}
              className={className}
            />
            <ComposeMessage
              autosize={{ minRows: 5, maxRows: 9 }}
              showButton
              rows={8}
            />
          </MessageDialog>
        </Modal>
        {!isEmpty(selectedRoom) && (
          <ToggleViewProfile style={{ justifyContent: 'space-between' }}>
            {/* <span onClick={() => dispatch(toggleViewProfile(selectedChatRoom))}> */}
            <div className="userInfoMain">
              {/* <Avatar
                size={40}
                src={selectedRoom.avtar_url || ''}
                icon={colorObj.name === '' ? <UserOutlined /> : null}
                style={{
                  marginRight: 20,
                  backgroundColor: colorObj.color || '#fff',
                  fontSize: 16,
                }}
              >
                {colorObj.name || '-'}
              </Avatar> */}
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                }}
              >
                <p
                  style={{
                    fontFamily: 'Inter',
                    fontSize: '16px',
                    fontWeight: 700,
                    lineHeight: '26px',
                    textAlign: 'left',
                    textUnderlinePosition: 'from-font',
                    textDecorationSkipInk: 'none',
                    color: '#171A1F',
                  }}
                >
                  {selectedRoom.ticket}
                </p>
                <p
                  style={{
                    width: '3px',
                    height: '3px',
                    borderRadius: '50%',
                    backgroundColor: '#171A1F',
                  }}
                />

                <p
                  style={{
                    fontFamily: 'Inter',
                    fontSize: '16px',
                    fontWeight: 600,
                    lineHeight: '26px',
                    textAlign: 'left',
                    textUnderlinePosition: 'from-font',
                    textDecorationSkipInk: 'none',
                    color: '#171A1F',
                  }}
                >
                  {selectedRoom.name}
                </p>
              </div>
              {/* <h5 className="title">{selectedRoom.name}</h5> */}
              {/* <p
                style={{ height: isTyping ? 16 : 0, opacity: isTyping ? 1 : 0 }}
              >
                <IntlMessages id="common.typing" />
              </p> */}
              {/* selectedRoom.is_tech_team ? null : */}
            </div>
            <ToggleViewProfile
              style={{ justifyContent: 'space-between', margin: '0px 6px' }}
            >
              <div style={{ display: 'none', alignItems: 'center' }}>
                {uData.user_type === 'chillbaby_admin' ? (
                  selectedRoom.tech_team_close ? null : chatTab ===
                    'Closed' ? null : (
                    <CButton
                      type="primary"
                      style={{ marginRight: 10 }}
                      onClick={returnToBrand}
                      loading={ticketLoader}
                      disabled={returnLoader}
                    >
                      <IntlMessages id="chat.return.ticket" />
                    </CButton>
                  )
                ) : selectedRoom.status ===
                  'Closed' ? null : uData.user_type ===
                  'chillbaby_admin' ? null : (
                  <CButton
                    type="primary"
                    style={{ marginRight: 10 }}
                    onClick={escalateTochillbaby}
                    loading={ticketLoader}
                    disabled={escalateLoader}
                  >
                    <IntlMessages id="chat.escalate.ticket" />
                  </CButton>
                )}
                {/* <MoreOutlined style={{ color: '#000', fontSize: 24 }} /> */}
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                {/* {uData.user_type ===
                'chillbaby_admin' ? null : selectedRoom.status ===
                  'Closed' ? null : (
                  <CButton
                    type="primary"
                    style={{ marginRight: 10 }}
                    onClick={closeTicket}
                    loading={ticketLoader}
                    disabled={ticketLoader}
                  >
                    <IntlMessages id="chat.close.ticket" />
                  </CButton>
                )} */}
                <Button
                  onClick={() => {
                    handlePinChat();
                  }}
                  loading={pinChatLoader}
                  style={{
                    padding: 0,
                    border: 'none',
                    background: 'none',
                    // marginBottom: 5,
                  }}
                >
                  <img
                    src={isPinChat ? pinBlueImg : pinGreyImg}
                    alt="pin-chat"
                  />{' '}
                </Button>
                <ReactPopOver
                  content={
                    <div
                      style={{
                        margin: '10px',
                        backgroundColor: '#FFFFFF',
                        width: 220,
                        position: 'absolute',
                        right: '-19px',
                        padding: '12px 14px 12px 12px',
                        gap: 0,
                        borderRadius: '4px',
                        boxShadow:
                          '0px 4px 9px 0px #171A1F1C, 0px 0px 2px 0px #171A1F1F',
                      }}
                    >
                      {content()}
                    </div>
                  }
                  isOpen={isDropDownOpen}
                  onClickOutside={() => setDropDownOpen(!isDropDownOpen)}
                  arrowPointAtCenter
                  className="action-popover"
                  positions="bottom"
                >
                  <Button
                    type="text"
                    onClick={() => {
                      setDropDownOpen(!isDropDownOpen);
                    }}
                    style={{
                      padding: 0,
                      border: 'none',
                      background: 'none',
                    }}
                  >
                    <img
                      src={isDropDownOpen ? horizonalDots : horizonalDots}
                      alt="action"
                    />
                  </Button>
                </ReactPopOver>
              </div>
            </ToggleViewProfile>
          </ToggleViewProfile>
        )}
        <Messages allChats={allChats} />
        <div>
          {!isEmpty(selectedRoom) && (
            <div className="userInfoMain" style={{ padding: '10px 25px' }}>
              <p
                style={{
                  height: isTyping ? 16 : 0,
                  opacity: isTyping ? 1 : 0,
                }}
              >
                <IntlMessages id="common.typing" />
              </p>
            </div>
          )}
          <ComposeMessage
            allChats={allChats}
            autosize={{ minRows: 2, maxRows: 6 }}
          />
        </div>
      </ChatBox>
      {viewProfile !== false ? (
        <ViewProfile
          user={selectedRoom}
          toggleViewProfile={() => dispatch(toggleViewProfile())}
          viewProfile={viewProfile}
        />
      ) : null}
      {visible ? (
        <ReturnToBrandForm
          // type={type}
          leadId={selectedRoom.lead_id}
          onlyId={selectedRoom.id}
          initVal={initObj}
          handleSubmit={submit}
          visible={visible}
          onClose={() =>
            setState((pre) => ({ ...pre, initObj: {}, visible: false }))
          }
        />
      ) : null}
    </ChatWindow>
  );
}

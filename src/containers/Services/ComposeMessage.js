/* eslint-disable prefer-destructuring */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
import React, { useEffect, useState } from 'react';
import { isObject, isEmpty } from 'lodash';
import { injectIntl } from 'react-intl';
import { useDispatch, useSelector } from 'react-redux';
import Upload from '@chill/components/uielements/upload';
import Button from '@chill/components/uielements/button';
import notification from '@chill/components/Notification';
import chatActions from '@chill/redux/chat/actions';
import uploadChat from '@chill/assets/images/chat-upload.png';
import chatSend from '@chill/assets/images/chat-send.png';

import getApiData from '@chill/lib/helpers/apiHelper';
import { ComposeMessageWrapper, Textarea } from './Messages.styles';

const { sendRequest, setMessages, sendMessage } = chatActions;

let isTypingInput = false;
let stopTypingTimeout;

/**
 *
 *@module CustomerService
 *
 */
function ComposeMessage(props) {
  const dispatch = useDispatch();
  const selectedChatRoom = useSelector((state) => state.Chat.selectedChatRoom);
  const selectedRoom = isObject(selectedChatRoom) ? selectedChatRoom : {};
  const uData = useSelector((state) => state.Auth.userData);
  const messages = useSelector((state) => state.Chat.messages);
  const { chatRooms } = useSelector((state) => state.Chat);
  const uId = isObject(uData) && uData.id ? uData.id : '';
  // const allChats = props.allChats;
  const [value, setValue] = React.useState('');
  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);
  const [diableText, setDisableText] = useState(0);
  /**
   * this function for display typing bubble
   * @function typing
   * @param {string} data type
   */
  const typing = (msgTxt, type) => {
    dispatch(
      sendRequest('/api/chat/chat-typing', {
        lead_id: selectedRoom.lead_id,
        receiver_id: selectedRoom.u_id,
        stop: type === 'stop',
      }),
    );
  };

  const resetStopTypingTimeout = () => {
    if (isTypingInput) {
      clearTimeout(stopTypingTimeout);
    }
    stopTypingTimeout = setTimeout(() => {
      isTypingInput = false;
      typing('', 'stop');
      stopTypingTimeout = undefined;
    }, 700);
  };

  /**
   * this function for input textarea
   * @function onChange
   * @param {string} data text
   */
  const onChange = (event) => {
    setValue(event.target.value);

    if (event.target.value) {
      if (isTypingInput === false) isTypingInput = true;
      typing('', 'start');
      resetStopTypingTimeout();
    }
  };

  const onKeyPress = (event) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      const inputVal = value ? value.trim() : '';
      if (inputVal && inputVal.length > 0) {
        dispatch(sendMessage(inputVal));
        setValue('');
      } else {
        notification('error', messageArray['req.msg']);
      }
    }
  };

  /**
   * this function for upload file in message
   * @function uploadFileMsg
   * @param {object} data uid, name, size, type
   */
  const uploadFileMsg = async (file) => {
    const msg = [...messages];
    const msgData = {
      lead_id: selectedRoom.lead_id,
      sender_id: uId,
      u_id: selectedRoom.uId,
      uid: file.uid,
      file_name: file.name,
      size: file.size,
      fileType: file.type,
      type: 'file',
      loading: true,
    };
    msg.push(msgData);
    dispatch(setMessages(msg));
    const msgIndex = msg.findIndex((fl) => fl.uid === file.uid);
    try {
      const formData = new FormData();
      formData.append('attachment', file);
      // formData.append('lead_id', selectedRoom.lead_id);
      // formData.append('receiver_id', selectedRoom.u_id);
      // formData.append('sender_id', uId);
      const response = await getApiData(
        'uploadChatAttachment',
        formData,
        'POST',
        {},
        true,
      );
      if (response && response.success && msgIndex > -1) {
        const resParamsData = {
          lead_id: selectedRoom.lead_id,
          receiver_id: selectedRoom.u_id,
          sender_id: uId,
          ...response,
        };
        const paramRes = await getApiData(
          'insertAttachment',
          resParamsData,
          'POST',
        );
        if (paramRes && paramRes.success) {
          msgData.file_url = response.fullUrl ? response.fullUrl : '';
        } else {
          notification('error', 'Unable to upload file');
        }
        msgData.loading = false;
        msg[msgIndex] = msgData;
        dispatch(setMessages(msg));
      } else {
        msgData.loading = false;
        msgData.error = true;
        notification('error', 'Unable to upload file');
        dispatch(setMessages(msg));
      }
    } catch (err) {
      console.log(err);
      if (msgIndex > -1) {
        msgData.loading = false;
        msgData.error = true;
        msg[msgIndex] = msgData;
      }
      dispatch(setMessages(msg));
      notification('error', 'Unable to upload file');
    }
  };

  const beforeUpload = () => {
    // const fType = file && file.type ? file.type : '';
    // const isValidFile = chatFilesVal(fType);
    const isValidFile = true;
    if (!isValidFile) {
      notification('error', 'Please upload valid file');
      return false;
    }
    return true;
  };

  const uploaderProps = {
    name: 'attatchment',
    showUploadList: false,
    action: uploadFileMsg,
    method: 'post',
    beforeUpload,
  };

  const disable =
    (!isEmpty(selectedRoom) && selectedRoom.status === 'Closed') ||
    isEmpty(selectedRoom);

  useEffect(() => {
    if (
      uData.user_type === 'chillbaby_admin' &&
      !isEmpty(chatRooms) &&
      chatRooms[0]?.tech_team_close === 1
    ) {
      if (chatRooms[0]?.tech_team_close) {
        setDisableText(chatRooms[0]?.tech_team_close);
        console.log('found----', disable);
      }
    }
  }, [chatRooms]);
  // console.log('disable----', diableText);
  // const msg = [...messages];
  // const lastIndex = isArray(msg) ? msg.length - 1 : -1;
  // const loading = lastIndex > -1 ? msg[lastIndex].loading : false;

  return (
    <ComposeMessageWrapper>
      <div className="mainDiv">
        <Textarea
          autosize={props.autosize}
          value={value}
          onChange={onChange}
          onKeyPress={onKeyPress}
          className="textInputStyle"
          style={{
            padding: 0,
            paddingTop: 14,
          }}
          placeholder={messageArray['chat.input.placeholder']}
          disabled={
            uData.user_type === 'chillbaby_admin' ? diableText : disable
          }
        />
        <div
          className="sendMessageButton"
          style={{
            justifyContent:
              !isEmpty(selectedRoom) && selectedRoom.status === 'Closed'
                ? 'flex-end'
                : 'space-between',
          }}
        >
          {!isEmpty(selectedRoom) && selectedRoom.status === 'Closed' ? null : (
            <Upload
              disabled={
                uData.user_type === 'chillbaby_admin' ? diableText : disable
              }
              {...uploaderProps}
            >
              <img src={uploadChat} alt="uploadChat" />
            </Upload>
          )}
          <Button
            // className="changeBtn"
            type="text"
            style={{
              padding: 0,
              border: 'none',
              backgroundColor: 'transparent',
            }}
            onClick={() => {
              const inputVal = value ? value.trim() : '';
              if (inputVal && inputVal.length > 0) {
                dispatch(sendMessage(inputVal));
                setValue('');
              } else {
                notification('error', messageArray['req.msg']);
              }
            }}
            icon={<img src={chatSend} alt="chatSend" />}
            disabled={
              uData.user_type === 'chillbaby_admin' ? diableText : disable
            }
          >
            <intlMessages id="common.sendMsg" />
          </Button>
        </div>

        {/* {!isEmpty(selectedRoom) && selectedRoom.status === 'Closed' ? null : (
        <div className="sendMessageButton">
          <Upload
            disabled={isEmpty(selectedRoom) || loading || allChats}
            {...uploaderProps}
          >
            <Button
              disabled={isEmpty(selectedRoom) || loading || allChats}
              shape="circle"
              icon={<PaperClipOutlined />}
            />
          </Upload>
        </div>
      )} */}
        {/* {props.showButton && ( */}
      </div>
      {/* )} */}
    </ComposeMessageWrapper>
  );
}

export default injectIntl(ComposeMessage);

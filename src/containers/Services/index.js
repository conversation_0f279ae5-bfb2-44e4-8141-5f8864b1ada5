/* eslint-disable global-require */
import React, { useEffect, useState } from 'react';
import { Col, Row, Progress, Affix, Select, Divider } from 'antd';
import { useSelector } from 'react-redux';
import IntlMessages from '@chill/components/utility/intlMessages';
import Counts from '@chill/components/Counts';
import { findIndex, isEmpty } from 'lodash';
// import chatSVG from '@chill/assets/images/chat.svg';
// import blockUserSVG from '@chill/assets/images/block-user.svg';
import mailBlue from '@chill/assets/images/mail-download-02.png';
import mailOpenBlue from '@chill/assets/images/mail-open.png';
import closedTicket from '@chill/assets/images/closed-ticket.png';
import ServiceStyles from './Services.styles';
import Chat from './Chat';
import IsoWidgetsWrapper from '../Widgets/WidgetsWrapper';
import ReportsWidget from '../Widgets/Report/ReportWidget';

const Services = () => {
  const { tickets, productList } = useSelector((state) => state.Chat);
  const { view } = useSelector((state) => state.App);
  const [deviceData, setDeviceData] = useState({});

  useEffect(() => {
    setDeviceData();
  }, [productList]);

  const MobileView = view === 'MobileView' || view === 'TabView';

  const Array = [
    {
      // iconName: 'messageOutlined',
      title: <IntlMessages id="ticket.total" />,
      text: tickets?.getAll || 0,
      iconColor: '#ea80fc',
      image: mailBlue,
    },
    // {
    //   title: <IntlMessages id="ticket.active" />,
    //   text: tickets?.active || 0,
    //   image: chatSVG,
    // },
    {
      // iconName: 'UserAddOutlined'
      image: mailOpenBlue,
      title: <IntlMessages id="ticket.open" />,
      text: tickets?.open || 0,
      iconColor: '#1e88e5',
    },
    {
      title: <IntlMessages id="ticket.closed" />,
      text: tickets?.closed || 0,
      image: closedTicket,
    },
  ];

  const counts = () => {
    return Array.map((item) => {
      return (
        <Counts
          iconName={item.iconName}
          title={item.title}
          text={item.text}
          iconColor={item.iconColor}
          image={item.image}
        />
      );
    });
  };

  const renderSecondCol = () => {
    return (
      <>
        <Row>
          <Col
            xs={24}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <span
              style={{
                fontFamily: 'Inter',
                fontSize: '11px',
                fontWeight: 700,
                lineHeight: '20px',
                textAlign: 'left',
                textUnderlinePosition: 'from-font',
                textDecorationSkipInk: 'none',
                color: '#171A1F',
                marginBottom: '4px',
              }}
            >
              Avg Tickets Resolved Today
            </span>
          </Col>
          <Col xl={22} lg={23} sm={24} md={24} xs={24} className="chartCol">
            <IsoWidgetsWrapper
              style={{
                height: '100%',
                alignSelf: 'center',
                textAlign: 'center',
              }}
            >
              <ReportsWidget
                labelClsName="titleText"
                // label={<IntlMessages id="ticket.avg.resolved" />}
                style={{
                  height: '100%',
                  borderRadius: 25,
                  border: 0,
                  boxShadow: '5px 5px 5px 5px #0000',
                  backgroundColor: 'transparent',
                  alignItems: 'center !important',
                  textAlign: 'center',
                }}
                widgetClassName="avgTickets"
              >
                <div
                  className="progressChart"
                  style={{
                    width: '100%',
                    alignItems: 'center',
                    textAlign: 'center',
                  }}
                >
                  <Progress
                    type="circle"
                    width={108}
                    height={108}
                    strokeWidth={10}
                    strokeColor="#00D9C0"
                    // strokeLinecap="square"
                    percent={tickets?.todayClosed || 50}
                    trailColor="#F2F3F9"
                  />
                </div>
              </ReportsWidget>
            </IsoWidgetsWrapper>
          </Col>
          <Col xl={22} lg={23} sm={24} md={24} xs={24}>
            <Divider
              style={{
                border: '1px solid #F3F5F7',
                margin: '10px 0px 20px 0px',
              }}
            />
            <div>
              <span
                style={{
                  fontFamily: 'Inter',
                  fontSize: 11,
                  fontWeight: 700,
                  lineHeight: '22px',
                  textAlign: 'left',
                  textDecoration: 'none',
                  color: '#171A1F',
                }}
              >
                <IntlMessages id="ticket.ticketStatus" />
              </span>
            </div>
            <div>{counts()}</div>
            <Divider
              style={{
                border: '1px solid #F3F5F7',
                margin: '55px 0px 20px 0px',
              }}
            />
          </Col>
        </Row>
      </>
    );
  };

  const getDeviceData = (e) => {
    const index = findIndex(productList, (i) => i.device_ssid === e);
    setDeviceData(productList[index]);
  };

  return (
    <ServiceStyles>
      <Row>
        <Col xl={17} xxl={21} lg={17} sm={24} md={24} xs={24}>
          <Chat />
          {!isEmpty(productList) && (
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                backgroundColor: '#fff',
                padding: 20,
                margin: '20px 0px',
              }}
            >
              <Row>
                <Col xl={6} sm={24} xs={24} lg={7} md={24}>
                  <Select
                    style={{ width: '100%' }}
                    className="brandDropdown"
                    placeholder={<IntlMessages id="chat.selectDevice" />}
                    allowClear
                    onChange={(e) => getDeviceData(e)}
                    value={deviceData?.device_ssid}
                  >
                    {productList.map((item) => {
                      return (
                        <Select.Option value={item.device_ssid}>
                          {item.device_ssid}
                        </Select.Option>
                      );
                    })}
                  </Select>
                </Col>
              </Row>
              {!isEmpty(deviceData) && (
                <div>
                  <Row style={{ marginTop: 10, marginBottom: 10 }}>
                    <Col>
                      <IntlMessages id="sidebar.deviceData" />:
                    </Col>
                  </Row>
                  <Row style={{ marginBottom: 2 }}>
                    <Col
                      xs={24}
                      sm={12}
                      md={6}
                      lg={6}
                      xl={4}
                      style={{ color: '#808080' }}
                    >
                      <IntlMessages id="deviceData.bleSsid" />:
                    </Col>
                    <Col>{deviceData?.device_ssid || '-'}</Col>
                  </Row>
                  <Row style={{ marginBottom: 2 }}>
                    <Col
                      xs={24}
                      sm={12}
                      md={6}
                      lg={6}
                      xl={4}
                      style={{ color: '#808080' }}
                    >
                      <IntlMessages id="deviceData.macAddress" />:
                    </Col>
                    <Col>{deviceData?.product_id || '-'}</Col>
                  </Row>
                  <Row style={{ marginBottom: 2 }}>
                    <Col
                      xs={24}
                      sm={12}
                      md={6}
                      lg={6}
                      xl={4}
                      style={{ color: '#808080' }}
                    >
                      <IntlMessages id="deviceData.mfgDate" />:
                    </Col>
                    <Col>{deviceData?.mf_date || '-'}</Col>
                  </Row>
                  <Row style={{ marginBottom: 2 }}>
                    <Col
                      xs={24}
                      sm={12}
                      md={6}
                      lg={6}
                      xl={4}
                      style={{ color: '#808080' }}
                    >
                      <IntlMessages id="deviceData.serialNumber" />:
                    </Col>
                    <Col>{deviceData?.sr_number || '-'}</Col>
                  </Row>
                  <Row style={{ marginBottom: 2 }}>
                    <Col
                      xs={24}
                      sm={12}
                      md={6}
                      lg={6}
                      xl={4}
                      style={{ color: '#808080' }}
                    >
                      <IntlMessages id="deviceData.productName" />:
                    </Col>
                    <Col>{deviceData?.product_name || '-'}</Col>
                  </Row>
                  <Row style={{ marginBottom: 2 }}>
                    <Col
                      xs={24}
                      sm={12}
                      md={6}
                      lg={6}
                      xl={4}
                      style={{ color: '#808080' }}
                    >
                      <IntlMessages id="deviceData.productModal" />:
                    </Col>
                    <Col>{deviceData?.product_model || '-'}</Col>
                  </Row>
                  <Row style={{ marginBottom: 2 }}>
                    <Col
                      xs={24}
                      sm={12}
                      md={6}
                      lg={6}
                      xl={4}
                      style={{ color: '#808080' }}
                    >
                      <IntlMessages id="deviceData.firmwareVersion" />:
                    </Col>
                    <Col>{deviceData?.version || '-'}</Col>
                  </Row>
                </div>
              )}
            </div>
          )}
        </Col>
        <Col
          className="secondCol"
          xl={7}
          xxl={3}
          sm={24}
          xs={24}
          lg={7}
          md={24}
        >
          {MobileView ? (
            renderSecondCol()
          ) : (
            <Affix
              offsetTop={100}
              style={
                {
                  // paddingTop: 10,
                }
              }
            >
              {renderSecondCol()}
            </Affix>
          )}
        </Col>
      </Row>
    </ServiceStyles>
  );
};

export default Services;

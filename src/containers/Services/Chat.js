import React, { lazy, useEffect } from 'react';
import { isObject, isEmpty, isArray } from 'lodash';
import { withRouter } from 'react-router';
import { useDispatch, useSelector } from 'react-redux';
// import { getDefaultPath } from '@chill/lib/helpers/url_sync';
import chatActions from '@chill/redux/chat/actions';
import { ChatViewWrapper } from './Messages.styles';

const DesktopView = lazy(() => import('./DesktopView'));
const MobileView = lazy(() => import('./MobileView'));
const { getChatList, setMessages } = chatActions;

function Chat(props) {
  const { history } = props;
  const query =
    isObject(history) &&
    isObject(history.location) &&
    isObject(history.location.query)
      ? history.location.query
      : {};
  const latestMsg =
    isObject(query.latestMsg) && !isEmpty(query.latestMsg)
      ? query.latestMsg
      : 'init';
  const userId = query.userId || '';
  const uData = useSelector((state) => state.Auth.userData);
  const { view, height } = useSelector((state) => state.App);
  const { socketData } = useSelector((state) => state.Chat);
  const dispatch = useDispatch();
  const ChatView = view === 'MobileView' ? MobileView : DesktopView;
  // const ChatView = DesktopView;
  // const preKeys = getDefaultPath();
  const preKeys = '';
  const currentPage = isArray(preKeys) && !isEmpty(preKeys) ? preKeys[0] : '';
  const allChats =
    isObject(uData) &&
    uData.user_type === 'admin' &&
    currentPage === 'all-chats';

  useEffect(() => {
    if (isObject(socketData) && !isEmpty(socketData)) {
      if (allChats) {
        dispatch(setMessages([]));
      }
      // if (isObject(selectedChatRoom) && !isEmpty(selectedChatRoom) && isArray(messages) && !isEmpty(messages))
      // 	type = null;
      if (userId) {
        dispatch(getChatList(userId));
      } else {
        dispatch(getChatList(latestMsg));
      }
    }
  }, [dispatch, socketData, latestMsg]);

  return (
    <ChatViewWrapper
      style={{
        height: view === 'MobileView' ? height - 108 : height - 138,
      }}
    >
      <ChatView height={height} view={view} allChats={allChats} />
    </ChatViewWrapper>
  );
}

export default withRouter(Chat);

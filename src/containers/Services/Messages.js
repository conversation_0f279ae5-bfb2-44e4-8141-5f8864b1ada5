/* eslint-disable no-nested-ternary */
/* eslint-disable consistent-return */
/* eslint-disable global-require */
/* eslint-disable no-return-assign */
import React, { useState, useRef } from 'react';
import { Avatar, Spin } from 'antd';
import { isObject, isEmpty, isArray } from 'lodash';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';
import ScrollView from 'react-inverted-scrollview';
// import Progress from '@chill/components/uielements/progress';
import Button from '@chill/components/uielements/button';
import IntlMessages from '@chill/components/utility/intlMessages';
import chatActions from '@chill/redux/chat/actions';
import {
  DownloadOutlined,
  DownOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { colorArray } from '@chill/lib/helpers/utility';
import pickerSVG from '@chill/assets/images/picker.svg';
import { MessageSingle, MessageChatWrapper } from './Messages.styles';

const {
  getChatList,
  // toggleViewProfile,
  // toggleMobileProfile,
  sendRequest,
  setMessages,
  setChatNotification,
} = chatActions;

/**
 *
 *@module Messages
 *
 */
export default function Messages() {
  const dispatch = useDispatch();
  const [pagination, setPagination] = useState({
    page: 1,
    loading: false,
  });
  const [showScrollToBtm, setShowScrollToBtm] = useState(false);

  let scrollRef = useRef(null);
  const { selectedChatRoom, messages, chatTab } = useSelector(
    (state) => state.Chat,
  );
  const selectedRoom = isObject(selectedChatRoom) ? selectedChatRoom : {};
  const user = useSelector((state) => state.Auth.userData);
  const userId = isObject(user) && !isEmpty(user) ? user.id : '';
  const scrollToBottom = () => {
    const messageChat = document.getElementById('messageChat');
    messageChat.scrollTop = messageChat.scrollHeight;
    if (scrollRef) scrollRef.scrollToBottom();
  };

  /**
   * This function for get messages
   * @function getMsgs
   * @param {string} type " "
   */
  async function getMsgs(type = '') {
    dispatch(
      sendRequest(
        '/api/chat/chat-details',
        {
          sender_id: selectedRoom.sender_id,
          lead_id: selectedRoom.lead_id,
          u_id: selectedRoom.u_id,
          page: type === 'init' ? 1 : pagination.page,
          per_page: 30,
        },
        (data) => {
          const resData = isObject(data) ? data : {};
          let oldMsgs = isArray(messages) ? messages : [];
          const msgs = isArray(resData.data) ? resData.data : [];
          if (pagination.page > 1) oldMsgs = [...msgs, ...oldMsgs];
          dispatch(setMessages(type === 'loadMore' ? oldMsgs : msgs));
          dispatch(getChatList('', chatTab));
          setPagination((pre) => ({
            ...pre,
            loadMore: resData.next_enable,
            page: pre.page + 1,
            loading: false,
            pageLoad: false,
          }));
          dispatch(setChatNotification());
          dispatch(sendRequest('/api/chat/get-badges-count'));
        },
      ),
    );
  }

  React.useEffect(() => {
    if (isObject(selectedChatRoom) && !isEmpty(selectedChatRoom)) {
      setPagination((pre) => ({ ...pre, page: 1, pageLoad: true }));
      setShowScrollToBtm(false);
      getMsgs('init');
    }
  }, [selectedChatRoom]);

  const loadMore = () => {
    setPagination((pre) => ({ ...pre, loading: true }));
    getMsgs('loadMore');
  };

  // this function for set bg color for user avatar
  function getColor(name = '') {
    const obj = {};
    const trimmedName = name.trim(); // Trim any leading or trailing whitespace

    const bgObj = colorArray().find(
      (item) => item.name === trimmedName.charAt(0).toUpperCase(),
    );
    const secondLetter = colorArray().find(
      (item) => item.name === trimmedName.charAt(1).toUpperCase(),
    );

    if (trimmedName !== '') {
      obj.name = bgObj && bgObj.name ? bgObj.name : '';
      if (secondLetter && secondLetter.name) {
        obj.name += secondLetter.name;
      }
    }
    obj.color = bgObj && bgObj.Color ? bgObj.Color : '#3498db';

    return obj;
  }

  const renderMsgContent = (message) => {
    if (message.type === 'file') {
      return (
        <div
          className="messageContentText attchmentDivStyle"
          style={{
            margin: '0px',
          }}
        >
          <div
            className="fileInfoContent"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
            }}
          >
            <img src={pickerSVG} alt="icon" style={{ marginRight: '5px' }} />
            <div className="fileInfo">
              <p>{message.file_name}</p>
            </div>
            {message.file_url ? (
              <DownloadOutlined
                className="downloadIconStyle"
                onClick={() => window.open(message.file_url)}
                // onClick={() => {
                //   downloadStaticFile({
                //     url: message.file_url,
                //     fileName: message.file_name,
                //   });
                // }}
              />
            ) : null}
          </div>
        </div>
      );
    }
  };

  const renderMessage = (message, index) => {
    const isUser = message.u_id ? userId === message.sender_id : true;
    console.log(
      `hello ~ file: Messages.js:161 ~ renderMessage ~ isUser:`,
      message,
    );
    const messageUser = isUser ? user : selectedChatRoom;
    const colorObj = getColor(
      isUser
        ? user.full_name
        : message.full_name
        ? message.full_name
        : message.name,
    );
    // const colorObj = getColor(isUser ? user.full_name : selectedChatRoom.name);
    // if (allChats) {
    //   isUser = message.sender_id === selectedChatRoom.sender_id;
    //   messageUser = isUser
    //     ? selectedChatRoom
    //     : getReceiverUser(selectedChatRoom);
    // }
    const lastIndex = isArray(messages) && messages.length - 1;
    console.log(
      `hello ~ file: Messages.js:181 ~ renderMessage ~ lastIndex:`,
      lastIndex,
      index,
    );
    return (
      <MessageSingle
        key={message.createdAt}
        // style={
        //   lastIndex === index
        //     ? {}
        //     : {
        //         borderBottom: '1px solid #eee',
        //       }
        // }
      >
        <div className={`${isUser ? 'messageUser' : 'messageNotUser'}`}>
          <div className="messageLeftRight">
            <div className="messageGravatar">
              <Avatar
                size={36}
                src={isUser ? user.user_profile : messageUser.avtar_url}
                icon={isUser || colorObj.name !== '' ? null : <UserOutlined />}
                style={{
                  boxShadow: '1px 1px 3px #dfdfdf',
                  backgroundColor: colorObj.color || '#fff',
                  fontSize: 16,
                }}
              >
                {colorObj.name || '-'}
              </Avatar>
            </div>
            <div className="mDetailsWithDate">
              {message.type === 'file' ? (
                <>{renderMsgContent(message)}</>
              ) : (
                <div className="messageContent notUser">
                  <div className="messageContentText msgUserDetail">
                    {/* <p style={{ fontWeight: '500' }}>
                  {isUser
                    ? user.full_name
                    : message.full_name
                    ? message.full_name
                    : message.name}
                </p> */}
                  </div>
                  <div className="messageContentText">
                    <p className="message-txt">{message.message}</p>
                  </div>

                  {/* <div className="messageTime">
            <p>{timeDifference(message.createdAt)}</p>
          </div> */}
                </div>
              )}
              <div className="timeStampStyle">
                <p className="dateTextStyle">
                  {moment(message.createdAt).fromNow()}
                </p>
                <p className="dateTextStyle">
                  {moment(message.createdAt).format('HH:mm a')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </MessageSingle>
    );
  };

  const handleScroll = ({ scrollTop, scrollBottom }) => {
    if (!pagination.pageLoad) {
      if (scrollTop < 15 && !pagination.loading && pagination.loadMore) {
        loadMore();
        setShowScrollToBtm(false);
      }
      if (scrollBottom > 300 && !showScrollToBtm) {
        setShowScrollToBtm(true);
      } else if (scrollBottom < 200 && showScrollToBtm) {
        setShowScrollToBtm(false);
      }
    }
  };

  return (
    <MessageChatWrapper id="messageChat">
      {pagination.pageLoad ? (
        <div className="loaderMain">
          <Spin />
        </div>
      ) : (
        <ScrollView
          onScroll={handleScroll}
          ref={(ref) => (scrollRef = ref)}
          width="100%"
          height="100%"
        >
          {messages.length > 0 && pagination.loadMore ? (
            <div className="loaderMain">
              <Button
                loading={pagination.loading}
                onClick={() => loadMore('loadMore')}
              >
                <IntlMessages id="common.loading" />
              </Button>
            </div>
          ) : null}
          {messages.map(renderMessage)}
        </ScrollView>
      )}
      {showScrollToBtm ? (
        <Button
          className="scrollToBtn"
          onClick={scrollToBottom}
          type="circle"
          icon={<DownOutlined style={{ marginTop: 5 }} />}
        />
      ) : null}
    </MessageChatWrapper>
  );
}

import styled from 'styled-components';
import WithDirection from '@chill/lib/helpers/rtl';

const ServiceStyles = styled.div`
  .isoTabText {
    padding-left: 16px;
  }
  .isoTabRow {
    padding-right: 16px;
    padding-top: 16px;
  }
  .isoTab > div > div::before {
    position: unset;
    border-bottom-color: red;
  }
  .isoTab > div > div > div > div > div {
    padding: 0px;
  }
  .isoTab > div > div > div > div {
    padding-bottom: 16px;
  }
  .isoTab > div > div > div > div > div {
    margin: 0px 46px 0px 0px;
    @media (min-width: 768px) and (max-width: 1024px) {
      margin: 0px 20px 0px 0px;
    }
  }
  .isoTab > div > div:nth-child(1) {
    margin: 0px;
  }
  .isoTab > div > div:nth-child(2) {
    display: none;
  }
  .horizontalDevider {
    margin: 0px;
  }
  .isoFilterView {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
  }
  .isoFilterIcon {
    display: flex;
    flex-direction: row;
  }
  .hiddenCol {
    padding-bottom: 16px;
    display: flex;
    justify-content: flex-end;
    @media (min-width: 767px) {
      display: none;
    }
  }
  .isoAddProduct {
    border-left: solid;
    border-left-width: 1px;
    border-left-color: #eee;
    @media (max-width: 767px) {
      display: none;
    }
  }
  .isoFilter1 {
    padding: 0px 16px;
    border-right: solid;
    border-right-width: 1px;
    border-right-color: #eee;
    @media (min-width: 768px) and (max-width: 1024px) {
      padding: 0px 7px;
    }
  }
  & .isoFilter2 {
    padding: 0px 16px;
    @media (min-width: 768px) and (max-width: 1024px) {
      padding: 0px 6px;
    }
  }
  .popMainDiv {
    display: flex;
    flex-direction: column;
    & < div < div < div {
      background-color: red;
    }
  }
  .isoDropdownLink {
    color: #000;
    padding: 8px 0px;
    cursor: pointer;
  }
  .chartCol {
    // background: linear-gradient(
    //   138deg,
    //   rgb(20, 135, 255) 12%,
    //   rgb(41, 239, 196) 90%
    // );
    text-align: center;
    display: flex;
    flex-direction: column;
    border-radius: 14px;
  }
  .progressChart > div > div > span {
    color: #171a1f;
  }
  .secondCol {
    padding: 0px 5px 13px 12px !important;
    background-color: #ffffff;
    @media (max-width: 1023px) {
      padding: 22px 30px 22px !important;
    }
    & > div:nth-child(1) {
      justify-content: center;
    }
    & > div:nth-child(2) {
      justify-content: center;
    }
  }
`;

export default WithDirection(ServiceStyles);

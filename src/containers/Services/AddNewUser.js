import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { isObject, isArray } from 'lodash';
import { Row, Col, Select, Input, Form } from 'antd';
import Tooltip from '@chill/components/uielements/tooltip';
import Modal from '@chill/components/Feedback/Modal';
import IntlMessages from '@chill/components/utility/intlMessages';
import getApiData from '@chill/lib/helpers/apiHelper';
import { getFullName, dropdownFilter } from '@chill/lib/helpers/utility';
import Notification from '@chill/components/Notification';
import chatActions from '@chill/redux/chat/actions';
import { AddUserBtn } from './Messages.styles';

const { sendRequest, getChatList } = chatActions;

const userTypes = [
  { name: 'admin', type: 'admin' },
  { name: 'therapist', type: 'therapie' },
  { name: 'patient', type: 'patient' },
];

function AddNewUser(props) {
  // const { getFieldDecorator, setFieldsValue, getFieldValue } = props.form;
  const [state, setState] = useState({ patients: [], modalVisible: false });
  const dispatch = useDispatch();
  const userData = useSelector((state) => state.Auth.userData);
  const uData = isObject(userData) && userData ? userData : {};
  const uType = uData.user_type ? uData.user_type : '';
  const userId = uData.id || '';
  const [form] = Form.useForm();

  const handleCancel = () => {
    setState((prevS) => ({ ...prevS, modalVisible: false }));
  };

  const getUsers = async (type) => {
    // CONDITION IS FOR DISPLAY ONE THERAPIST ALLOCATED TO PATIENT
    if (type === 'therapie' && uType === 'patient') {
      setState((prevS) => ({ ...prevS, patients: [] }));
      return false;
    }

    setState((prevS) => ({ ...prevS, uTypeLoading: true }));
    let apiUrl = 'user/patient';
    let uId;
    if (type === 'admin') apiUrl = 'user/admin';
    else if (type === 'therapie') apiUrl = 'user/therapie';
    else if (type === 'patient' && uType === 'therapie') {
      // NEXT CONDITION IS FOR GETTING PATIENTS OF LOGGED IN THERAPIST
      apiUrl = 'getTherapiePatients';
      uId = uData.id ? uData.id : '';
    }
    try {
      const res = await getApiData(
        apiUrl,
        { allData: true, therapie_id: uId },
        'GET',
      );
      if (res.success && isObject(res.data)) {
        let users = isArray(res.data) ? res.data : [];
        setState((prevS) => ({
          ...prevS,
          patients: users,
          uTypeLoading: false,
        }));
      } else {
        setState((prevS) => ({ ...prevS, patients: [], uTypeLoading: false }));
        Notification('error', res.message);
      }
    } catch (error) {
      console.log(error);
      setState((prevS) => ({ ...prevS, patients: [], uTypeLoading: false }));
      Notification('error', 'Something went wrong');
    }
    // setFieldsValue({ receiver_id: '' });
  };

  useEffect(
    () => {
      const uId = isObject(userData) && userData.id ? userData.id : '';
      async function fetchAlldata() {
        try {
          const res = await getApiData(
            'getTherapiePatients',
            { therapie_id: uId },
            'GET',
          );
          if (res.success && res.data) {
            setState((prevS) => ({ ...prevS, patients: res.data }));
          } else {
            Notification('error', res.message);
          }
        } catch (error) {
          console.log(error);
          Notification('error', 'Something went wrong');
        }
      }
      if (state.modalVisible && uType === 'therapie') {
        fetchAlldata();
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [state.modalVisible],
  );

  const handleSubmit = (e) => {
    e.preventDefault();
    props.form.validateFieldsAndScroll((err, values) => {
      console.log('err=====');
      console.log(err);
      if (!err) {
        dispatch(
          sendRequest('/api/chat/send-message', values, () => {
            props.form.resetFields();
            dispatch(getChatList(values.receiver_id));
            handleCancel();
          }),
        );
      }
    });
  };

  const initAddUser = () => {
    setState((prevS) => ({ ...prevS, modalVisible: true }));
  };
  const patients =
    isObject(state) && isArray(state.patients) ? state.patients : [];
  const toolTipMsg =
    uType !== 'patient'
      ? 'Send a new message to user'
      : 'Send a new message to Admin';
  // const userType = getFieldValue('user_type');
  return (
    <div>
      <Tooltip placement="topRight" title={toolTipMsg}>
        <AddUserBtn onClick={initAddUser}>
          <i className="ion-android-add" />
        </AddUserBtn>
      </Tooltip>
      <Modal
        visible={state.modalVisible}
        onClose={handleCancel}
        title={<IntlMessages id="common.newMsg" />}
        okText={<IntlMessages id="Send" />}
        onOk={handleSubmit}
        onCancel={handleCancel}
      >
        <Form onSubmit={handleSubmit} layout="vertical" form={form}>
          {/* <Row gutter={6}>
            <Col span={24}>
              <Form.Item label={<IntlMessages id="common.uType" />}>
                {getFieldDecorator('user_type', {
                  initialValue: '',
                  rules: [
                    {
                      required: true,
                      message: <IntlMessages id="Please select User Type" />,
                    },
                  ],
                })(
                  <Select onChange={(e) => getUsers(e)}>
                    {userTypes.map((u) => {
                      if (uType === 'patient' && u.type === 'patient')
                        return null;
                      return (
                        <Select.Option key={u.type} value={u.type}>
                          <IntlMessages
                            id={`common.${u.name.toLocaleLowerCase()}`}
                          />
                        </Select.Option>
                      );
                    })}
                  </Select>,
                )}
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={6}>
            <Col span={24}>
              <Form.Item label={<IntlMessages id="common.user" />}>
                {getFieldDecorator('receiver_id', {
                  rules: [
                    {
                      required: true,
                      message: <IntlMessages id="Please select User" />,
                    },
                  ],
                })(
                  <Select showSearch filterOption={dropdownFilter}>
                    {uType === 'patient' &&
                    userType === 'therapie' &&
                    uData.therapie_name &&
                    uData.therapie_id ? (
                      <Select.Option
                        key={uData.therapie_id}
                        value={uData.therapie_id}
                      >
                        {uData.therapie_name}
                      </Select.Option>
                    ) : null}
                    {patients.map((item) => {
                      if (userId === item.id) return null;
                      return (
                        <Select.Option key={item.id} value={item.id}>
                          {getFullName(item.first_name, item.last_name)}
                        </Select.Option>
                      );
                    })}
                  </Select>,
                )}
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={6}>
            <Col span={24}>
              <Form.Item label={<IntlMessages id="common.message" />}>
                {getFieldDecorator('message', {
                  rules: [
                    {
                      required: true,
                      message: <IntlMessages id="Please enter message" />,
                    },
                  ],
                })(<Input.TextArea type="textarea" rows={5} />)}
              </Form.Item>
            </Col>
          </Row> */}
        </Form>
      </Modal>
    </div>
  );
}

// const AddNewUserForm = Form.create()(AddNewUser);
export default AddNewUser;

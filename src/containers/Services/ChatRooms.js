/* eslint-disable no-nested-ternary */
/* eslint-disable no-debugger */
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { isArray, isObject, isEmpty } from 'lodash';
// import { palette } from 'styled-theme';
import { injectIntl } from 'react-intl';
import { Avatar, Col, Input, Row, Select, Tabs, Typography } from 'antd';
import moment from 'moment';
import Scrollbars from '@chill/components/utility/customScrollBar';
import IntlMessages from '@chill/components/utility/intlMessages';
import Button from '@chill/components/uielements/button';
import Badge from '@chill/components/uielements/badge';
import HelperText from '@chill/components/utility/helper-text';
import chatActions from '@chill/redux/chat/actions';
import { TabPane } from '@chill/components/uielements/tabs';
import { UserOutlined } from '@ant-design/icons';
import { colorArray } from '@chill/lib/helpers/utility';
// import AddNewUser from './AddNewUser';
import getApiData from '@chill/lib/helpers/apiHelper';
import chatSearch from '@chill/assets/images/Search-chat.png';
import styled from 'styled-components';
import blackblackLightdownArrow from '@chill/assets/images/black-light-arrow.png';
import { UserListsWrapper, UserLists, ChatSidebar } from './Messages.styles';

const {
  setSelectedChatroom,
  toggleMobileList,
  toggleCompose,
  setChatTab,
  getChatList,
  setProductList,
} = chatActions;

const { Option } = Select;

const { Text } = Typography;

function ChatRooms() {
  const dispatch = useDispatch();
  const { users, chatRooms, selectedChatRoom, chatTab, tabBadges, typingData } =
    useSelector((state) => state.Chat);
  console.log(
    `hello ~ file: ChatRooms.js:39 ~ ChatRooms ~ tabBadges:`,
    tabBadges,
  );
  const view = useSelector((state) => state.App.view);
  const [currentChatRooms, setCurrentChatRooms] = React.useState(chatRooms);
  const [selectedValue, setSelectedValue] = React.useState(null);
  const [typingDataState, setTypingData] = React.useState({});
  const [filterObj, setFilterObj] = React.useState({});
  const currentChatRoom = view === 'DesktopView' ? selectedChatRoom : {};

  async function getDeviceList() {
    try {
      const response = await getApiData(
        'getUserProductData',
        { user_id: currentChatRoom.u_id },
        'POST',
      );
      if (response.success && isArray(response.data)) {
        console.log('response,,', response.data);
        dispatch(setProductList(response.data));
      } else {
        console.log('err response,,', response);
      }
    } catch (err) {
      console.log('err,,', err);
    }
  }
  React.useEffect(() => {
    setCurrentChatRooms(chatRooms);
    // getDeviceList();
  }, [chatRooms]);
  React.useEffect(() => {
    getDeviceList();
  }, [selectedChatRoom]);

  React.useEffect(() => {
    setTypingData(typingData);
  }, [typingData]);

  // const activeBadge =
  //   isObject(tabBadges) && !isEmpty(tabBadges) ? tabBadges.active : 0;
  // const openBadge =
  //   isObject(tabBadges) && !isEmpty(tabBadges) ? tabBadges.open : 0;

  // this function for set bg color for user avatar
  function getColor(name = '') {
    const obj = {};
    const trimmedName = name.trim(); // Trim any leading or trailing whitespace

    const bgObj = colorArray().find(
      (item) => item.name === trimmedName.charAt(0).toUpperCase(),
    );
    const secondLetter = colorArray().find(
      (item) => item.name === trimmedName.charAt(1).toUpperCase(),
    );

    if (trimmedName !== '') {
      obj.name = bgObj && bgObj.name ? bgObj.name : '';
      if (secondLetter && secondLetter.name) {
        obj.name += secondLetter.name;
      }
    }
    obj.color = bgObj && bgObj.Color ? bgObj.Color : '#3498db';

    return obj;
  }

  const singleChatRoom = (chatRoom, index) => {
    const { name, message, createdAt, avtar_url = '', count } = chatRoom;
    console.log(
      `hello ~ file: ChatRooms.js:110 ~ singleChatRoom ~ message:`,
      message,
      chatRoom,
    );
    const colorObj = getColor(name);
    const selected =
      currentChatRoom &&
      chatRoom &&
      currentChatRoom.lead_id === chatRoom.lead_id;
    const style = {
      background: selected ? '#D4F0FC' : '#FFFFFF',
      height: '110px',
      alignItems: 'flex-start',
    };
    const selectChatroom = (event) => {
      // getDeviceList();
      event.stopPropagation();
      const cRooms = [...chatRooms];
      if (isArray(cRooms) && index > -1 && isObject(cRooms[index])) {
        cRooms[index].count = 0;
        setCurrentChatRooms(cRooms);
      }

      if (!selected) {
        dispatch(setSelectedChatroom(chatRoom));
        if (chatTab === 'Open') {
          dispatch(setChatTab('Active'));
        }
      }
      if (toggleMobileList) {
        dispatch(toggleMobileList(false));
      }
    };
    // const disuType = type === 'therapie' ? 'therapist' : type;
    const disCount = selected ? '' : count;

    return (
      <UserLists key={index} style={style} onClick={selectChatroom}>
        <div className="userListsGravatar">
          <Avatar
            size={36}
            src={avtar_url}
            icon={colorObj.name === '' ? <UserOutlined /> : null}
            style={{
              backgroundColor: colorObj.color || '#fff',
              fontSize: 16,
            }}
          >
            {colorObj.name || '-'}
          </Avatar>
        </div>
        <div className="userListsContent">
          <div className="userListTitleMain">
            <span
              className="chatTitle"
              style={{
                color: selected ? '#424955' : '#323842',
                fontWeight: selected ? 400 : 600,
              }}
            >
              {name || '-'}{' '}
            </span>
            <span
              className="userListsTime"
              style={{
                color: selected ? '#424955' : '#9095A0',
              }}
            >
              {moment(createdAt).fromNow()}
            </span>
          </div>
          <div className="chatExcerpt">
            {isObject(typingDataState) &&
            !isEmpty(typingDataState) &&
            chatRoom.lead_id === typingDataState.lead_id &&
            !typingDataState.stop &&
            selectedChatRoom.lead_id !== typingDataState.lead_id ? (
              <p>
                <IntlMessages id="common.typing" />
              </p>
            ) : (
              <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
                {chatRoom?.previous_message && (
                  <Text
                    ellipsis
                    className="previous-message-txt"
                    style={{
                      color: selected ? '#323842' : '#171A1F',
                      fontWeight: selected ? 400 : 700,
                    }}
                  >
                    {chatRoom?.previous_message}
                  </Text>
                )}
                <Text
                  ellipsis
                  className="pMessage"
                  style={{
                    color: selected ? '#424955' : '#323842',
                    fontWeight: 400,
                  }}
                >
                  {message || chatRoom?.file_url || '-'}
                </Text>
              </div>
            )}
            <Badge
              style={{ backgroundColor: '#1474ec', top: '7px' }}
              count={disCount}
              offset={[-20]}
            >
              <span className="head-example" />
            </Badge>
          </div>
        </div>
      </UserLists>
    );
  };

  const chatList =
    isArray(currentChatRooms) && !isEmpty(currentChatRooms)
      ? currentChatRooms
      : [];
  console.log(
    `hello ~ file: ChatRooms.js:176 ~ ChatRooms ~ chatList:`,
    chatList,
    currentChatRooms,
  );

  // React.useEffect(() => {
  //   currentChatRooms.forEach((i) => {
  //     if (lastReturn !== undefined) {
  //       if (i.lead_id === lastReturn.lead_id) {
  //         currentChatRooms[0].message = lastReturn.message;
  //         setCurrentChatRooms(currentChatRooms);
  //       }
  //     }
  //   });
  // }, [lastReturn]);

  const renderChatList = () => {
    if (!isEmpty(chatList)) {
      return chatList.map(singleChatRoom);
    }
    return (
      <HelperText
        text={<IntlMessages id="common.noconversion" />}
        className="messageHelperText"
      />
    );
  };

  const filterOptions = [
    { label: 'Most Recent', value: 'most_recent' },
    { label: 'Unread', value: 'unread' },
  ];

  useEffect(() => {
    if (!filterObj.search_value && !filterObj.filter_type) {
      setCurrentChatRooms(chatRooms);
    }
  }, [filterObj.search_value, filterObj.filter_type]);

  async function getFilterApi(searchtxt = '', filterType = '') {
    const payloadData = {
      search_value: searchtxt || '',
      filter_type: filterType || '',
      status: chatTab || 'Active',
    };
    console.log(
      `hello ~ file: ChatRooms.js:242 ~ getFilterApi ~ payloadData:`,
      payloadData,
      searchtxt,
      // filterObj.search_value,
    );
    try {
      const response = await getApiData('chat/chat-list', payloadData, 'POST');
      if (response.success) {
        setCurrentChatRooms(response.data);
        console.log('response,,', response.data);
        // dispatch(setProductList(response.data));
      } else {
        console.log('err response,,', response);
      }
    } catch (err) {
      console.log('err,,', err);
    }
  }

  const handleChange = (value) => {
    setSelectedValue(value);
    setFilterObj({ ...filterObj, filter_type: value });
    getFilterApi('', value);
    console.log('Selected value:', value);
  };

  const FilterWrapper = styled.div(() => ({
    padding: '10px 20px',

    '& .search': {
      border: 'none',
      background: '#DEE1E600',
    },
    '& .filterSelect': {
      width: '100%',
    },
    '& .ant-select-selector': {
      border: 'none !important',
    },
  }));
  console.log(
    `hello ~ file: ChatRooms.js:245 ~ FilterWrapper ~ FilterWrapper:`,
    FilterWrapper,
  );
  const handleFilterView = () => {
    // if (isEmpty(chatList)) {
    //   return null;
    // }
    return (
      <Row style={{ margin: '9px 20px 9px 12px' }}>
        <Col xs={24} xl={12} lg={12} sm={12} md={12}>
          <Input
            className="search"
            placeholder="Search..."
            prefix={<img src={chatSearch} alt="search" />}
            onChange={(e) => {
              getFilterApi(e.target.value);
              setFilterObj({ ...filterObj, search_value: e.target.value });
            }}
          />
        </Col>
        <Col xs={24} xl={12} lg={12} sm={12} md={12}>
          <Select
            placeholder="Select a Filter"
            onChange={handleChange}
            value={selectedValue}
            className="filterSelect"
            allowClear
            suffixIcon={<img src={blackblackLightdownArrow} alt="arrow" />}
          >
            {filterOptions?.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Col>
      </Row>
    );
  };

  return (
    <ChatSidebar>
      {/* <SidebarSearchBox>
        <Input
          onChange={onSearch}
          placeholder={props.intl.formatMessage({ id: 'Search User' })}
        />
        {!allChats ? <AddNewUser /> : null}
      </SidebarSearchBox> */}

      <div>
        <Tabs
          // renderTabBar={renderTabBar}
          onChange={(key) => {
            const tab = key === '1' ? 'Active' : 'Closed';
            dispatch(setChatTab(tab));
            dispatch(getChatList('', tab));
          }}
          activeKey={chatTab === 'Active' ? '1' : '3'}
          className="chatTabStyle"
        >
          <TabPane
            tab={
              <div className="flexStyle">
                <IntlMessages id="ticket.open" />
                {chatTab === 'Active' && <span>({chatList.length})</span>}
                {/* {activeBadge > 0 ? (
                  <div className="tabBadgeStyle">{activeBadge}</div>
                ) : null} */}
              </div>
            }
            key="1"
          />
          {/* <TabPane
            tab={
              <div className="flexStyle">
                <IntlMessages id="common.open" />
                {openBadge > 0 ? (
                  <div className="tabBadgeStyle">{openBadge}</div>
                ) : null}
              </div>
            }
            key="2"
          /> */}
          <TabPane
            tab={
              <div className="flexStyle">
                <IntlMessages id="ticket.closed" />
                {chatTab === 'Closed' && <span>({chatList.length})</span>}
                {/* {activeBadge > 0 ? (
                <div className="tabBadgeStyle">{activeBadge}</div>
              ) : null} */}
              </div>
            }
            key="3"
          />
        </Tabs>
      </div>
      <UserListsWrapper>
        <Scrollbars style={{ height: '100%' }}>
          {handleFilterView()}

          {renderChatList()}
        </Scrollbars>
      </UserListsWrapper>

      {users && users.length > 0 && (
        <div className="ComposeMessageButton">
          <Button onClick={() => dispatch(toggleCompose())} type="primary">
            <IntlMessages id="common.compose" />
          </Button>
        </div>
      )}
    </ChatSidebar>
  );
}

export default injectIntl(ChatRooms);

/* eslint-disable react/jsx-no-bind */
/* eslint-disable no-unused-vars */
import { Row, Col, Tabs, Avatar } from 'antd';
import React from 'react';
import _, { findIndex, isArray, isEmpty, isObject } from 'lodash';
import TableWrapper from '@chill/assets/styles/AntTables.styles';
import {
  PlusCircleOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useSelector } from 'react-redux';
import theme from '@chill/config/theme/default';
import IntlMessages from '@chill/components/utility/intlMessages';
import Popover from '@chill/components/uielements/popover';
import { TabPane } from '@chill/components/uielements/tabs';
import { useHistory, useLocation } from 'react-router';
import getApiData from '@chill/lib/helpers/apiHelper';
import moment from 'moment';
import Popconfirms from '@chill/components/Feedback/Popconfirm';
import Notification from '@chill/components/Notification';
import addIcon from '@chill/assets/images/add-square.png';
import AccountStyle from './Account.styles';
import ChangePassword from '../ChangePassword';
import Setting from '../Settings';
import AddNewAccountDrawer from './AddNewAccountDrawer';

/**
 *
 * @module Account
 */
const Account = () => {
  const [actionMenu, setActionMenu] = React.useState(false);
  const [isIndex, setIsIndex] = React.useState([]);
  const location = useLocation();
  const history = useHistory();
  // const params = location.search;
  // const type = qs.parse(params);
  const [isTab, setIsTab] = React.useState('');
  const [state, setState] = React.useState({
    drawerVisible: false,
    user_profile: '',
    initObj: {},
    loading: false,
  });
  const [employeeData, setEmployeeData] = React.useState({
    empData: [],
    empLoad: false,
  });
  const imgUrl =
    isObject(employeeData.empData) && employeeData.empData.user_profile
      ? employeeData.empData.user_profile
      : '';

  const [edit, setEdit] = React.useState(false);
  const [filter, setFilter] = React.useState({ pagination: {}, filters: {} });
  const userData = useSelector((st) => st.Auth.userData);

  const { loading, initObj } = state;
  console.log('employeeData ==>', employeeData);
  function handleVisible() {
    setActionMenu((visiblePre) => !visiblePre);
  }

  React.useEffect(() => {
    if (location.pathname === '/dashboard/settings') {
      setIsTab('settings');
    }
    if (location.pathname === '/dashboard/account') {
      setIsTab('account');
    }
    if (location.pathname === '/dashboard/changePassword') {
      setIsTab('changePassword');
    }
  }, []);

  // this function for get brands
  /** this function use for get data list
   * @function getEmpList
   * @param {object} data {}
   */
  async function getEmpList(data = {}) {
    setEmployeeData((p) => ({
      ...p,
      empLoad: true,
    }));
    Object.assign(data, { type: 'employee' });
    try {
      const response = await getApiData('user/get-user', data, 'POST');
      if (response.success && isArray(response.data)) {
        setEmployeeData((preState) => ({
          ...preState,
          empData: isArray(response.data) ? response.data : [],
          empLoad: false,
        }));
        const pagination =
          filter && filter.pagination ? { ...filter.pagination } : {};
        pagination.total = response.total_count || 0;
        pagination.pageSize = response.per_page || 10;
        pagination.current = response.page || 1;
        setFilter((f) => ({ ...f, pagination }));
      } else {
        setEmployeeData((preState) => ({
          ...preState,
          empLoad: false,
        }));
      }
    } catch (err) {
      setEmployeeData((preState) => ({
        ...preState,
        empLoad: false,
      }));
    }
  }

  /** this function use for delete account
   * @function deleteAccount
   * @param {object} data user_id
   */
  async function deleteAccount() {
    setState((preState) => ({
      ...preState,
      loading: true,
    }));
    const data = {
      // user_id: userId,
      user_id:
        isObject(state) &&
        isObject(state.initObj) &&
        !isEmpty(state.initObj) &&
        state.initObj.id,
    };
    const empAry = isArray(employeeData.empData) ? employeeData.empData : [];
    setEmployeeData({
      empLoad: true,
    });

    try {
      const response = await getApiData('user/remove-user', data, 'POST');
      let msgTitle = 'error';
      if (response.success) {
        msgTitle = 'success';
      } else {
        // setEmployeeData((pre) => ({
        //   ...pre,
        //   empData: empAry,
        //   empLoad: false,
        // }));
      }
      getEmpList();
      Notification(msgTitle, response.message);
      setState((preState) => ({
        ...preState,
        loading: false,
      }));
    } catch (error) {
      console.log('error ===', error);
      setState((preState) => ({
        ...preState,
        loading: false,
      }));
      setEmployeeData((pre) => ({
        ...pre,
        empData: empAry,
        empLoad: false,
      }));
    }
  }

  React.useEffect(() => {
    getEmpList();
  }, [state.drawerVisible]);

  const content = (
    <AccountStyle>
      <div className="popMainDiv">
        <div
          className="isoDropdownLink"
          style={{ cursor: 'pointer' }}
          onClick={() => {
            setState({ ...state, drawerVisible: true });
            setEdit(true);
            setIsIndex(null);
          }}
          role="button"
          onKeyPress={() => {
            setState({ ...state, drawerVisible: true });
            setEdit(true);
            setIsIndex(null);
          }}
          tabIndex="-1"
        >
          <EditOutlined style={{ paddingRight: 12, fontSize: 16 }} />
          <IntlMessages id="account.edit" />
        </div>
        <div className="isoDropdownLink" style={{ cursor: 'pointer' }}>
          <Popconfirms
            title={<IntlMessages id="Sure to delete?" />}
            okText={<IntlMessages id="DELETE" />}
            cancelText={<IntlMessages id="No" />}
            onConfirm={() => {
              deleteAccount();
              setIsIndex(null);
            }}
            onCancel={null}
          >
            <DeleteOutlined style={{ paddingRight: 12, fontSize: 16 }} />
            <IntlMessages id="account.delete" />
          </Popconfirms>
        </div>
      </div>
    </AccountStyle>
  );

  // set item id in state
  function handleVisibleChange(item1) {
    setIsIndex(item1.id);
  }

  // this function for update new product data
  /** this function for update data
   * @function updateData
   * @param {object} data full_name, email, phone
   */
  function updateData(data) {
    const empAry = isArray(employeeData.empData)
      ? [...employeeData.empData]
      : [];
    const editMode = !isEmpty(state.initObj) && data.id;
    const dataIndex = findIndex(empAry, { id: data.id });
    if (editMode && dataIndex > -1) {
      empAry[dataIndex] = data;
    } else {
      getEmpList();
    }
    setEmployeeData({
      empData: empAry,
    });
    setState({ initObj: {}, visible: false, view: false });
  }

  const columns = [
    {
      key: 'sort_created',
      title: <IntlMessages id="No" />,
      dataIndex: 'no',
      rowKey: 'no',
      width: 50,
      className: 'fullname-cell',
      render: (text, data, index) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="No" />
            </span>
            <span className="mobile-lbl-val">{index + 1 || '-'}</span>
          </div>
        );
      },
    },
    {
      key: 'sort_created',
      title: <IntlMessages id="Name" />,
      dataIndex: 'full_name',
      rowKey: 'full_name',
      width: 175,
      className: 'fullname-cell',
      render: (text, data) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Name" />
            </span>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <>
                {data.user_profile === null || '' ? (
                  <Avatar
                    src={imgUrl}
                    size={30}
                    icon={<UserOutlined />}
                    style={{ marginRight: 12 }}
                  />
                ) : (
                  <img
                    src={data.user_profile || ''}
                    style={{
                      width: 30,
                      height: 30,
                      backgroundColor: '#eee',
                      marginRight: 12,
                      borderRadius: 30,
                    }}
                    alt=""
                  />
                )}
                <span className="mobile-lbl-val">{text || '-'}</span>
              </>
            </div>
          </div>
        );
      },
    },
    {
      key: 'sort_created',
      title: <IntlMessages id="Phone Number" />,
      dataIndex: 'phone',
      rowKey: 'phone',
      width: 175,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Phone Number" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      key: 'sort_created',
      title: <IntlMessages id="Email ID" />,
      dataIndex: 'email',
      rowKey: 'email',
      width: 175,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Email ID" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      key: 'sort_created',
      title: <IntlMessages id="Role" />,
      dataIndex: 'user_type',
      rowKey: 'user_type',
      width: 175,
      className: 'fullname-cell',
      render: (text) => {
        return (
          <div>
            <span className="label">
              <IntlMessages id="Role" />
            </span>
            <span className="mobile-lbl-val">{text || '-'}</span>
          </div>
        );
      },
    },
    {
      key: 'sort_created',
      title: <IntlMessages id="Added on" />,
      dataIndex: 'createdAt',
      rowKey: 'createdAt',
      width: 175,
      className: 'fullname-cell',
      render: (text) => {
        const date = text ? moment(text).format('lll') : '-';
        return (
          <div>
            <span className="label">
              <IntlMessages id="Added on" />
            </span>
            <span className="mobile-lbl-val">{date || '-'}</span>
          </div>
        );
      },
    },
  ];

  const action = {
    key: 'sort_created',
    title: <IntlMessages id="Action" />,
    dataIndex: 'action',
    rowKey: 'action',
    className: 'fullname-cell',
    width: 50,
    fixed: 'right',
    render: (text, item) => {
      return (
        <div>
          <span className="label">
            <IntlMessages id="Action" />
          </span>
          <span className="mobile-lbl-val">
            <Popover
              content={content}
              trigger="click"
              open={isIndex === item.id ? !actionMenu : false}
              onOpenChange={handleVisible}
              arrowPointAtCenter
              placement="bottomRight"
            >
              <MoreOutlined
                onClick={() => {
                  handleVisibleChange(item);
                  setState({ ...state, initObj: item });
                }}
                style={{ color: '#000', fontSize: 24 }}
              />
            </Popover>
          </span>
        </div>
      );
    },
  };

  if (userData.user_type !== 'manager') {
    columns.splice(6, 0, action);
  }

  const accountList = () => {
    return (
      <AccountStyle>
        <Row>
          <Col className="dataList" xl={24} lg={24} sm={24} md={24} xs={24}>
            <TableWrapper
              scroll={{ x: 1400 }}
              loading={loading}
              rowKey={(record) => record.id}
              dataSource={employeeData.empData}
              columns={columns}
              pagination={{ showSizeChanger: false }}
              showSorterTooltip={false}
            />
          </Col>
        </Row>
      </AccountStyle>
    );
  };

  const addNew = () => {
    return (
      <div
        onClick={() => {
          setState((p) => ({
            ...p,
            drawerVisible: true,
            initObj: { phone_code: '+44' },
          }));
        }}
        role="button"
        onKeyPress={() => {
          setState((p) => ({ ...p, drawerVisible: true }));
        }}
        tabIndex="-1"
        className="iconStyle"
      >
        <div
          className="addNewUser"
          style={{
            color: theme.colors.primaryColor,
          }}
        >
          <IntlMessages id="Add User" />
          <img src={addIcon} alt="addIcon" />
        </div>
      </div>
    );
  };

  return (
    <div style={{ backgroundColor: '#fff', height: '100%' }}>
      <AccountStyle>
        <Row className="isoTabRow" gutter={[0, 12]}>
          <Col xs={24} className="topCol">
            <div className="isoTabText">
              <IntlMessages id="Admin/Brand Name" />
            </div>
            {isTab !== 'changePassword' && addNew()}
          </Col>
          {/* {userData.user_type === 'manager' ? null : ( */}

          {/* )} */}
          <Col className="isoTab" xl={8} lg={8} sm={24} md={12} xs={24}>
            <Tabs
              activeKey={isTab}
              onChange={(key) => {
                setIsTab(key);
                window.history.replaceState(null, '', `/dashboard/${key}`);
              }}
            >
              <TabPane tab={<IntlMessages id="Account" />} key="account" />
              <TabPane tab={<IntlMessages id="Settings" />} key="settings" />
              <TabPane
                tab={<IntlMessages id="changePassword" />}
                key="changePassword"
              />
            </Tabs>
          </Col>
          {/* {userData.user_type === 'manager' ? null : ( */}

          {/* // )} */}
        </Row>
        {isTab === 'account' ? accountList() : null}
        {isTab === 'settings' ? <Setting tab={isTab} /> : null}
        {isTab === 'changePassword' ? <Setting /> : null}
        {state.drawerVisible ? (
          <AddNewAccountDrawer
            initialValues={initObj}
            visible={state.drawerVisible}
            setState={setState}
            state={state}
            edit={edit}
            setEdit={setEdit}
            updateData={updateData}
          />
        ) : null}
      </AccountStyle>
    </div>
  );
};

export default Account;

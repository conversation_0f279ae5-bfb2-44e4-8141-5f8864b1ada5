import styled from 'styled-components';
import WithDirection from '@chill/lib/helpers/rtl';

const AccountStyle = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  .popMainDiv {
    display: flex;
    flex-direction: column;
  }
  .isoDropdownLink {
    color: #000;
    padding: 8px 0px;
  }
  .isoTab {
    display: flex;
    justify-content: flex-start;
  }
  .isoTab > div > div::before {
    position: unset;
  }

  .isoTabText {
    font-family: Inter;
    font-size: 18px;
    font-weight: 700;
    line-height: 36px;
    color: #242731;
    span {
      font-family: Inter;
    }
  }

  .ant-tabs-tab-active {
    .ant-tabs-tab-btn {
      font-family: Inter;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: #242731 !important;
      span {
        font-family: Inter;
      }
    }
  }

  .ant-tabs-ink-bar-animated {
    height: 4px !important;
  }

  .ant-tabs-tab-btn {
    font-family: Inter;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #9a9ea5 !important;
    span {
      font-family: Inter;
    }
  }

  .isoTabRow {
    padding: 16px 16px 0px;
  }

  .topCol {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .isoTab > div > div::before {
    position: unset;
  }
  .isoTab > div > div > div > div > div {
    padding: 0px;
  }
  .isoTab > div > div > div > div {
    padding-bottom: 16px;
  }
  .isoTab > div > div > div > div > div {
    margin: 0px 20px 0px 0px;
    @media (min-width: 768px) and (max-width: 1024px) {
      margin: 0px 20px 0px 0px;
    }
  }
  .isoTab > div > div:nth-child(1) {
    margin: 0px;
  }
  .isoTab > div > div:nth-child(2) {
    display: none;
  }
  .hiddenCol {
    padding-bottom: 16px;
    display: flex;
    justify-content: flex-end;
    @media (min-width: 767px) {
      display: none;
    }
  }
  .isoAddNew {
    @media (max-width: 767px) {
      display: none;
    }
  }
  .dataList > div > div > div > ul {
    margin: 16px;
  }
  .listNameView {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .nameWithImg {
    width: 30px;
    height: 30px;
    background-color: #eee;
    margin-right: 12px;
    border-radius: 30px;
  }
  .addNewUser {
    padding-left: 24px;
    display: flex;
    justify-content: flex-end;
    cursor: pointer;
    gap: 5.98px;

    span {
      font-family: Inter;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      text-align: right;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #0086ff;
    }
  }
  .iconImg {
    width: 16px;
    height: 16px;
    margin-right: 12px;
  }
`;

export default WithDirection(AccountStyle);

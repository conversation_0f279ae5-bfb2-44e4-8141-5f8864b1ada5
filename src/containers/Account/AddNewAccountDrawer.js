/* eslint-disable react/jsx-no-bind */
/* eslint-disable no-shadow */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
import React, { useEffect, useState } from 'react';
import {
  Drawer,
  Input,
  Row,
  Upload,
  Select,
  Col,
  Avatar,
  Typography,
  Button,
} from 'antd';
import { isEmpty, isObject } from 'lodash';
import IntlMessages from '@chill/components/utility/intlMessages';
import Form from '@chill/components/uielements/form';
import {
  BottomViewWrapper,
  FormWrapper,
} from '@chill/assets/styles/drawerFormStyles';
import CButton from '@chill/components/uielements/CButton';
import getApiData from '@chill/lib/helpers/apiHelper';
import Notification from '@chill/components/Notification';
import { getBase64 } from '@chill/lib/helpers/utility';
import CountryPicker from '@chill/components/uielements/countryPicker';
import { useSelector } from 'react-redux';
import useResetFormOnClose from '@chill/lib/hooks/useResetForm';
import uploadIcon from '@chill/assets/images/upload-icon.png';
import countriesAry from '@chill/config/staticData/countries.json';
import CustomerWrapper from './Account.styles';
import userRole from './config';

const { Text } = Typography;

function AccountDrawer(props) {
  const {
    visible,
    state,
    updateData,
    setState = () => {},
    initialValues,
    edit,
    setEdit,
  } = props;

  const { language } = useSelector((state) => state.LanguageSwitcher);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);
  const uData = useSelector((stat) => stat.Auth.userData);
  // const user = isObject(uData) ? uData : {};
  // const userId = isObject(user) && user.id ? user.id : '';
  const [btnLoader, setBtnLoader] = useState(false);
  const [form] = Form.useForm();

  const [countrySelCode, setCountrySelCode] = React.useState('');

  const initVal = initialValues || {};

  const showDialCode =
    (!isEmpty(countrySelCode) &&
      countriesAry?.find((c) => c?.dial_code === countrySelCode)) ||
    form.getFieldValue('phone_code');

  console.log(
    'first===========>',
    showDialCode,
    countrySelCode,
    countriesAry,
    countriesAry?.find((c) => c.dial_code === countrySelCode),
    form.getFieldValue('phone_code'),
  );

  useEffect(() => {
    if (!isEmpty(initialValues)) {
      const cCode = countriesAry?.find(
        (c) => c?.dial_code === initialValues?.phone_code,
      );
      setCountrySelCode(cCode?.code);
    }
  }, [initialValues]);
  const onClose = (type = '', data = {}) => {
    form.resetFields();
    setEdit(false);
    if (type === 'success') {
      updateData(data);
    }
    setState((p) => ({
      ...p,
      drawerVisible: false,
      user_profile: '',
      initObj: {},
    }));
  };

  useResetFormOnClose({ form, visible, initVal });
  // this function for add User
  async function addUser(values) {
    const obj = { ...values };
    if (edit) {
      Object.assign(obj, { user_id: initVal.id });
    }
    Object.assign(obj, { type: 'employee' });
    const url = edit ? 'user/update-user' : 'user/add-user';

    const valid = true;
    obj.user_profile = initVal.user_profile || '';
    if (state.user_profile !== '' || !isObject(state.user_profile)) {
      obj.user_profile = state.user_profile || initVal.user_profile;
    }
    // if (!edit && isEmpty(state.user_profile)) {
    //   Notification('error', messageArray['common.uploadImg']);
    //   valid = false;
    // }
    if (valid) {
      setBtnLoader(true);
      try {
        const response = await getApiData(url, obj, 'POST');
        if (response.success) {
          Notification('success', response.message);
          onClose('success', response.data);
        } else {
          Notification('error', response.message);
        }
        setBtnLoader(false);
      } catch (error) {
        console.log(error);
        setBtnLoader(false);
        Notification('error', messageArray['err.wenWrong']);
      }
    }
  }

  const beforeUpload = (file, type = '') => {
    const validType =
      file.type === 'image/jpg' ||
      file.type === 'image/jpeg' ||
      file.type === 'image/png';

    // Check the file size is not larger than 10MB
    const isLt5M = file.size / 1024 / 1024 < 10;
    if (!isLt5M) {
      // If the file size is larger than 5MB, show an error notification
      Notification('error', messageArray['err.size']);
      return false;
    }

    if (!validType) {
      Notification('error', messageArray['common.img.upload']);
      return false;
    }

    if (type === 'img') {
      getBase64(file, (imageUrl) => {
        setState((p) => ({ ...p, user_profile: imageUrl, loading: false }));
      });
    }

    return false;
  };
  // const uploadButton = (
  //   <div>
  //     <Icon type="plus" />
  //     <div className="ant-upload-text">
  //       <IntlMessages id="common.upload" />
  //     </div>
  //   </div>
  // );

  const prefixSelector = (
    <Form.Item
      name="phone_code"
      className="countryFormItem-cform"
      style={{ marginBottom: -2 }}
    >
      <CountryPicker
        valType="dial_code"
        notShowLabel
        className="countryPicker"
        onChange={(val) => setCountrySelCode(val)}
      />
    </Form.Item>
  );

  return (
    <CustomerWrapper>
      <div>
        <Drawer
          title={<IntlMessages id={edit ? 'Update User' : 'Add User'} />}
          width={450}
          style={{ height: window.innerHeight - 60, marginTop: 70 }}
          onClose={onClose}
          visible={visible}
          bodyStyle={{ paddingBottom: 80 }}
          destroyOnClose
          form={form}
        >
          <FormWrapper>
            <Form
              form={form}
              onFinish={addUser}
              layout="vertical"
              initialValues={initVal}
            >
              <Row
                gutter={[12, 20]}
                style={{
                  marginBottom: 20,
                }}
              >
                <Col xs={24} className="profilePic-acc">
                  <div className="profilePic">
                    {!isEmpty(state.user_profile) ||
                    !isEmpty(initVal?.user_profile) ? (
                      <Avatar
                        size={62}
                        src={
                          !isEmpty(state.user_profile)
                            ? state.user_profile
                            : initVal.user_profile
                        }
                        shape="circle"
                        alt="UserProfile"
                      />
                    ) : (
                      <Avatar
                        size={62}
                        shape="circle"
                        style={{
                          width: 62,
                          height: 62,
                          border: '1px solid #6894F1',
                          backgroundColor: '#266BF0',
                        }}
                      />
                    )}
                  </div>
                  <div className="profile-acc-upload">
                    <Text className="profile-acc-upload-title">
                      Profile Picture
                    </Text>
                    <div className="profile-acc-upload-btns">
                      <Upload
                        showUploadList={false}
                        beforeUpload={(file) => beforeUpload(file, 'img')}
                        listType="text"
                      >
                        <Button className="uploadBtn">
                          <img src={uploadIcon} alt="Upload Icon" />
                          Upload image
                        </Button>
                      </Upload>
                      <Button
                        className="removeBtn"
                        onClick={() => {
                          setState((p) => ({
                            ...p,
                            user_profile: '',
                          }));
                        }}
                      >
                        Remove
                      </Button>
                    </div>
                    <Text className="profile-acc-upload-warn">
                      *.png,*.jpeg files up to 10MB at least 400px by 400px
                    </Text>
                  </div>
                </Col>
                {/* <Upload
                  showUploadList={false}
                  listType="picture-card"
                  beforeUpload={(file) => beforeUpload(file, 'img')}
                >
                  {!isEmpty(state.user_profile) ||
                  !isEmpty(initVal?.user_profile) ? (
                    <img
                      src={
                        !isEmpty(state.user_profile)
                          ? state.user_profile
                          : initVal.user_profile
                      }
                      alt="UserProfile"
                      style={{ width: '100%' }}
                    />
                  ) : (
                    uploadButton
                  )}
                </Upload> */}
              </Row>
              <Form.Item
                label={<IntlMessages id="antTable.title.fullName" />}
                name="full_name"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="err.segmentName" />,
                  },
                  {
                    max: 50,
                    message: <IntlMessages id="err.max.50char" />,
                  },
                  {
                    whitespace: true,
                    message: <IntlMessages id="err.blankSpace" />,
                  },
                ]}
              >
                <Input placeholder="Enter Full Name" />
              </Form.Item>
              <Form.Item
                label={<IntlMessages id="email-id" />}
                name="email"
                rules={[
                  {
                    type: 'email',
                    required: true,
                    message: <IntlMessages id="req.email" />,
                  },
                ]}
              >
                <Input placeholder="Enter email-id" />
              </Form.Item>
              {uData.user_type === 'brand_admin' ||
              uData.user_type === 'manager' ? (
                <Form.Item
                  label={<IntlMessages id="antTable.title.role" />}
                  name="role"
                  rules={[
                    {
                      required: !edit,
                      message: <IntlMessages id="req.role" />,
                    },
                  ]}
                >
                  <Select placeholder={initVal.user_type || 'Eg: Manager'}>
                    {userRole.map((item) => (
                      <Select.Option key={`${item.label}`} value={item.value}>
                        {item.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              ) : null}
              <Form.Item
                className="formLabelforCountry"
                label={
                  <>
                    <IntlMessages id="Phone Number" />
                    <span
                      className={`flag flag-${(
                        showDialCode?.code || 'gg'
                      ).toLowerCase()}`}
                    />
                  </>
                }
                name="phone"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.contactnumber" />,
                  },
                  {
                    max: 12,
                    message: <IntlMessages id="err.contactnumber" />,
                  },
                  {
                    min: 4,
                    message: <IntlMessages id="err.contactnumber" />,
                  },
                ]}
              >
                <Input
                  style={{ width: '100%', marginTop: '3px' }}
                  min={1}
                  maxLength={15}
                  type="number"
                  addonBefore={prefixSelector}
                  placeholder="xxxxxxxxxxxx"
                />
              </Form.Item>
              <BottomViewWrapper className="bottomBtnWrapper">
                <Row style={{ width: '100%' }}>
                  <Col xs={24}>
                    <Form.Item className="btn-form">
                      <CButton
                        className="submitBtnStyle"
                        htmlType="submit"
                        loading={btnLoader}
                        disabled={btnLoader}
                        style={{ marginRight: 20 }}
                      >
                        {/* <IntlMessages id={edit ? 'Update User' : 'Add User'} /> */}
                        Save Changes
                      </CButton>
                    </Form.Item>
                  </Col>
                  {/* <Form.Item className="btn-form">
                    <div
                      className="cancelBtnStyle"
                      onClick={onClose}
                      role="button"
                      onKeyPress=""
                      tabIndex="-1"
                    >
                      <IntlMessages id="common.cancel" />
                    </div>
                  </Form.Item> */}
                </Row>
              </BottomViewWrapper>
            </Form>
          </FormWrapper>
        </Drawer>
      </div>
    </CustomerWrapper>
  );
}

export default AccountDrawer;

import React, { lazy, Suspense } from 'react';
import { createRoot } from 'react-dom/client';
import * as serviceWorker from './serviceWorker';
import Loader from './components/utility/loader.style';

const App = lazy(() => import('./app'));
const rootEle = document.getElementById('root');

const root = createRoot(rootEle); // ✅ create root once

root.render(
  <Suspense fallback={<Loader />}>
    <App />
  </Suspense>,
  rootEle,
);

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: https://bit.ly/CRA-PWA
serviceWorker.register();

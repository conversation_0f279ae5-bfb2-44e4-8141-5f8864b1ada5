import React from 'react';
import { ConfigProvider } from 'antd';
import { IntlProvider } from 'react-intl';
import { useSelector } from 'react-redux';
import { ThemeProvider } from 'styled-components';
import themes from '@chill/config/theme/theme.config';
import { fixAutocomplete } from '@chill/lib/helpers/utility';
import AppLocale from '@chill/config/translation';

// Disable chrome in-built autocomplete for all inputs
fixAutocomplete();

export default function AppProvider({ children }) {
  const language = useSelector((state) => state.LanguageSwitcher.language);
  const { themeName } = useSelector(
    (state) => state.ThemeSwitcher.changeThemes,
  );

  const langCode = language ? language.toLowerCase() : 'en';

  const currentAppLocale = AppLocale[langCode] || {};

  const dir = langCode === 'ar' ? 'rtl' : 'ltr';

  return (
    <ConfigProvider
      getPopupContainer={(node) => {
        // Scroll dropdown along parent node
        if (node && node.className === 'ant-select-selector') {
          return node.parentNode;
        }
        return document.body;
      }}
      locale={currentAppLocale.antd}
      direction={dir}
    >
      <IntlProvider
        locale={langCode}
        messages={currentAppLocale.messages || {}}
      >
        <ThemeProvider theme={themes[themeName]}>{children}</ThemeProvider>
      </IntlProvider>
    </ConfigProvider>
  );
}

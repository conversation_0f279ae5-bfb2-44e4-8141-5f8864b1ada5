/* eslint-disable no-param-reassign */

/**
 * @name Document Management Tests
 *
 * @desc Checks insert, update and delete operation in Document management
 *
 */

const puppeteer = require('puppeteer');
const { waitFor, waitRes } = require('../commonFunc/utility');
const login = require('../commonFunc/login');

const timeout = 60000;

let browser;
let page;

beforeAll(async () => {
  browser = await puppeteer.launch({
    headless: false,
  });
  page = await browser.newPage();
  await page.goto(URL, {
    waitUntil: 'networkidle0',
  });
});

async function clearNotification() {
  await page.$$eval('.ant-notification-notice-close', (elHandles) =>
    elHandles.forEach((el) => el.click()),
  );
}

async function fillAddUserData(cred = {}) {
  const fName = cred.pName || 'John';
  const lName = cred.pDate || 'Parker';
  const email =
    cred.email || new Date().getTime().toString() + '@mailinator.com';

  await page.click('#first_name');
  await page.type('#first_name', fName);

  await page.click('#last_name');
  await page.type('#last_name', lName);

  await page.click('#email');
  await page.type('#email', email);

  await waitFor(page, '#is_selected', 'click');
  await page.waitFor(500);

  await waitFor(page, '#add-user', 'click');
  await page.waitFor(500);

  // expect(res).toBe(true);

  /** Submit the form */
  // await page.click('button[type="submit"]');
}

async function fillFolderDetail(cred = {}, successTxt) {
  const name = cred.name || 'Folder one' + new Date().getTime().toString();

  await page.click('#name');
  await page.$eval('#name', (el) => {
    el.value = '';
  });
  await page.type('#name', name);

  /** Submit the modal */
  await page.click('#create-folder');

  /** Waiting for response message */
  const res = await waitRes(page, successTxt);
  expect(res).toBe(true);
}

describe('Document Module', () => {
  test(
    'Add Folder',
    async () => {
      await login(page, {
        email: '<EMAIL>',
        password: '123456@aA',
      });

      await page.goto(`${URL}/dashboard/documents`, {
        waitUntil: 'domcontentloaded',
      });

      /** Fill required fields */
      await waitFor(page, '#new-doc', 'click');
      await page.waitFor(500);

      await waitFor(page, '#folder-menu', 'click');
      await page.waitFor(500);

      await fillFolderDetail({}, 'Folder has been created.');
    },
    timeout,
  );
  test(
    'Rename Folder',
    async () => {
      await page.goto(`${URL}/dashboard/documents`, {
        waitUntil: 'networkidle0',
      });

      /** Fill required fields */
      await waitFor(page, '#folderActionBtn', 'click');
      await page.waitFor(500);

      await waitFor(page, '#rename', 'click');
      await page.waitFor(500);

      await fillFolderDetail(
        {
          name: 'Folder Name' + new Date().getTime().toString(),
        },
        'Success!',
      );
    },
    timeout,
  );

  test(
    'Create Document',
    async () => {
      await page.goto(`${URL}/dashboard/documents`, {
        waitUntil: 'domcontentloaded',
      });

      await waitFor(page, '#new-doc', 'click');
      await page.waitFor(500);

      await waitFor(page, '#create-doc', 'click');
      await page.waitFor(500);

      await page.click('#name');
      await page.$eval('#name', (el) => {
        el.value = '';
      });
      await page.type('#name', new Date().getTime().toString());

      await waitFor(page, '.ant-modal-footer .ant-btn-primary', 'click');
      await page.waitForNavigation();

      // await waitFor(page, '#save-doc', 'click');
      // await page.waitFor(500);

      // const res = await waitRes(page, 'Document has been saved');
      // expect(res).toBe(true);
    },
    timeout,
  );

  test(
    'Copy File',
    async () => {
      await page.goto(`${URL}/dashboard/documents`, {
        waitUntil: 'networkidle0',
      });
      await waitFor(page, '#fileActionBtn', 'click');
      await page.waitFor(500);

      await waitFor(page, '#copy', 'click');
      await page.waitFor(500);
      await waitFor(
        page,
        '.ant-tree-treenode.ant-tree-treenode-switcher-close.ant-tree-treenode-leaf-last',
        'click',
      );
      await page.waitFor(500);
      await waitFor(page, '#copy-to', 'click');
      await page.waitFor(500);

      /** Waiting for response message */
      const res = await waitRes(page, 'File has been copied.');
      expect(res).toBe(true);
      // browser.close();
    },
    timeout,
  );

  test(
    'Move File',
    async () => {
      await page.goto(`${URL}/dashboard/documents`, {
        waitUntil: 'networkidle0',
      });
      await waitFor(page, '#fileActionBtn', 'click');
      await page.waitFor(500);

      await waitFor(page, '#move', 'click');
      await page.waitFor(500);
      await waitFor(
        page,
        '.ant-tree-treenode.ant-tree-treenode-switcher-close.ant-tree-treenode-leaf-last',
        'click',
      );
      await page.waitFor(500);
      await waitFor(page, '#move-to', 'click');
      await page.waitFor(500);

      /** Waiting for response message */
      const res = await waitRes(page, 'Document has been moved.');
      expect(res).toBe(true);
      // browser.close();
    },
    timeout,
  );

  test(
    'Add tags into file',
    async () => {
      await page.goto(`${URL}/dashboard/documents`, {
        waitUntil: 'networkidle0',
      });
      await waitFor(page, '#fileActionBtn', 'click');
      await page.waitFor(500);

      await waitFor(page, '#tags', 'click');
      await page.waitFor(500);

      // Wait for tag input selector
      await waitFor(page, '#rc_select_0', 'click');

      // Fill data in tag input and press tab
      await page.click('#rc_select_0');
      await page.type('#rc_select_0', 'abcTAG123');
      await page.keyboard.press('Tab');

      await waitFor(page, '#add-tags', 'click');

      /** Waiting for response message */
      const res = await waitRes(page, 'Tags has been updated');
      expect(res).toBe(true);
    },
    timeout,
  );

  test(
    'Make classified document',
    async () => {
      await page.goto(`${URL}/dashboard/documents`, {
        waitUntil: 'networkidle0',
      });
      await waitFor(page, '#fileActionBtn', 'click');
      await page.waitFor(500);

      await waitFor(page, '#classified', 'click');
      await page.waitFor(500);

      await waitFor(page, '#security_2', 'click');
      await page.waitFor(500);

      // Wait for doc_password input selector
      await waitFor(page, '#doc_password', 'click');

      await page.type('#doc_password', '123456');

      await waitFor(page, '#create-password', 'click');

      /** Waiting for response message */
      const res = await waitRes(page, 'Set Security type successfully.');
      expect(res).toBe(true);
    },
    timeout,
  );

  test(
    'Status Change document',
    async () => {
      await page.goto(`${URL}/dashboard/documents`, {
        waitUntil: 'networkidle0',
      });
      await waitFor(page, '#fileActionBtn', 'click');
      await page.waitFor(500);

      await waitFor(page, '#change-status', 'click');
      await page.waitFor(500);

      await waitFor(page, '.ant-modal-footer .ant-btn-primary', 'click');
      await page.waitFor(500);

      /** Waiting for response message */
      const res = await waitRes(page, 'Change status successfully.');
      expect(res).toBe(true);
    },
    timeout,
  );

  test(
    'Send document',
    async () => {
      await page.goto(`${URL}/dashboard/documents`, {
        waitUntil: 'networkidle0',
      });
      await waitFor(page, '#fileActionBtn', 'click');
      await page.waitFor(500);

      await waitFor(page, '#send-file', 'click');
      await page.waitFor(500);

      await waitFor(page, '#message', 'click');
      await page.type('#message', 'hello');

      await waitFor(page, '.ant-select-selection-search-input', 'click');
      await page.waitFor(500);

      await waitFor(page, '.ant-select-item-option', 'click');
      await page.waitFor(500);

      await fillAddUserData({});
      clearNotification();

      await waitFor(page, '#add-users', 'click');
      await page.waitFor(500);

      /** Waiting for response message */
      const res = await waitRes(page, 'Success!');
      expect(res).toBe(true);
    },
    timeout,
  );

  test(
    'Share document',
    async () => {
      await page.goto(`${URL}/dashboard/documents`, {
        waitUntil: 'networkidle0',
      });
      await waitFor(page, '#fileActionBtn', 'click');
      await page.waitFor(500);

      await waitFor(page, '#share', 'click');
      await page.waitFor(500);

      await waitFor(page, '.ant-select-selection-search-input', 'click');
      await page.waitFor(500);

      await waitFor(page, '.ant-select-item-option', 'click');
      await page.waitFor(500);

      await fillAddUserData({});
      clearNotification();

      await waitFor(page, '#add-users', 'click');
      await page.waitFor(500);

      /** Waiting for response message */
      const res = await waitRes(page, 'Document has been Share Successfully.');
      expect(res).toBe(true);
    },
    timeout,
  );

  test('Delete File', async () => {
    await page.goto(`${URL}/dashboard/documents`, {
      waitUntil: 'networkidle0',
    });

    await waitFor(page, '#fileActionBtn', 'click');
    await page.waitFor(500);

    await waitFor(page, '#delete', 'click');
    await page.waitFor(500);
    await waitFor(page, '#confirm-delete', 'click');
    await page.waitFor(500);

    try {
      await page.waitForSelector('#doc_password', {
        timeout: 2000,
        visible: true,
      });
      await page.click('#doc_password');
      await page.type('#doc_password', '123456');
      await page.waitFor(500);

      await waitFor(page, '.ant-modal-footer .ant-btn-primary', 'click');
      await page.waitFor(500);

      const res = await waitRes(
        page,
        'Document has been deleted successfully.',
      );
      expect(res).toBe(true);
    } catch (err) {
      const res = await waitRes(
        page,
        'Document has been deleted successfully.',
      );
      expect(res).toBe(true);
    }

    /** Waiting for response message */
  }, 60000);

  test(
    'Delete Folder',
    async () => {
      await page.goto(`${URL}/dashboard/documents`, {
        waitUntil: 'networkidle0',
      });

      await waitFor(page, '#folderActionBtn', 'click');
      await page.waitFor(500);

      await waitFor(page, '#delete', 'click');
      await page.waitFor(500);
      await waitFor(page, '#confirm-delete', 'click');
      await page.waitFor(500);

      /** Waiting for response message */
      const res = await waitRes(page, 'Folder has been deleted successfully.');
      expect(res).toBe(true);
      browser.close();
    },
    timeout,
  );
});

/* eslint-disable no-param-reassign */

/**
 * @name Team Management Tests
 *
 * @desc Checks insert, update and delete operation in Team management
 *
 */

const puppeteer = require('puppeteer');
const { waitFor, waitRes } = require('../commonFunc/utility');
const login = require('../commonFunc/login');

const timeout = 30000;

let browser;
let page;

beforeAll(async () => {
  browser = await puppeteer.launch({
    headless: false,
  });
  page = await browser.newPage();
  await page.goto(URL, {
    waitUntil: 'networkidle0',
  });
});

async function fillContactDetails(cred = {}) {
  const fName = cred.fName || 'Testing';
  const lName = cred.lName || 'DEMO';
  const email = cred.email || '<EMAIL>';

  await page.click('#first_name');
  await page.$eval('#first_name', (el) => {
    el.value = '';
  });
  await page.type('#first_name', fName);

  await page.click('#last_name');
  await page.$eval('#last_name', (el) => {
    el.value = '';
  });
  await page.type('#last_name', lName);

  if (email !== '-') {
    await page.click('#email');
    await page.$eval('#email', (el) => {
      el.value = '';
    });
    await page.type('#email', email);
  }

  /** Submit the form */
  await page.click('button[type="submit"]');
}

describe('Test Contact Module', () => {
  test(
    'Add contact',
    async () => {
      await login(page, {
        email: '<EMAIL>',
        password: '123456@aA',
      });

      await page.goto(`${URL}/dashboard/contacts`, {
        waitUntil: 'domcontentloaded',
      });

      await waitFor(page, '#create-dept', 'click');
      await page.waitFor(500);

      await fillContactDetails({});

      const res = await waitRes(page, 'Contact has been added!');
      expect(res).toBe(true);
    },
    timeout,
  );

  test(
    'Edit contact',
    async () => {
      await page.goto(`${URL}/dashboard/contacts`, {
        waitUntil: 'domcontentloaded',
      });

      await waitFor(page, '#edit-dept', 'click');
      await page.waitFor(500);

      await fillContactDetails({
        lName: 'demo34',
        fName: 'testing34',
        email: '-',
      });

      const res = await waitRes(page, 'Contact has been updated successfully.');
      expect(res).toBe(true);
    },
    timeout,
  );

  test(
    'Del contact',
    async () => {
      await page.goto(`${URL}/dashboard/contacts`, {
        waitUntil: 'domcontentloaded',
      });

      await waitFor(page, '#delete-confirm-dept', 'click');
      await page.waitFor(500);

      await waitFor(page, '#delete-item', 'click');

      const res = await waitRes(page, 'Contact has been deleted successfully.');
      expect(res).toBe(true);

      browser.close();
    },
    timeout,
  );
});

import React from 'react';
import { Provider } from 'react-redux';
import GlobalStyles from '@chill/assets/styles/globalStyle';
// import { BrowserRouter as Router } from 'react-router-dom';
import { store } from './redux/store';
import Boot from './redux/boot';
import Routes from './router';
import AppProvider from './AppProvider';
// import Auth0ProviderWithHistory from './Auth0ProviderWithHistory';

const App = () => (
  <Provider store={store}>
    <AppProvider>
      {/* <Router>
        <Auth0ProviderWithHistory> */}
      <>
        <GlobalStyles />
        <Routes />
      </>
      {/* </Auth0ProviderWithHistory>
      </Router> */}
    </AppProvider>
  </Provider>
);
Boot()
  .then(() => App())
  .catch((error) => console.error(error));

export default App;

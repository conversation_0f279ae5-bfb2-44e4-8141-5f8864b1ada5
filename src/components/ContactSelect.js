import React, { useEffect, useRef, useState } from 'react';
import { Select } from 'antd';
import IntlMessages from '@chill/components/utility/intlMessages';
import { isEmpty } from 'lodash';
import { getFullName } from '@chill/lib/helpers/utility';
import getApiData from '@chill/lib/helpers/apiHelper';

const { Option } = Select;

function ContactSelect(props) {
  const { visible, onChange = () => {}, value, vlKey = '' } = props;

  const usersSelectRef = useRef(null);
  const [state, setState] = useState({
    addContactVis: false,
    contacts: [],
    cLoading: false,
  });

  /**
   * Function to get all contacts
   * @param {*} data - required API values
   */
  async function getContacts(data) {
    setState((pre) => ({ ...pre, cLoading: !pre.contacts }));
    try {
      const res = await getApiData('contact/get-contact', data);
      if (res.success) {
        setState((pre) => ({
          ...pre,
          contacts: res.data || [],
          cLoading: false,
        }));
      } else {
        setState((pre) => ({ ...pre, cLoading: false }));
      }
    } catch (err) {
      setState((pre) => ({ ...pre, cLoading: false }));
    }
  }

  useEffect(() => {
    if (visible) getContacts();
  }, [visible]);

  function filterFunc(input, option) {
    const opObj = option?.obj || {};
    const opKey = option?.key || '';

    // Filter with name and email both
    if (!isEmpty(opObj)) {
      const email = opObj.email ? opObj.email.toLowerCase() : '';
      const name = opObj.name ? opObj.name.toLowerCase() : '';
      const fname = opObj.first_name ? opObj.first_name.toLowerCase() : '';
      const lname = opObj.last_name ? opObj.last_name.toLowerCase() : '';
      return (
        email.indexOf(input.toLowerCase()) >= 0 ||
        name.indexOf(input.toLowerCase()) >= 0 ||
        fname.indexOf(input.toLowerCase()) >= 0 ||
        lname.indexOf(input.toLowerCase()) >= 0
      );
    }
    if (opKey === 'new') return true;

    return false;
  }

  function submitContact(data = {}, selected) {
    const cData = {
      ...data,
      name: getFullName(data.first_name, data.last_name),
    };

    // Add contact into dropdown array
    const mem = [...state.contacts];
    mem.push(cData);

    // Add contact into selected list's array
    if (selected) {
      onChange(cData[vlKey || 'email'], cData);
    }

    // Close modal
    setState((pre) => ({ ...pre, contacts: mem, addContactVis: false }));
  }

  function renderUser(item) {
    return (
      <Option value={item[vlKey || 'email']} key={item.id} obj={item}>
        <p style={{ fontWeight: 550 }}>
          {getFullName(item.first_name, item.last_name)}
        </p>
        <p>{item.email}</p>
      </Option>
    );
  }

  return (
    <>
      <Select
        showSearch
        style={{ width: '100%' }}
        ref={usersSelectRef}
        placeholder={<IntlMessages id="common.searchNew" />}
        notFoundContent={null}
        onChange={(vl, vlObj) => {
          if (vl && vl.toString() === 'new') {
            setState((pre) => ({ ...pre, addContactVis: true }));
          } else {
            onChange(vl, vlObj?.obj || {});
          }
        }}
        value={value}
        filterOption={filterFunc}
        loading={state.cLoading}
      >
        <Option key="new">
          <p style={{ fontWeight: 550 }}>
            <IntlMessages id="common.addNew" />
          </p>
        </Option>
        {state.contacts.map(renderUser)}
      </Select>
    </>
  );
}

export default ContactSelect;

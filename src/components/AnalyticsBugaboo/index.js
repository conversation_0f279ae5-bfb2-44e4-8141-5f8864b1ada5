/* eslint-disable no-param-reassign */
/* eslint-disable no-shadow */
/* eslint-disable no-unused-vars */
/* eslint-disable consistent-return */
/* eslint-disable no-nested-ternary */
import React, { useEffect, useState } from 'react';
import { Row, Col, Spin } from 'antd';
import {
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  BarChart,
  Bar,
  Cell,
  ReferenceLine,
} from 'recharts';

import basicStyle from '@chill/assets/styles/constants';
import IntlMessages from '@chill/components/utility/intlMessages';
import { isEmpty } from 'lodash';
// import getApiData from '@chill/lib/helpers/apiHelper';
// import qs from 'query-string';
// import { useLocation } from 'react-router-dom';
// import { injectIntl } from 'react-intl';
// import { useSelector } from 'react-redux';
import theme from '@chill/config/theme/default';
import LinkBox from '@chill/components/LinkBox';
import { FilterOutlined, SwapOutlined } from '@ant-design/icons';
import IsoWidgetsWrapper from '@chill/containers/Widgets/WidgetsWrapper';
import ReportsWidget from '@chill/containers/Widgets/Report/ReportWidget';
import StickerWidget from '@chill/containers/Widgets/Sticker/StickerWidget';
import MarketingWrapper from '@chill/containers/Marketing/Marketing.Styles';

import lineSVG from '@chill/assets/images/LineChart.svg';
import goalSVG from '@chill/assets/images/goal.svg';
import fanSVG from '@chill/assets/images/blackfan.png';

import meterPng from '@chill/assets/images/meter.png';
import locSVG from '@chill/assets/images/location.svg';

import getApiData from '@chill/lib/helpers/apiHelper';
import UsageCustomerProfile from '../UsageCustomerProfile';
import CleanAirCustomerProfile from '../CleanAirCustomerProfile';
import NotificationCustomerProfile from '../NotificationCustomerProfile';
import TripsCustomerProfile from '../TripsCustomerProfile';

const AnalyticsBugaboo = (props) => {
  const { analytics, stEndDate, childProfiles, period_type, analyticAQSData } =
    props;
  const { rowStyle, colStyle } = basicStyle;
  // const access = useSelector((state) => state.Auth.access);
  // const accLoading = access.loading || false;
  // const [pageLoad, setPageLoad] = useState(false); //true in api false =======TODO
  const [filterMode, setFilterMode] = React.useState(false);
  const [dData, setdData] = useState({ data: {}, loader: true });
  const [downloadReport, setDownloadReport] = useState(false);

  function getWindowDimensions() {
    const { innerWidth: width, innerHeight: height } = window;
    return {
      width,
      height,
    };
  }
  function useWindowDimensions() {
    const [windowDimensions, setWindowDimensions] = useState(
      getWindowDimensions(),
    );

    useEffect(() => {
      function handleResize() {
        setWindowDimensions(getWindowDimensions());
      }

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, []);

    return windowDimensions;
  }
  const { height, width } = useWindowDimensions();

  const renderCustomizedLabel = (props) => {
    const { x, y, widtha, heighta, value } = props;

    const fireOffset = value.toString().length < 5;
    const offset = fireOffset ? -40 : 5;
    return (
      <text
        x={x + widtha - offset}
        y={y + heighta - 5}
        fill={fireOffset ? '#285A64' : '#fff'}
        textAnchor="end"
      >
        {value}
      </text>
    );
  };
  const topBoxes = [
    {
      key: 'totalNumberOfTrips',
      text: 'customer.noOfTrips',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.data) ? dData.data.totalNumberOfTrips : 0,
      img: lineSVG,
    },
    {
      key: 'totalConnectedTime',
      text: 'customer.connectedTime',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.data) ? dData.data.totalConnectedTime : 0,
      img: goalSVG,
    },
    {
      key: 'filteringTime',
      text: 'customer.filteringTime',
      link: '/contacts',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.data) ? dData.data.filteringTime : 0,
      img: fanSVG,
      fan: true,
    },
    {
      key: 'averageAirQuality',
      text: 'customer.AveAQS',
      link: '/manage-tags',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.data) ? dData.data.aqsAvg : 0,
      img: meterPng,
    },
  ];

  const noOfChildProfilesBoxes = [
    {
      key: 'sentMessages',
      text: 1,
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: childProfiles?.one_child
        ? `${childProfiles?.one_child?.toFixed(2)}%`
        : '0%',
      img: lineSVG,
    },
    {
      key: 'totalClicks',
      text: 2,
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: childProfiles?.two_child
        ? `${childProfiles?.two_child?.toFixed(2)}%`
        : '0%',
      img: goalSVG,
    },
    {
      key: 'totalClicks',
      text: 3,
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: childProfiles?.three_child
        ? `${childProfiles?.three_child?.toFixed(2)}%`
        : '0%',
      img: goalSVG,
    },
  ];
  const AQI_TITLE = (aqs) => {
    if (aqs < 0) return 'Unknown';
    switch (true) {
      case aqs <= 50:
        return 'Good';
      case aqs >= 51 && aqs <= 100:
        return 'Moderate';
      case aqs >= 101 && aqs <= 150:
        return 'Sensitive';
      case aqs >= 151 && aqs <= 300:
        return 'Unhealthy';
      case aqs >= 301:
        return 'Hazardous';
      default:
        return '';
    }
  };
  /** Function to fetch getAnalyticsAqsData data
   * @function getAnalyticsAqsData
   * @param {object} data aqs data
   */
  async function getAnalyticsAqsData(data = {}) {
    data.period_type = period_type || 'week';
    data.start_end_date = stEndDate;
    try {
      const res = await getApiData(
        'analytics-page-bugaboo/aqs-analytic-data',
        data,
        'post',
      );

      if (res.success && !isEmpty(res.data)) {
        const data = {};
        data.aqsAvg = res?.data?.aqsAvg;
        data.filteringTime = res?.data?.filteringTime;
        data.totalConnectedTime = res?.data?.totalConnectedTime;
        data.aqiTitle = AQI_TITLE(res?.data?.aqsAvg);
        data.totalNumberOfTrips = res?.data?.totalTrips;
        data.aqsData = res?.data?.aqsData;
        data.totalNumberOfTrips = res.data?.totalNumberOfTrips;
        data.tripsPerDay = res.data?.tripsPerDay;
        setdData((pre) => ({ ...pre, data, loader: false }));
        setTimeout(() => {
          setDownloadReport(true);
        }, 500);
      } else {
        setdData((pre) => ({ ...pre, loader: false }));
      }
    } catch (err) {
      console.log('err ===', err);
      setdData((pre) => ({ ...pre, loader: false }));
    }
  }

  useEffect(() => {
    setdData((pre) => ({ ...pre, loader: true }));
    getAnalyticsAqsData();
  }, [period_type, stEndDate]);

  function renderNoOfChildBoxes() {
    return (
      <Row gutter={10}>
        <Col
          lg={8}
          xs={24}
          style={{ display: 'flex', justifyContent: 'center' }}
        >
          <div
            style={{
              alignItems: 'center',
              display: 'flex',
            }}
          >
            <span
              style={{
                color: '#7C94B8',
              }}
            >
              {analytics ? (
                <>
                  <IntlMessages id="analytics.noOfChildProfiles" />
                </>
              ) : (
                <IntlMessages id="customer.activeBg" />
              )}
            </span>
          </div>
        </Col>
        {noOfChildProfilesBoxes.map((widget) => {
          return (
            <Col lg={5} xs={24}>
              <div
                style={{
                  backgroundColor: '#fff',
                  borderRadius: 17,
                  padding: 20,
                  margin: 10,
                  marginTop: 5,
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}
              >
                <div>
                  <span style={{ color: '#7C94B8' }}>
                    <IntlMessages id={widget.text} />
                  </span>
                  <h4 style={{ color: '#637693' }}>{widget.count || 0}</h4>
                </div>
              </div>
            </Col>
          );
        })}
      </Row>
    );
  }

  function renderTopBoxes() {
    return (
      <Row style={rowStyle} className="boxRowStyle">
        {topBoxes.map((widget, index) => {
          return (
            <Col
              lg={12}
              md={12}
              sm={24}
              xs={24}
              style={{
                marginBottom: 25,
                paddingRight: index === 0 || index === 2 ? 17 : 0,
                paddingLeft: index === 1 || index === 3 ? 7 : 0,
              }}
              className="boxesStyle"
              key={widget.text}
            >
              <IsoWidgetsWrapper className="topMarketingBox">
                <LinkBox link="">
                  <StickerWidget
                    number={widget.count || 0}
                    text={<IntlMessages id={widget.text} />}
                    icon={widget.icon}
                    fontColor={widget.fontColor}
                    bgColor={widget.bgColor}
                    image={widget.img}
                    fan={widget.fan}
                    aqs
                  />
                </LinkBox>
              </IsoWidgetsWrapper>
            </Col>
          );
        })}
      </Row>
    );
  }
  const good = '#90D233';
  const moderate = '#E99A53';
  const bad = '#C16C8A';
  const getColor = (p) => {
    if (p <= 50) {
      return good;
    }
    if (p >= 51 && p <= 100) {
      return moderate;
    }
    if (p >= 101) {
      return bad;
    }
  };
  const aqsData = !isEmpty(dData.data) ? dData.data.aqsData : [];
  const tripsPerDayData = !isEmpty(dData.data) ? dData.data.tripsPerDay : [];
  return (
    <MarketingWrapper>
      {/* page ----1 */}
      {renderNoOfChildBoxes()}
      <Row gutter={24} style={{ padding: 16, margin: 0 }}>
        <Col lg={8} sm={24} md={11} xs={24} className="overViewCol">
          <Row>
            <div className="overViewContainer">
              <h2 className="overViewText">
                <Row className="marginWrapper">
                  {/* <img
                    src={meterPng}
                    alt="chat"
                    style={{
                      width: 60,
                      height: 60,
                    }}
                  /> */}
                  <p
                    style={{
                      alignContent: 'center',
                      display: 'flex',
                      flexWrap: 'wrap',
                      marginLeft: 30,
                      marginTop: 25,
                      marginBottom: 15,
                      fontSize: 20,
                    }}
                  >
                    <IntlMessages id="analytics.cleanAirAnalytics" />
                  </p>
                </Row>
              </h2>
            </div>
          </Row>
          <div style={{ marginTop: '15px' }}>{renderTopBoxes()}</div>
        </Col>
        <Col lg={16} md={12} sm={24} xs={24}>
          <Row style={rowStyle} gutter={0} justify="start">
            <Col
              className="dataList isoTrafficList"
              xl={24}
              lg={24}
              sm={24}
              md={24}
              xs={24}
              style={colStyle}
            >
              <IsoWidgetsWrapper style={{ height: '100%' }}>
                <Spin spinning={dData?.loader}>
                  <ReportsWidget
                    widgetClassName="mb0"
                    labelClsName="mb15"
                    style={{
                      height: '100%',
                      borderRadius: 25,
                      boxShadow: '5px 5px 5px 5px #0000',
                    }}
                  >
                    <div style={{ display: 'flex', justifyContent: 'center' }}>
                      Average air quality :
                      {!isEmpty(dData.data)
                        ? ` ${dData?.data?.aqiTitle}`
                        : 'Good'}
                    </div>
                    <BarChart
                      width={width * 0.46}
                      height={300}
                      data={aqsData}
                      margin={{
                        // top: 5,
                        // right: 30,
                        // left: 20,
                        bottom: 15,
                      }}
                    >
                      <CartesianGrid strokeDasharray="1 5" />
                      <XAxis
                        dataKey="day"
                        fontSize={12}
                        tickCount={40}
                        // interval={0}
                        angle={
                          period_type === 'month' ||
                          period_type === 'this_month'
                            ? 45
                            : 0
                        }
                        dy={
                          period_type === 'month' ||
                          period_type === 'this_month'
                            ? 10
                            : 0
                        }
                      />
                      <YAxis fontSize={12} />
                      <Tooltip cursor={{ fill: '#fff' }} />

                      <Bar dataKey="AQS" barSize={25} radius={[7, 7, 0, 0]}>
                        {!isEmpty(dData.data)
                          ? dData.data.aqsData
                            ? aqsData?.map((entry, index) => (
                                <Cell fill={getColor(entry.AQS)} />
                              ))
                            : null
                          : null}
                      </Bar>
                      <Bar
                        dataKey="AQS_Peak"
                        barSize={25}
                        radius={[7, 7, 0, 0]}
                        fill="#82ca9d"
                      >
                        {!isEmpty(dData.data)
                          ? dData.data.aqsData
                            ? aqsData?.map((entry, index) => (
                                <Cell fill={getColor(entry.AQS_Peak)} />
                              ))
                            : null
                          : null}
                      </Bar>
                      <Bar
                        dataKey="Filtered_AQS"
                        barSize={25}
                        radius={[7, 7, 0, 0]}
                        fill="#E99A53"
                      >
                        {!isEmpty(dData.data)
                          ? dData.data.aqsData
                            ? aqsData?.map((entry, index) => (
                                <Cell fill={getColor(entry.Filtered_AQS)} />
                              ))
                            : null
                          : null}
                      </Bar>
                      <ReferenceLine
                        y={50}
                        stroke="blue"
                        label="WHO Threshold"
                      />
                    </BarChart>
                  </ReportsWidget>
                </Spin>
              </IsoWidgetsWrapper>
            </Col>
          </Row>
        </Col>
      </Row>
      {/* page ----1 */}
      <TripsCustomerProfile
        analytics={analytics}
        period_type={period_type}
        stEndDate={stEndDate}
        tripsPerDayData={tripsPerDayData}
        totalNumberOfTrips={
          !isEmpty(dData.data) ? dData.data?.totalNumberOfTrips : 0
        }
      />
      {/* page ----2 */}
      <UsageCustomerProfile
        analytics={analytics}
        period_type={period_type}
        stEndDateA={stEndDate}
        totalUseTime={!isEmpty(dData.data) ? dData.data?.totalConnectedTime : 0}
        totalFiltering={!isEmpty(dData.data) ? dData.data?.filteringTime : 0}
      />
      {/* page ----3 */}
      <CleanAirCustomerProfile
        analytics={analytics}
        period_type={period_type}
        stEndDateA={stEndDate}
      />
      {/* page ----5 */}
      {/* <NotificationCustomerProfile
        analytics={analytics}
        period_type={period_type}
        stEndDateA={stEndDate}
      /> */}
    </MarketingWrapper>
  );
};

export default AnalyticsBugaboo;

import React from 'react';
import { Popover, Tag, Tooltip } from 'antd';
import { isArray, take } from 'lodash';
import IntlMessages from '@chill/components/utility/intlMessages';

function AllTags(props) {
  const { tags = [], clickTag = () => {}, maxTagLen = 4 } = props;

  const allTags = isArray(tags) ? tags : [];
  const allTagsLen = allTags.length;
  const aTags = allTags.length > maxTagLen ? take(allTags, maxTagLen) : allTags;

  /**
   * Render all tags when user clicks on more tag
   */
  function renderAllTags(tgs = []) {
    return (
      <div style={{ maxWidth: 300 }}>
        {tgs.map((ct) => (
          <Tag
            className="pointer"
            onClick={(e) => {
              e.stopPropagation();
              clickTag(ct);
            }}
            key={ct.id}
            style={{ marginBottom: 5 }}
          >
            {ct.name}
          </Tag>
        ))}
      </div>
    );
  }

  return aTags.map((tag, i) => {
    const name = tag && tag.name ? tag.name : '';
    const last = allTagsLen > maxTagLen && i === maxTagLen - 1;
    if (last)
      return (
        <Popover
          key={tag.id}
          title={<IntlMessages id="common.tags" />}
          content={renderAllTags(allTags)}
          trigger="click"
          arrowPointAtCenter
          placement="bottom"
        >
          <Tag
            className="pointer"
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            ...
          </Tag>
        </Popover>
      );
    return (
      <Tooltip key={tag.id} title={name} placement="bottom">
        <Tag
          className="pointer"
          onClick={(e) => {
            e.stopPropagation();
            clickTag(name);
          }}
        >
          {name}
        </Tag>
      </Tooltip>
    );
  });
}

export default AllTags;

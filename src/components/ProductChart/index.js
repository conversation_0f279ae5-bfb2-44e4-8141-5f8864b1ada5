import React from 'react';
import Gauge<PERSON>hart from 'react-gauge-chart';
import _ from 'lodash';

const Products = (props) => {
  const { title, text, type, level, color, percentage } = props;
  return (
    <div>
      <h3>{title}</h3>
      <span style={{ paddingTop: 8 }}>{text}</span>
      <div style={{ alignSelf: 'center' }}>
        <GaugeChart
          id={type}
          nrOfLevels={level}
          colors={color}
          arcWidth={0.3}
          percent={percentage}
          textColor="#000"
        />
      </div>
    </div>
  );
};

export default Products;

import styled from 'styled-components';
import { palette } from 'styled-theme';

const BottomWrapper = styled.div`
  display: none;

  .singleTab {
    &.active-tab {
      color: ${palette('primary', 0)};
    }

    @media (max-width: 375px) {
      padding: 7px;
    }
    @media (max-width: 320px) {
      padding: 6px;
      & > span > span {
        font-size: 11px !important;
      }
    }
  }
  & > i {
    font-size: 24px;
    @media (max-width: 375px) {
      font-size: 15px;
    }
  }
  & > span {
    font-size: 20px;
    text-align: center;
    @media (max-width: 375px) {
      font-size: 13px;
    }
  }
  @media (max-width: 767px) {
    overflow: hidden;
    background: #fff;
    border: 1px solid #e9e9e9;
    box-shadow: 1px 7px 6px #e9e9e9;
    border-top: 3px solid #e9e9e9;
    position: fixed;
    align-items: center;
    height: 55px;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: space-around;
    z-index: 1000;

    .singleTab {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 10px;
      font-size: 22px;
      & .iconImg {
        width: 20px;
      }
      & .iconImg.active-tab {
        filter: invert(10%) sepia(87%) saturate(6065%) hue-rotate(11deg)
          brightness(94%) contrast(101%);
      }
    }
  }
`;

export default BottomWrapper;

import React from 'react';
import { isObject, includes, isArray, isEmpty } from 'lodash';
import { useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import getDefaultPath from '@chill/lib/helpers/url_sync';
import AntIcon from '@chill/components/antdIcon';
import BottomWrapper from './BottomTab.style';
import options from './options';

const BottomTab = () => {
  const preKeys = getDefaultPath();
  const currentPage = isArray(preKeys) && !isEmpty(preKeys) ? preKeys[0] : '';
  const user = useSelector((state) => state.Auth.userData);
  const userType = isObject(user) && user.user_type ? user.user_type : '';
  return (
    <BottomWrapper>
      {options.map((item) => {
        const findUserType = item.userType
          ? includes(item.userType, userType)
          : '';
        if (!findUserType) return null;
        const activeTab = item.key === currentPage;
        const activeCls = activeTab ? 'active-tab' : '';
        return (
          <div key={item.key}>
            <Link
              to={`/dashboard/${item.key}`}
              className={`singleTab linkSecondary ${activeCls}`}
            >
              <AntIcon name={item.img} />
            </Link>
          </div>
        );
      })}
    </BottomWrapper>
  );
};

export default BottomTab;

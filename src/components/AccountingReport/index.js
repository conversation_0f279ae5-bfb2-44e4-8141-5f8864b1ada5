/* eslint-disable dot-notation */
/* eslint-disable array-callback-return */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
import React, { useEffect, useState } from 'react';
import {
  Document,
  Page,
  Text,
  View,
  Image,
  StyleSheet,
} from '@react-pdf/renderer';
import { isArray, isEmpty } from 'lodash';
import moment from 'moment';
import logoPng from '@chill/assets/images/logo.png';
// import IntlMessages from '../utility/intlMessages';

// Create styles for PDF
const styles = StyleSheet.create({
  tableContainer: { marginTop: 20 },
  tableTitle: {
    fontFamily: 'Helvetica-Bold',
    fontSize: 10,
    marginTop: '5px',
    marginBottom: '5px',
    textOverFlow: 'ellipsis',
  },
  tableContent: {
    fontSize: 10,
    marginBottom: '5px',
    textOverFlow: 'ellipsis',
    marginTop: '5px',
  },
  table: {
    borderTop: 1,
    borderRight: 1,
    borderBottom: 1,
    borderLeft: 1,
    borderColor: '#DDDDDD',
    flex: 1,
    flexDirection: 'column',
  },
  tableRow: {
    flex: 1,
    display: 'flex',
    flexDirection: 'row',
  },
  tableCell: {
    flex: 4,
    textAlign: 'center',
    // color: "red",
  },
  bgDark: {
    backgroundColor: '#EEEEEE',
  },
  borderTB: {
    borderTop: 1,
    borderBottom: 1,
    borderColor: '#DDDDDD',
  },
  borderLR: {
    borderRight: 1,
    borderLeft: 1,
    borderColor: '#DDDDDD',
  },
  cardContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  cardViewStyle: {
    flex: 1,
    padding: 5,
    backgroundColor: '#f7f7f7',
    borderRadius: 8,
    marginHorizontal: 5,
  },
  cardTitleStyle: { fontSize: 10, marginBottom: 7 },
  cardCountStyle: { fontSize: 12 },
  imgViewStyle: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});

const AccountingReport = (props) => {
  const { initialValues, campaignData, language } = props;
  const [newData, setNewData] = useState([]);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  useEffect(() => {
    if (
      isArray(initialValues?.products) &&
      initialValues?.products.length > 0
    ) {
      const productsArr = initialValues?.products;
      if (productsArr.length > 0) {
        productsArr.map((item) => {
          console.log('qqqqq', item);
        });
      }

      setNewData(productsArr);
    }
  }, [initialValues]);

  return (
    <Document>
      <Page size="A4">
        <View style={{ flex: 1, padding: 20 }}>
          <View style={styles.imgViewStyle}>
            <Image src={logoPng} alt="Logo" style={{ width: 160 }} />
            <Text style={{ fontSize: 16, marginTop: 10 }}>
              {initialValues?.brand_name || ''}
            </Text>
          </View>
          <View style={styles.tableContainer}>
            <View style={styles.cardContainer}>
              <View style={styles.cardViewStyle}>
                <Text style={styles.cardTitleStyle}>
                  {messageArray['accounting.totalUnitSales']}
                </Text>
                <Text style={styles.cardCountStyle}>
                  {!isEmpty(campaignData) ? initialValues?.totalUnit || 0 : 0}
                </Text>
              </View>
              <View style={styles.cardViewStyle}>
                <Text style={styles.cardTitleStyle}>
                  {messageArray['accounting.totalSalesValue']}
                </Text>
                <Text style={styles.cardCountStyle}>
                  {!isEmpty(campaignData) ? initialValues?.totalSale || 0 : 0}
                </Text>
              </View>
              <View style={styles.cardViewStyle}>
                <Text style={styles.cardTitleStyle}>
                  {messageArray['accounting.averageConRate']}
                </Text>
                <Text style={styles.cardCountStyle}>
                  {!isEmpty(campaignData)
                    ? initialValues.conversion_rate || 0
                    : 0}
                </Text>
              </View>
              <View style={styles.cardViewStyle}>
                <Text style={styles.cardTitleStyle}>
                  {messageArray['accounting.brandCommission']}
                </Text>
                <Text style={styles.cardCountStyle}>
                  {!isEmpty(campaignData) ? initialValues?.commission || 0 : 0}
                </Text>
              </View>
            </View>
          </View>
          <View style={styles.tableContainer}>
            <Text style={styles.tableTitle}>
              {messageArray['accounting.transactions']}
            </Text>
            <View style={styles.table}>
              <View style={[styles.tableRow, styles.bgDark]}>
                <View style={styles.tableCell}>
                  <Text style={styles.tableTitle}>
                    {messageArray['accounting.transactionNumber']}
                  </Text>
                </View>
                <View style={[styles.tableCell, styles.borderLR]}>
                  <Text style={styles.tableTitle}>
                    {messageArray['accounting.transactionDate']}
                  </Text>
                </View>
                <View style={[styles.tableCell, styles.borderLR]}>
                  <Text style={styles.tableTitle}>
                    {messageArray['Product Name']}
                  </Text>
                </View>
                <View style={[styles.tableCell, styles.borderLR]}>
                  <Text style={styles.tableTitle}>{messageArray['SKU']}</Text>
                </View>
                <View style={[styles.tableCell, styles.borderLR]}>
                  <Text style={styles.tableTitle}>
                    {messageArray['accounting.unitQTY']}
                  </Text>
                </View>
                <View style={[styles.tableCell, styles.borderLR]}>
                  <Text style={styles.tableTitle}>
                    {messageArray['accounting.salaesValue']}
                  </Text>
                </View>
                <View style={[styles.tableCell, styles.borderLR]}>
                  <Text style={styles.tableTitle}>
                    {messageArray['label.commision']}
                  </Text>
                </View>
                <View style={[styles.tableCell, styles.borderLR]}>
                  <Text style={styles.tableTitle}>
                    {messageArray['accounting.commission']}
                  </Text>
                </View>
              </View>
              {campaignData && isArray(newData) && newData.length > 0
                ? newData.map((item) => (
                    <View style={[styles.tableRow, styles.borderTB]}>
                      <View style={styles.tableCell}>
                        {/* <Text style={styles.tableContent}>{item.month}</Text> */}
                        <Text
                          style={[
                            styles.tableContent,
                            {
                              marginRight: 10,
                              marginTop: 10,
                              marginBottom: 10,
                              paddingBottom: 10,
                              paddingTop: 10,
                              paddingRight: 10,
                              // color: "cyan",
                            },
                          ]}
                        >
                          {item?.transaction_id}
                        </Text>
                      </View>
                      <View style={[styles.tableCell, styles.borderLR]}>
                        <Text style={styles.tableContent}>
                          {moment(item?.transaction_date).format('YYYY-MM-DD')}
                        </Text>
                      </View>
                      <View style={[styles.tableCell, styles.borderLR]}>
                        <Text style={styles.tableContent}>{item.title}</Text>
                      </View>
                      <View style={[styles.tableCell, styles.borderLR]}>
                        <Text style={styles.tableContent}>{item.sku}</Text>
                      </View>
                      <View style={[styles.tableCell, styles.borderLR]}>
                        <Text style={styles.tableContent}>{item.unit_qty}</Text>
                      </View>
                      <View style={[styles.tableCell, styles.borderLR]}>
                        <Text style={styles.tableContent}>
                          {item.sales_value}
                        </Text>
                      </View>
                      <View style={[styles.tableCell, styles.borderLR]}>
                        <Text style={styles.tableContent}>
                          {item.commission_rate}
                        </Text>
                      </View>
                      <View style={[styles.tableCell, styles.borderLR]}>
                        <Text style={styles.tableContent}>
                          {item.commission}
                        </Text>
                      </View>
                    </View>
                  ))
                : null}
            </View>
          </View>
        </View>
      </Page>
    </Document>
  );
};

export default AccountingReport;

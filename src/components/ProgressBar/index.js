/* eslint-disable consistent-return */
import { isEmpty } from 'lodash';
import React from 'react';
import formatValue from '../utility/commonFunction';

function progressBar({ array }) {
  let allStates = [];
  if (!isEmpty(array)) {
    allStates = array.flatMap((item) => item.states);
    return (
      allStates &&
      allStates?.map((value) => {
        return (
          <div className="chartLegend" style={{ display: 'block' }}>
            <div
              className="legendRow"
              style={{ display: 'block', margin: 3, marginBottom: 15 }}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span className="legendText">{value.state}</span>
                <span className="legendText">
                  {formatValue(value.count) || 0}
                </span>
              </div>

              <div
                className="progress"
                style={{
                  padding: 0,
                  width: '100%',
                  height: '2px',
                  overflow: 'hidden',
                  background: '#B6EAFF',
                  borderRadius: '6px',
                }}
              >
                <div
                  className="bar"
                  style={{
                    width: value.count,
                    position: 'relative',
                    float: 'left',
                    minWidth: '1%',
                    height: '100%',
                    background: 'rgba(4, 184, 255, 1)',
                  }}
                />
              </div>
            </div>
          </div>
        );
      })
    );
  }
}

export default progressBar;

import { Input } from 'antd';
import React from 'react';
import styled from 'styled-components';
import searchImg from '@chill/assets/images/search-grey.png';

const StyledInput = styled.div`
  .search-input {
    width: 100%; /* Set desired width */
    padding: 8px 12px;
    border: none;
    border-bottom: 1px solid #d9d9d9;
    border-radius: 0px;
    font-family: Inter;
    font-size: 18px;
    font-weight: 400;
    line-height: 28px;
    box-shadow: none;
    transition: all 0.3s ease;

    &:focus {
      border-color: #40a9ff;
      outline: none;
      box-shadow: 0 0 4px rgba(24, 144, 255, 0.2);
    }
    .ant-input-prefix {
      margin-right: 16px;
    }

    .ant-input::placeholder {
      font-family: Inter;
      font-size: 18px;
      font-weight: 400;
      line-height: 28px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #9a9ea5;
    }
  }
`;

/**
 * A simple search input component.
 *
 * @param {Object} props - The component props
 * @param {Function} props.onChange - The function to call when the input value changes
 * @param {String} [props.value] - The initial value of the input
 * @param {String} [props.placeholder] - The placeholder text of the input
 */
function SearchInput(props) {
  const { onChange, value, placeholder } = props;
  return (
    <StyledInput>
      <Input
        // Use a search icon as the prefix
        prefix={<img src={searchImg} alt="search" />}
        // Style the input
        className="search-input"
        // Call the onChange function when the input value changes
        onChange={(e) => onChange(e)}
        // Set the initial value of the input
        placeholder={placeholder}
        // Set the value of the input
        value={value}
        // Allow the input to be cleared
        allowClear
        style={{
          height: '44px',
        }}
      />
    </StyledInput>
  );
}

export default SearchInput;

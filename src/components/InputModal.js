/* eslint-disable react/jsx-props-no-spreading */
import React, { useState } from 'react';
import { Row, Col, Form, Modal, Input, InputNumber } from 'antd';
import IntlMessages from '@chill/components/utility/intlMessages';
import useResetFormOnClose from '@chill/lib/hooks/useResetForm';
import getApiData from '@chill/lib/helpers/apiHelper';
import Notification from '@chill/components/Notification';
import { isEmpty, isObject } from 'lodash';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import trans from '@chill/config/translation/translate';
import Editor from './uielements/editor';

const { confirm } = Modal;

/**
 * Component for Single input modal
 * @param {object} props - Required props
 */
function InputModal(props) {
  const {
    visible,
    onClose,
    initVal = {},
    handleSubmit = () => {},
    apiData = {},
    formdata = {},
    modalProps = {},
  } = props;
  const [form] = Form.useForm();

  const [submitting, setSubmitting] = useState(false);

  useResetFormOnClose({ form, visible, initVal });

  async function submitForm(data) {
    setSubmitting(true);

    try {
      const res = await getApiData(apiData.url, data, 'POST');
      if (res.success) {
        handleSubmit(data);
        setTimeout(() => {
          Notification('success', res.message);
        }, 100);
      } else {
        Notification('error', res.message);
      }
      setSubmitting(false);
    } catch (err) {
      setSubmitting(false);
      Notification('error');
    }
  }

  function onOk() {
    form.validateFields().then((values) => {
      if (isObject(apiData) && !isEmpty(apiData)) {
        const aData = apiData.data || {};

        if (formdata.confirm) {
          confirm({
            title: trans(formdata.confirmTitle),
            icon: <ExclamationCircleOutlined />,
            maskClosable: true,
            okText: trans(formdata.confirmBtnTxt),
            okButtonProps: { type: 'danger', id: 'danger-btn' },
            onOk() {
              submitForm({ ...values, ...aData, id: initVal.id });
            },
          });
        } else {
          submitForm({ ...values, ...aData, id: initVal.id });
        }
      } else {
        handleSubmit(values);
      }
    });
  }

  function onKeyDown(e) {
    if (e.key === 'Enter') {
      onOk();
    }
  }

  function renderInput() {
    if (formdata.type === 'password') {
      return (
        <Input.Password autoComplete="new-password" onKeyDown={onKeyDown} />
      );
    }
    if (formdata.type === 'textarea') {
      return <Input.TextArea />;
    }
    if (formdata.type === 'editor') {
      return <Editor />;
    }
    if (formdata.type === 'number') {
      return (
        <InputNumber
          max={formdata.maxNum}
          min={1}
          style={{ minWidth: '100%' }}
        />
      );
    }
    return <Input autoComplete="off" onKeyDown={onKeyDown} />;
  }

  function getRules() {
    const rules = [];

    if (formdata.reqMsg) {
      rules.push({
        required: !!formdata.reqMsg,
        message: <IntlMessages id={formdata.reqMsg} />,
      });
    }
    if (formdata.maxchar) {
      rules.push({
        max: formdata.maxchar,
        message: <IntlMessages id={`err.max.${formdata.maxchar}char`} />,
      });
    }
    if (formdata.type !== 'number') {
      rules.push({
        whitespace: true,
        message: <IntlMessages id="err.blankSpace" />,
      });
    }
    return rules;
  }

  return (
    <Modal
      title={<IntlMessages id={formdata.title} />}
      visible={visible}
      okText={<IntlMessages id="common.submit" />}
      onCancel={submitting ? null : onClose}
      onOk={submitting ? null : onOk}
      confirmLoading={submitting}
      {...modalProps}
    >
      <Form form={form} layout="vertical">
        <Row gutter={6}>
          <Col span={24}>
            <Form.Item
              label={formdata.label && <IntlMessages id={formdata.label} />}
              name={formdata.name}
              rules={[...getRules()]}
            >
              {renderInput()}
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
}

export default InputModal;

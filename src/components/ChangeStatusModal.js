import React, { useState } from 'react';
import { Row, Col, Form, Select, Modal } from 'antd';
import IntlMessages from '@chill/components/utility/intlMessages';
import useResetFormOnClose from '@chill/lib/hooks/useResetForm';
import getApiData from '@chill/lib/helpers/apiHelper';
import Notification from '@chill/components/Notification';
import { allStatus } from '@chill/lib/helpers/utilityData';

const { Option } = Select;

function ChangeStatusModal(props) {
  const { visible, onClose, initVal = {}, handleSubmit } = props;
  const [form] = Form.useForm();

  const [submitting, setSubmitting] = useState(false);

  useResetFormOnClose({ form, visible, initVal });

  async function changeStatus(data) {
    setSubmitting(true);

    try {
      const res = await getApiData('file/change-status', data, 'POST');
      if (res.success) {
        Notification('success', res.message);
        handleSubmit(data);
      } else {
        Notification('error', res.message);
      }
      setSubmitting(false);
    } catch (err) {
      setSubmitting(false);
      Notification('error');
    }
  }

  function onOk() {
    form.validateFields().then((values) => {
      changeStatus({ ...values, id: initVal.id });
    });
  }

  return (
    <Modal
      title={<IntlMessages id="documents.changeStatus" />}
      visible={visible}
      okText={<IntlMessages id="common.submit" />}
      onCancel={submitting ? null : onClose}
      onOk={submitting ? null : onOk}
      confirmLoading={submitting}
    >
      <Form
        form={form}
        initialValues={{ status: initVal.status }}
        layout="vertical"
      >
        <Row gutter={6}>
          <Col span={24}>
            <Form.Item
              label={<IntlMessages id="common.search.status" />}
              name="status"
              rules={[
                {
                  required: true,
                  message: <IntlMessages id="req.status" />,
                },
              ]}
            >
              <Select placeholder={<IntlMessages id="common.search.status" />}>
                {allStatus.map((status) => {
                  if (status.disabled) return null;
                  return (
                    <Option key={status.name} value={status.name}>
                      {status.name}
                    </Option>
                  );
                })}
              </Select>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
}

export default ChangeStatusModal;

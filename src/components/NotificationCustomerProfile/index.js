/* eslint-disable no-param-reassign */
/* eslint-disable no-unused-vars */
/* eslint-disable no-nested-ternary */
import React, { useEffect, useState } from 'react';
import { Row, Col, Spin } from 'antd';
import { XA<PERSON>s, YAxis, CartesianGrid, Tooltip, BarChart, Bar } from 'recharts';
import basicStyle from '@chill/assets/styles/constants';
import IntlMessages from '@chill/components/utility/intlMessages';
import { isEmpty, isUndefined } from 'lodash';
// import getApiData from '@chill/lib/helpers/apiHelper';

import { useSelector } from 'react-redux';

import IsoWidgetsWrapper from '@chill/containers/Widgets/WidgetsWrapper';
import ReportsWidget from '@chill/containers/Widgets/Report/ReportWidget';
import StickerWidget from '@chill/containers/Widgets/Sticker/StickerWidget';
import bell from '@chill/assets/images/bellRed.png';
import drop from '@chill/assets/images/drop.png';
import thermometer from '@chill/assets/images/thermometer.png';
import settings from '@chill/assets/images/settings.png';
import timeline from '@chill/assets/images/timeline.png';
import dashboard from '@chill/assets/images/dashboard.png';
import home from '@chill/assets/images/home.png';
import meterPng from '@chill/assets/images/meter.png';
import getApiData from '@chill/lib/helpers/apiHelper';

const NotificationCustomerProfile = (prop) => {
  const { rowStyle, colStyle } = basicStyle;
  const {
    analytics,
    stEndDateA,
    period_type,
    cperiodType,
    stEndDate,
    initialValues,
  } = prop;
  const [userProfileData, setUserProfileData] = useState({});
  const [pageLoad, setPageLoad] = useState(false); // true in api false =======TODO
  const { language } = useSelector((st) => st.LanguageSwitcher);

  const [dData, setdData] = useState({ data: {}, loader: false });

  function getWindowDimensions() {
    const { innerWidth: width, innerHeight: height } = window;
    return {
      width,
      height,
    };
  }
  function useWindowDimensions() {
    const [windowDimensions, setWindowDimensions] = useState(
      getWindowDimensions(),
    );

    useEffect(() => {
      function handleResize() {
        setWindowDimensions(getWindowDimensions());
      }

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, []);

    return windowDimensions;
  }
  const { width } = useWindowDimensions();
  const pageViewBoxes = [
    {
      key: 'hPerc',
      text: 'customer.home',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.datad) ? `${dData.datad?.hPerc}%` : 0,
      img: home,
    },
    {
      key: 'dPerc',
      text: 'customer.dashborad',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.datad) ? `${dData.datad?.dPerc}%` : 0,
      img: dashboard,
    },
    {
      key: 'tPerc',
      text: 'customer.timeline',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.datad) ? `${dData.datad?.tPerc}%` : 0,
      img: timeline,
    },
    {
      key: 'sPerc',
      text: 'customer.settings',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.datad) ? `${dData.datad?.sPerc}%` : 0,
      img: settings,
    },
  ];
  const activeBGBoxes = [
    {
      key: 'bgPercentage',
      text: 'customer.back',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.datad) ? `${dData.datad?.bgPercentage}%` : 0,
      //   img: lineSVG,
    },
    {
      key: 'activePercentage',
      text: 'customer.active',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.datad) ? `${dData.datad?.activePercentage}%` : 0,
      //   img: goalSVG,
    },
  ];

  async function notificationData(data = {}) {
    data.period_type = period_type || 'week';
    data.start_end_date = stEndDateA;
    try {
      const res = await getApiData(
        'analytics-page-bugaboo/notifications-data',
        data,
        'post',
      );

      if (res.success && !isEmpty(res.data)) {
        const datad = {};
        datad.activePercentage =
          res.data?.appStateTime?.activePercentage.toFixed(2);
        datad.bgPercentage = res.data?.appStateTime?.bgPercentage.toFixed(2);
        datad.notificationsTriggered = res.data?.notificationsTriggered;
        datad.air_quality_alerts = res.data?.air_quality_alerts;
        datad.temp_alerts = res?.data?.temp_alerts;
        datad.totalNotifications = res?.data?.totalNotifications;
        datad.hPerc = res.data?.pageViews?.hPerc.toFixed(2);
        datad.dPerc = res.data?.pageViews?.dPerc.toFixed(2);
        datad.tPerc = res.data?.pageViews?.tPerc.toFixed(2);
        datad.sPerc = res.data?.pageViews?.sPerc.toFixed(2);
        datad.responseRate = res.data?.responseRate.toFixed(2);
        setdData((pre) => ({ ...pre, datad, loader: false }));
      } else {
        setdData((pre) => ({ ...pre, loader: false }));
      }
    } catch (err) {
      console.log('err ===', err);
      setdData((pre) => ({ ...pre, loader: false }));
    }
  }

  async function pageViewData(data = {}) {
    data.period_type = cperiodType;
    data.start_end_date = stEndDate;
    data.user_id = initialValues?.id;
    try {
      const res = await getApiData(
        'user-profile-platform/page-views-data',
        data,
        'post',
      );

      if (res.success && !isEmpty(res.data)) {
        const datad = {};
        datad.activePercentage =
          res.data?.appStateTime?.activePercentage.toFixed(2);
        datad.bgPercentage = res.data?.appStateTime?.bgPercentage.toFixed(2);
        datad.notificationsTriggered = res.data?.notificationsTriggered;
        datad.air_quality_alerts = res.data?.air_quality_alerts;
        datad.temp_alerts = res?.data?.temp_alerts;
        datad.totalNotifications = res?.data?.totalNotifications;
        datad.hPerc = res.data?.pageViews?.hPerc.toFixed(2);
        datad.dPerc = res.data?.pageViews?.dPerc.toFixed(2);
        datad.tPerc = res.data?.pageViews?.tPerc.toFixed(2);
        datad.sPerc = res.data?.pageViews?.sPerc.toFixed(2);
        datad.responseRate = res.data?.responseRate.toFixed(2);
        setdData((pre) => ({ ...pre, datad, loader: false }));
      } else {
        setdData((pre) => ({ ...pre, loader: false }));
      }
    } catch (err) {
      console.log('err ===', err);
      setdData((pre) => ({ ...pre, loader: false }));
    }
  }

  useEffect(() => {
    setdData((pre) => ({ ...pre, loader: true }));
    if (!analytics) {
      pageViewData();
    } else {
      notificationData();
    }
  }, [cperiodType, stEndDate, period_type, stEndDateA]);

  function renderPageViewBoxes() {
    return (
      <Row gutter={10}>
        <Col lg={4} xs={24}>
          <div
            style={{
              alignSelf: 'center',
              justifyContent: 'center',
              marginLeft: 40,
              marginTop: 20,
            }}
          >
            <span style={{ color: '#7C94B8' }}>
              <IntlMessages id="customer.pgView" />
            </span>
          </div>
        </Col>
        {pageViewBoxes.map((widget) => {
          return (
            <Col lg={5} xs={24}>
              <div
                style={{
                  backgroundColor: '#fff',
                  borderRadius: 17,
                  padding: 20,
                  margin: 10,
                  marginTop: 5,
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}
              >
                <div>
                  <span style={{ color: '#7C94B8' }}>
                    <IntlMessages id={widget.text} />
                  </span>
                  <h4 style={{ color: '#637693' }}>{widget.count || 0}</h4>
                </div>
                <div className="isoIconWrapper">
                  <img
                    src={widget.img}
                    alt="chat"
                    style={{
                      width: 45,
                      height: 45,
                      marginTop: 2,

                      marginLeft: 0,
                    }}
                  />
                </div>
              </div>
            </Col>
          );
        })}
      </Row>
    );
  }

  function renderActiveBGBoxes() {
    return (
      <Row gutter={10}>
        <Col lg={4} xs={24}>
          <div
            style={{
              alignSelf: 'center',
              justifyContent: 'center',
              marginLeft: 40,
              marginTop: 20,
            }}
          >
            <span style={{ color: '#7C94B8' }}>
              <IntlMessages id="customer.activeBg" />
            </span>
          </div>
        </Col>
        {activeBGBoxes.map((widget) => {
          return (
            <Col lg={5} xs={24}>
              <div
                style={{
                  backgroundColor: '#fff',
                  borderRadius: 17,
                  padding: 20,
                  margin: 10,
                  marginTop: 5,
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}
              >
                <div>
                  <span style={{ color: '#7C94B8' }}>
                    <IntlMessages id={widget.text} />
                  </span>
                  <h4 style={{ color: '#637693' }}>{widget.count || 0}</h4>
                </div>
              </div>
            </Col>
          );
        })}
      </Row>
    );
  }
  const notificationBoxes = [
    {
      key: 'totalNumberOfTrips',
      text: 'customer.totalNotification',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData?.datad) ? dData?.datad?.totalNotifications : 0,
      img: bell,
      imgName: 'bell',
    },
    {
      key: 'totalConnectedTime',
      text: 'customer.airQAlerts',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData?.datad) ? dData?.datad?.air_quality_alerts : 0,
      img: meterPng,
      imgName: 'meter',
    },
    {
      key: 'filteringTime',
      text: 'customer.tempAlerts',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData?.datad) ? dData?.datad?.temp_alerts : 0,
      img: thermometer,
      imgName: 'temp',
    },
    // {
    //   key: 'averageAirQuality',
    //   text: 'customer.humidityAlerts',
    //   fontColor: '#000000',
    //   bgColor: '#ffffffff',
    //   count: !isEmpty(dData.data) ? dData.data.humidityAlerts : 0,
    //   img: drop,
    //   imgName: 'drop',
    // },
  ];
  function renderNotificationBoxes() {
    return (
      <Row style={rowStyle} className="boxRowStyle">
        {notificationBoxes.map((widget, index) => {
          return (
            <Col
              lg={12}
              md={12}
              sm={24}
              xs={24}
              style={{
                marginBottom: 25,
                paddingRight: index === 0 || index === 2 ? 17 : 0,
                paddingLeft: index === 1 || index === 3 ? 7 : 0,
              }}
              className="boxesStyle"
              key={widget.text}
            >
              <IsoWidgetsWrapper className="topMarketingBox">
                <StickerWidget
                  number={widget.count || 0}
                  text={<IntlMessages id={widget.text} />}
                  icon={widget.icon}
                  fontColor={widget.fontColor}
                  bgColor={widget.bgColor}
                  image={widget.img}
                  imgName={widget.imgName}
                />
              </IsoWidgetsWrapper>
            </Col>
          );
        })}
      </Row>
    );
  }
  const aqsData = !isEmpty(dData?.datad)
    ? dData?.datad?.notificationsTriggered
    : [];
  return (
    <>
      <Row gutter={24} style={{ padding: 16, margin: 0 }}>
        <Col lg={9} sm={24} md={11} xs={24} className="overViewCol">
          <div style={{ marginTop: '5px' }}>{renderNotificationBoxes()}</div>
          <Row>
            <div className="overViewContainer">
              <h2 className="overViewText">
                <Row className="marginWrapper">
                  <div style={{ marginBottom: 30, marginTop: 35 }}>
                    <span
                      style={{
                        color: '#fff',
                      }}
                    >
                      <IntlMessages id="customer.notificationResRate" />
                    </span>
                    {!isEmpty(dData.datad)
                      ? ` ${dData.datad?.responseRate}%`
                      : ` - 0`}
                  </div>
                </Row>
              </h2>
            </div>
          </Row>
        </Col>
        <Col lg={15} md={12} sm={24} xs={24}>
          <Row style={rowStyle} gutter={0} justify="start">
            <Col
              className="dataList isoTrafficList"
              xl={24}
              lg={24}
              sm={24}
              md={24}
              xs={24}
              style={colStyle}
            >
              <IsoWidgetsWrapper style={{ height: '100%' }}>
                <Spin spinning={dData?.loader}>
                  <ReportsWidget
                    widgetClassName="mb0"
                    labelClsName="mb15"
                    style={{
                      height: '100%',
                      borderRadius: 25,
                      boxShadow: '5px 5px 5px 5px #0000',
                    }}
                  >
                    <span style={{ color: '#7C94B8', marginBottom: 10 }}>
                      <IntlMessages id="customer.notiTriggrd" />
                    </span>
                    <BarChart
                      width={width * 0.45}
                      height={300}
                      data={aqsData}
                      margin={{
                        // top: 5,
                        // right: 30,
                        // left: 20,
                        bottom: 15,
                      }}
                    >
                      <CartesianGrid strokeDasharray="1 5" />
                      <XAxis
                        dataKey="day"
                        fontSize={12}
                        tickCount={40}
                        // interval={0}
                        angle={
                          cperiodType === 'month' ||
                          cperiodType === 'this_month'
                            ? 45
                            : period_type === 'month' ||
                              period_type === 'this_month'
                            ? 45
                            : 0
                        }
                        dy={
                          cperiodType === 'month' ||
                          cperiodType === 'this_month'
                            ? 10
                            : period_type === 'month' ||
                              period_type === 'this_month'
                            ? 10
                            : 0
                        }
                      />
                      <YAxis fontSize={12} />
                      <Tooltip cursor={{ fill: '#fff' }} />
                      {/* <Legend /> */}

                      <Bar dataKey="count" barSize={15} fill="#6496f9">
                        {/* <LabelList
                              dataKey="aqs"
                              content={renderCustomizedLabel}
                              position="top"
                              style={{ fill: 'black' }}
                            /> */}
                      </Bar>
                    </BarChart>
                  </ReportsWidget>
                </Spin>
              </IsoWidgetsWrapper>
            </Col>
          </Row>
        </Col>
      </Row>
      {renderPageViewBoxes()}
      {renderActiveBGBoxes()}
    </>
  );
};
export default NotificationCustomerProfile;

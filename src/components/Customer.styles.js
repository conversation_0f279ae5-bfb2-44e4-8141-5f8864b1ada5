import styled from 'styled-components';
import { palette } from 'styled-theme';
import WithDirection from '@chill/lib/helpers/rtl';

const CardWrapper = styled.div`
  width: auto;
  overflow: inherit;
  position: relative;
  .isoInvoiceTable {
    table {
      tbody {
        tr {
          td {
            .isoInvoiceBtnView {
              display: flex;
              flex-direction: row;
              & > button {
                margin: ${(props) =>
                  props['data-rtl'] === 'rtl' ? '0 0 0 10px' : '0 10px 0 0'};
              }
              > a {
                margin: ${(props) =>
                  props['data-rtl'] === 'rtl' ? '0 0 0 15px' : '0 15px 0 0'};
              }
              & > button.deleteCircleBtn {
                color: red;
              }

              & .signBtn {
                line-height: 35px;
              }
            }
            & a.videoLink {
              display: block;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
              margin-left: 10px;
            }
          }
        }
      }
    }
  }

  .invoiceListTable {
    .patientInfo {
      max-width: 70%;
      @media only screen and (max-width: 1600px) {
        max-width: 100%;
      }
    }
    .MoreInfoLabel {
      font-size: 14px;
      color: ${palette('text', 6)};
      @media only screen and (max-width: 768px) and (min-width: 430px) {
        white-space: nowrap !important;
        width: 100px !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }
    }
    .MoreInfoValue {
      font-size: 14px;
      color: ${palette('text', 1)};
      margin-left: 10px;
      @media (max-width: 375px) {
        white-space: nowrap !important;
        width: 110px !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }
    }

    .invoiceViewBtn {
      color: ${palette('text', 3)};

      &:hover {
        color: ${palette('primary', 0)};
      }
    }

    .invoiceDltBtn {
      font-size: 18px;

      & button,
      & button:hover {
        border: 0;
        color: ${palette('error', 0)};
      }
    }
  }
`;

export default WithDirection(CardWrapper);

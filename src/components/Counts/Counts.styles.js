import styled from 'styled-components';
import WithDirection from '@chill/lib/helpers/rtl';

const CountsStyle = styled.div`
  .isoCountsCol {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
    // background-color: #ffffff;
    // border-radius: 10px;
    margin: 24px 0px 0px;
    padding: 0px 16px;
    isoCountsCol @media (max-width: 720px) {
      margin: 0px 0px 16px;
    }
  }
  & .isoCountsTextDiv {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  & .isoCountsText {
    span {
      font-family: Inter;
      font-size: 11px;
      font-weight: 500;
      line-height: 22px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #06a3e1;
    }
  }
  .icon {
    font-size: 80px;
  }
`;

export default WithDirection(CountsStyle);

import React from 'react';
import AntIcon from '@chill/components/antdIcon';
import CountsStyle from './Counts.styles';

const Counts = (props) => {
  const { iconName, title, text, iconColor, image } = props;

  return (
    <CountsStyle>
      <div className="isoCountsCol">
        <div className="isoCountsTextDiv">
          <text className="isoCountsText">{title}</text>
          {iconName ? (
            <div style={{ color: iconColor, fontSize: 20 }}>
              <AntIcon name={iconName} />
            </div>
          ) : (
            <img
              src={image}
              alt="chat"
              style={{ width: 20, height: 20, marginTop: 2 }}
            />
          )}
        </div>
        <text
          style={{
            fontFamily: 'Inter',
            fontSize: 20,
            fontWeight: 700,
            lineHeight: '22px',
            textAlign: 'left',
            textDecoration: 'none',
            textDecorationSkipInk: 'none',
            color: '#171A1F',
          }}
        >
          {text}
        </text>
      </div>
    </CountsStyle>
  );
};

export default Counts;

/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
import React from 'react';
import {
  Document,
  Page,
  Text,
  View,
  Image,
  StyleSheet,
} from '@react-pdf/renderer';
import { isArray, isEmpty, isObject } from 'lodash';
import logoPng from '@chill/assets/images/logo.png';

// Create styles for PDF
const styles = StyleSheet.create({
  tableContainer: { marginTop: 20 },
  tableTitle: {
    fontFamily: 'Helvetica-Bold',
    fontSize: 12,
    marginTop: '5px',
    marginBottom: '5px',
    textOverFlow: 'ellipsis',
  },
  tableContent: {
    fontSize: 12,
    marginBottom: '5px',
    textOverFlow: 'ellipsis',
    marginTop: '5px',
  },
  table: {
    borderTop: 1,
    borderRight: 1,
    borderBottom: 1,
    borderLeft: 1,
    borderColor: '#DDDDDD',
    flex: 1,
    flexDirection: 'column',
  },
  tableRow: {
    flex: 1,
    display: 'flex',
    flexDirection: 'row',
  },
  tableCell: {
    flex: 4,
    textAlign: 'center',
  },
  bgDark: {
    backgroundColor: '#EEEEEE',
  },
  borderTB: {
    borderTop: 1,
    borderBottom: 1,
    borderColor: '#DDDDDD',
  },
  borderLR: {
    borderRight: 1,
    borderLeft: 1,
    borderColor: '#DDDDDD',
  },
  imgViewStyle: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});

const AnalyticsReport = (props) => {
  const { analyticsData, performanceChart, language } = props;
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  return (
    <Document>
      <Page size="A4">
        <View style={{ flex: 1, padding: 20 }}>
          <View style={styles.imgViewStyle}>
            <Image src={logoPng} alt="Logo" style={{ width: 160 }} />
          </View>
          <View style={styles.tableContainer}>
            <Text style={styles.tableTitle}>
              {messageArray['dashboard.activeUser']}
            </Text>
            <View style={styles.table}>
              <View style={[styles.tableRow, styles.bgDark]}>
                <View style={styles.tableCell}>
                  <Text style={styles.tableTitle}>
                    {messageArray['common.date']}
                  </Text>
                </View>
                <View style={[styles.tableCell, styles.borderLR]}>
                  <Text style={styles.tableTitle}>
                    {messageArray['common.users']}
                  </Text>
                </View>
              </View>
              {analyticsData &&
              isArray(analyticsData.ActiveUserChart) &&
              analyticsData.ActiveUserChart.length > 0
                ? analyticsData.ActiveUserChart.map((item, index) => {
                    if (index === 0) {
                      return null;
                    }

                    return (
                      <View
                        key={`active_${item[0]}`}
                        style={[styles.tableRow, styles.borderTB]}
                      >
                        <View style={styles.tableCell}>
                          <Text style={styles.tableContent}>{item[0]}</Text>
                        </View>
                        <View style={[styles.tableCell, styles.borderLR]}>
                          <Text style={styles.tableContent}>{item[1]}</Text>
                        </View>
                      </View>
                    );
                  })
                : null}
            </View>
          </View>
          <View style={styles.tableContainer}>
            <Text style={styles.tableTitle}>
              {messageArray['analytics.performanceAnalysis']}
            </Text>
            {isObject(performanceChart) &&
            !isEmpty(performanceChart) &&
            !isEmpty(performanceChart.newDailyUser) &&
            isArray(performanceChart.newDailyUser.data) &&
            performanceChart.newDailyUser.data.length > 0 ? (
              <View style={styles.tableContainer}>
                <Text style={styles.tableTitle}>
                  {messageArray['chart.newdailyusers']}
                </Text>
                <View style={styles.table}>
                  <View style={[styles.tableRow, styles.bgDark]}>
                    <View style={styles.tableCell}>
                      <Text style={styles.tableTitle}>
                        {messageArray['common.date']}
                      </Text>
                    </View>
                    <View style={[styles.tableCell, styles.borderLR]}>
                      <Text style={styles.tableTitle}>
                        {messageArray['common.users']}
                      </Text>
                    </View>
                  </View>
                  {performanceChart.newDailyUser.data.map((item, index) => {
                    if (index === 0) {
                      return null;
                    }

                    return (
                      <View
                        key={`daily_user_${item[0]}`}
                        style={[styles.tableRow, styles.borderTB]}
                      >
                        <View style={styles.tableCell}>
                          <Text style={styles.tableContent}>{item[0]}</Text>
                        </View>
                        <View style={[styles.tableCell, styles.borderLR]}>
                          <Text style={styles.tableContent}>{item[1]}</Text>
                        </View>
                      </View>
                    );
                  })}
                </View>
              </View>
            ) : null}
            {isObject(performanceChart) &&
            !isEmpty(performanceChart) &&
            !isEmpty(performanceChart.postShares) &&
            isArray(performanceChart.postShares.data) &&
            performanceChart.postShares.data.length > 0 ? (
              <View style={styles.tableContainer}>
                <Text style={styles.tableTitle}>
                  {messageArray['chart.postshares']}
                </Text>
                <View style={styles.table}>
                  <View style={[styles.tableRow, styles.bgDark]}>
                    <View style={styles.tableCell}>
                      <Text style={styles.tableTitle}>
                        {messageArray['common.date']}
                      </Text>
                    </View>
                    <View style={[styles.tableCell, styles.borderLR]}>
                      <Text style={styles.tableTitle}>
                        {messageArray['common.users']}
                      </Text>
                    </View>
                  </View>
                  {performanceChart.postShares.data.map((item, index) => {
                    if (index === 0) {
                      return null;
                    }

                    return (
                      <View
                        key={`share_${item[0]}`}
                        style={[styles.tableRow, styles.borderTB]}
                      >
                        <View style={styles.tableCell}>
                          <Text style={styles.tableContent}>{item[0]}</Text>
                        </View>
                        <View style={[styles.tableCell, styles.borderLR]}>
                          <Text style={styles.tableContent}>{item[1]}</Text>
                        </View>
                      </View>
                    );
                  })}
                </View>
              </View>
            ) : null}
            {isObject(performanceChart) &&
            !isEmpty(performanceChart) &&
            !isEmpty(performanceChart.productUsed) &&
            isArray(performanceChart.productUsed.data) &&
            performanceChart.productUsed.data.length > 0 ? (
              <View style={styles.tableContainer}>
                <Text style={styles.tableTitle}>
                  {messageArray['chart.productsperussers(Avg)']}
                </Text>
                <View style={styles.table}>
                  <View style={[styles.tableRow, styles.bgDark]}>
                    <View style={styles.tableCell}>
                      <Text style={styles.tableTitle}>
                        {messageArray['common.date']}
                      </Text>
                    </View>
                    <View style={[styles.tableCell, styles.borderLR]}>
                      <Text style={styles.tableTitle}>
                        {messageArray['common.users']}
                      </Text>
                    </View>
                  </View>
                  {performanceChart.productUsed.data.map((item, index) => {
                    if (index === 0) {
                      return null;
                    }

                    return (
                      <View
                        key={`used_${item[0]}`}
                        style={[styles.tableRow, styles.borderTB]}
                      >
                        <View style={styles.tableCell}>
                          <Text style={styles.tableContent}>{item[0]}</Text>
                        </View>
                        <View style={[styles.tableCell, styles.borderLR]}>
                          <Text style={styles.tableContent}>{item[1]}</Text>
                        </View>
                      </View>
                    );
                  })}
                </View>
              </View>
            ) : null}
          </View>
          <View style={styles.tableContainer}>
            <Text style={styles.tableTitle}>
              {messageArray['analytics.operatingSystem']}
            </Text>
            <View style={styles.table}>
              <View style={[styles.tableRow, styles.bgDark]}>
                <View style={styles.tableCell}>
                  <Text style={styles.tableTitle}>
                    {messageArray['th.platforms']}
                  </Text>
                </View>
                <View style={[styles.tableCell, styles.borderLR]}>
                  <Text style={styles.tableTitle}>
                    {messageArray['common.count']}
                  </Text>
                </View>
              </View>
              {analyticsData &&
              isArray(analyticsData.operatingSystemChart) &&
              analyticsData.operatingSystemChart.length > 0
                ? analyticsData.operatingSystemChart.map((item, index) => {
                    if (index === 0) {
                      return null;
                    }

                    return (
                      <View
                        key={`system_chart_${item[0]}`}
                        style={[styles.tableRow, styles.borderTB]}
                      >
                        <View style={styles.tableCell}>
                          <Text style={styles.tableContent}>{item[0]}</Text>
                        </View>
                        <View style={[styles.tableCell, styles.borderLR]}>
                          <Text style={styles.tableContent}>{item[1]}</Text>
                        </View>
                      </View>
                    );
                  })
                : null}
            </View>
          </View>
          <View style={styles.tableContainer}>
            <Text style={styles.tableTitle}>
              {messageArray['analytics.topLinkClicks']}
            </Text>
            <View style={styles.table}>
              <View style={[styles.tableRow, styles.bgDark]}>
                <View style={styles.tableCell}>
                  <Text style={styles.tableTitle}>
                    {messageArray['marketing.campaignName']}
                  </Text>
                </View>
                <View style={[styles.tableCell, styles.borderLR]}>
                  <Text style={styles.tableTitle}>
                    {messageArray['common.count']}
                  </Text>
                </View>
              </View>
              {analyticsData &&
              isArray(analyticsData.topLinkClicks) &&
              analyticsData.topLinkClicks.length > 0
                ? analyticsData.topLinkClicks.map((item) => (
                    <View
                      key={`link_${item.campaign_name}`}
                      style={[styles.tableRow, styles.borderTB]}
                    >
                      <View style={styles.tableCell}>
                        <Text style={styles.tableContent}>
                          {item.campaign_name}
                        </Text>
                      </View>
                      <View style={[styles.tableCell, styles.borderLR]}>
                        <Text style={styles.tableContent}>{item.total}</Text>
                      </View>
                    </View>
                  ))
                : null}
            </View>
          </View>
          {analyticsData &&
          isArray(analyticsData.goalCompletion) &&
          analyticsData.goalCompletion.length > 0 ? (
            <View style={styles.tableContainer}>
              <Text style={styles.tableTitle}>
                {messageArray['analytics.goalCOmpletion']}
              </Text>
              <View style={styles.table}>
                <View style={[styles.tableRow, styles.bgDark]}>
                  <View style={styles.tableCell}>
                    <Text style={styles.tableTitle}>
                      {messageArray['antTable.title']}
                    </Text>
                  </View>
                  <View style={[styles.tableCell, styles.borderLR]}>
                    <Text style={styles.tableTitle}>
                      {messageArray['antTable.title.status']}
                    </Text>
                  </View>
                </View>
                {analyticsData &&
                isArray(analyticsData.goalCompletion) &&
                analyticsData.goalCompletion.length > 0
                  ? analyticsData.goalCompletion.map((item) => (
                      <View
                        key={`goal_${item.goal_title}`}
                        style={[styles.tableRow, styles.borderTB]}
                      >
                        <View style={styles.tableCell}>
                          <Text style={styles.tableContent}>
                            {item.goal_title}
                          </Text>
                        </View>
                        <View style={[styles.tableCell, styles.borderLR]}>
                          <Text style={styles.tableContent}>{item.status}</Text>
                        </View>
                      </View>
                    ))
                  : null}
              </View>
            </View>
          ) : null}
          {analyticsData &&
          isArray(analyticsData.deviceList) &&
          analyticsData.deviceList.length > 0 ? (
            <View style={styles.tableContainer}>
              <Text style={styles.tableTitle}>
                {messageArray['analytics.devices']}
              </Text>
              <View style={styles.table}>
                <View style={[styles.tableRow, styles.bgDark]}>
                  <View style={styles.tableCell}>
                    <Text style={styles.tableTitle}>
                      {messageArray['analytics.deviceName']}
                    </Text>
                  </View>
                  <View style={[styles.tableCell, styles.borderLR]}>
                    <Text style={styles.tableTitle}>
                      {messageArray['common.count']}
                    </Text>
                  </View>
                </View>
                {analyticsData &&
                isArray(analyticsData.deviceList) &&
                analyticsData.deviceList.length > 0
                  ? analyticsData.deviceList.map((item) => (
                      <View
                        key={`device_${item.device_name}`}
                        style={[styles.tableRow, styles.borderTB]}
                      >
                        <View style={styles.tableCell}>
                          <Text style={styles.tableContent}>
                            {item.device_name}
                          </Text>
                        </View>
                        <View style={[styles.tableCell, styles.borderLR]}>
                          <Text style={styles.tableContent}>
                            {item.userCount}
                          </Text>
                        </View>
                      </View>
                    ))
                  : null}
              </View>
            </View>
          ) : null}
        </View>
      </Page>
    </Document>
  );
};

export default AnalyticsReport;

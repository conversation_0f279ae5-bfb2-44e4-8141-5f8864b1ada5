import styled from 'styled-components';
import withDirection from '@chill/lib/helpers/rtl';
import { palette } from 'styled-theme';

const DirectoryWrapper = withDirection(styled.div`
  & .moveHome {
    padding: 6px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      background-color: #f5f5f5;
    }

    &.selected {
      background: ${palette('primary', 0)};
      color: #fff;
    }

    & p {
      font-size: 15px;
      & .anticon {
        margin: ${(props) =>
          props['data-rtl'] === 'rtl' ? '0 25px 0 10px' : '0 10px 0 0'};
        font-size: 16px;
      }
    }
  }

  & .ant-tree {
    & .ant-tree-list {
      & .ant-tree-list-holder-inner {
        overflow-x: auto;

        & .ant-tree-treenode {
          & .ant-tree-indent {
            white-space: initial;
          }
        }
      }

      & .ant-tree-switcher,
      & .ant-tree-iconEle {
        height: 34px;
        line-height: 34px;

        & .anticon {
          font-size: 13px;
        }
      }
      & .ant-tree-iconEle {
        & .anticon {
          font-size: 18px;
        }
      }
      & .ant-tree-node-content-wrapper {
        min-height: 34px;
        line-height: 34px;

        & .ant-tree-title {
          font-size: 15px;
          font-weight: 500;
          margin-left: 5px;
        }
      }
    }
  }
`);

export default DirectoryWrapper;

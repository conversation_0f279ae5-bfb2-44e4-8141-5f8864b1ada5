/* eslint-disable no-param-reassign */
import React, { useState, useEffect } from 'react';
import { Tree, Spin, Empty } from 'antd';
import Notification from '@chill/components/Notification';
import getApiData from '@chill/lib/helpers/apiHelper';
import IntlMessages from '@chill/components/utility/intlMessages';
import Scrollbar from '@chill/components/utility/customScrollBar';
import { isEmpty, isEqual } from 'lodash';
import isServer from '@chill/lib/helpers/isServer';

import { HomeOutlined } from '@ant-design/icons';
import DirectoryWrapper from './Directory.styles';

const { DirectoryTree: DirectoryTreeAnt } = Tree;

function DirectoryTree(props) {
  const {
    visible = false,
    dData = {},
    folderId = '',
    onSelect = () => {},
    height,
    selected = [],
    hint = '',
  } = props;

  const [treeData, setTreeData] = useState({ loading: false, data: [] });
  const wHeight = !isServer ? window.innerHeight : 800;

  /**
   * Function to fetch all folders of user
   */
  async function fetchTree() {
    setTreeData((pre) => ({ ...pre, loading: true }));
    try {
      const res = await getApiData('file/get-tree', {
        doc_type: dData?.apiData?.doc_type || 'Document',
      });
      if (res.success) {
        /** Disable current folder */
        // res.data.forEach(function iter(a) {
        //   if (a.id === folderId) {
        //     a.disabled = true;
        //     delete a.children;
        //   }
        //   if (isArray(a.children)) {
        //     a.children.forEach(iter);
        //   }
        // });
        setTreeData((pre) => ({ ...pre, data: res.data, loading: false }));
      } else {
        setTreeData((pre) => ({ ...pre, loading: false }));
        Notification('error', res.message);
      }
    } catch (err) {
      setTreeData((pre) => ({ ...pre, loading: false }));
      Notification('error');
    }
  }

  useEffect(() => {
    if (visible) {
      fetchTree();
    } else {
      onSelect([]);
    }
  }, [visible]);

  /**
   * Function to handle selection of folders
   */
  function handleTreeSelection(data) {
    const val = isEqual(data, selected) ? [] : data;
    onSelect(val);
  }

  /**
   * Handle home click event
   */
  function clickHome() {
    handleTreeSelection(['0']);
  }

  /**
   * Function to render Home directory option
   */
  function renderMoveHome() {
    const homeSelected = selected && selected[0] === '0';
    if (folderId === '0') return null;
    return (
      <div
        className={`moveHome ${homeSelected ? 'selected' : ''}`}
        role="button"
        tabIndex={-1}
        onKeyDown={clickHome}
        onClick={clickHome}
      >
        <p>
          <HomeOutlined />
          <IntlMessages id="documents.homedir" />
        </p>
      </div>
    );
  }

  return (
    <DirectoryWrapper>
      {hint && (
        <p className="mb20">
          <IntlMessages id={hint} />
        </p>
      )}
      <Spin spinning={treeData.loading || false}>
        {!isEmpty(treeData.data) ? (
          <Scrollbar
            style={{
              height: height || wHeight - Math.round(wHeight / 1.5),
            }}
          >
            {renderMoveHome()}
            <DirectoryTreeAnt
              height={height}
              treeData={treeData.data}
              onSelect={handleTreeSelection}
              selectedKeys={selected}
            />
          </Scrollbar>
        ) : (
          <Empty description={<IntlMessages id="documents.noDir" />} />
        )}
      </Spin>
    </DirectoryWrapper>
  );
}

export default DirectoryTree;

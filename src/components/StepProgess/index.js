import React from 'react';
import { map } from 'lodash';
import StepProgressWrapper from './StepProgress.Style';

/**
 * Component to display Progress
 * @param {object} props - Required props
 */
function StepProgess(props) {
  const { steps, className = '', renderPGBar } = props;

  function renderProgressBar(step, i) {
    return (
      // eslint-disable-next-line react/no-array-index-key
      <div key={i} className="progress-bar" style={{ width: step.width }} />
    );
  }

  return (
    <StepProgressWrapper className={className || ''}>
      {map(steps, renderPGBar || renderProgressBar)}
    </StepProgressWrapper>
  );
}

export default StepProgess;

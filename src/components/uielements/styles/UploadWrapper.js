import styled from 'styled-components';
import withDirection from '@chill/lib/helpers/rtl';
import { palette } from 'styled-theme';

const UploadWrapper = withDirection(styled.div`
  & .fName {
    margin: 7px auto 0px;
    max-width: 250px;
  }

  & .verify-container {
    display: inline-block;
    margin: 0 auto;
    padding: 5px;
    border: 1px dashed #999;
    position: relative;

    & .imgMain {
      max-width: 140px;
    }

    & .fileMain {
      width: 80px;
      margin: 15px 0px;
      color: ${palette('primary', 0)};

      & .anticon-file-pdf {
        font-size: 40px;
      }
      & .viewTxt {
        margin-top: 10px;
      }
    }

    & img {
      max-width: 100%;
    }

    & .absolute-remove {
      position: absolute;
      top: -10px;
      right: -10px;
      font-size: 20px;
      line-height: 1;
      background-color: #fff;
    }
  }
`);

export default UploadWrapper;

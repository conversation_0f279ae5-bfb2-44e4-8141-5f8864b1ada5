import styled from 'styled-components';
import WithDirection from '@chill/lib/helpers/rtl';

const ButtonWrapper = WithDirection(styled.div`
  & > button {
    // background: linear-gradient(
    //   90deg,
    //   rgba(47, 149, 247, 1) 25%,
    //   rgba(70, 182, 248, 1) 50%
    // );
    border: none;
    border-radius: 12px;
    // &:hover {
    //   background: linear-gradient(
    //     90deg,
    //     rgba(47, 149, 247, 1) 25%,
    //     rgba(70, 182, 248, 1) 50%
    //   );
    // }
    & > span {
    }
  }
`);

export default ButtonWrapper;

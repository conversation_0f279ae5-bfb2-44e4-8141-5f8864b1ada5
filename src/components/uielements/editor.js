/* eslint-disable no-multi-str */
import React from 'react';
import PropTypes from 'prop-types';
import siteConfig from '@chill/config/site.config';
import { Editor } from '@tinymce/tinymce-react';

const fontsToPreload = [
  'Arial=arial,helvetica,sans-serif',
  'Book Antiqua=book antiqua,palatino',
  'Comic Sans MS=comic sans ms,sans-serif',
  'Courier New=courier new,courier',
  'Georgia=georgia,palatino',
  'Helvetica=helvetica',
  'Times New Roman=times new roman,times',
  `Open Sans='Open Sans', sans-serif`,
  `<PERSON>='<PERSON>', sans-serif`,
  `Amiri_ar='<PERSON><PERSON>', serif _ ar`,
  `<PERSON>em <PERSON>_ar='<PERSON>em <PERSON>', sans-serif _ ar`,
  `Aref Ruqaa_ar='Aref Ruqaa', serif _ ar`,
  `Scheherazade_ar='Scheherazade', serif _ ar`,
  `Ta<PERSON>wal_ar='<PERSON><PERSON><PERSON>', sans-serif _ ar`,
  `Vibes_ar='Vibes', cursive`,
  `Luc<PERSON>='Lucida Console', Monaco, monospace`,
  `Comic Sans MS='Comic Sans MS', 'Comic Sans', cursive, sans-serif`,
];

class EditorCmp extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  onEditorChange = (content) => {
    this.triggerChange(content);
  };

  triggerChange = (changedValue) => {
    const { onChange } = this.props;
    if (onChange) {
      onChange(changedValue);
    }
  };

  render() {
    const { value, disabled, extraProps } = this.props;
    return (
      <Editor
        apiKey={siteConfig.tinyEditorKey}
        onEditorChange={this.onEditorChange}
        value={value}
        init={{
          height: 340,
          plugins: [
            'advlist autolink lists link image charmap print preview anchor',
            'searchreplace visualblocks code fullscreen table',
          ],
          content_style:
            '@import url("https://fonts.googleapis.com/css2?family=Open+Sans&family=Oswald&display=swap&family=Reem+Kufi&display=swap&family=Aref+Ruqaa&family=Scheherazade&family=Tajawal:wght@500&family=Vibes&display=swap&family=Amiri");',
          font_formats: fontsToPreload.join(';'),
          toolbar:
            'undo redo | fontselect || fontsizeselect | formatselect | bold italic forecolor backcolor | \
             alignleft aligncenter alignright alignjustify | \
						 bullist numlist outdent indent | removeformat | \
             table tabledelete | tableprops tablerowprops tablecellprops | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol',
          ...(extraProps && { ...extraProps }),
        }}
        disabled={disabled}
      />
    );
  }
}

EditorCmp.propTypes = {
  disabled: PropTypes.bool,
  extraProps: PropTypes.objectOf(PropTypes.any),
};

EditorCmp.defaultProps = {
  disabled: false,
  extraProps: {},
};

export default EditorCmp;

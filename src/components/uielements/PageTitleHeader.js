import { Typography } from 'antd';
import React from 'react';

import styled from 'styled-components';

const { Text } = Typography;

const TitleDiv = styled.div`
  /* Add your styles here */
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  //   margin-bottom: 36px;
  .p-title {
    font-family: Inter;
    font-size: 20px;
    font-weight: 500;
    line-height: 36px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #1c2a53;
  }
`;

function PageTitleHeader(props) {
  return (
    <TitleDiv>
      <Text className="p-title">{props?.title}</Text>
    </TitleDiv>
  );
}

export default PageTitleHeader;

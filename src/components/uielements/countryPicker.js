/* eslint-disable react/jsx-props-no-spreading */
import React from 'react';
import Select, {
  SelectOption as Option,
} from '@chill/components/uielements/select';
import countriesAry from '../../config/staticData/countries.json';
import OptionWrapper from './styles/countryPicker.style';
import './styles/flags.css';

export default function CountryPicker(props) {
  const {
    dropDownCustom,
    valType,
    allCountries,
    values,
    keyType,
    notShowLabel,
    ...rest
  } = props;
  const key = keyType || valType;

  return (
    <Select
      optionLabelProp="label"
      dropdownMatchSelectWidth={false}
      dropdownClassName="countryDropdown"
      {...rest}
    >
      {countriesAry.map((ct) => (
        <Option
          countrycode={ct.code}
          countryname={ct.name}
          key={ct[key]}
          value={ct[valType]}
          label={
            notShowLabel ? null : (
              <span className={`flag flag-${ct.code.toLowerCase()}`} />
            )
          }
        >
          <OptionWrapper>
            <span className={`flag flag-${ct.code.toLowerCase()}`} />
            <span className="name">{ct.name}</span>
            <span className="code">
              {dropDownCustom ? ct.code : ct.dial_code}
            </span>
          </OptionWrapper>
        </Option>
      ))}
    </Select>
  );
}

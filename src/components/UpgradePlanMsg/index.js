import React from 'react';
import { Modal, Result } from 'antd';
import { Link } from 'react-router-dom';
import IntlMessages from '@chill/components/utility/intlMessages';
import Button from '@chill/components/uielements/button';
import ResultWrapper from './UpgradePlan.Styles';

function UpgradePlanMsg({
  modal = false,
  visible = false,
  onClose = () => {},
  msg = '',
}) {
  function renderUpMsg() {
    return (
      <ResultWrapper>
        <Result
          status="warning"
          title={<IntlMessages id="upgrade.plan" />}
          subTitle={<IntlMessages id={msg || 'upgrade.subTxt'} />}
          extra={
            <Link to="/plans">
              <Button type="primary">
                <IntlMessages id="common.upgrade" />
              </Button>
            </Link>
          }
        />
      </ResultWrapper>
    );
  }

  function renderModalMsg() {
    return (
      <Modal
        visible={visible}
        onClose={onClose}
        title={<IntlMessages id="common.upgrade" />}
        onCancel={onClose}
        destroyOnClose
        footer={null}
      >
        {renderUpMsg()}
      </Modal>
    );
  }

  function renderContent() {
    if (modal) return renderModalMsg();
    return renderUpMsg();
  }

  return renderContent();
}

export default UpgradePlanMsg;

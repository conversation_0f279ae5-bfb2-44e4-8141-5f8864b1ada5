import React, { useState, useEffect } from 'react';
import { isObject, debounce, isArray, isEmpty } from 'lodash';
import { Spin, Select } from 'antd';
import IntlMessages from '@chill/components/utility/intlMessages';
import getApiData from '@chill/lib/helpers/apiHelper';

const { Option } = Select;

function RemoteSelect(props) {
  const {
    onChange = () => {},
    onSelect = () => {},
    onDeselect = () => {},
    placeholder,
    apiData = {},
    initVal,
    fullData = false,
    allowClear = true,
    disabled = false,
    mode,
    vKey = '',
    lKey = '',
    value,
    refSelect,
    renderOption = null,
    addNew = false,
  } = props;

  const [state, setState] = useState({ data: [], fetching: false });
  let lastFetchId = 0;
  const { fetching, data } = state;

  useEffect(() => {
    if (isObject(initVal) && !isEmpty(initVal)) {
      setState((pre) => ({ ...pre, value: initVal }));
    }
  }, [initVal]);

  const fetchUser = debounce(async (val) => {
    lastFetchId += 1;
    const fetchId = lastFetchId;

    // Value of Search Input
    const valString = val ? val.trim() : '';
    const valLen = valString.length;

    // API Related Data
    const apiDataVar = isObject(apiData) ? apiData : {};

    if (valLen > 1 && !isEmpty(apiDataVar)) {
      setState((pre) => ({
        ...pre,
        data: [],
        fetching: true,
      }));

      try {
        const res = await getApiData(apiDataVar.apiUrl, {
          [apiDataVar.apiParam]: valString,
          fullData: fullData || null,
        });
        const arr = res && isArray(res.data) ? res.data : [];
        if (fetchId !== lastFetchId) {
          // for fetch callback order
          return;
        }
        setState((pre) => ({ ...pre, data: arr, fetching: false }));
      } catch (err) {
        setState((pre) => ({ ...pre, data: [], fetching: false }));
      }
    } else {
      setState((pre) => ({
        ...pre,
        data: [],
      }));
    }
  }, 800);

  const valKey = vKey || 'id';
  const labelKey = lKey || 'name';

  function renderLOption(d) {
    return (
      <Option value={d[valKey]} key={d[valKey]} obj={d}>
        {d[labelKey]}
      </Option>
    );
  }

  function renderNew() {
    if (addNew)
      return (
        <Option key="new">
          <p style={{ fontWeight: 550 }}>
            <IntlMessages id="common.addNew" />
          </p>
        </Option>
      );
    return null;
  }

  const renderLabel = renderOption || renderLOption;

  return (
    <Select
      onChange={onChange}
      placeholder={placeholder}
      ref={refSelect}
      notFoundContent={
        fetching ? (
          <div className="flexCenter">
            <Spin size="small" />
          </div>
        ) : null
      }
      filterOption={false}
      onSearch={fetchUser}
      style={{ width: '100%' }}
      allowClear={allowClear}
      showSearch
      disabled={disabled}
      mode={mode}
      onSelect={onSelect}
      onDeselect={onDeselect}
      value={value}
    >
      {data.map(renderLabel)}
      {renderNew()}
    </Select>
  );
}

export default RemoteSelect;

import React from 'react';
import {
  SettingOutlined,
  TeamOutlined,
  GlobalOutlined,
  DashboardOutlined,
  ContainerOutlined,
  TranslationOutlined,
  FolderOutlined,
  SolutionOutlined,
  ApartmentOutlined,
  SafetyOutlined,
  TagsOutlined,
  FileTextOutlined,
  UserOutlined,
  FileSyncOutlined,
  FolderFilled,
  FileTextFilled,
  RollbackOutlined,
  DownloadOutlined,
  FileDoneOutlined,
  GroupOutlined,
  ForkOutlined,
  ReconciliationOutlined,
  DeleteOutlined,
  ShoppingOutlined,
  PlusCircleOutlined,
  CameraFilled,
  MessageOutlined,
  WechatOutlined,
  UserAddOutlined,
  LoadingOutlined,
} from '@ant-design/icons';

function AntIcon({ name = '' }) {
  if (name === 'users') {
    return <TeamOutlined />;
  }

  if (name === 'settings') {
    return <SettingOutlined />;
  }

  if (name === 'globe') {
    return <GlobalOutlined />;
  }
  if (name === 'translation') {
    return <SolutionOutlined />;
  }

  if (name === 'dashboard') {
    return <DashboardOutlined />;
  }

  if (name === 'profile') {
    return <UserOutlined />;
  }

  if (name === 'container') {
    return <ContainerOutlined />;
  }

  if (name === 'language') {
    return <TranslationOutlined />;
  }

  if (name === 'dept') {
    return <ApartmentOutlined />;
  }

  if (name === 'verification') {
    return <SafetyOutlined />;
  }

  if (name === 'file') {
    return <FileTextOutlined />;
  }

  if (name === 'file-filled') {
    return <FileTextFilled />;
  }
  if (name === 'file-sharing') {
    return <FileSyncOutlined />;
  }

  if (name === 'folder') {
    return <FolderOutlined />;
  }
  if (name === 'folder-filled') {
    return <FolderFilled />;
  }
  if (name === 'back-filled') {
    return <RollbackOutlined />;
  }
  if (name === 'tags') {
    return <TagsOutlined />;
  }
  if (name === 'download') {
    return <DownloadOutlined />;
  }

  if (name === 'file-sign') {
    return <FileDoneOutlined />;
  }

  if (name === 'grouped') {
    return <GroupOutlined />;
  }
  if (name === 'workflow') {
    return <ForkOutlined />;
  }
  if (name === 'reports') {
    return <ReconciliationOutlined />;
  }
  if (name === 'trash') return <DeleteOutlined />;

  if (name === 'orders') {
    return <ShoppingOutlined />;
  }

  if (name === 'plus') {
    return <PlusCircleOutlined />;
  }

  if (name === 'camera') {
    return <CameraFilled />;
  }

  if (name === 'messageOutlined') {
    return <MessageOutlined />;
  }

  if (name === 'WechatOutlined') {
    return <WechatOutlined />;
  }
  if (name === 'UserAddOutlined') {
    return <UserAddOutlined />;
  }

  if (name === 'LoadingOutlined') {
    return <LoadingOutlined />;
  }

  return null;
}

export default AntIcon;

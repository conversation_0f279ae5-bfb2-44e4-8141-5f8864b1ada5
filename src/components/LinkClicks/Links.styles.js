import styled from 'styled-components';
import WithDirection from '@chill/lib/helpers/rtl';

const LinkStyle = styled.div`
  .isoView {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
  }
  & .isoCountsTextDiv {
    display: flex;
    flex-direction: column;
    padding-left: 10px;
  }
  & .isoLinkText {
    color: #808080;
  }
  & .leftText {
    display: flex;
    flex-direction: column;
  }
  & .isoCountTextView {
    background-color: #e1f5fe;
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 20px;
    padding-right: 20px;
    border-radius: 10px;
  }
  & .isoCountText {
    font-size: 14px;
    color: #01579b;
    font-weight: 500;
  }
`;

export default WithDirection(LinkStyle);

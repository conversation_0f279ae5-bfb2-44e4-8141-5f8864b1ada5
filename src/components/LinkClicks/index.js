import React from 'react';
import LinkStyle from './Links.styles';

const LinksClick = (props) => {
  const { title, text, count, border } = props;

  return (
    <LinkStyle>
      <div className="isoView">
        <div className="leftText">
          <text>{title}</text>
          <text className="isoLinkText">{text}</text>
        </div>
        <div className="isoCountTextView">
          <text className="isoCountText">{count}</text>
        </div>
      </div>

      {/* BottomBorder */}

      {border ? (
        <div
          style={{
            width: '100%',
            height: 1,
            backgroundColor: '#fafafa',
            marginTop: 8,
          }}
        ></div>
      ) : null}
    </LinkStyle>
  );
};

export default LinksClick;

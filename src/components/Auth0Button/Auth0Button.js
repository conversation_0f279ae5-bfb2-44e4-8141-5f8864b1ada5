import React from 'react';
import Button from '@chill/components/uielements/button';
import IntlMessages from '@chill/components/utility/intlMessages';
import Auth0 from '@chill/containers/Authentication/Auth0/Auth0';

const Auth0Button = () => {
  return (
    <>
      <Button
        type="primary"
        className="btnAuthZero"
        onClick={() => Auth0.login()}
      >
        <IntlMessages id="page.loginAuth0" />
      </Button>
      <Button
        type="primary"
        className="btnAuthZero"
        onClick={() => Auth0.logout()}
      >
        LOGOUT
      </Button>
    </>
  );
};

export default Auth0Button;

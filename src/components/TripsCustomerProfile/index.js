/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import { Row, Col } from 'antd';
import {
  XAxi<PERSON>,
  YAxis,
  CartesianGrid,
  Tooltip,
  LineC<PERSON>,
  Line,
} from 'recharts';
import basicStyle from '@chill/assets/styles/constants';
import IntlMessages from '@chill/components/utility/intlMessages';
import { isEmpty } from 'lodash';
// import getApiData from '@chill/lib/helpers/apiHelper';

import { useSelector } from 'react-redux';

import IsoWidgetsWrapper from '@chill/containers/Widgets/WidgetsWrapper';
import ReportsWidget from '@chill/containers/Widgets/Report/ReportWidget';

const TripsCustomerProfile = (prop) => {
  const { rowStyle } = basicStyle;
  const { tripsPerDayData, totalNumberOfTrips } = prop;
  const [userProfileData, setUserProfileData] = useState({});
  const [pageLoad, setPageLoad] = useState(false); // true in api false =======TODO
  const { language } = useSelector((st) => st.LanguageSwitcher);

  const [dData, setdData] = useState({ data: {}, loader: false });

  const lineData = [
    {
      name: '19 DEC',
      pv: 1,
    },
    {
      name: '20 DEC',
      pv: 1,
    },
    {
      name: '21 DEC',
      pv: 2,
    },
    {
      name: '22 DEC',
      pv: 2,
    },
    {
      name: '23 DEC',
      pv: 3,
    },
    {
      name: '24 DEC',
      pv: 1,
    },
    {
      name: '25 DEC',
      pv: 2,
    },
  ];
  function getWindowDimensions() {
    const { innerWidth: width, innerHeight: height } = window;
    return {
      width,
      height,
    };
  }
  function useWindowDimensions() {
    const [windowDimensions, setWindowDimensions] = useState(
      getWindowDimensions(),
    );

    useEffect(() => {
      function handleResize() {
        setWindowDimensions(getWindowDimensions());
      }

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, []);

    return windowDimensions;
  }
  const { width } = useWindowDimensions();
  const TempBox = [
    {
      key: 'sentMessages',
      text: 'customer.noOfTrips',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: `${totalNumberOfTrips} TRIPS`,
      //   img: lineSVG,
    },
  ];

  function renderTempBoxes() {
    return (
      <Row gutter={10}>
        <Col lg={16} xs={24}>
          {TempBox.map((widget) => {
            return (
              <div
                style={{
                  border: '1px solid rgba(0, 0, 0, 0.05)',
                  backgroundColor: '#fff',
                  borderRadius: 17,
                  borderWidth: 1,
                  borderColor: '#7C94B8',
                  padding: 20,
                  margin: 10,
                }}
              >
                <span style={{ color: '#7C94B8' }}>
                  <IntlMessages id={widget.text} />
                </span>
                <h4 style={{ color: '#637693' }}>{widget.count || 0}</h4>
              </div>
            );
          })}
        </Col>
      </Row>
    );
  }

  const CustomizedLabel = (props) => {
    const { x, y, stroke, value } = props;
    return (
      <text
        x={x}
        y={y}
        dy={value === 0 ? -5 : 14}
        fill={stroke}
        fontSize={12}
        textAnchor="middle"
      >
        {value}
      </text>
    );
  };
  return (
    <>
      <IsoWidgetsWrapper style={{ height: '100%', marginTop: '20px' }}>
        <ReportsWidget
          widgetClassName="mb0"
          labelClsName="mb15"
          style={{
            height: '100%',
            borderRadius: 25,
            boxShadow: '5px 5px 5px 5px #0000',
          }}
        >
          <div
            style={{
              marginLeft: '45px',
              marginBottom: '20px',
              fontSize: '20px',
            }}
          >
            Trips
          </div>
          <Row gutter={24} style={{ padding: 16, margin: 0 }}>
            <Col lg={15} md={12} sm={24} xs={24}>
              <Row style={rowStyle} gutter={0} justify="start">
                <Col style={{ flex: 1 }}>
                  <LineChart
                    width={width * 0.5}
                    height={200}
                    data={tripsPerDayData}
                    style={{ paddingTop: 10 }}
                    // margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    // padding={{ top: 5, right: 0, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="1 5" />
                    <XAxis dataKey="day" fontSize={12} />
                    <YAxis fontSize={12} />
                    <Tooltip />

                    <Line
                      name="Trips"
                      type="linear"
                      dataKey="tripsPerDay"
                      stroke="#79CCDC"
                      label={CustomizedLabel}
                      //   margin={{ top: 50, right: 0, left: 0, bottom: 0 }}
                    />
                  </LineChart>
                </Col>
              </Row>
            </Col>
            <Col lg={9} sm={24} md={11} xs={24}>
              <div style={{ marginLeft: 50 }}>{renderTempBoxes()}</div>
            </Col>
          </Row>
          <Row style={rowStyle} gutter={0} justify="start">
            <div
              style={{
                height: '5px',
                width: '5px',
                backgroundColor: '#79CCDC',
                alignSelf: 'center',
                marginRight: '5px',
                marginLeft: width * 0.1,
              }}
            />
            <div
              style={{
                marginRight: '25px',
              }}
            >
              No of trips per day
            </div>
          </Row>
        </ReportsWidget>
      </IsoWidgetsWrapper>
    </>
  );
};
export default TripsCustomerProfile;

/* eslint-disable dot-notation */
/* eslint-disable array-callback-return */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
import React, { useEffect, useState } from 'react';
import {
  Document,
  Page,
  Text,
  View,
  Image,
  StyleSheet,
} from '@react-pdf/renderer';
import { findIndex, isArray, isEmpty } from 'lodash';
import logoPng from '@chill/assets/images/logo.png';

// Create styles for PDF
const styles = StyleSheet.create({
  tableContainer: { marginTop: 20 },
  tableTitle: {
    fontFamily: 'Helvetica-Bold',
    fontSize: 12,
    marginTop: '5px',
    marginBottom: '5px',
    textOverFlow: 'ellipsis',
  },
  tableContent: {
    fontSize: 12,
    marginBottom: '5px',
    textOverFlow: 'ellipsis',
    marginTop: '5px',
  },
  table: {
    borderTop: 1,
    borderRight: 1,
    borderBottom: 1,
    borderLeft: 1,
    borderColor: '#DDDDDD',
    flex: 1,
    flexDirection: 'column',
  },
  tableRow: {
    flex: 1,
    display: 'flex',
    flexDirection: 'row',
  },
  tableCell: {
    flex: 4,
    textAlign: 'center',
  },
  bgDark: {
    backgroundColor: '#EEEEEE',
  },
  borderTB: {
    borderTop: 1,
    borderBottom: 1,
    borderColor: '#DDDDDD',
  },
  borderLR: {
    borderRight: 1,
    borderLeft: 1,
    borderColor: '#DDDDDD',
  },
  cardContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  cardViewStyle: {
    flex: 1,
    padding: 15,
    backgroundColor: '#f7f7f7',
    borderRadius: 8,
    marginHorizontal: 10,
  },
  cardTitleStyle: { fontSize: 12, marginBottom: 10 },
  cardCountStyle: { fontSize: 10 },
  imgViewStyle: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});

const MarketingReport = (props) => {
  const { initialValues, campaignData, language } = props;
  const [newData, setNewData] = useState([]);
  const messageArray = require(`@chill/config/translation/locales/${language}.json`);

  useEffect(() => {
    if (
      isArray(campaignData.outComeChartData) &&
      campaignData.outComeChartData.length > 0
    ) {
      const arr = [];
      campaignData.outComeChartData.map((item) => {
        const obj = {};
        if (isArray(arr) && arr.length > 0) {
          const nIndex = findIndex(arr, (dd) => dd.month === item.month);
          if (nIndex > -1) {
            if (item.type === 'delivered') {
              arr[nIndex].delivered = item.value;
            } else if (item.type === 'clicked') {
              arr[nIndex].clicked = item.value;
            } else {
              arr[nIndex].failed = item.value;
            }
          } else {
            obj.month = item.month;
            if (item.type === 'delivered') {
              obj.delivered = item.value;
            } else if (item.type === 'clicked') {
              obj.clicked = item.value;
            } else {
              obj.failed = item.value;
            }
          }
        } else {
          obj.month = item.month;
          if (item.type === 'delivered') {
            obj.delivered = item.value;
          } else if (item.type === 'clicked') {
            obj.clicked = item.value;
          } else {
            obj.failed = item.value;
          }
        }
        if (!isEmpty(obj)) {
          arr.push(obj);
        }
      });
      setNewData(arr);
    }
  }, [campaignData]);

  return (
    <Document>
      <Page size="A4">
        <View style={{ flex: 1, padding: 20 }}>
          <View style={styles.imgViewStyle}>
            <Image src={logoPng} alt="Logo" style={{ width: 160 }} />
            <Text style={{ fontSize: 16, marginTop: 10 }}>
              {initialValues.campaign_name || ''}
            </Text>
          </View>
          <View style={styles.tableContainer}>
            <View style={styles.cardContainer}>
              <View style={styles.cardViewStyle}>
                <Text style={styles.cardTitleStyle}>
                  {messageArray['marketing.sentMessages']}
                </Text>
                <Text style={styles.cardCountStyle}>
                  {!isEmpty(campaignData) ? campaignData.getSentMessages : 0}
                </Text>
              </View>
              <View style={styles.cardViewStyle}>
                <Text style={styles.cardTitleStyle}>
                  {messageArray['marketing.totalClicks']}
                </Text>
                <Text style={styles.cardCountStyle}>
                  {!isEmpty(campaignData) ? campaignData.totalClicks : 0}
                </Text>
              </View>
              <View style={styles.cardViewStyle}>
                <Text style={styles.cardTitleStyle}>
                  {messageArray['marketing.totalShares']}
                </Text>
                <Text style={styles.cardCountStyle}>
                  {!isEmpty(campaignData) ? campaignData.totalShare : 0}
                </Text>
              </View>
              <View style={styles.cardViewStyle}>
                <Text style={styles.cardTitleStyle}>
                  {messageArray['marketing.totalSales']}
                </Text>
                <Text style={styles.cardCountStyle}>
                  {!isEmpty(campaignData) ? campaignData?.totalSales || 0 : 0}
                </Text>
              </View>
            </View>
          </View>
          <View style={styles.tableContainer}>
            <Text style={styles.tableTitle}>
              {messageArray['title.outcomeStatistics']}
            </Text>
            <View style={styles.table}>
              <View style={[styles.tableRow, styles.bgDark]}>
                <View style={styles.tableCell}>
                  <Text style={styles.tableTitle}>
                    {messageArray['common.date']}
                  </Text>
                </View>
                <View style={[styles.tableCell, styles.borderLR]}>
                  <Text style={styles.tableTitle}>
                    {messageArray['tab.delivered']}
                  </Text>
                </View>
                <View style={[styles.tableCell, styles.borderLR]}>
                  <Text style={styles.tableTitle}>
                    {messageArray['common.clicked']}
                  </Text>
                </View>
                <View style={[styles.tableCell, styles.borderLR]}>
                  <Text style={styles.tableTitle}>
                    {messageArray['common.failed']}
                  </Text>
                </View>
              </View>
              {campaignData && isArray(newData) && newData.length > 0
                ? newData.map((item) => (
                    <View style={[styles.tableRow, styles.borderTB]}>
                      <View style={styles.tableCell}>
                        <Text style={styles.tableContent}>{item.month}</Text>
                      </View>
                      <View style={[styles.tableCell, styles.borderLR]}>
                        <Text style={styles.tableContent}>
                          {item.delivered}
                        </Text>
                      </View>
                      <View style={[styles.tableCell, styles.borderLR]}>
                        <Text style={styles.tableContent}>{item.clicked}</Text>
                      </View>
                      <View style={[styles.tableCell, styles.borderLR]}>
                        <Text style={styles.tableContent}>{item.failed}</Text>
                      </View>
                    </View>
                  ))
                : null}
            </View>
          </View>
          <View style={styles.tableContainer}>
            <Text style={styles.tableTitle}>
              {messageArray['analytics.operatingSystem']}
            </Text>
            <View style={styles.table}>
              <View style={[styles.tableRow, styles.bgDark]}>
                <View style={styles.tableCell}>
                  <Text style={styles.tableTitle}>
                    {messageArray['th.platforms']}
                  </Text>
                </View>
                <View style={[styles.tableCell, styles.borderLR]}>
                  <Text style={styles.tableTitle}>
                    {messageArray['common.count']}
                  </Text>
                </View>
              </View>
              {campaignData &&
              isArray(campaignData.operatingSystemChart) &&
              campaignData.operatingSystemChart.length > 0
                ? campaignData.operatingSystemChart.map((item, index) => {
                    if (index === 0) {
                      return null;
                    }

                    return (
                      <View
                        key={`system_chart_${item[0]}`}
                        style={[styles.tableRow, styles.borderTB]}
                      >
                        <View style={styles.tableCell}>
                          <Text style={styles.tableContent}>{item[0]}</Text>
                        </View>
                        <View style={[styles.tableCell, styles.borderLR]}>
                          <Text style={styles.tableContent}>{item[1]}</Text>
                        </View>
                      </View>
                    );
                  })
                : null}
            </View>
          </View>

          <View
            style={{
              ...styles.tableContainer,
              backgroundColor: '#eee',
              padding: 16,
              borderRadius: 25,
            }}
          >
            <Text style={styles.tableTitle}>{messageArray['DETAILS']}</Text>
            <View
              style={{
                marginTop: 16,
              }}
            >
              <View>
                <View
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}
                >
                  <View>
                    <Text style={{ fontSize: 13 }}>
                      {messageArray['CAMPAIGNNAME']}
                    </Text>
                    <Text style={{ fontSize: 11, marginTop: 5 }}>
                      {initialValues.campaign_name}
                    </Text>
                  </View>
                  <View>
                    <Text style={{ fontSize: 13 }}>
                      {messageArray['CAMPAIGNTITLE']}
                    </Text>
                    <Text style={{ fontSize: 11, marginTop: 5 }}>
                      {initialValues.post_title}
                    </Text>
                  </View>
                </View>
                <View style={{ marginTop: 16 }}>
                  <Text style={{ fontSize: 13 }}>
                    {messageArray['DESCRIPTION']}
                  </Text>
                  <Text style={{ fontSize: 11, marginTop: 5 }}>
                    {initialValues.description}
                  </Text>
                </View>
                <View
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginTop: 16,
                  }}
                >
                  <View>
                    <Text style={{ fontSize: 13 }}>
                      {messageArray['common.tags']}
                    </Text>
                    <View
                      style={{
                        display: 'flex',
                        flex: 1,
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginRight: 10,
                        marginTop: 5,
                      }}
                    >
                      {/* {initialValues.campaign_tags} */}
                      {!isEmpty(initialValues) &&
                        isArray(initialValues.campaign_tags) &&
                        initialValues.campaign_tags.map((data) => {
                          return (
                            <Text
                              className="topLinkCountWrapper"
                              style={{
                                marginRight: 5,
                                fontSize: 11,
                                backgroundColor: '#d0d9ef',
                                color: '#1172ec',
                                paddingVertical: 5,
                                paddingHorizontal: 5,
                                borderRadius: 7,
                              }}
                            >
                              {data}
                            </Text>
                          );
                        })}
                    </View>
                  </View>
                  <View>
                    <Text style={{ fontSize: 13 }}>
                      {messageArray['CAMPAIGNTYPE']}
                    </Text>
                    <Text style={{ fontSize: 11, marginTop: 5 }}>
                      {initialValues.campaign_type}
                    </Text>
                  </View>
                </View>
                <View
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginTop: 16,
                  }}
                >
                  <View>
                    <Text style={{ fontSize: 13 }}>
                      {messageArray['marketing.SEGMENTGROUP']}
                    </Text>
                    <Text style={{ fontSize: 11, marginTop: 5 }}>
                      {`${initialValues.segment_name} (${
                        initialValues.user_count || 0
                      })`}
                    </Text>
                  </View>
                  <View>
                    <Text style={{ fontSize: 13 }}>
                      {messageArray['antTable.title.startDate']}
                    </Text>
                    <Text style={{ fontSize: 11, marginTop: 5 }}>
                      {initialValues.start_date}
                    </Text>
                  </View>
                  <View>
                    <Text style={{ fontSize: 13 }}>
                      {messageArray['antTable.title.endDate']}
                    </Text>
                    <Text style={{ fontSize: 11, marginTop: 5 }}>
                      {initialValues.end_date}
                    </Text>
                  </View>
                </View>
              </View>
              {/* <View> */}
              {/* {initialValues.campaign_type === 'in_app_message' ? ( */}
              {/* <View style={{ display: 'flex', flex: 1 }}>
                <View style={{ width: '100%' }}>
                  <Text
                    className="title"
                    style={{
                      fontSize: 16,
                      textAlign: 'center',
                      width: '100%',
                      color: '#000',
                      padding: 6,
                    }}
                  >
                    {initialValues.post_title || 'Heading'}
                  </Text>
                </View>
                <Image
                  src={{ uri: initialValues.post_file }}
                  style={{ width: '100%', height: '10%' }}
                />
              </View> */}
              {/* ) : null} */}
              {/* </View> */}
            </View>
          </View>
        </View>
      </Page>
    </Document>
  );
};

export default MarketingReport;

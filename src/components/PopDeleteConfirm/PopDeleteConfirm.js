import React from 'react';
import { DeleteOutlined } from '@ant-design/icons';
import Button from '@chill/components/uielements/button';
import Popconfirms from '@chill/components/Feedback/Popconfirm';
import getApiData from '@chill/lib/helpers/apiHelper';
import Notification from '@chill/components/Notification';
import IntlMessages from '@chill/components/utility/intlMessages';
import { isEmpty, isObject } from 'lodash';
import PopDeleteConfirmWrapper from './PopDeleteConfirm.style';

export default function PopDeleteConfirm(props) {
  const {
    apiUrl,
    apiData,
    fetchData = () => {},
    setState = () => {},
    onConfirm = () => {},
    btnId,
  } = props;

  async function deleteItem() {
    if (isObject(apiData) && !isEmpty(apiData)) {
      setState((preState) => ({
        ...preState,
        loading: true,
      }));
      try {
        const res = await getApiData(apiUrl, apiData, 'POST');
        let msgTitle = 'error';
        if (res.success) {
          msgTitle = 'success';
          fetchData();
        } else {
          setState((preState) => ({
            ...preState,
            loading: false,
          }));
        }

        // Timeout for smooth animation to display notification
        setTimeout(() => {
          Notification(msgTitle, res.message);
        }, 200);
      } catch (err) {
        setState((preState) => ({
          ...preState,
          loading: false,
        }));
        Notification('error');
      }
    } else {
      onConfirm();
    }
  }

  return (
    <PopDeleteConfirmWrapper>
      <Popconfirms
        title={<IntlMessages id="common.sureDelete" />}
        okText={<IntlMessages id="common.deleteCap" />}
        okButtonProps={{ id: 'delete-item' }}
        cancelText={<IntlMessages id="common.no" />}
        onConfirm={deleteItem}
        className="deleteBtn"
        onCancel={null}
      >
        <div>
          <Button
            id={btnId}
            shape="circle"
            danger
            icon={<DeleteOutlined />}
            className="deleteCircle"
          />
        </div>
      </Popconfirms>
    </PopDeleteConfirmWrapper>
  );
}

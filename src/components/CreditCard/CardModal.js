import React, { useState } from 'react';
import { Form, Modal } from 'antd';
import IntlMessages from '@chill/components/utility/intlMessages';
import { useElements, useStripe, CardElement } from '@stripe/react-stripe-js';
import Notification from '@chill/components/Notification';
import getApiData from '@chill/lib/helpers/apiHelper';
import useResetFormOnClose from '@chill/lib/hooks/useResetForm';
import CardSection from './CardSection';
import StripeInfo from '../StripeInfo';

function CardModal({ visible, onClose = () => {}, handleSubmit = () => {} }) {
  const elements = useElements();
  const stripe = useStripe();

  const [form] = Form.useForm();

  const [payLoading, setPayLoading] = useState(false);

  useResetFormOnClose({ form, visible, initVal: {} });

  /**
   * Function to call API for adding card
   */
  async function addCard(token = '') {
    setPayLoading(true);
    try {
      const res = await getApiData(
        'subscription/add-new-card',
        { token },
        'POST',
      );
      if (res.success) handleSubmit(res.card_detail || {});
      setPayLoading(false);
      setTimeout(() => {
        Notification(res.success ? 'success' : 'error', res.message);
      }, 200);
    } catch (err) {
      setPayLoading(false);
      Notification('error');
    }
  }

  /**
   * Function to create stripe token from card data
   */
  async function handleSubmitCard(values) {
    let valid = true;
    if (!stripe || !Element) {
      valid = false;
      Notification('error');
    }

    if (valid) {
      const cElements = elements.getElement(CardElement);

      setPayLoading(true);
      try {
        const { token, error } = await stripe.createToken(cElements, {
          ...values,
        });
        if (token) {
          const tokenId = token.id;
          addCard(tokenId);
        } else {
          setPayLoading(false);
          Notification('error', error.message);
        }
      } catch (err) {
        setPayLoading(false);
        Notification('error', err.message);
      }
    }
  }

  function onOk() {
    form.validateFields().then((values) => {
      handleSubmitCard(values);
    });
  }

  return (
    <Modal
      title={<IntlMessages id="card.addCard" />}
      visible={visible}
      okText={<IntlMessages id="common.submit" />}
      onCancel={payLoading ? null : onClose}
      confirmLoading={payLoading}
      onOk={payLoading ? null : onOk}
      destroyOnClose
    >
      <Form form={form} layout="vertical">
        <CardSection />
      </Form>
      <div className="mt25">
        <StripeInfo />
      </div>
    </Modal>
  );
}

export default CardModal;

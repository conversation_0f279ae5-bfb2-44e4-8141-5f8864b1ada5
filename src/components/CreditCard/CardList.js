import React, {
  useState,
  useEffect,
  useImperative<PERSON><PERSON>le,
  forwardRef,
} from 'react';
import { List, Popconfirm, Tooltip, Tag, Spin } from 'antd';
import Scrollbar from '@chill/components/utility/customScrollBar';
import PopDeleteConfirm from '@chill/components/PopDeleteConfirm/PopDeleteConfirm';
import IntlMessages from '@chill/components/utility/intlMessages';
import Notification from '@chill/components/Notification';
import Button from '@chill/components/uielements/button';
import getApiData from '@chill/lib/helpers/apiHelper';
import { isArray, isEqual } from 'lodash';
import { LinkOutlined, CheckOutlined } from '@ant-design/icons';
import CardListWrapper from './Card.Styles';

/**
 * Component to display cards list
 * @component
 */
function CardList(props, ref) {
  const { type = '' } = props;
  const selectable = type === 'select';

  const [cData, setcData] = useState({
    cards: [],
    loading: false,
  });
  const [sCard, setSCard] = useState({});

  const wHeight = window.innerHeight || 800;

  /**
   * Get user's cards
   */
  async function fetchCards() {
    setcData((pre) => ({ ...pre, loading: true }));
    try {
      const res = await getApiData('user/get-cards');
      if (res.success && res.data && isArray(res.data.data)) {
        setcData((pre) => ({ ...pre, loading: false, cards: res.data.data }));

        if (selectable && res.data.data[0]) {
          setSCard(res.data.data[0]);
        }
      } else {
        setcData((pre) => ({ ...pre, cards: [], loading: false }));
      }
    } catch (err) {
      setcData((pre) => ({ ...pre, loading: false }));
    }
  }

  useEffect(() => {
    fetchCards();
  }, []);

  /** Handle reference methods */
  useImperativeHandle(ref, () => ({
    fetchCards() {
      fetchCards();
    },
    getSelectedCard() {
      if (selectable) {
        return sCard;
      }
      return {};
    },
  }));

  /**
   * Make default card
   */
  async function makeDefault(id = '') {
    setcData((pre) => ({ ...pre, loading: true }));
    try {
      const res = await getApiData(
        'subscription/change-default-card',
        {
          card_id: id,
        },
        'POST',
      );
      if (res.success) {
        Notification('success', res.message);
        fetchCards();
      } else {
        setcData((pre) => ({ ...pre, loading: false }));
        Notification('error', res.message);
      }
    } catch (err) {
      setcData((pre) => ({ ...pre, loading: false }));
      Notification('error');
    }
  }

  function clickCard(card) {
    if (selectable) {
      setSCard(card);
    }
  }

  /**
   * Render card
   */
  function renderCard(card, i) {
    const eMonth = card.exp_month <= 9 ? `0${card.exp_month}` : card.exp_month;
    const defaultCard = i === 0;
    return (
      <List.Item
        className={selectable ? 'selectableList' : 'cardListMeta'}
        onClick={() => clickCard(card)}
        actions={
          selectable
            ? undefined
            : [
                <Popconfirm
                  title={<IntlMessages id="common.sureDefault" />}
                  okText={<IntlMessages id="common.makeDefault" />}
                  cancelText={<IntlMessages id="common.no" />}
                  onConfirm={() => makeDefault(card.id)}
                  className="deleteBtn"
                  onCancel={null}
                  disabled={defaultCard}
                >
                  <div>
                    <Tooltip
                      placement="bottom"
                      title={<IntlMessages id="common.makeDefault" />}
                    >
                      <Button
                        shape="circle"
                        icon={<LinkOutlined />}
                        className="invoiceViewBtn"
                        disabled={defaultCard}
                      />
                    </Tooltip>
                  </div>
                </Popconfirm>,
                <PopDeleteConfirm
                  btnId="delete-confirm-card"
                  apiUrl="subscription/delete-card"
                  apiData={{ card_id: card.id }}
                  fetchData={() => fetchCards()}
                  setState={setcData}
                />,
              ]
        }
      >
        <List.Item.Meta
          title={
            <div>
              <span className="cardNum">XXXX XXXX XXXX {card.last4}</span>
              {defaultCard && (
                <Tag color="processing" style={{ marginLeft: 10 }}>
                  <IntlMessages id="common.default" />
                </Tag>
              )}
            </div>
          }
          description={
            <>
              <p>{card.name}</p>
              <span>{card.brand.toUpperCase()}</span>
              <span style={{ margin: '0px 5px' }}>•</span>
              <span>
                <IntlMessages id="card.expires" />{' '}
                <span>
                  {eMonth}/{card.exp_year}
                </span>
              </span>
            </>
          }
        />
      </List.Item>
    );
  }

  return (
    <CardListWrapper>
      <Spin spinning={cData.loading || false}>
        <Scrollbar
          autoHide={false}
          style={{ minHeight: '100%', height: wHeight / 4 }}
        >
          <List
            className="cardsList"
            dataSource={cData.cards}
            renderItem={renderCard}
          />
        </Scrollbar>
      </Spin>
    </CardListWrapper>
  );
}

export default forwardRef(CardList);

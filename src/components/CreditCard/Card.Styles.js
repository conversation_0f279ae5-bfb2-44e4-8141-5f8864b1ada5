import styled from 'styled-components';
import withDirection from '@chill/lib/helpers/rtl';
import { palette } from 'styled-theme';

const CardListWrapper = withDirection(styled.div`
  & .cardsList {
    &.ant-list-split .ant-list-item {
      border-bottom: 1px solid #e9e9e9;
    }
    & .ant-list-item-meta-title {
      margin-bottom: 2px;

      & .cardNum {
        font-weight: 550;
        font-size: 16px;
        color: ${palette('secondary', 0)};
      }
    }
    & .ant-list-item-meta-description {
      font-weight: 550;
      font-size: 13px;
      color: ${palette('secondary', 2)};

      & > p {
        margin-bottom: 2px;
      }
    }

    & .selectableList {
      cursor: pointer;

      & .ant-list-item-meta {
        align-items: center;
        & .checkCircle {
          margin-left: 5px;
        }
      }
    }
  }
`);

const CardWrapper = withDirection(styled.div`
  & .isoComponentTitle {
    font-size: 16px;
    &:before {
      height: 25px;
    }
  }

  & .inputContainer {
    margin-bottom: 10px;

    & .postalCode {
      font-size: 16px;
      color: #424770;
      letter-spacing: 0.025em;
      font-family: Source Code Pro, monospace;

      &::placeholder {
        color: #aab7c4;
      }
    }

    & .label {
      display: inline-block;
      margin-bottom: 5px;
      color: ${palette('secondary', 5)};
      font-size: 14px;

      &.required {
        &::before {
          display: inline-block;
          margin-right: 4px;
          color: #ff4d4f;
          font-size: 14px;
          font-family: SimSun, sans-serif;
          line-height: 1;
          content: '*';
        }
      }
    }

    & .StripeElement,
    & .ant-form-item-control-input {
      height: 40px;
      padding: 10px 12px;
      width: 100%;
      background-color: white;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      -webkit-transition: box-shadow 150ms ease;
      transition: box-shadow 150ms ease;
    }

    & .ant-form-item {
      margin-bottom: 0px;
      & .ant-form-item-control-input {
        min-height: unset;
      }
    }

    & input {
      border: none;
      padding: 0;
      margin: 0;
      height: 18px;
      display: flex;
      font-family: Source Code Pro, monospace;
      color: #32325d;
      font-size: 16px;

      &::placeholder {
        color: ${palette('secondary', 2)};
      }

      &:focus,
      &:active {
        outline: 0;
        border: none;
        box-shadow: none;
      }
    }

    & .ant-select {
      font-size: 16px;
      color: #32325d;

      & .ant-select-selector {
        padding: 0;
        border: none !important;
        box-shadow: none !important;

        & .ant-select-selection-search {
          width: 100%;
          left: 0;
        }

        & .ant-select-selection-placeholder {
          color: ${palette('secondary', 2)};
          opacity: 1;
        }

        & .ant-select-selection-item {
          padding-right: 28px;
        }
      }
    }

    & .StripeElement--invalid {
      border-color: #fa755a;
    }
  }
`);

export { CardWrapper };
export default CardListWrapper;

import React, { useState } from 'react';
import { CardElement } from '@stripe/react-stripe-js';
import { Row, Col, Spin, Form, Input } from 'antd';
import IntlMessages from '@chill/components/utility/intlMessages';
import { injectIntl } from 'react-intl';
import theme from '@chill/config/theme/default';
import { countryFilter } from '@chill/lib/helpers/utility';
import CountryPicker from '@chill/components/uielements/countryPicker';
import { CardWrapper } from './Card.Styles';

const createOptions = () => {
  return {
    hidePostalCode: true,
    style: {
      base: {
        fontSize: '16px',
        color: theme.palette.secondary[0],
        letterSpacing: '0.025em',
        fontFamily: 'Source Code Pro, monospace',
        '::placeholder': {
          color: theme.palette.secondary[2],
        },
      },
      input: {
        height: 45,
      },
      invalid: {
        color: '#9e2146',
      },
    },
  };
};

function CardSection(props) {
  const { intl } = props;

  const [loading, setLoading] = useState(false);

  return (
    <Spin spinning={loading || false}>
      <CardWrapper>
        <div className="inputContainer">
          <Row gutter={[12, 12]}>
            <Col xs={24}>
              <span className="label required">
                <IntlMessages id="common.cardDetails" />
              </span>
              <CardElement
                onReady={(e) => {
                  e.focus();
                  setLoading(false);
                }}
                options={createOptions()}
              />
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="common.fullName" />}
                name="name"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.name" />,
                  },
                  {
                    max: 50,
                    message: <IntlMessages id="err.max.50char" />,
                  },
                  {
                    whitespace: true,
                    message: <IntlMessages id="err.blankSpace" />,
                  },
                ]}
              >
                <Input
                  placeholder={intl.formatMessage({
                    id: 'common.fullName',
                  })}
                />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="card.add_1" />}
                name="address_line1"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.add_1" />,
                  },
                  {
                    whitespace: true,
                    message: <IntlMessages id="err.blankSpace" />,
                  },
                ]}
              >
                <Input
                  placeholder={intl.formatMessage({
                    id: 'card.add_1',
                  })}
                />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label={<IntlMessages id="card.add_2" />}
                name="address_line2"
                rules={[
                  {
                    whitespace: true,
                    message: <IntlMessages id="err.blankSpace" />,
                  },
                ]}
              >
                <Input
                  placeholder={intl.formatMessage({
                    id: 'card.add_2',
                  })}
                />
              </Form.Item>
            </Col>
            <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="antTable.title.city" />}
                name="address_city"
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.city" />,
                  },
                  {
                    max: 255,
                    message: <IntlMessages id="err.max.255char" />,
                  },
                  {
                    whitespace: true,
                    message: <IntlMessages id="err.blankSpace" />,
                  },
                ]}
              >
                <Input
                  placeholder={intl.formatMessage({
                    id: 'antTable.title.city',
                  })}
                />
              </Form.Item>
            </Col>
            <Col xs={12}>
              <Form.Item
                label={<IntlMessages id="antTable.title.zipCode" />}
                name="address_zip"
                rules={[
                  {
                    max: 10,
                    message: <IntlMessages id="err.min.10char" />,
                  },
                  {
                    whitespace: true,
                    message: <IntlMessages id="err.blankSpace" />,
                  },
                ]}
              >
                <Input
                  placeholder={intl.formatMessage({
                    id: 'antTable.title.zipCode',
                  })}
                />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="address_state"
                label={<IntlMessages id="card.state" />}
                rules={[
                  {
                    max: 255,
                    message: <IntlMessages id="err.max.255char" />,
                  },
                  {
                    whitespace: true,
                    message: <IntlMessages id="err.blankSpace" />,
                  },
                ]}
              >
                <Input
                  placeholder={intl.formatMessage({
                    id: 'card.state',
                  })}
                />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="address_country"
                label={<IntlMessages id="antTable.title.country" />}
                rules={[
                  {
                    required: true,
                    message: <IntlMessages id="req.country" />,
                  },
                ]}
              >
                <CountryPicker
                  allCountries
                  valType="code"
                  filterOption={countryFilter}
                  showSearch
                  dropDownCustom
                  placeholder={intl.formatMessage({
                    id: 'common.selectCountry',
                  })}
                />
              </Form.Item>
            </Col>
          </Row>
        </div>
      </CardWrapper>
    </Spin>
  );
}

export default injectIntl(CardSection);

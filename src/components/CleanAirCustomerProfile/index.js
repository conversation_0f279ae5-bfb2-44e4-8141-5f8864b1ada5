/* eslint-disable no-nested-ternary */
/* eslint-disable no-param-reassign */
/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import { Row, Col, Spin } from 'antd';
import {
  <PERSON>A<PERSON><PERSON>,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  Tooltip,
  LineChart,
  Line,
} from 'recharts';
import basicStyle from '@chill/assets/styles/constants';
import IntlMessages from '@chill/components/utility/intlMessages';
import { isEmpty } from 'lodash';
import getApiData from '@chill/lib/helpers/apiHelper';
import CanopyTempCustomerProfile from '@chill/components/CanopyTempCustomerProfile';
// import { injectIntl } from 'react-intl';
import { useSelector } from 'react-redux';
import IsoWidgetsWrapper from '@chill/containers/Widgets/WidgetsWrapper';
import ReportsWidget from '@chill/containers/Widgets/Report/ReportWidget';
import lineSVG from '@chill/assets/images/LineChart.svg';
import goalSVG from '@chill/assets/images/goal.svg';
import fanSVG from '@chill/assets/images/blackfan.png';
import exchangeSVG from '@chill/assets/images/meter.png';

const CleanAirCustomerProfile = (prop) => {
  const { rowStyle } = basicStyle;
  const {
    analytics,
    stEndDateA,
    period_type,
    cperiodType,
    stEndDate,
    initialValues,
  } = prop;
  const [userProfileData, setUserProfileData] = useState({});
  const [pageLoad, setPageLoad] = useState(false); // true in api false =======TODO
  const { language } = useSelector((st) => st.LanguageSwitcher);

  const [dData, setdData] = useState({ data: {}, loader: false });

  async function getCleanAirData(data = {}) {
    data.period_type = cperiodType;
    data.start_end_date = stEndDate;
    data.user_id = initialValues?.id;
    try {
      const res = await getApiData(
        'user-profile-platform/clean-air-data',
        data,
        'post',
      );

      if (res.success && !isEmpty(res.data)) {
        const datad = {};
        datad.PeakFilterAqs = res.data?.PeakFilterAqs;
        datad.canopyPositionUpdates = res.data?.canopyPositionUpdates;
        datad.canopyPosition = res.data?.canopyPosition;
        datad.airQualityIncrease = res.data?.airQualityIncrease;
        datad.averageDailyTemp = res.data?.averageDailyTemp;
        datad.cleanAirDataArray = res.data?.cleanAirDataArray;
        datad.peakAQS = res.data?.peakAQS;
        datad.highestTemp = res.data?.highestTemp;
        datad.lowestTemp = res.data?.lowestTemp;
        datad.temp_avg = res.data?.temp_avg;
        setdData((pre) => ({ ...pre, datad, loader: false }));
      } else {
        setdData((pre) => ({ ...pre, loader: false }));
      }
    } catch (err) {
      console.log('err ===', err);
      setdData((pre) => ({ ...pre, loader: false }));
    }
  }

  async function getCleanAirDataAnalytics(data = {}) {
    data.period_type = period_type || 'week';
    data.start_end_date = stEndDateA;
    try {
      const res = await getApiData(
        'analytics-page-bugaboo/clean-air-data',
        data,
        'post',
      );

      if (res.success && !isEmpty(res.data)) {
        const datad = {};
        datad.PeakFilterAqs = res.data?.PeakFilterAqs;
        datad.canopyPositionUpdates = res.data?.canopyPositionUpdates;
        datad.canopyPosition = res.data?.canopyPosition;
        datad.airQualityIncrease = res.data?.airQualityIncrease;
        datad.averageDailyTemp = res.data?.averageDailyTemp;
        datad.cleanAirDataArray = res.data?.cleanAirDataArray;
        datad.peakAQS = res.data?.peakAQS;
        datad.highestTemp = res.data?.highestTemp;
        datad.lowestTemp = res.data?.lowestTemp;
        datad.temp_avg = res.data?.temp_avg;
        setdData((pre) => ({ ...pre, datad, loader: false }));
      } else {
        setdData((pre) => ({ ...pre, loader: false }));
      }
    } catch (err) {
      console.log('err ===', err);
      setdData((pre) => ({ ...pre, loader: false }));
    }
  }

  useEffect(() => {
    if (!analytics) {
      setdData((pre) => ({ ...pre, loader: true }));
      getCleanAirData();
    } else {
      getCleanAirDataAnalytics();
    }
  }, [cperiodType, stEndDate, period_type, stEndDateA]);

  const lineData = [
    {
      name: '19 DEC',
      uv: 25,
      pv: 40,
    },
    {
      name: '20 DEC',
      uv: 8,
      pv: 10,
    },
    {
      name: '21 DEC',
      uv: 15,
      pv: 85,
    },
    {
      name: '22 DEC',
      uv: 35,
      pv: 65,
    },
    {
      name: '23 DEC',
      uv: 54,
      pv: 46,
    },
    {
      name: '24 DEC',
      uv: 72,
      pv: 28,
    },
    {
      name: '25 DEC',
      uv: 32,
      pv: 68,
    },
  ];
  function getWindowDimensions() {
    const { innerWidth: width, innerHeight: height } = window;
    return {
      width,
      height,
    };
  }
  function useWindowDimensions() {
    const [windowDimensions, setWindowDimensions] = useState(
      getWindowDimensions(),
    );

    useEffect(() => {
      function handleResize() {
        setWindowDimensions(getWindowDimensions());
      }

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, []);

    return windowDimensions;
  }
  const { width } = useWindowDimensions();
  const cleanAirBox = [
    {
      key: 'highestAqsRecord',
      text: 'customer.peakAQS',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.datad) ? dData.datad?.peakAQS : 0,
      img: lineSVG,
    },
    {
      key: 'PeakFilterAqs',
      text: 'customer.peakFilteredAQS',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.datad) ? dData.datad?.PeakFilterAqs : 0,
      img: goalSVG,
    },
    {
      key: 'canopyPositionUpdates',
      text: 'customer.canopyPositionUpdates',
      link: '/contacts',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.datad) ? dData.datad?.canopyPositionUpdates : 0,
      img: fanSVG,
    },
    {
      key: 'totalSales',
      text: 'customer.airQualityIncrease',
      link: '/manage-tags',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.datad) ? dData.datad?.airQualityIncrease : 0,
      img: exchangeSVG,
    },
  ];
  function renderCleanAirBoxes() {
    return (
      <Row gutter={10}>
        <Col lg={16} xs={24}>
          {cleanAirBox.map((widget, index) => {
            return (
              <div
                style={{
                  border: '1px solid rgba(0, 0, 0, 0.05)',
                  backgroundColor: '#fff',
                  borderRadius: 17,
                  borderWidth: 1,
                  borderColor: '#7C94B8',
                  padding: 20,
                  margin: 10,
                  paddingLeft: 50,
                }}
              >
                <span style={{ color: '#7C94B8' }}>
                  <IntlMessages id={widget.text} />
                </span>
                <h4 style={{ color: '#637693' }}>{widget.count || 0}</h4>
              </div>
            );
          })}
        </Col>
      </Row>
    );
  }
  const aqsData = !isEmpty(dData.datad) ? dData.datad?.cleanAirDataArray : [];
  return (
    <>
      <IsoWidgetsWrapper style={{ height: '100%', marginTop: '20px' }}>
        <Spin spinning={dData?.loader}>
          <ReportsWidget
            widgetClassName="mb0"
            labelClsName="mb15"
            style={{
              height: '100%',
              borderRadius: 25,
              boxShadow: '5px 5px 5px 5px #0000',
            }}
          >
            <div
              style={{
                marginLeft: '45px',
                marginBottom: '20px',
                fontSize: '20px',
              }}
            >
              Clean Air data
            </div>
            <Row gutter={24} style={{ padding: 16, margin: 0 }}>
              <Col lg={15} md={12} sm={24} xs={24}>
                <Row style={rowStyle} gutter={0} justify="start">
                  <Col style={{ flex: 1 }}>
                    <LineChart
                      width={width * 0.5}
                      height={400}
                      data={aqsData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 15 }}
                    >
                      <CartesianGrid strokeDasharray="1 5" />
                      <XAxis
                        dataKey="day"
                        fontSize={12}
                        tickCount={40}
                        // interval={0}
                        angle={
                          cperiodType === 'month' ||
                          cperiodType === 'this_month'
                            ? 45
                            : period_type === 'month' ||
                              period_type === 'this_month'
                            ? 45
                            : 0
                        }
                        dy={
                          cperiodType === 'month' ||
                          cperiodType === 'this_month'
                            ? 10
                            : period_type === 'month' ||
                              period_type === 'this_month'
                            ? 10
                            : 0
                        }
                      />
                      <YAxis fontSize={12} />
                      <Tooltip />
                      {/* <Legend /> */}
                      <Line
                        name="Average AQS"
                        type="linear"
                        dataKey="AQS_Avg"
                        stroke="#79CCDC"
                        dot
                      />
                      <Line
                        name="Adjusted AQS"
                        type="linear"
                        dataKey="Adjusted_AQS_Avg"
                        stroke="#7C94B8"
                        dot
                      />
                    </LineChart>
                  </Col>
                </Row>
              </Col>
              <Col lg={9} sm={24} md={11} xs={24}>
                <div style={{ marginLeft: 50 }}>{renderCleanAirBoxes()}</div>
              </Col>
            </Row>
            <Row style={rowStyle} gutter={0} justify="start">
              <div
                style={{
                  height: '5px',
                  width: '5px',
                  backgroundColor: '#79CCDC',
                  alignSelf: 'center',
                  marginRight: '5px',
                  marginLeft: width * 0.1,
                }}
              />
              <div
                style={{
                  marginRight: '25px',
                }}
              >
                Air quality
              </div>

              <div
                style={{
                  height: '5px',
                  width: '5px',
                  backgroundColor: '#7C94B8',
                  alignSelf: 'center',
                  marginRight: '5px',
                }}
              />
              <div
                style={{
                  marginRight: '25px',
                }}
              >
                Filtered air quality
              </div>
            </Row>
          </ReportsWidget>
        </Spin>
      </IsoWidgetsWrapper>
      {/* page ----4 */}
      <CanopyTempCustomerProfile
        analytics={analytics}
        averageDailyTemp={
          !isEmpty(dData.datad) ? dData.datad?.averageDailyTemp : []
        }
        highestTemp={!isEmpty(dData.datad) ? dData.datad?.highestTemp : 0}
        lowestTemp={!isEmpty(dData.datad) ? dData.datad?.lowestTemp : 0}
        canopyPosition={!isEmpty(dData.datad) ? dData.datad?.canopyPosition : 0}
        temp_avg={!isEmpty(dData.datad) ? dData.datad?.temp_avg : 0}
      />
    </>
  );
};
export default CleanAirCustomerProfile;

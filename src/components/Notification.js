import { notification } from 'antd';
import trans from '@chill/config/translation/translate';

const errMsg = 'err.wenWrong';
const successMsg = 'common.successNoti';

const Notification = (type, message = '', description = '', timeout = 3.5) => {
  let msg = message || '';
  if (type === 'error' && !msg) msg = errMsg;
  if (type === 'success' && !msg) msg = successMsg;

  const transMsg = trans(msg);

  notification[type]({
    message: transMsg,
    description,
    duration: timeout,
    style: {
      zIndex: 9999, // Set a high z-index value
    },
  });
};
export default Notification;

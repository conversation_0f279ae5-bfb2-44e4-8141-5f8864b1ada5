/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import { Row, Col } from 'antd';
import {
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  LineChart,
  Line,
} from 'recharts';
import basicStyle from '@chill/assets/styles/constants';
import IntlMessages from '@chill/components/utility/intlMessages';
import { isEmpty } from 'lodash';
// import getApiData from '@chill/lib/helpers/apiHelper';

import { useLocation } from 'react-router-dom';

import IsoWidgetsWrapper from '@chill/containers/Widgets/WidgetsWrapper';
import ReportsWidget from '@chill/containers/Widgets/Report/ReportWidget';
import openPng from '@chill/assets/images/open.png';
import halfOpenPng from '@chill/assets/images/halfopen.png';
import closePng from '@chill/assets/images/close.png';
import raincoverPng from '@chill/assets/images/raincover.png';

const CanopyTempCustomerProfile = (prop) => {
  const { rowStyle } = basicStyle;
  const {
    analytics,
    averageDailyTemp,
    highestTemp,
    lowestTemp,
    canopyPosition,
    temp_avg,
  } = prop;
  const [userProfileData, setUserProfileData] = useState({});
  const [pageLoad, setPageLoad] = useState(false); // true in api false =======TODO

  const [dData, setdData] = useState({ data: {}, loader: false });

  const lineData = [
    {
      name: '19 DEC',
      uv: 25,
      pv: 40,
    },
    {
      name: '20 DEC',
      uv: 8,
      pv: 10,
    },
    {
      name: '21 DEC',
      uv: 15,
      pv: 85,
    },
    {
      name: '22 DEC',
      uv: 35,
      pv: 65,
    },
    {
      name: '23 DEC',
      uv: 54,
      pv: 46,
    },
    {
      name: '24 DEC',
      uv: 72,
      pv: 28,
    },
    {
      name: '25 DEC',
      uv: 32,
      pv: 68,
    },
  ];
  function getWindowDimensions() {
    const { innerWidth: width, innerHeight: height } = window;
    return {
      width,
      height,
    };
  }
  function useWindowDimensions() {
    const [windowDimensions, setWindowDimensions] = useState(
      getWindowDimensions(),
    );

    useEffect(() => {
      function handleResize() {
        setWindowDimensions(getWindowDimensions());
      }

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, []);

    return windowDimensions;
  }
  const { width } = useWindowDimensions();
  const TempBox = [
    {
      key: 'highestTemp',
      text: 'customer.highestTemp',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: `${highestTemp} °C`,
      //   img: lineSVG,
    },
    {
      key: 'lowestTemp',
      text: 'customer.lowestTemp',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: `${lowestTemp} °C`,
      //   img: goalSVG,
    },
    {
      key: 'avgTemp',
      text: 'customer.avgTemp',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: `${temp_avg} °C`,
      //   img: goalSVG,
    },
  ];
  const canopyBox = [
    {
      key: 'sentMessages',
      text: 'customer.open',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(canopyPosition)
        ? `${Math.round(canopyPosition?.open)}%`
        : '0',
      img: openPng,
    },
    {
      key: 'totalClicks',
      text: 'customer.halfOpen',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(canopyPosition)
        ? `${Math.round(canopyPosition?.halfOpen)}%`
        : '0',
      img: halfOpenPng,
    },
    {
      key: 'sentMessages',
      text: 'customer.closed',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(canopyPosition)
        ? `${Math.round(canopyPosition?.closed)}%`
        : '0',
      img: closePng,
    },
    {
      key: 'totalClicks',
      text: 'customer.raincover',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(canopyPosition)
        ? `${Math.round(canopyPosition?.raincover)}%`
        : '0',
      img: raincoverPng,
      rain: true,
    },
  ];
  function renderCanopyBoxes() {
    return (
      <Row gutter={10}>
        <Col lg={3} xs={24}>
          <div
            style={{
              alignSelf: 'center',
              justifyContent: 'center',
              marginLeft: 40,
              marginTop: 50,
            }}
          >
            <IntlMessages id="customer.canopyPosition" />
          </div>
        </Col>
        {canopyBox.map((widget) => {
          return (
            <Col lg={5} xs={24}>
              <div
                style={{
                  backgroundColor: '#fff',
                  borderRadius: 17,
                  padding: 20,
                  margin: 10,
                  marginTop: 35,
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}
              >
                <div>
                  <span style={{ color: '#666666' }}>
                    <IntlMessages id={widget.text} />
                  </span>
                  <h3 style={{ color: '#666666' }}>{widget.count || 0}</h3>
                </div>
                <div className="isoIconWrapper">
                  <img
                    src={widget.img}
                    alt="chat"
                    style={{
                      width: widget.rain ? 45 : 50,
                      height: widget.rain ? 35 : 50,
                    }}
                  />
                </div>
              </div>
            </Col>
          );
        })}
      </Row>
    );
  }
  function renderTempBoxes() {
    return (
      <Row gutter={10}>
        <Col lg={16} xs={24}>
          {TempBox.map((widget) => {
            return (
              <div
                style={{
                  border: '1px solid rgba(0, 0, 0, 0.05)',
                  backgroundColor: '#fff',
                  borderRadius: 17,
                  borderWidth: 1,
                  borderColor: '#7C94B8',
                  padding: 20,
                  margin: 10,
                }}
              >
                <span style={{ color: '#7C94B8' }}>
                  <IntlMessages id={widget.text} />
                </span>
                <h4 style={{ color: '#637693' }}>{widget.count || 0}</h4>
              </div>
            );
          })}
        </Col>
      </Row>
    );
  }

  return (
    <>
      {renderCanopyBoxes()}
      {analytics && <div style={{ marginBottom: 30 }} />}
      {!analytics && (
        <IsoWidgetsWrapper style={{ height: '100%', marginTop: '20px' }}>
          <ReportsWidget
            widgetClassName="mb0"
            labelClsName="mb15"
            style={{
              height: '100%',
              borderRadius: 25,
              boxShadow: '5px 5px 5px 5px #0000',
            }}
          >
            <div
              style={{
                marginLeft: '45px',
                marginBottom: '20px',
                fontSize: '20px',
              }}
            >
              Temperature data
            </div>
            <Row gutter={24} style={{ padding: 16, margin: 0 }}>
              <Col lg={15} md={12} sm={24} xs={24}>
                <Row style={rowStyle} gutter={0} justify="start">
                  <Col style={{ flex: 1 }}>
                    <LineChart
                      width={width * 0.5}
                      height={300}
                      data={averageDailyTemp}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="1 5" />
                      <XAxis dataKey="day" fontSize={12} />
                      <YAxis fontSize={12} />
                      <Tooltip />
                      {/* <Legend /> */}
                      <Line
                        name="Average Temp"
                        type="linear"
                        dataKey="temp_avg"
                        stroke="#79CCDC"
                      />
                    </LineChart>
                  </Col>
                </Row>
              </Col>
              <Col lg={9} sm={24} md={11} xs={24}>
                <div style={{ marginLeft: 50 }}>{renderTempBoxes()}</div>
              </Col>
            </Row>
            <Row style={rowStyle} gutter={0} justify="start">
              <div
                style={{
                  height: '5px',
                  width: '5px',
                  backgroundColor: '#79CCDC',
                  alignSelf: 'center',
                  marginRight: '5px',
                  marginLeft: width * 0.1,
                }}
              />
              <div
                style={{
                  marginRight: '25px',
                }}
              >
                Average Temp
              </div>
            </Row>
          </ReportsWidget>
        </IsoWidgetsWrapper>
      )}
    </>
  );
};
export default CanopyTempCustomerProfile;

import React, { useState, useEffect } from 'react';
import { Row, Col, Form, Input, Modal } from 'antd';
import { isObject, isEmpty } from 'lodash';
import { FormWrapper } from '@chill/assets/styles/drawerFormStyles';
import Notification from '@chill/components/Notification';
import getApiData from '@chill/lib/helpers/apiHelper';
import IntlMessages from '@chill/components/utility/intlMessages';
import useResetFormOnClose from '@chill/lib/hooks/useResetForm';

export default function NotesForm(props) {
  const {
    visible,
    onClose,
    handleSubmit,
    initVal = {},
    type,
    id,
    apiUrl,
  } = props;

  const [form] = Form.useForm();
  const [submiting, setsubmiting] = useState(false);

  const edit = isObject(initVal) && !isEmpty(initVal);

  useEffect(() => {
    if (visible && edit) {
      setTimeout(() => {
        form.setFieldsValue(initVal);
      }, 200);
    }
  }, [visible]);

  useResetFormOnClose({ form, visible });

  async function submitJob(values) {
    setsubmiting(true);
    let url = edit ? `notes/update/${initVal.id}` : 'notes/create';

    if (apiUrl) url = apiUrl;

    const method = edit ? 'PUT' : 'POST';
    try {
      const apiData = {
        notes: values.notes,
        id,
        type,
      };
      const res = await getApiData(url, apiData, method);
      setsubmiting(false);
      if (res.success && res.data) {
        form.resetFields();
        Notification('success', res.message);
        handleSubmit(res.data, edit);
      } else {
        Notification('error', res.message);
      }
    } catch (err) {
      Notification('error');
      setsubmiting(false);
    }
  }

  function onOk() {
    form
      .validateFields()
      .then((values) => {
        submitJob(values);
      })
      .catch((info) => {
        console.log('Validate Failed:', info);
      });
  }

  return (
    <div>
      <FormWrapper>
        <Modal
          title={
            <IntlMessages id={edit ? 'common.editNotes' : 'common.addNotes'} />
          }
          visible={visible}
          okText={<IntlMessages id="common.submit" />}
          onCancel={submiting ? null : onClose}
          confirmLoading={submiting}
          onOk={submiting ? null : onOk}
        >
          <Form form={form} initialValues={initVal} layout="vertical">
            <Row gutter={6}>
              <Col span={24}>
                <Form.Item
                  name="notes"
                  label={<IntlMessages id="common.notes" />}
                  rules={[
                    {
                      required: true,
                      message: <IntlMessages id="req.notes" />,
                    },
                    {
                      max: 255,
                      message: <IntlMessages id="err.min.255char" />,
                    },
                    {
                      whitespace: true,
                      message: <IntlMessages id="err.blankSpace" />,
                    },
                  ]}
                >
                  <Input.TextArea rows={5} />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Modal>
      </FormWrapper>
    </div>
  );
}

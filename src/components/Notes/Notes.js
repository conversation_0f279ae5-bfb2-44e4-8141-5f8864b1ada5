/* eslint-disable react/no-array-index-key */
import React, { useState } from 'react';
import { Avatar, Spin, List } from 'antd';
import { FileTextFilled, EditOutlined } from '@ant-design/icons';
import moment from 'moment';
import { isArray } from 'lodash';
import Button from '@chill/components/uielements/button';
import IntlMessages from '@chill/components/utility/intlMessages';
import TimelineList from '@chill/components/TimelineList/TimelineList';
import PopDeleteConfirm from '@chill/components/PopDeleteConfirm/PopDeleteConfirm';

import getApiData from '@chill/lib/helpers/apiHelper';

import { Box } from '@chill/assets/styles/drawerFormStyles';
import Notification from '@chill/components/Notification';
import Scrollbars from '@chill/components/utility/customScrollBar';
import NotesForm from './NotesForm';

import NotesWrapper from './Notes.style';

export default function Notes({ nData, setUser, type }) {
  const [state, setState] = useState({
    visible: false,
    initObj: {},
  });
  const [userLoader, setUserLoader] = useState(false);
  const notes = isArray(nData.notes) ? [...nData.notes] : [];

  const { visible, initObj } = state;

  async function deleteNote(id) {
    setUserLoader(true);
    try {
      const res = await getApiData('notes/delete', { id }, 'POST');
      if (res.success) {
        const sId = isArray(id) && id[0] ? id[0] : '';
        const fIndex = notes.findIndex((note) => note.id === sId);
        if (fIndex > -1) {
          notes.splice(fIndex, 1);
          setUser(notes);
        }
        if (res.message) {
          Notification('success', res.message);
        }
      } else {
        Notification('error', res.message);
      }
      setUserLoader(false);
    } catch (err) {
      setUserLoader(false);
      Notification('error', 'Something went wrong. Try later');
    }
  }

  function submitNotes(data = {}, edit) {
    setState((pre) => ({ ...pre, visible: false }));
    if (edit) {
      const fIndex = notes.findIndex((note) => note.id === data.id);
      if (fIndex > -1) notes[fIndex] = data;
    } else {
      notes.unshift(data);
    }
    setUser(notes);
  }

  function renderList(item) {
    const title =
      item.notes &&
      item.notes.split('\n').map((i, index) => {
        return <p key={index}>{i}</p>;
      });
    return (
      <List.Item
        key={item.id}
        actions={[
          <Button
            shape="circle"
            icon={<EditOutlined />}
            onClick={() =>
              setState((pre) => ({
                ...pre,
                initObj: item,
                visible: true,
              }))
            }
            className="invoiceViewBtn"
          />,
          <PopDeleteConfirm onConfirm={() => deleteNote([item.id])} />,
        ]}
      >
        <List.Item.Meta
          title={title || '-'}
          key={item.id}
          description={
            <div>
              {moment(item.createdAt).format('lll')}
              {/* {item.fullname || 'admin user'} ·{' '}
              <small>{moment(item.createdAt).format('lll')}</small> */}
            </div>
          }
        />
      </List.Item>
    );
  }

  return (
    <NotesWrapper>
      <Box
        title={<IntlMessages id="common.notes" />}
        rightBtns={
          <Button
            onClick={() =>
              setState((pre) => ({
                ...pre,
                initObj: {},
                visible: true,
              }))
            }
            type="primary"
            className="mateAdd InvoiceBtn"
          >
            <IntlMessages id="common.addNotes" />
          </Button>
        }
        className="cardBox"
      >
        <div className="cardWrapper">
          <Spin spinning={userLoader}>
            <Scrollbars style={{ minHeight: 460 }}>
              <TimelineList
                data={notes}
                dot={<Avatar icon={<FileTextFilled />} />}
                renderItem={renderList}
              />
            </Scrollbars>
          </Spin>
        </div>
      </Box>
      <NotesForm
        type={type}
        id={nData.id}
        initVal={initObj}
        handleSubmit={submitNotes}
        visible={visible}
        onClose={() =>
          setState((pre) => ({ ...pre, initObj: {}, visible: false }))
        }
      />
    </NotesWrapper>
  );
}

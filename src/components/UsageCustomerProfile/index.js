/* eslint-disable no-nested-ternary */
/* eslint-disable no-param-reassign */
/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import { Row, Col } from 'antd';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Label,
} from 'recharts';
import basicStyle from '@chill/assets/styles/constants';
import IntlMessages from '@chill/components/utility/intlMessages';
import { isEmpty } from 'lodash';
import getApiData from '@chill/lib/helpers/apiHelper';
import qs from 'query-string';
import { useLocation } from 'react-router-dom';

import { useSelector } from 'react-redux';

import IsoWidgetsWrapper from '@chill/containers/Widgets/WidgetsWrapper';
import ReportsWidget from '@chill/containers/Widgets/Report/ReportWidget';

import lineSVG from '@chill/assets/images/LineChart.svg';
import goalSVG from '@chill/assets/images/goal.svg';
import fanSVG from '@chill/assets/images/blackfan.png';
import exchangeSVG from '@chill/assets/images/meter.png';
import openPng from '@chill/assets/images/open.png';
import halfOpenPng from '@chill/assets/images/halfopen.png';
import closePng from '@chill/assets/images/close.png';
import raincoverPng from '@chill/assets/images/raincover.png';
import clock1 from '@chill/assets/images/clock1.png';
import clock2 from '@chill/assets/images/clock2.png';
import clock3 from '@chill/assets/images/clock3.png';
import clock4 from '@chill/assets/images/clock4.png';

const UsageCustomerProfile = (props) => {
  const { rowStyle } = basicStyle;
  const {
    analytics,
    stEndDateA,
    period_type,
    cperiodType,
    stEndDate,
    initialValues,
    totalUseTime,
    totalFiltering,
  } = props;

  const [userProfileData, setUserProfileData] = useState({});
  const [pageLoad, setPageLoad] = useState(false); // true in api false =======TODO
  const { language } = useSelector((st) => st.LanguageSwitcher);

  const [dData, setdData] = useState({ data: {}, loader: false });

  const dataAreaUsage = [
    { name: '19 DEC', x: 30, y: 70, z: 40, a: 50, t: 70 },
    { name: '20 DEC', x: 12, y: 88, z: 45, a: 60, t: 88 },
    { name: '21 DEC', x: 15, y: 85, z: 55, a: 70, t: 85 },
    { name: '22 DEC', x: 35, y: 65, z: 70, a: 50, t: 65 },
    { name: '23 DEC', x: 54, y: 46, z: 60, a: 40, t: 46 },
    { name: '24 DEC', x: 72, y: 28, z: 50, a: 30, t: 28 },
    { name: '25 DEC', x: 32, y: 68, z: 20, a: 10, t: 68 },
  ];

  const chartData = [
    { name: '19 DEC', x: 946702800000 },
    { name: '20 DEC', x: 946703800000 },
    { name: '21 DEC', x: 946704800000 },
    { name: '22 DEC', x: 946705800000 },
    { name: '23 DEC', x: 946706800000 },
  ];

  function getWindowDimensions() {
    const { innerWidth: width, innerHeight: height } = window;
    return {
      width,
      height,
    };
  }
  function useWindowDimensions() {
    const [windowDimensions, setWindowDimensions] = useState(
      getWindowDimensions(),
    );

    useEffect(() => {
      function handleResize() {
        setWindowDimensions(getWindowDimensions());
      }

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, []);

    return windowDimensions;
  }
  const { width } = useWindowDimensions();
  const timeBox = [
    {
      key: 'sentMessages',
      text: '6am-10am',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.datad)
        ? `${dData.datad?.tripTime?.time1.toFixed(2)}%`
        : '-',
      img: clock1,
      is1: true,
    },
    {
      key: 'totalClicks',
      text: '10am-2pm',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.datad)
        ? `${dData.datad?.tripTime?.time2.toFixed(2)}%`
        : '-',
      img: clock2,
      is2: true,
    },
    {
      key: 'sentMessages',
      text: '2pm-6pm',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.datad)
        ? `${dData.datad?.tripTime?.time3.toFixed(2)}%`
        : '-',
      img: clock3,
      is3: true,
    },
    {
      key: 'totalClicks',
      text: '6pm-10pm',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.datad)
        ? `${dData.datad?.tripTime?.time4.toFixed(2)}%`
        : '-',
      img: clock4,
      is4: true,
    },
  ];

  async function useageGraphData(data = {}) {
    data.period_type = cperiodType;
    data.start_end_date = stEndDate;
    data.user_id = initialValues?.id;
    try {
      const res = await getApiData(
        'user-profile-platform/useage-graph-data',
        data,
        'post',
      );

      if (res.success && !isEmpty(res.data)) {
        const datad = {};
        // datad.appOpenTime = res.data?.appOpenTime;
        // datad.totalUse = res.data?.totalUse;
        // datad.totalFiltering = res.data?.totalFiltering;
        datad.totalUse = res.data?.totalUse;
        datad.openTime = res.data?.openTime;
        datad.boxTripsLength = res.data?.boxTripsLength;
        const avgLengthOfTrips = res.data?.aveLengthOfTrip;
        const appOpenData = res.data?.appOpenTime;
        const totalFilteringArr = res.data?.totalFiltering;
        const totalUse = res.data?.totalUse;
        const arr3 = appOpenData.map((item, i) => ({
          ...item,
          ...totalUse[i],
          ...totalFilteringArr[i],
          ...avgLengthOfTrips[i],
        }));
        datad.mainDataArr = arr3;
        setdData((pre) => ({ ...pre, datad, loader: false }));
      } else {
        setdData((pre) => ({ ...pre, loader: false }));
      }
    } catch (err) {
      console.log('err ===', err);
      setdData((pre) => ({ ...pre, loader: false }));
    }
  }

  async function useageGraphDataAnalytics(data = {}) {
    data.period_type = period_type || 'week';
    data.start_end_date = stEndDateA;
    try {
      const res = await getApiData(
        'analytics-page-bugaboo/useage-graph-data',
        data,
        'post',
      );

      if (res.success && !isEmpty(res.data)) {
        const datad = {};
        // datad.appOpenTime = res.data?.appOpenTime;
        // datad.totalUse = res.data?.totalUse;
        // datad.totalFiltering = res.data?.totalFiltering;
        datad.totalUse = res.data?.totalUse;
        datad.openTime = res.data?.openTime;
        datad.boxTripsLength = res.data?.boxTripsLength;
        datad.tripTime = res.data?.tripTime;
        const avgLengthOfTrips = res.data?.aveLengthOfTrip;
        const appOpenData = res.data?.appOpenTime;
        const totalFilteringArr = res.data?.totalFiltering;
        const totalUse = res.data?.totalUse;
        const arr3 = appOpenData.map((item, i) => ({
          ...item,
          ...totalUse[i],
          ...totalFilteringArr[i],
          ...avgLengthOfTrips[i],
        }));
        console.log('Dddddd---', arr3);
        datad.mainDataArr = arr3;
        setdData((pre) => ({ ...pre, datad, loader: false }));
      } else {
        setdData((pre) => ({ ...pre, loader: false }));
      }
    } catch (err) {
      console.log('err ===', err);
      setdData((pre) => ({ ...pre, loader: false }));
    }
  }
  useEffect(() => {
    setdData((pre) => ({ ...pre, loader: true }));
    if (!analytics) {
      useageGraphData();
    } else {
      console.log('ssssss---', period_type);
      useageGraphDataAnalytics();
    }
  }, [cperiodType, stEndDate, period_type, stEndDateA]);
  const appOpenData = !isEmpty(dData.datad) ? dData?.datad?.mainDataArr : [];

  function renderTripTimeBoxes() {
    return (
      <Row gutter={10}>
        <Col lg={3} xs={24}>
          <div
            style={{
              alignSelf: 'center',
              justifyContent: 'center',
              marginLeft: 40,
              marginTop: 50,
            }}
          >
            <IntlMessages id="customer.tripTimeOfDay" />
          </div>
        </Col>
        {timeBox.map((widget) => {
          return (
            <Col lg={5} xs={24}>
              <div
                style={{
                  border: '1px solid rgba(0, 0, 0, 0.1)',
                  borderWidth: 1,
                  backgroundColor: '#fff',
                  borderRadius: 17,
                  padding: 20,
                  margin: 10,
                  marginTop: 35,
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}
              >
                <div>
                  <span style={{ color: '#666666' }}>
                    <IntlMessages id={widget.text} />
                  </span>
                  <h3 style={{ color: '#666666' }}>{widget.count || 0}</h3>
                </div>
                <div className="isoIconWrapper">
                  <img
                    src={widget.img}
                    alt="chat"
                    style={{
                      width: widget.is2
                        ? 75
                        : widget.is1
                        ? 85
                        : widget.is4
                        ? 70
                        : 100, // widget.rain ? 45 : 50,
                      height: widget.is2 ? 80 : widget.is4 ? 70 : 60,
                      marginTop: widget.is2
                        ? -20
                        : widget.is1
                        ? -2
                        : widget.is4
                        ? -10
                        : widget.is3
                        ? -2
                        : null,
                    }}
                  />
                </div>
              </div>
            </Col>
          );
        })}
      </Row>
    );
  }
  const usageBoxes = [
    {
      key: 'totalFiltering',
      text: 'customer.totalFiltering',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: totalFiltering,
      img: lineSVG,
    },
    {
      key: 'totalClicks',
      text: 'customer.avLengthOfTrips',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.datad) ? dData.datad?.boxTripsLength : '-',
      img: goalSVG,
    },
    {
      key: 'totalShares',
      text: 'customer.appOpenTime',
      link: '/contacts',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: !isEmpty(dData.datad) ? dData.datad?.openTime : 0,
      img: fanSVG,
    },
    {
      key: 'totalUseTime',
      text: 'customer.totalUseTime',
      link: '/manage-tags',
      fontColor: '#000000',
      bgColor: '#ffffffff',
      count: totalUseTime,
      img: exchangeSVG,
    },
  ];
  function renderUsageBoxes() {
    return (
      <Row gutter={10}>
        <Col lg={16} xs={24}>
          {usageBoxes.map((widget) => {
            return (
              <div
                style={{
                  border: '1px solid rgba(0, 0, 0, 0.05)',
                  backgroundColor: '#fff',
                  borderRadius: 17,
                  borderWidth: 1,
                  borderColor: '#7C94B8',
                  padding: 20,
                  margin: 10,
                  paddingLeft: 50,
                }}
              >
                <span style={{ color: '#7C94B8' }}>
                  <IntlMessages id={widget.text} />
                </span>
                <h4 style={{ color: '#637693' }}>{widget.count || 0}</h4>
              </div>
            );
          })}
        </Col>
      </Row>
    );
  }
  return (
    <IsoWidgetsWrapper style={{ height: '100%', marginTop: '20px' }}>
      <ReportsWidget
        widgetClassName="mb0"
        labelClsName="mb15"
        style={{
          height: '100%',
          borderRadius: 25,
          boxShadow: '5px 5px 5px 5px #0000',
        }}
      >
        <div
          style={{
            marginLeft: '45px',
            marginBottom: '20px',
            fontSize: '20px',
          }}
        >
          <IntlMessages id="customer.usage" />
        </div>
        <Row gutter={24} style={{ padding: 16, margin: 0 }}>
          <Col lg={15} md={12} sm={24} xs={24}>
            <Row style={rowStyle} gutter={0} justify="start">
              <Col style={{ flex: 1 }}>
                <AreaChart width={width * 0.5} height={400} data={appOpenData}>
                  <CartesianGrid strokeDasharray="1 5" />
                  <XAxis dataKey="day" fontSize={12} />
                  <YAxis>
                    {/* // dataKey="appOpenTime" fontSize={12} */}
                    {/* // tickFormatter={(x) => moment(x).format('H:mm')} */}
                    <Label
                      value="Minutes"
                      angle={-90}
                      position="insideBottomLeft"
                    />
                  </YAxis>
                  <Tooltip />
                  {/* <Legend /> */}
                  <Area
                    name="Total use"
                    dataKey="use"
                    stackId="1"
                    stroke="#7C94B8"
                    fill="#7C94B8"
                  />
                  <Area
                    name="App open time"
                    dataKey="appOpenTime"
                    stackId="2"
                    stroke="#578CB8"
                    fill="#578CB8"
                  />
                  <Area
                    name="Ave length of trips"
                    dataKey="avgLengthOfTrip"
                    stackId="3"
                    stroke="#5DA6C9"
                    fill="#5DA6C9"
                  />
                  <Area
                    name="Total Filtering"
                    dataKey="totalFiltering"
                    stackId="4"
                    stroke="#79CCDC"
                    fill="#79CCDC"
                  />
                </AreaChart>
              </Col>
            </Row>
          </Col>
          <Col lg={9} sm={24} md={11} xs={24}>
            <div style={{ marginLeft: 50 }}>{renderUsageBoxes()}</div>
          </Col>
        </Row>
        <Row style={rowStyle} gutter={0} justify="start">
          <div
            style={{
              height: '5px',
              width: '5px',
              backgroundColor: '#79CCDC',
              alignSelf: 'center',
              marginRight: '5px',
              marginLeft: width * 0.1,
            }}
          />
          <div
            style={{
              marginRight: '25px',
            }}
          >
            <IntlMessages id="customer.totalFiltering" />
          </div>
          <div
            style={{
              height: '5px',
              width: '5px',
              backgroundColor: '#5DA6C9',
              alignSelf: 'center',
              marginRight: '5px',
            }}
          />
          <div
            style={{
              marginRight: '25px',
            }}
          >
            <IntlMessages id="customer.avLengthOfTrips" />
          </div>
          <div
            style={{
              height: '5px',
              width: '5px',
              backgroundColor: '#578CB8',
              alignSelf: 'center',
              marginRight: '5px',
            }}
          />
          <div
            style={{
              marginRight: '25px',
            }}
          >
            <IntlMessages id="customer.appOpenTime" />
          </div>
          <div
            style={{
              height: '5px',
              width: '5px',
              backgroundColor: '#7C94B8',
              alignSelf: 'center',
              marginRight: '5px',
            }}
          />
          <div
            style={{
              marginRight: '25px',
            }}
          >
            <IntlMessages id="customer.totalUseTime" />
          </div>
        </Row>
      </ReportsWidget>
      {analytics && renderTripTimeBoxes()}
    </IsoWidgetsWrapper>
  );
};
export default UsageCustomerProfile;

import React from 'react';
import { List, Timeline } from 'antd';
import Scrollbars from '@chill/components/utility/customScrollBar';
import TimelineWrapper from './TimelineList.style';

export default function TimelineList(props) {
  const { data, dot, renderItem = () => {} } = props;
  return (
    <Scrollbars autoHeight autoHeightMax={600}>
      <TimelineWrapper>
        <Timeline>
          <List
            dataSource={data}
            renderItem={(item) => (
              <Timeline.Item key={item.id} dot={dot}>
                <ul key={item.id}>{renderItem(item)}</ul>
              </Timeline.Item>
            )}
          />
        </Timeline>
      </TimelineWrapper>
    </Scrollbars>
  );
}

import styled from 'styled-components';
// import { palette } from 'styled-theme';
import WithDirection from '@chill/lib/helpers/rtl';

const TimelineWrapper = styled.div`
  margin-top: 20px;
  padding: 0px 12px;

  & .ant-list {
    & .ant-list-items {
      // Hide last timeline's tail
      & .ant-timeline-item:last-child {
        & .ant-timeline-item-tail {
          display: none;
        }
      }

      & .ant-timeline-item-content {
        top: -10px;

        & .ant-list-item {
          padding-left: 20px;
          padding-top: 0px;

          & .ant-list-item-meta {
            & .ant-list-item-meta-title {
              font-size: 15px;
              margin-bottom: 0;
            }
            & .ant-list-item-meta-description {
              font-size: 13px;
            }
          }
        }
      }
    }
  }
`;

export default WithDirection(TimelineWrapper);

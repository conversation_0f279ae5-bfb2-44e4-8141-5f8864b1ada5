import React from 'react';
import { Link } from 'react-router-dom';

export default ({ collapsed, logo = '', sLogo = '' }) => {
  return (
    <div className="isoLogoWrapper">
      <div>
        <Link to="/dashboard">
          {(sLogo || logo) && (
            <img
              src={collapsed ? sLogo || logo : logo}
              alt="Logo"
              width={collapsed ? 50 : 160}
            />
          )}
        </Link>
      </div>
    </div>
  );
};

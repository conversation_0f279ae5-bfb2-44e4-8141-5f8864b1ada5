/* eslint-disable react/jsx-props-no-spreading */
import React from 'react';
import { Scrollbars } from 'react-custom-scrollbars';

export default ({
  id,
  style,
  children,
  className,
  autoHide = true,
  autoHeight,
  autoHeightMin = 0,
  autoHeightMax = 200,
  sRef,
  ...rest
}) => (
  <Scrollbars
    ref={sRef}
    id={id}
    className={className}
    style={style}
    autoHide={autoHide}
    autoHideTimeout={1000}
    autoHideDuration={200}
    thumbMinSize={30}
    universal
    // For Auto Height
    autoHeight={autoHeight}
    autoHeightMin={autoHeightMin}
    autoHeightMax={autoHeightMax}
    // Rest props
    {...rest}
  >
    {children}
  </Scrollbars>
);

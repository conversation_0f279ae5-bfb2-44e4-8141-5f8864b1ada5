/* eslint-disable import/prefer-default-export */
import styled from 'styled-components';

const LayoutContentWrapper = styled.div`
  // padding: 40px 40px;
  display: flex;
  flex-flow: row wrap;
  overflow: hidden;

  .layoutWrapCon {
    display: flex;
    flex-flow: row wrap;
    align-items: flex-start;
    overflow: hidden;
    width: 100%;
  }

  @media only screen and (max-width: 767px) {
    padding: 50px 20px;
  }

  @media (max-width: 580px) {
    padding: 15px;
  }
`;

export { LayoutContentWrapper };

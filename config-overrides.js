const path = require('path');

// const rewireWebpackBundleAnalyzer = require('react-app-rewire-webpack-bundle-analyzer');

const {
  override,
  fixBabelImports,
  addWebpackAlias,
  addLessLoader,
} = require('customize-cra');

module.exports = (cnfg, env) => {
  const config = { ...cnfg };

  // Uncomment below code for generating bundle analyze report

  // if (env === 'production') {
  //   config = rewireWebpackBundleAnalyzer(config, env, {
  //     analyzerMode: 'static',
  //     reportFilename: 'report.html',
  //   });
  // }

  return Object.assign(
    config,
    override(
      fixBabelImports('import', {
        libraryName: 'antd',
        libraryDirectory: 'es',
        style: true,
      }),
      //  Customize theme variables
      addLessLoader({
        javascriptEnabled: true,
        modifyVars: {
          hack: `true;@import "${require.resolve('./public/css/theme.less')}"`, // Override with less file
        },
      }),
      // add an alias for "our" imports
      addWebpackAlias({
        '@chill/assets': path.resolve(__dirname, 'src/assets'),
        '@chill/components': path.resolve(__dirname, 'src/components'),
        '@chill/config': path.resolve(__dirname, 'src/config'),
        '@chill/containers': path.resolve(__dirname, 'src/containers'),
        '@chill/editor': path.resolve(__dirname, 'src/editor'),
        '@chill/redux': path.resolve(__dirname, 'src/redux'),
        '@chill/lib': path.resolve(__dirname, 'src/shared/common/library'),
      }),
    )(config, env),
  );
};
